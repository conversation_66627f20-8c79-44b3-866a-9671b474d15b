;METADATA=(Diff=true, UseCommands=true)
[/Script/UnrealEd.EditorPerProjectUserSettings]
bDisplayDocumentationLink=False
bDisplayActionListItemRefIds=False
bAlwaysGatherBehaviorTreeDebuggerData=False
bDisplayBlackboardKeysInAlphabeticalOrder=False
bUseSimplygonSwarm=False
SimplygonServerIP=127.0.0.1
bEnableSwarmDebugging=False
SimplygonSwarmDelay=5000
SwarmNumOfConcurrentJobs=16
SwarmMaxUploadChunkSizeInMB=100
SwarmIntermediateFolder=G:/Gamedev/RoughReality/Intermediate/Simplygon/
bShowCompilerLogOnCompileError=False
DataSourceFolder=(Path="")
bAnimationReimportWarnings=False
bConfirmEditorClose=False
bSCSEditorShowFloor=False
bAlwaysBuildUAT=True
SCSViewportCameraSpeed=4
bShowSelectionSubcomponents=True
AssetViewerProfileName=
PreviewFeatureLevel=4
PreviewPlatformName=None
PreviewShaderFormatName=None
PreviewShaderPlatformName=None
bPreviewFeatureLevelActive=False
bPreviewFeatureLevelWasDefault=True
PreviewDeviceProfileName=None

[/Script/UnrealEd.EditorStyleSettings]
ApplicationScale=1.000000
bColorVisionDeficiencyCorrection=False
bColorVisionDeficiencyCorrectionPreviewWithDeficiency=False
SelectionColor=(R=0.828000,G=0.364000,B=0.003000,A=1.000000)
AdditionalSelectionColors[0]=(R=0.019382,G=0.496933,B=1.000000,A=1.000000)
AdditionalSelectionColors[1]=(R=0.356400,G=0.040915,B=0.520996,A=1.000000)
AdditionalSelectionColors[2]=(R=1.000000,G=0.168269,B=0.332452,A=1.000000)
AdditionalSelectionColors[3]=(R=1.000000,G=0.051269,B=0.051269,A=1.000000)
AdditionalSelectionColors[4]=(R=1.000000,G=0.715693,B=0.010330,A=1.000000)
AdditionalSelectionColors[5]=(R=0.258183,G=0.539479,B=0.068478,A=1.000000)
ViewportToolOverlayColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000)
bEnableEditorWindowBackgroundColor=False
EditorWindowBackgroundColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000)
MenuSearchFieldVisibilityThreshold=10
bUseGrid=True
RegularColor=(R=0.024000,G=0.024000,B=0.024000,A=1.000000)
RuleColor=(R=0.010000,G=0.010000,B=0.010000,A=1.000000)
CenterColor=(R=0.005000,G=0.005000,B=0.005000,A=1.000000)
GridSnapSize=16
GraphBackgroundBrush=(TintColor=(SpecifiedColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),ColorUseRule=UseColor_Specified),DrawAs=Image,Tiling=NoTile,Mirroring=NoMirror,ImageType=NoImage,ImageSize=(X=32.000000,Y=32.000000),Margin=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),ResourceObject=None,OutlineSettings=(CornerRadii=(X=0.000000,Y=0.000000,Z=0.000000,W=0.000000),Color=(SpecifiedColor=(R=0.000000,G=0.000000,B=0.000000,A=0.000000),ColorUseRule=UseColor_Specified),Width=0.000000,RoundingType=HalfHeightRadius,bUseBrushTransparency=False),UVRegion=(Min=(X=0.000000,Y=0.000000),Max=(X=0.000000,Y=0.000000),bIsValid=False),bIsDynamicallyLoaded=False,ResourceName="")
bShowNativeComponentNames=True
AssetEditorOpenLocation=Default
bEnableColorizedEditorTabs=True
CurrentAppliedTheme=134380265FBB4A9CA00A1DC9770217B8

[/Script/UnrealEd.LevelEditorPlaySettings]
LaptopScreenResolutions=(Description="Apple MacBook Air 11",Width=1366,Height=768,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Air 13\"",Width=1440,Height=900,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 13\"",Width=1280,Height=800,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 13\" (Retina)",Width=2560,Height=1600,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 15\"",Width=1440,Height=900,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Apple MacBook Pro 15\" (Retina)",Width=2880,Height=1800,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
LaptopScreenResolutions=(Description="Generic 14-15.6\" Notebook",Width=1366,Height=768,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="19\" monitor",Width=1440,Height=900,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="20\" monitor",Width=1600,Height=900,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="22\" monitor",Width=1680,Height=1050,AspectRatio="16:10",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="21.5-24\" monitor",Width=1920,Height=1080,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
MonitorScreenResolutions=(Description="27\" monitor",Width=2560,Height=1440,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="iPad Pro 12.9-inch (3rd gen.)",Width=1024,Height=1366,AspectRatio="~3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro3_129")
TabletScreenResolutions=(Description="iPad Pro 12.9-inch (2nd gen.)",Width=1024,Height=1366,AspectRatio="~3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro2_129")
TabletScreenResolutions=(Description="iPad Pro 11-inch",Width=834,Height=1194,AspectRatio="5:7",bCanSwapAspectRatio=True,ProfileName="iPadPro11")
TabletScreenResolutions=(Description="iPad Pro 10.5-inch",Width=834,Height=1112,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro105")
TabletScreenResolutions=(Description="iPad Pro 12.9-inch",Width=1024,Height=1366,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro129")
TabletScreenResolutions=(Description="iPad Pro 9.7-inch",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadPro97")
TabletScreenResolutions=(Description="iPad (6th gen.)",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPad6")
TabletScreenResolutions=(Description="iPad (5th gen.)",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPad5")
TabletScreenResolutions=(Description="iPad Air 3",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadAir3")
TabletScreenResolutions=(Description="iPad Air 2",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadAir2")
TabletScreenResolutions=(Description="iPad Mini 5",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadMini5")
TabletScreenResolutions=(Description="iPad Mini 4",Width=768,Height=1024,AspectRatio="3:4",bCanSwapAspectRatio=True,ProfileName="iPadMini4")
TabletScreenResolutions=(Description="LG G Pad X 8.0",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Asus Zenpad 3s 10",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Huawei MediaPad M3",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Microsoft Surface RT",Width=768,Height=1366,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TabletScreenResolutions=(Description="Microsoft Surface Pro",Width=1080,Height=1920,AspectRatio="9:16",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="720p (HDTV, Blu-ray)",Width=1280,Height=720,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="1080i, 1080p (HDTV, Blu-ray)",Width=1920,Height=1080,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="4K Ultra HD",Width=3840,Height=2160,AspectRatio="16:9",bCanSwapAspectRatio=True,ProfileName="")
TelevisionScreenResolutions=(Description="4K Digital Cinema",Width=4096,Height=2160,AspectRatio="1.90:1",bCanSwapAspectRatio=True,ProfileName="")
GameGetsMouseControl=False
UseMouseForTouch=False
MouseControlLabelPosition=LabelAnchorMode_TopLeft
ViewportGetsHMDControl=False
bShouldMinimizeEditorOnNonVRPIE=False
bEmulateStereo=False
SoloAudioInFirstPIEClient=False
EnablePIEEnterAndExitSounds=False
PlayInEditorSoundQualityLevel=0
bUseNonRealtimeAudioDevice=False
bPreferToStreamLevelsInPIE=False
bPromoteOutputLogWarningsDuringPIE=False
NewWindowPosition=(X=-1,Y=-1)
PIEAlwaysOnTop=False
DisableStandaloneSound=False
AdditionalLaunchParameters=
BuildGameBeforeLaunch=PlayOnBuild_Default
LaunchConfiguration=LaunchConfig_Default
PackFilesForLaunch=NoPak
bAutoCompileBlueprintsOnLaunch=True
bLaunchSeparateServer=False
PlayNetMode=PIE_Standalone
RunUnderOneProcess=True
PlayNumberOfClients=1
PrimaryPIEClientIndex=0
ServerPort=17777
ClientWindowWidth=640
RouteGamepadToSecondWindow=False
CreateAudioDeviceForEveryPlayer=False
ClientWindowHeight=480
ServerMapNameOverride=
AdditionalServerGameOptions=
bShowServerDebugDrawingByDefault=True
ServerDebugDrawingColorTintStrength=0.000000
ServerDebugDrawingColorTint=(R=0.000000,G=0.000000,B=0.000000,A=1.000000)
bHMDForPrimaryProcessOnly=True
AdditionalServerLaunchParameters=
ServerFixedFPS=0
NetworkEmulationSettings=(bIsNetworkEmulationEnabled=False,EmulationTarget=Server,CurrentProfile="Custom",OutPackets=(MinLatency=0,MaxLatency=0,PacketLossPercentage=0),InPackets=(MinLatency=0,MaxLatency=0,PacketLossPercentage=0))
LastSize=(X=0,Y=0)
LastExecutedLaunchDevice=Windows@DESKTOP-68ONPC8
LastExecutedLaunchName=DESKTOP-68ONPC8
LastExecutedPIEPreviewDevice=
DeviceToEmulate=
PIESafeZoneOverride=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000)

[/Script/UnrealEd.LevelEditorViewportSettings]
FlightCameraControlExperimentalNavigation=False
MinimumOrthographicZoom=250.000000
bAllowArcballRotate=False
bAllowScreenRotate=False
bShowActorEditorContext=True
bAllowEditWidgetAxisDisplay=True
bUseLegacyCameraMovementNotifications=False
SnapToSurface=(bEnabled=False,SnapOffsetExtent=0.000000,bSnapRotation=True)
bEnableLayerSnap=False
ActiveSnapLayerIndex=0
PreserveNonUniformScale=True
PreviewMeshes=/Engine/EditorMeshes/ColorCalibrator/SM_ColorCalibrator.SM_ColorCalibrator
BillboardScale=1.000000
TransformWidgetSizeAdjustment=0
bSaveEngineStats=False
MeasuringToolUnits=MeasureUnits_Centimeters
SelectedSplinePointSizeAdjustment=0.000000
SplineLineThicknessAdjustment=0.000000
SplineTangentHandleSizeAdjustment=0.000000
SplineTangentScale=0.500000
LastInViewportMenuLocation=(X=0.000000,Y=0.000000)
MaterialForDroppedTextures=None
MaterialParamsForDroppedTextures=()
EditorViews=(("/Game/ThirdPerson/Maps/ThirdPersonMap.ThirdPersonMap", (LevelViewportsInfo=((CamPosition=(X=-1394.555908,Y=1374.702148,Z=1545.173706),CamOrthoZoom=12081.584961),(CamPosition=(X=-1394.555908,Y=1374.702148,Z=1545.173706),CamOrthoZoom=12081.584961),(CamPosition=(X=-1394.555908,Y=1374.702148,Z=1545.173706),CamOrthoZoom=12081.584961),(CamPosition=(X=-1246.098513,Y=1715.197680,Z=1558.908061),CamRotation=(Pitch=-28.539801,Yaw=-0.034974,Roll=0.000000)),(CamUpdated=True),(CamUpdated=True),(CamUpdated=True),(CamUpdated=True)))))
PropertyColorationColorForMatchingObjects=(B=0,G=0,R=255,A=255)
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport0",ConfigSettings=(ViewportType=LVT_OrthoYZ,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,DeferredLighting=1,Editor=1,BSPTriangles=0,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VirtualTexturePendingMips=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1",GameShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,DeferredLighting=1,Editor=0,BSPTriangles=0,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VirtualTexturePendingMips=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=False,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport1",ConfigSettings=(ViewportType=LVT_Perspective,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=1,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=0,Lighting=1,DeferredLighting=1,Editor=1,BSPTriangles=1,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=0,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VirtualTexturePendingMips=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1",GameShowFlagsString="PostProcessing=1,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=0,Lighting=1,DeferredLighting=1,Editor=0,BSPTriangles=1,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=0,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VirtualTexturePendingMips=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=True,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport2",ConfigSettings=(ViewportType=LVT_OrthoXZ,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,DeferredLighting=1,Editor=1,BSPTriangles=0,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VirtualTexturePendingMips=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1",GameShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,DeferredLighting=1,Editor=0,BSPTriangles=0,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VirtualTexturePendingMips=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=False,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))
PerInstanceSettings=(ConfigName="FourPanes2x2.Viewport 1.Viewport3",ConfigSettings=(ViewportType=LVT_OrthoXY,PerspViewModeIndex=VMI_Lit,OrthoViewModeIndex=VMI_BrushWireframe,EditorShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=0,CompositeEditorPrimitives=1,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=0,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=1,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=1,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,DeferredLighting=1,Editor=1,BSPTriangles=0,LargeVertices=0,Grid=1,Snap=0,MeshEdges=0,Cover=0,Splines=1,Selection=1,VisualizeLevelInstanceEditing=1,ModeWidgets=1,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=1,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=1,Fog=1,Volumes=1,Game=0,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=1,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VirtualTexturePendingMips=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1",GameShowFlagsString="PostProcessing=0,Bloom=1,LocalExposure=1,Tonemapper=1,AntiAliasing=1,TemporalAA=1,AmbientCubemap=1,EyeAdaptation=1,VisualizeHDR=0,VisualizeSkyLightIlluminance=0,VisualizeLocalExposure=0,LensFlares=1,LensDistortion=1,GlobalIllumination=1,Vignette=1,Grain=1,AmbientOcclusion=1,Decals=1,CameraImperfections=1,OnScreenDebug=1,OverrideDiffuseAndSpecular=0,LightingOnlyOverride=0,ReflectionOverride=0,VisualizeBuffer=0,VisualizeNanite=0,VisualizeLumen=0,VisualizeSubstrate=0,VisualizeGroom=0,VisualizeVirtualShadowMap=0,DirectLighting=1,DirectionalLights=1,PointLights=1,SpotLights=1,RectLights=1,ColorGrading=1,VectorFields=0,DepthOfField=1,GBufferHints=0,MotionBlur=1,CompositeEditorPrimitives=0,OpaqueCompositeEditorPrimitives=0,TestImage=0,VisualizeDOF=0,VertexColors=0,PhysicalMaterialMasks=0,Refraction=1,CameraInterpolation=1,SceneColorFringe=1,ToneCurve=1,SeparateTranslucency=1,ScreenPercentage=1,VisualizeMotionBlur=0,VisualizeMotionVectors=0,VisualizeReprojection=0,VisualizeTemporalUpscaler=0,VisualizeTSR=0,MegaLights=1,ReflectionEnvironment=1,VisualizeOutOfBoundsPixels=0,Diffuse=1,Specular=1,SelectionOutline=0,ScreenSpaceReflections=1,LumenReflections=1,ContactShadows=1,RayTracedDistanceFieldShadows=1,CapsuleShadows=1,SubsurfaceScattering=1,VisualizeSSS=0,VolumetricLightmap=1,IndirectLightingCache=1,DebugAI=0,VisLog=1,Navigation=0,GameplayDebug=1,TexturedLightProfiles=1,LightFunctions=1,InstancedStaticMeshes=1,InstancedFoliage=1,HISMCOcclusionBounds=0,HISMCClusterTree=0,VisualizeInstanceUpdates=0,InstancedGrass=1,DynamicShadows=1,Particles=1,Niagara=1,HeterogeneousVolumes=1,SkeletalMeshes=1,BuilderBrush=1,Translucency=1,BillboardSprites=1,LOD=1,LightComplexity=0,ShaderComplexity=0,StationaryLightOverlap=0,LightMapDensity=0,StreamingBounds=0,Constraints=0,MassProperties=0,CameraFrustums=0,AudioRadius=0,ForceFeedbackRadius=1,BSPSplit=0,Brushes=1,Lighting=1,DeferredLighting=1,Editor=0,BSPTriangles=0,LargeVertices=0,Grid=0,Snap=0,MeshEdges=0,Cover=0,Splines=0,Selection=0,VisualizeLevelInstanceEditing=1,ModeWidgets=0,Bounds=0,HitProxies=0,LightInfluences=0,Pivot=0,ShadowFrustums=0,Wireframe=1,Materials=1,StaticMeshes=1,Landscape=1,LightRadius=0,Fog=1,Volumes=0,Game=1,BSP=1,Collision=0,CollisionVisibility=0,CollisionPawn=0,LightShafts=1,PostProcessMaterial=1,Atmosphere=1,Cloud=1,CameraAspectRatioBars=0,CameraSafeFrames=0,TextRender=1,Rendering=1,HMDDistortion=0,StereoRendering=0,DistanceCulledPrimitives=0,VisualizeLightCulling=0,PrecomputedVisibility=1,SkyLighting=1,PreviewShadowsIndicator=1,PrecomputedVisibilityCells=0,VisualizeVolumetricLightmap=0,VolumeLightingSamples=0,Paper2DSprites=1,VisualizeDistanceFieldAO=0,VisualizeMeshDistanceFields=0,PhysicsField=0,VisualizeGlobalDistanceField=0,VisualizeLightingOnProbes=0,ScreenSpaceAO=1,DistanceFieldAO=1,LumenGlobalIllumination=1,VolumetricFog=1,VisualizeSSR=0,VisualizeShadingModels=0,VisualizeSenses=1,LODColoration=0,HLODColoration=0,QuadOverdraw=0,ShaderComplexityWithQuadOverdraw=0,PrimitiveDistanceAccuracy=0,MeshUVDensityAccuracy=0,MaterialTextureScaleAccuracy=0,OutputMaterialTextureScales=0,RequiredTextureResolution=0,VirtualTexturePendingMips=0,WidgetComponents=1,Bones=0,ServerDrawDebug=0,MediaPlanes=1,VREditing=0,OcclusionMeshes=0,VisualizeInstanceOcclusionQueries=0,DisableOcclusionQueries=0,PathTracing=0,RayTracingDebug=0,VisualizeSkyAtmosphere=0,VisualizeLightFunctionAtlas=0,VisualizeCalibrationColor=0,VisualizeCalibrationGrayscale=0,VisualizeCalibrationCustom=0,VisualizePostProcessStack=0,VirtualTexturePrimitives=0,VisualizeVolumetricCloudConservativeDensity=0,VisualizeVolumetricCloudEmptySpaceSkipping=0,VirtualShadowMapPersistentData=1,DebugDrawDistantVirtualSMLights=0,VirtualTextureResidency=1,InputDebugVisualizer=1,LumenScreenTraces=1,LumenDetailTraces=1,LumenGlobalTraces=1,LumenFarFieldTraces=1,LumenSecondaryBounces=1,LumenShortRangeAmbientOcclusion=1,NaniteMeshes=1,NaniteStreamingGeometry=1,VisualizeGPUSkinCache=0,VisualizeLWCComplexity=0,ShaderPrint=1,SceneCaptureCopySceneDepth=1,Cameras=1,Hair=1",BufferVisualizationMode="",NaniteVisualizationMode="",LumenVisualizationMode="",SubstrateVisualizationMode="",GroomVisualizationMode="",VirtualShadowMapVisualizationMode="",RayTracingDebugVisualizationMode="",GPUSkinCacheVisualizationMode="",ExposureSettings=(FixedEV100=1.000000,bFixed=False),FOVAngle=90.000000,FarViewPlane=0.000000,bIsRealtime=False,bShowOnScreenStats=True,EnabledStats=,bShowFullToolbar=True,bAllowCinematicControl=True))

[ContentBrowser]
ContentBrowserTab1.SelectedPaths=
ContentBrowserTab1.SourcesExpanded=True
ContentBrowserTab1.IsLocked=False
ContentBrowserTab1.FavoritesAreaExpanded=False
ContentBrowserTab1.PathAreaExpanded=True
ContentBrowserTab1.CollectionAreaExpanded=False
ContentBrowserTab1.FavoritesSearchAreaExpanded=False
ContentBrowserTab1.PathSearchAreaExpanded=False
ContentBrowserTab1.CollectionSearchAreaExpanded=False
ContentBrowserTab1.VerticalSplitter.FixedSlotSize0=150
ContentBrowserTab1.VerticalSplitter.SlotSize1=0.75
ContentBrowserTab1.FavoriteSplitter.SlotSize0=0.200000003
ContentBrowserTab1.FavoriteSplitter.SlotSize1=0.800000012
ContentBrowserTab1.FavoriteSplitter.SlotSize2=0.400000006
ContentBrowserTab1.PluginFilters=
ContentBrowserTab1.Favorites.SelectedPaths=
FavoritePaths=
ContentBrowserTab1.SelectedCollections=
ContentBrowserTab1.ExpandedCollections=
ContentBrowserTab1.ThumbnailSize=2
ContentBrowserTab1.CurrentViewType=1
ContentBrowserTab1.ZoomScale=0

[EditorStartup]
LastLevel=/Game/ThirdPerson/Maps/ThirdPersonMap

[ModuleFileTracking]
RoughReality.TimeStamp=2025.06.01-00.48.54
RoughReality.LastCompileMethod=External
StorageServerClient.TimeStamp=2025.04.27-13.20.10
StorageServerClient.LastCompileMethod=Unknown
CookOnTheFly.TimeStamp=2025.04.27-13.20.02
CookOnTheFly.LastCompileMethod=Unknown
StreamingFile.TimeStamp=2025.04.27-13.20.10
StreamingFile.LastCompileMethod=Unknown
NetworkFile.TimeStamp=2025.04.27-13.20.07
NetworkFile.LastCompileMethod=Unknown
PakFile.TimeStamp=2025.04.27-13.20.07
PakFile.LastCompileMethod=Unknown
RSA.TimeStamp=2025.04.27-13.20.09
RSA.LastCompileMethod=Unknown
SandboxFile.TimeStamp=2025.04.27-13.20.09
SandboxFile.LastCompileMethod=Unknown
CoreUObject.TimeStamp=2025.04.27-13.20.03
CoreUObject.LastCompileMethod=Unknown
Engine.TimeStamp=2025.04.27-13.20.05
Engine.LastCompileMethod=Unknown
UniversalObjectLocator.TimeStamp=2025.04.27-13.20.11
UniversalObjectLocator.LastCompileMethod=Unknown
Renderer.TimeStamp=2025.04.27-13.20.08
Renderer.LastCompileMethod=Unknown
AnimGraphRuntime.TimeStamp=2025.04.27-13.20.01
AnimGraphRuntime.LastCompileMethod=Unknown
SlateRHIRenderer.TimeStamp=2025.04.27-13.20.10
SlateRHIRenderer.LastCompileMethod=Unknown
Landscape.TimeStamp=2025.04.27-13.20.06
Landscape.LastCompileMethod=Unknown
RHICore.TimeStamp=2025.04.27-13.20.09
RHICore.LastCompileMethod=Unknown
RenderCore.TimeStamp=2025.04.27-13.20.08
RenderCore.LastCompileMethod=Unknown
TextureCompressor.TimeStamp=2025.04.27-13.20.10
TextureCompressor.LastCompileMethod=Unknown
OpenColorIOWrapper.TimeStamp=2025.04.27-13.20.07
OpenColorIOWrapper.LastCompileMethod=Unknown
Virtualization.TimeStamp=2025.04.27-13.20.12
Virtualization.LastCompileMethod=Unknown
MessageLog.TimeStamp=2025.04.27-13.20.07
MessageLog.LastCompileMethod=Unknown
AudioEditor.TimeStamp=2025.04.27-13.20.01
AudioEditor.LastCompileMethod=Unknown
PropertyEditor.TimeStamp=2025.04.27-13.20.08
PropertyEditor.LastCompileMethod=Unknown
AnimationModifiers.TimeStamp=2025.04.27-13.20.00
AnimationModifiers.LastCompileMethod=Unknown
IoStoreOnDemand.TimeStamp=2025.04.27-13.20.06
IoStoreOnDemand.LastCompileMethod=Unknown
OpusAudioDecoder.TimeStamp=2025.04.27-13.20.07
OpusAudioDecoder.LastCompileMethod=Unknown
VorbisAudioDecoder.TimeStamp=2025.04.27-13.20.12
VorbisAudioDecoder.LastCompileMethod=Unknown
AdpcmAudioDecoder.TimeStamp=2025.04.27-13.20.00
AdpcmAudioDecoder.LastCompileMethod=Unknown
BinkAudioDecoder.TimeStamp=2025.04.27-13.20.01
BinkAudioDecoder.LastCompileMethod=Unknown
RadAudioDecoder.TimeStamp=2025.04.27-13.20.08
RadAudioDecoder.LastCompileMethod=Unknown
FastBuildController.TimeStamp=2025.04.27-13.25.00
FastBuildController.LastCompileMethod=Unknown
UbaController.TimeStamp=2025.04.27-13.28.00
UbaController.LastCompileMethod=Unknown
XGEController.TimeStamp=2025.04.27-13.28.13
XGEController.LastCompileMethod=Unknown
PlatformCrypto.TimeStamp=2025.04.27-13.24.35
PlatformCrypto.LastCompileMethod=Unknown
PlatformCryptoTypes.TimeStamp=2025.04.27-13.24.36
PlatformCryptoTypes.LastCompileMethod=Unknown
PlatformCryptoOpenSSL.TimeStamp=2025.04.27-13.24.36
PlatformCryptoOpenSSL.LastCompileMethod=Unknown
PythonScriptPluginPreload.TimeStamp=2025.04.27-13.24.36
PythonScriptPluginPreload.LastCompileMethod=Unknown
PerforceSourceControl.TimeStamp=2025.04.27-13.23.23
PerforceSourceControl.LastCompileMethod=Unknown
SourceControl.TimeStamp=2025.04.27-13.20.10
SourceControl.LastCompileMethod=Unknown
PlasticSourceControl.TimeStamp=2025.04.27-13.23.23
PlasticSourceControl.LastCompileMethod=Unknown
DesktopPlatform.TimeStamp=2025.04.27-13.20.03
DesktopPlatform.LastCompileMethod=Unknown
ChaosCloth.TimeStamp=2025.04.27-13.23.19
ChaosCloth.LastCompileMethod=Unknown
EditorPerformance.TimeStamp=2025.04.27-13.24.07
EditorPerformance.LastCompileMethod=Unknown
EditorTelemetry.TimeStamp=2025.04.27-13.24.07
EditorTelemetry.LastCompileMethod=Unknown
NFORDenoise.TimeStamp=2025.04.27-13.24.22
NFORDenoise.LastCompileMethod=Unknown
AnalyticsLog.TimeStamp=2025.04.27-13.24.37
AnalyticsLog.LastCompileMethod=Unknown
AnalyticsHorde.TimeStamp=2025.04.27-13.24.37
AnalyticsHorde.LastCompileMethod=Unknown
StudioTelemetry.TimeStamp=2025.04.27-13.24.37
StudioTelemetry.LastCompileMethod=Unknown
Analytics.TimeStamp=2025.04.27-13.20.00
Analytics.LastCompileMethod=Unknown
TelemetryUtils.TimeStamp=2025.04.27-13.20.10
TelemetryUtils.LastCompileMethod=Unknown
NiagaraShader.TimeStamp=2025.04.27-13.25.01
NiagaraShader.LastCompileMethod=Unknown
NiagaraVertexFactories.TimeStamp=2025.04.27-13.25.01
NiagaraVertexFactories.LastCompileMethod=Unknown
ExrReaderGpu.TimeStamp=2025.04.27-13.25.16
ExrReaderGpu.LastCompileMethod=Unknown
WmfMedia.TimeStamp=2025.04.27-13.25.21
WmfMedia.LastCompileMethod=Unknown
Media.TimeStamp=2025.04.27-13.20.06
Media.LastCompileMethod=Unknown
EOSShared.TimeStamp=2025.04.27-13.25.24
EOSShared.LastCompileMethod=Unknown
OnlineServicesInterface.TimeStamp=2025.04.27-13.25.25
OnlineServicesInterface.LastCompileMethod=Unknown
OnlineServicesCommon.TimeStamp=2025.04.27-13.25.25
OnlineServicesCommon.LastCompileMethod=Unknown
OnlineServicesCommonEngineUtils.TimeStamp=2025.04.27-13.25.25
OnlineServicesCommonEngineUtils.LastCompileMethod=Unknown
OnlineSubsystem.TimeStamp=2025.04.27-13.25.27
OnlineSubsystem.LastCompileMethod=Unknown
HTTP.TimeStamp=2025.04.27-13.20.05
HTTP.LastCompileMethod=Unknown
SSL.TimeStamp=2025.04.27-13.20.10
SSL.LastCompileMethod=Unknown
XMPP.TimeStamp=2025.04.27-13.20.12
XMPP.LastCompileMethod=Unknown
WebSockets.TimeStamp=2025.04.27-13.20.12
WebSockets.LastCompileMethod=Unknown
OnlineSubsystemNULL.TimeStamp=2025.04.27-13.25.27
OnlineSubsystemNULL.LastCompileMethod=Unknown
Sockets.TimeStamp=2025.04.27-13.20.10
Sockets.LastCompileMethod=Unknown
OnlineSubsystemUtils.TimeStamp=2025.04.27-13.25.27
OnlineSubsystemUtils.LastCompileMethod=Unknown
OnlineBlueprintSupport.TimeStamp=2025.04.27-13.25.27
OnlineBlueprintSupport.LastCompileMethod=Unknown
LauncherChunkInstaller.TimeStamp=2025.04.27-13.25.32
LauncherChunkInstaller.LastCompileMethod=Unknown
AISupportModule.TimeStamp=2025.04.27-13.23.11
AISupportModule.LastCompileMethod=Unknown
ACLPlugin.TimeStamp=2025.04.27-13.23.11
ACLPlugin.LastCompileMethod=Unknown
OptimusSettings.TimeStamp=2025.04.27-13.23.15
OptimusSettings.LastCompileMethod=Unknown
PixWinPlugin.TimeStamp=2025.04.27-13.23.23
PixWinPlugin.LastCompileMethod=Unknown
RenderDocPlugin.TimeStamp=2025.04.27-13.23.23
RenderDocPlugin.LastCompileMethod=Unknown
DatasmithContent.TimeStamp=2025.04.27-13.23.33
DatasmithContent.LastCompileMethod=Unknown
GLTFExporter.TimeStamp=2025.04.27-13.23.37
GLTFExporter.LastCompileMethod=Unknown
VariantManagerContent.TimeStamp=2025.04.27-13.23.38
VariantManagerContent.LastCompileMethod=Unknown
NNEDenoiserShaders.TimeStamp=2025.04.27-13.25.24
NNEDenoiserShaders.LastCompileMethod=Unknown
ChunkDownloader.TimeStamp=2025.04.27-13.25.33
ChunkDownloader.LastCompileMethod=Unknown
ExampleDeviceProfileSelector.TimeStamp=2025.04.27-13.25.34
ExampleDeviceProfileSelector.LastCompileMethod=Unknown
ComputeFramework.TimeStamp=2025.04.27-13.25.33
ComputeFramework.LastCompileMethod=Unknown
HairStrandsCore.TimeStamp=2025.04.27-13.26.49
HairStrandsCore.LastCompileMethod=Unknown
WindowsDeviceProfileSelector.TimeStamp=2025.04.27-13.27.59
WindowsDeviceProfileSelector.LastCompileMethod=Unknown
HoldoutComposite.TimeStamp=2025.04.27-13.24.06
HoldoutComposite.LastCompileMethod=Unknown
D3D12RHI.TimeStamp=2025.04.27-13.20.03
D3D12RHI.LastCompileMethod=Unknown
WindowsPlatformFeatures.TimeStamp=2025.04.27-13.20.12
WindowsPlatformFeatures.LastCompileMethod=Unknown
GameplayMediaEncoder.TimeStamp=2025.04.27-13.20.05
GameplayMediaEncoder.LastCompileMethod=Unknown
AVEncoder.TimeStamp=2025.04.27-13.20.01
AVEncoder.LastCompileMethod=Unknown
Chaos.TimeStamp=2025.04.27-13.20.02
Chaos.LastCompileMethod=Unknown
GeometryCore.TimeStamp=2025.04.27-13.20.05
GeometryCore.LastCompileMethod=Unknown
ChaosSolverEngine.TimeStamp=2025.04.27-13.20.02
ChaosSolverEngine.LastCompileMethod=Unknown
ChaosVDRuntime.TimeStamp=2025.04.27-13.20.02
ChaosVDRuntime.LastCompileMethod=Unknown
DirectoryWatcher.TimeStamp=2025.04.27-13.20.03
DirectoryWatcher.LastCompileMethod=Unknown
Settings.TimeStamp=2025.04.27-13.20.09
Settings.LastCompileMethod=Unknown
InputCore.TimeStamp=2025.04.27-13.20.05
InputCore.LastCompileMethod=Unknown
TargetPlatform.TimeStamp=2025.04.27-13.20.10
TargetPlatform.LastCompileMethod=Unknown
TurnkeySupport.TimeStamp=2025.04.27-13.20.11
TurnkeySupport.LastCompileMethod=Unknown
TextureFormat.TimeStamp=2025.04.27-13.20.10
TextureFormat.LastCompileMethod=Unknown
TextureFormatASTC.TimeStamp=2025.04.27-13.20.10
TextureFormatASTC.LastCompileMethod=Unknown
TextureFormatDXT.TimeStamp=2025.04.27-13.20.10
TextureFormatDXT.LastCompileMethod=Unknown
TextureFormatETC2.TimeStamp=2025.04.27-13.20.10
TextureFormatETC2.LastCompileMethod=Unknown
TextureFormatIntelISPCTexComp.TimeStamp=2025.04.27-13.20.10
TextureFormatIntelISPCTexComp.LastCompileMethod=Unknown
TextureFormatUncompressed.TimeStamp=2025.04.27-13.20.10
TextureFormatUncompressed.LastCompileMethod=Unknown
TextureFormatOodle.TimeStamp=2025.04.27-13.23.23
TextureFormatOodle.LastCompileMethod=Unknown
ImageWrapper.TimeStamp=2025.04.27-13.20.05
ImageWrapper.LastCompileMethod=Unknown
AndroidTargetPlatform.TimeStamp=2025.04.27-13.19.45
AndroidTargetPlatform.LastCompileMethod=Unknown
AndroidTargetPlatformSettings.TimeStamp=2025.04.27-13.19.45
AndroidTargetPlatformSettings.LastCompileMethod=Unknown
AndroidTargetPlatformControls.TimeStamp=2025.04.27-13.19.45
AndroidTargetPlatformControls.LastCompileMethod=Unknown
IOSTargetPlatform.TimeStamp=2025.04.27-13.19.50
IOSTargetPlatform.LastCompileMethod=Unknown
IOSTargetPlatformSettings.TimeStamp=2025.04.27-13.19.50
IOSTargetPlatformSettings.LastCompileMethod=Unknown
IOSTargetPlatformControls.TimeStamp=2025.04.27-13.19.50
IOSTargetPlatformControls.LastCompileMethod=Unknown
LinuxTargetPlatform.TimeStamp=2025.04.27-13.19.50
LinuxTargetPlatform.LastCompileMethod=Unknown
LinuxTargetPlatformSettings.TimeStamp=2025.04.27-13.19.50
LinuxTargetPlatformSettings.LastCompileMethod=Unknown
LinuxTargetPlatformControls.TimeStamp=2025.04.27-13.19.50
LinuxTargetPlatformControls.LastCompileMethod=Unknown
LinuxArm64TargetPlatform.TimeStamp=2025.04.27-13.19.50
LinuxArm64TargetPlatform.LastCompileMethod=Unknown
LinuxArm64TargetPlatformSettings.TimeStamp=2025.04.27-13.19.50
LinuxArm64TargetPlatformSettings.LastCompileMethod=Unknown
LinuxArm64TargetPlatformControls.TimeStamp=2025.04.27-13.19.50
LinuxArm64TargetPlatformControls.LastCompileMethod=Unknown
MacTargetPlatform.TimeStamp=2025.04.27-13.20.06
MacTargetPlatform.LastCompileMethod=Unknown
MacTargetPlatformSettings.TimeStamp=2025.04.27-13.20.06
MacTargetPlatformSettings.LastCompileMethod=Unknown
MacTargetPlatformControls.TimeStamp=2025.04.27-13.20.06
MacTargetPlatformControls.LastCompileMethod=Unknown
TVOSTargetPlatform.TimeStamp=2025.04.27-13.19.50
TVOSTargetPlatform.LastCompileMethod=Unknown
TVOSTargetPlatformSettings.TimeStamp=2025.04.27-13.19.50
TVOSTargetPlatformSettings.LastCompileMethod=Unknown
TVOSTargetPlatformControls.TimeStamp=2025.04.27-13.19.50
TVOSTargetPlatformControls.LastCompileMethod=Unknown
WindowsTargetPlatform.TimeStamp=2025.04.27-13.20.12
WindowsTargetPlatform.LastCompileMethod=Unknown
WindowsTargetPlatformSettings.TimeStamp=2025.04.27-13.20.12
WindowsTargetPlatformSettings.LastCompileMethod=Unknown
WindowsTargetPlatformControls.TimeStamp=2025.04.27-13.20.12
WindowsTargetPlatformControls.LastCompileMethod=Unknown
AudioFormatOPUS.TimeStamp=2025.04.27-13.20.01
AudioFormatOPUS.LastCompileMethod=Unknown
AudioFormatOGG.TimeStamp=2025.04.27-13.20.01
AudioFormatOGG.LastCompileMethod=Unknown
AudioFormatADPCM.TimeStamp=2025.04.27-13.20.01
AudioFormatADPCM.LastCompileMethod=Unknown
AudioFormatBINK.TimeStamp=2025.04.27-13.20.01
AudioFormatBINK.LastCompileMethod=Unknown
AudioFormatRAD.TimeStamp=2025.04.27-13.20.01
AudioFormatRAD.LastCompileMethod=Unknown
ShaderFormatVectorVM.TimeStamp=2025.04.27-13.20.09
ShaderFormatVectorVM.LastCompileMethod=Unknown
ShaderFormatD3D.TimeStamp=2025.04.27-13.20.09
ShaderFormatD3D.LastCompileMethod=Unknown
ShaderFormatOpenGL.TimeStamp=2025.04.27-13.20.09
ShaderFormatOpenGL.LastCompileMethod=Unknown
VulkanShaderFormat.TimeStamp=2025.04.27-13.20.12
VulkanShaderFormat.LastCompileMethod=Unknown
MetalShaderFormat.TimeStamp=2025.04.27-13.20.07
MetalShaderFormat.LastCompileMethod=Unknown
DerivedDataCache.TimeStamp=2025.04.27-13.20.03
DerivedDataCache.LastCompileMethod=Unknown
ShaderPreprocessor.TimeStamp=2025.04.27-13.20.09
ShaderPreprocessor.LastCompileMethod=Unknown
NullInstallBundleManager.TimeStamp=2025.04.27-13.20.07
NullInstallBundleManager.LastCompileMethod=Unknown
AssetRegistry.TimeStamp=2025.04.27-13.20.01
AssetRegistry.LastCompileMethod=Unknown
TargetDeviceServices.TimeStamp=2025.04.27-13.20.10
TargetDeviceServices.LastCompileMethod=Unknown
MeshUtilities.TimeStamp=2025.04.27-13.20.07
MeshUtilities.LastCompileMethod=Unknown
MaterialBaking.TimeStamp=2025.04.27-13.20.06
MaterialBaking.LastCompileMethod=Unknown
MeshMergeUtilities.TimeStamp=2025.04.27-13.20.07
MeshMergeUtilities.LastCompileMethod=Unknown
MeshReductionInterface.TimeStamp=2025.04.27-13.20.07
MeshReductionInterface.LastCompileMethod=Unknown
QuadricMeshReduction.TimeStamp=2025.04.27-13.20.08
QuadricMeshReduction.LastCompileMethod=Unknown
ProxyLODMeshReduction.TimeStamp=2025.04.27-13.23.25
ProxyLODMeshReduction.LastCompileMethod=Unknown
SkeletalMeshReduction.TimeStamp=2025.04.27-13.24.37
SkeletalMeshReduction.LastCompileMethod=Unknown
MeshBoneReduction.TimeStamp=2025.04.27-13.20.07
MeshBoneReduction.LastCompileMethod=Unknown
StaticMeshDescription.TimeStamp=2025.04.27-13.20.10
StaticMeshDescription.LastCompileMethod=Unknown
GeometryProcessingInterfaces.TimeStamp=2025.04.27-13.20.05
GeometryProcessingInterfaces.LastCompileMethod=Unknown
NaniteBuilder.TimeStamp=2025.04.27-13.20.07
NaniteBuilder.LastCompileMethod=Unknown
MeshBuilder.TimeStamp=2025.04.27-13.20.07
MeshBuilder.LastCompileMethod=Unknown
KismetCompiler.TimeStamp=2025.04.27-13.20.06
KismetCompiler.LastCompileMethod=Unknown
MovieSceneTools.TimeStamp=2025.04.27-13.20.07
MovieSceneTools.LastCompileMethod=Unknown
Sequencer.TimeStamp=2025.04.27-13.20.09
Sequencer.LastCompileMethod=Unknown
CurveEditor.TimeStamp=2025.04.27-13.20.03
CurveEditor.LastCompileMethod=Unknown
AssetDefinition.TimeStamp=2025.04.27-13.20.01
AssetDefinition.LastCompileMethod=Unknown
Core.TimeStamp=2025.04.27-13.20.03
Core.LastCompileMethod=Unknown
Networking.TimeStamp=2025.04.27-13.20.07
Networking.LastCompileMethod=Unknown
LiveCoding.TimeStamp=2025.04.27-13.20.06
LiveCoding.LastCompileMethod=Unknown
HeadMountedDisplay.TimeStamp=2025.04.27-13.20.05
HeadMountedDisplay.LastCompileMethod=Unknown
SourceCodeAccess.TimeStamp=2025.04.27-13.20.10
SourceCodeAccess.LastCompileMethod=Unknown
Messaging.TimeStamp=2025.04.27-13.20.07
Messaging.LastCompileMethod=Unknown
MRMesh.TimeStamp=2025.04.27-13.20.07
MRMesh.LastCompileMethod=Unknown
UnrealEd.TimeStamp=2025.04.27-13.20.11
UnrealEd.LastCompileMethod=Unknown
LandscapeEditorUtilities.TimeStamp=2025.04.27-13.20.06
LandscapeEditorUtilities.LastCompileMethod=Unknown
SubobjectDataInterface.TimeStamp=2025.04.27-13.20.10
SubobjectDataInterface.LastCompileMethod=Unknown
SlateCore.TimeStamp=2025.04.27-13.20.10
SlateCore.LastCompileMethod=Unknown
Slate.TimeStamp=2025.04.27-13.20.10
Slate.LastCompileMethod=Unknown
SlateReflector.TimeStamp=2025.04.27-13.20.10
SlateReflector.LastCompileMethod=Unknown
EditorStyle.TimeStamp=2025.04.27-13.20.03
EditorStyle.LastCompileMethod=Unknown
UMG.TimeStamp=2025.04.27-13.20.11
UMG.LastCompileMethod=Unknown
UMGEditor.TimeStamp=2025.04.27-13.20.11
UMGEditor.LastCompileMethod=Unknown
AssetTools.TimeStamp=2025.04.27-13.20.01
AssetTools.LastCompileMethod=Unknown
ScriptableEditorWidgets.TimeStamp=2025.04.27-13.20.09
ScriptableEditorWidgets.LastCompileMethod=Unknown
CollisionAnalyzer.TimeStamp=2025.04.27-13.20.02
CollisionAnalyzer.LastCompileMethod=Unknown
WorkspaceMenuStructure.TimeStamp=2025.04.27-13.20.12
WorkspaceMenuStructure.LastCompileMethod=Unknown
FunctionalTesting.TimeStamp=2025.04.27-13.20.05
FunctionalTesting.LastCompileMethod=Unknown
BehaviorTreeEditor.TimeStamp=2025.04.27-13.20.01
BehaviorTreeEditor.LastCompileMethod=Unknown
GameplayTasksEditor.TimeStamp=2025.04.27-13.20.05
GameplayTasksEditor.LastCompileMethod=Unknown
StringTableEditor.TimeStamp=2025.04.27-13.20.10
StringTableEditor.LastCompileMethod=Unknown
VREditor.TimeStamp=2025.04.27-13.20.12
VREditor.LastCompileMethod=Unknown
Overlay.TimeStamp=2025.04.27-13.20.07
Overlay.LastCompileMethod=Unknown
OverlayEditor.TimeStamp=2025.04.27-13.20.07
OverlayEditor.LastCompileMethod=Unknown
MediaAssets.TimeStamp=2025.04.27-13.20.06
MediaAssets.LastCompileMethod=Unknown
ClothingSystemRuntimeNv.TimeStamp=2025.04.27-13.20.02
ClothingSystemRuntimeNv.LastCompileMethod=Unknown
ClothingSystemEditor.TimeStamp=2025.04.27-13.20.02
ClothingSystemEditor.LastCompileMethod=Unknown
AnimationDataController.TimeStamp=2025.04.27-13.20.00
AnimationDataController.LastCompileMethod=Unknown
TimeManagement.TimeStamp=2025.04.27-13.20.10
TimeManagement.LastCompileMethod=Unknown
AnimGraph.TimeStamp=2025.04.27-13.20.01
AnimGraph.LastCompileMethod=Unknown
WorldPartitionEditor.TimeStamp=2025.04.27-13.20.12
WorldPartitionEditor.LastCompileMethod=Unknown
PacketHandler.TimeStamp=2025.04.27-13.20.07
PacketHandler.LastCompileMethod=Unknown
NetworkReplayStreaming.TimeStamp=2025.04.27-13.20.07
NetworkReplayStreaming.LastCompileMethod=Unknown
MassEntity.TimeStamp=2025.04.27-13.20.06
MassEntity.LastCompileMethod=Unknown
MassEntityTestSuite.TimeStamp=2025.04.27-13.20.06
MassEntityTestSuite.LastCompileMethod=Unknown
AndroidFileServer.TimeStamp=2025.04.27-13.25.32
AndroidFileServer.LastCompileMethod=Unknown
WebMMoviePlayer.TimeStamp=2025.04.27-13.27.59
WebMMoviePlayer.LastCompileMethod=Unknown
WindowsMoviePlayer.TimeStamp=2025.04.27-13.27.59
WindowsMoviePlayer.LastCompileMethod=Unknown
EnhancedInput.TimeStamp=2025.04.27-13.23.28
EnhancedInput.LastCompileMethod=Unknown
InputBlueprintNodes.TimeStamp=2025.04.27-13.23.28
InputBlueprintNodes.LastCompileMethod=Unknown
BlueprintGraph.TimeStamp=2025.04.27-13.20.01
BlueprintGraph.LastCompileMethod=Unknown
ChaosCaching.TimeStamp=2025.04.27-13.23.50
ChaosCaching.LastCompileMethod=Unknown
ChaosCachingEditor.TimeStamp=2025.04.27-13.23.50
ChaosCachingEditor.LastCompileMethod=Unknown
LevelEditor.TimeStamp=2025.04.27-13.20.06
LevelEditor.LastCompileMethod=Unknown
MainFrame.TimeStamp=2025.04.27-13.20.06
MainFrame.LastCompileMethod=Unknown
HotReload.TimeStamp=2025.04.27-13.20.05
HotReload.LastCompileMethod=Unknown
CommonMenuExtensions.TimeStamp=2025.04.27-13.20.02
CommonMenuExtensions.LastCompileMethod=Unknown
PixelInspectorModule.TimeStamp=2025.04.27-13.20.08
PixelInspectorModule.LastCompileMethod=Unknown
TakeRecorder.TimeStamp=2025.04.27-13.28.08
TakeRecorder.LastCompileMethod=Unknown
FullBodyIK.TimeStamp=2025.04.27-13.24.08
FullBodyIK.LastCompileMethod=Unknown
PBIK.TimeStamp=2025.04.27-13.24.08
PBIK.LastCompileMethod=Unknown
PythonScriptPlugin.TimeStamp=2025.04.27-13.24.36
PythonScriptPlugin.LastCompileMethod=Unknown
NiagaraCore.TimeStamp=2025.04.27-13.25.00
NiagaraCore.LastCompileMethod=Unknown
Niagara.TimeStamp=2025.04.27-13.25.00
Niagara.LastCompileMethod=Unknown
NiagaraEditor.TimeStamp=2025.04.27-13.25.01
NiagaraEditor.LastCompileMethod=Unknown
ContentBrowser.TimeStamp=2025.04.27-13.20.02
ContentBrowser.LastCompileMethod=Unknown
ContentBrowserData.TimeStamp=2025.04.27-13.20.02
ContentBrowserData.LastCompileMethod=Unknown
ToolMenus.TimeStamp=2025.04.27-13.20.10
ToolMenus.LastCompileMethod=Unknown
LevelSequence.TimeStamp=2025.04.27-13.20.06
LevelSequence.LastCompileMethod=Unknown
SignalProcessing.TimeStamp=2025.04.27-13.20.09
SignalProcessing.LastCompileMethod=Unknown
NiagaraAnimNotifies.TimeStamp=2025.04.27-13.25.00
NiagaraAnimNotifies.LastCompileMethod=Unknown
NiagaraSimCaching.TimeStamp=2025.04.27-13.25.11
NiagaraSimCaching.LastCompileMethod=Unknown
NiagaraSimCachingEditor.TimeStamp=2025.04.27-13.25.11
NiagaraSimCachingEditor.LastCompileMethod=Unknown
ImgMediaEngine.TimeStamp=2025.04.27-13.25.16
ImgMediaEngine.LastCompileMethod=Unknown
TcpMessaging.TimeStamp=2025.04.27-13.25.21
TcpMessaging.LastCompileMethod=Unknown
UdpMessaging.TimeStamp=2025.04.27-13.25.21
UdpMessaging.LastCompileMethod=Unknown
EnvironmentQueryEditor.TimeStamp=2025.04.27-13.23.11
EnvironmentQueryEditor.LastCompileMethod=Unknown
AnimationData.TimeStamp=2025.04.27-13.23.11
AnimationData.LastCompileMethod=Unknown
OptimusCore.TimeStamp=2025.04.27-13.23.15
OptimusCore.LastCompileMethod=Unknown
OptimusDeveloper.TimeStamp=2025.04.27-13.23.15
OptimusDeveloper.LastCompileMethod=Unknown
Paper2D.TimeStamp=2025.04.27-13.23.10
Paper2D.LastCompileMethod=Unknown
IKRig.TimeStamp=2025.04.27-13.23.16
IKRig.LastCompileMethod=Unknown
IKRigDeveloper.TimeStamp=2025.04.27-13.23.16
IKRigDeveloper.LastCompileMethod=Unknown
ControlRig.TimeStamp=2025.04.27-13.23.12
ControlRig.LastCompileMethod=Unknown
Constraints.TimeStamp=2025.04.27-13.20.02
Constraints.LastCompileMethod=Unknown
ControlRigDeveloper.TimeStamp=2025.04.27-13.23.12
ControlRigDeveloper.LastCompileMethod=Unknown
EngineCameras.TimeStamp=2025.04.27-13.23.19
EngineCameras.LastCompileMethod=Unknown
GameplayCameras.TimeStamp=2025.04.27-13.23.19
GameplayCameras.LastCompileMethod=Unknown
AnimationSharing.TimeStamp=2025.04.27-13.23.21
AnimationSharing.LastCompileMethod=Unknown
PropertyAccessNode.TimeStamp=2025.04.27-13.23.23
PropertyAccessNode.LastCompileMethod=Unknown
RigLogicLib.TimeStamp=2025.04.27-13.23.17
RigLogicLib.LastCompileMethod=Unknown
RigLogicLibTest.TimeStamp=2025.04.27-13.23.18
RigLogicLibTest.LastCompileMethod=Unknown
RigLogicDeveloper.TimeStamp=2025.04.27-13.23.17
RigLogicDeveloper.LastCompileMethod=Unknown
AssetManagerEditor.TimeStamp=2025.04.27-13.23.24
AssetManagerEditor.LastCompileMethod=Unknown
TreeMap.TimeStamp=2025.04.27-13.20.11
TreeMap.LastCompileMethod=Unknown
DataValidation.TimeStamp=2025.04.27-13.23.24
DataValidation.LastCompileMethod=Unknown
FacialAnimation.TimeStamp=2025.04.27-13.23.24
FacialAnimation.LastCompileMethod=Unknown
FacialAnimationEditor.TimeStamp=2025.04.27-13.23.24
FacialAnimationEditor.LastCompileMethod=Unknown
GameplayTagsEditor.TimeStamp=2025.04.27-13.23.24
GameplayTagsEditor.LastCompileMethod=Unknown
InterchangeNodes.TimeStamp=2025.04.27-13.25.12
InterchangeNodes.LastCompileMethod=Unknown
InterchangeFactoryNodes.TimeStamp=2025.04.27-13.25.12
InterchangeFactoryNodes.LastCompileMethod=Unknown
InterchangeImport.TimeStamp=2025.04.27-13.25.12
InterchangeImport.LastCompileMethod=Unknown
InterchangePipelines.TimeStamp=2025.04.27-13.25.12
InterchangePipelines.LastCompileMethod=Unknown
ActorSequence.TimeStamp=2025.04.27-13.25.21
ActorSequence.LastCompileMethod=Unknown
NNERuntimeORT.TimeStamp=2025.04.27-13.25.24
NNERuntimeORT.LastCompileMethod=Unknown
NNEEditor.TimeStamp=2025.04.27-13.20.07
NNEEditor.LastCompileMethod=Unknown
AudioSynesthesiaCore.TimeStamp=2025.04.27-13.25.32
AudioSynesthesiaCore.LastCompileMethod=Unknown
AudioSynesthesia.TimeStamp=2025.04.27-13.25.32
AudioSynesthesia.LastCompileMethod=Unknown
AudioAnalyzer.TimeStamp=2025.04.27-13.20.01
AudioAnalyzer.LastCompileMethod=Unknown
CustomMeshComponent.TimeStamp=2025.04.27-13.25.33
CustomMeshComponent.LastCompileMethod=Unknown
LocationServicesBPLibrary.TimeStamp=2025.04.27-13.27.07
LocationServicesBPLibrary.LastCompileMethod=Unknown
MetasoundGraphCore.TimeStamp=2025.04.27-13.27.09
MetasoundGraphCore.LastCompileMethod=Unknown
MetasoundGenerator.TimeStamp=2025.04.27-13.27.09
MetasoundGenerator.LastCompileMethod=Unknown
MetasoundFrontend.TimeStamp=2025.04.27-13.27.09
MetasoundFrontend.LastCompileMethod=Unknown
MetasoundStandardNodes.TimeStamp=2025.04.27-13.27.10
MetasoundStandardNodes.LastCompileMethod=Unknown
MetasoundEngine.TimeStamp=2025.04.27-13.27.09
MetasoundEngine.LastCompileMethod=Unknown
WaveTable.TimeStamp=2025.04.27-13.27.59
WaveTable.LastCompileMethod=Unknown
MetasoundEngineTest.TimeStamp=2025.04.27-13.27.09
MetasoundEngineTest.LastCompileMethod=Unknown
MetasoundEditor.TimeStamp=2025.04.27-13.27.09
MetasoundEditor.LastCompileMethod=Unknown
AudioWidgets.TimeStamp=2025.04.27-13.25.32
AudioWidgets.LastCompileMethod=Unknown
AdvancedWidgets.TimeStamp=2025.04.27-13.20.00
AdvancedWidgets.LastCompileMethod=Unknown
MsQuicRuntime.TimeStamp=2025.04.27-13.27.16
MsQuicRuntime.LastCompileMethod=Unknown
PropertyAccessEditor.TimeStamp=2025.04.27-13.27.40
PropertyAccessEditor.LastCompileMethod=Unknown
ProceduralMeshComponent.TimeStamp=2025.04.27-13.27.40
ProceduralMeshComponent.LastCompileMethod=Unknown
ResonanceAudio.TimeStamp=2025.04.27-13.27.40
ResonanceAudio.LastCompileMethod=Unknown
SignificanceManager.TimeStamp=2025.04.27-13.27.42
SignificanceManager.LastCompileMethod=Unknown
RigVM.TimeStamp=2025.04.27-13.27.41
RigVM.LastCompileMethod=Unknown
RigVMDeveloper.TimeStamp=2025.04.27-13.27.41
RigVMDeveloper.LastCompileMethod=Unknown
SoundFields.TimeStamp=2025.04.27-13.27.43
SoundFields.LastCompileMethod=Unknown
StateTreeModule.TimeStamp=2025.04.27-13.27.43
StateTreeModule.LastCompileMethod=Unknown
TraceServices.TimeStamp=2025.04.27-13.20.11
TraceServices.LastCompileMethod=Unknown
TraceAnalysis.TimeStamp=2025.04.27-13.20.10
TraceAnalysis.LastCompileMethod=Unknown
StateTreeTestSuite.TimeStamp=2025.04.27-13.27.43
StateTreeTestSuite.LastCompileMethod=Unknown
Synthesis.TimeStamp=2025.04.27-13.27.44
Synthesis.LastCompileMethod=Unknown
CableComponent.TimeStamp=2025.04.27-13.25.33
CableComponent.LastCompileMethod=Unknown
SQLiteCore.TimeStamp=2025.04.27-13.25.33
SQLiteCore.LastCompileMethod=Unknown
Concert.TimeStamp=2025.04.27-13.23.21
Concert.LastCompileMethod=Unknown
ConcertClient.TimeStamp=2025.04.27-13.23.21
ConcertClient.LastCompileMethod=Unknown
ConcertTransport.TimeStamp=2025.04.27-13.23.21
ConcertTransport.LastCompileMethod=Unknown
ConcertServer.TimeStamp=2025.04.27-13.23.21
ConcertServer.LastCompileMethod=Unknown
ConcertSyncCore.TimeStamp=2025.04.27-13.23.22
ConcertSyncCore.LastCompileMethod=Unknown
ClassViewer.TimeStamp=2025.04.27-13.20.02
ClassViewer.LastCompileMethod=Unknown
ChaosClothEditor.TimeStamp=2025.04.27-13.23.19
ChaosClothEditor.LastCompileMethod=Unknown
ChaosVD.TimeStamp=2025.04.27-13.23.20
ChaosVD.LastCompileMethod=Unknown
ChaosVDBlueprint.TimeStamp=2025.04.27-13.23.20
ChaosVDBlueprint.LastCompileMethod=Unknown
InputEditor.TimeStamp=2025.04.27-13.23.28
InputEditor.LastCompileMethod=Unknown
MeshPaintEditorMode.TimeStamp=2025.04.27-13.25.21
MeshPaintEditorMode.LastCompileMethod=Unknown
MeshPaintingToolset.TimeStamp=2025.04.27-13.25.21
MeshPaintingToolset.LastCompileMethod=Unknown
RenderGraphInsights.TimeStamp=2025.04.27-13.25.32
RenderGraphInsights.LastCompileMethod=Unknown
TraceUtilities.TimeStamp=2025.04.27-13.28.00
TraceUtilities.LastCompileMethod=Unknown
EditorTraceUtilities.TimeStamp=2025.04.27-13.28.00
EditorTraceUtilities.LastCompileMethod=Unknown
TraceTools.TimeStamp=2025.04.27-13.20.11
TraceTools.LastCompileMethod=Unknown
WorldMetricsCore.TimeStamp=2025.04.27-13.28.13
WorldMetricsCore.LastCompileMethod=Unknown
WorldMetricsTest.TimeStamp=2025.04.27-13.28.13
WorldMetricsTest.LastCompileMethod=Unknown
CsvMetrics.TimeStamp=2025.04.27-13.28.13
CsvMetrics.LastCompileMethod=Unknown
AdvancedRenamer.TimeStamp=2025.04.27-13.23.38
AdvancedRenamer.LastCompileMethod=Unknown
AutomationUtils.TimeStamp=2025.04.27-13.23.42
AutomationUtils.LastCompileMethod=Unknown
AutomationUtilsEditor.TimeStamp=2025.04.27-13.23.42
AutomationUtilsEditor.LastCompileMethod=Unknown
BackChannel.TimeStamp=2025.04.27-13.23.50
BackChannel.LastCompileMethod=Unknown
FractureEditor.TimeStamp=2025.04.27-13.23.50
FractureEditor.LastCompileMethod=Unknown
ChaosNiagara.TimeStamp=2025.04.27-13.24.04
ChaosNiagara.LastCompileMethod=Unknown
ChaosSolverEditor.TimeStamp=2025.04.27-13.24.04
ChaosSolverEditor.LastCompileMethod=Unknown
ChaosUserDataPT.TimeStamp=2025.04.27-13.24.04
ChaosUserDataPT.LastCompileMethod=Unknown
DataflowAssetTools.TimeStamp=2025.04.27-13.24.06
DataflowAssetTools.LastCompileMethod=Unknown
DataflowEnginePlugin.TimeStamp=2025.04.27-13.24.06
DataflowEnginePlugin.LastCompileMethod=Unknown
DataflowSimulation.TimeStamp=2025.04.27-13.20.03
DataflowSimulation.LastCompileMethod=Unknown
DataflowNodes.TimeStamp=2025.04.27-13.24.06
DataflowNodes.LastCompileMethod=Unknown
TedsCore.TimeStamp=2025.04.27-13.24.07
TedsCore.LastCompileMethod=Unknown
TypedElementFramework.TimeStamp=2025.04.27-13.20.11
TypedElementFramework.LastCompileMethod=Unknown
MassEntityEditor.TimeStamp=2025.04.27-13.20.06
MassEntityEditor.LastCompileMethod=Unknown
MassEntityDebugger.TimeStamp=2025.04.27-13.20.06
MassEntityDebugger.LastCompileMethod=Unknown
TedsUI.TimeStamp=2025.04.27-13.24.07
TedsUI.LastCompileMethod=Unknown
GeometryCollectionEditor.TimeStamp=2025.04.27-13.24.08
GeometryCollectionEditor.LastCompileMethod=Unknown
GeometryCollectionTracks.TimeStamp=2025.04.27-13.24.08
GeometryCollectionTracks.LastCompileMethod=Unknown
GeometryCollectionSequencer.TimeStamp=2025.04.27-13.24.08
GeometryCollectionSequencer.LastCompileMethod=Unknown
GeometryCollectionEngine.TimeStamp=2025.04.27-13.20.05
GeometryCollectionEngine.LastCompileMethod=Unknown
GeometryCollectionNodes.TimeStamp=2025.04.27-13.24.08
GeometryCollectionNodes.LastCompileMethod=Unknown
GeometryCollectionDepNodes.TimeStamp=2025.04.27-13.24.08
GeometryCollectionDepNodes.LastCompileMethod=Unknown
GeometryFlowCore.TimeStamp=2025.04.27-13.24.09
GeometryFlowCore.LastCompileMethod=Unknown
GeometryFlowMeshProcessing.TimeStamp=2025.04.27-13.24.09
GeometryFlowMeshProcessing.LastCompileMethod=Unknown
GeometryFlowMeshProcessingEditor.TimeStamp=2025.04.27-13.24.09
GeometryFlowMeshProcessingEditor.LastCompileMethod=Unknown
LocalizableMessage.TimeStamp=2025.04.27-13.24.12
LocalizableMessage.LastCompileMethod=Unknown
LocalizableMessageBlueprint.TimeStamp=2025.04.27-13.24.12
LocalizableMessageBlueprint.LastCompileMethod=Unknown
MeshModelingToolsExp.TimeStamp=2025.04.27-13.24.13
MeshModelingToolsExp.LastCompileMethod=Unknown
MeshModelingToolsEditorOnlyExp.TimeStamp=2025.04.27-13.24.12
MeshModelingToolsEditorOnlyExp.LastCompileMethod=Unknown
GeometryProcessingAdapters.TimeStamp=2025.04.27-13.24.12
GeometryProcessingAdapters.LastCompileMethod=Unknown
ModelingEditorUI.TimeStamp=2025.04.27-13.24.13
ModelingEditorUI.LastCompileMethod=Unknown
ModelingUI.TimeStamp=2025.04.27-13.24.13
ModelingUI.LastCompileMethod=Unknown
SkeletalMeshModifiers.TimeStamp=2025.04.27-13.24.13
SkeletalMeshModifiers.LastCompileMethod=Unknown
ToolPresetAsset.TimeStamp=2025.04.27-13.24.49
ToolPresetAsset.LastCompileMethod=Unknown
ToolPresetEditor.TimeStamp=2025.04.27-13.24.49
ToolPresetEditor.LastCompileMethod=Unknown
NiagaraBlueprintNodes.TimeStamp=2025.04.27-13.25.00
NiagaraBlueprintNodes.LastCompileMethod=Unknown
NiagaraEditorWidgets.TimeStamp=2025.04.27-13.25.01
NiagaraEditorWidgets.LastCompileMethod=Unknown
AlembicImporter.TimeStamp=2025.04.27-13.25.11
AlembicImporter.LastCompileMethod=Unknown
AlembicLibrary.TimeStamp=2025.04.27-13.25.11
AlembicLibrary.LastCompileMethod=Unknown
GeometryCache.TimeStamp=2025.04.27-13.25.34
GeometryCache.LastCompileMethod=Unknown
GeometryCacheEd.TimeStamp=2025.04.27-13.25.34
GeometryCacheEd.LastCompileMethod=Unknown
ImgMedia.TimeStamp=2025.04.27-13.25.16
ImgMedia.LastCompileMethod=Unknown
MediaCompositing.TimeStamp=2025.04.27-13.25.16
MediaCompositing.LastCompileMethod=Unknown
MediaPlate.TimeStamp=2025.04.27-13.25.16
MediaPlate.LastCompileMethod=Unknown
MediaPlateEditor.TimeStamp=2025.04.27-13.25.16
MediaPlateEditor.LastCompileMethod=Unknown
OnlineBase.TimeStamp=2025.04.27-13.25.24
OnlineBase.LastCompileMethod=Unknown
InterchangeTests.TimeStamp=2025.04.27-13.28.00
InterchangeTests.LastCompileMethod=Unknown
InterchangeTestEditor.TimeStamp=2025.04.27-13.28.00
InterchangeTestEditor.LastCompileMethod=Unknown
TakeMovieScene.TimeStamp=2025.04.27-13.28.08
TakeMovieScene.LastCompileMethod=Unknown
TakeSequencer.TimeStamp=2025.04.27-13.28.08
TakeSequencer.LastCompileMethod=Unknown
ACLPluginEditor.TimeStamp=2025.04.27-13.23.11
ACLPluginEditor.LastCompileMethod=Unknown
AnimationModifierLibrary.TimeStamp=2025.04.27-13.23.11
AnimationModifierLibrary.LastCompileMethod=Unknown
ControlRigSpline.TimeStamp=2025.04.27-13.23.15
ControlRigSpline.LastCompileMethod=Unknown
Paper2DEditor.TimeStamp=2025.04.27-13.23.10
Paper2DEditor.LastCompileMethod=Unknown
PaperSpriteSheetImporter.TimeStamp=2025.04.27-13.23.10
PaperSpriteSheetImporter.LastCompileMethod=Unknown
PaperTiledImporter.TimeStamp=2025.04.27-13.23.10
PaperTiledImporter.LastCompileMethod=Unknown
LiveLink.TimeStamp=2025.04.27-13.23.16
LiveLink.LastCompileMethod=Unknown
LiveLinkComponents.TimeStamp=2025.04.27-13.23.16
LiveLinkComponents.LastCompileMethod=Unknown
LiveLinkEditor.TimeStamp=2025.04.27-13.23.16
LiveLinkEditor.LastCompileMethod=Unknown
LiveLinkGraphNode.TimeStamp=2025.04.27-13.23.16
LiveLinkGraphNode.LastCompileMethod=Unknown
LiveLinkMovieScene.TimeStamp=2025.04.27-13.23.16
LiveLinkMovieScene.LastCompileMethod=Unknown
LiveLinkSequencer.TimeStamp=2025.04.27-13.23.16
LiveLinkSequencer.LastCompileMethod=Unknown
GameplayCamerasUncookedOnly.TimeStamp=2025.04.27-13.23.19
GameplayCamerasUncookedOnly.LastCompileMethod=Unknown
OodleNetworkHandlerComponent.TimeStamp=2025.04.27-13.23.21
OodleNetworkHandlerComponent.LastCompileMethod=Unknown
AnimationSharingEd.TimeStamp=2025.04.27-13.23.21
AnimationSharingEd.LastCompileMethod=Unknown
CLionSourceCodeAccess.TimeStamp=2025.04.27-13.23.21
CLionSourceCodeAccess.LastCompileMethod=Unknown
DumpGPUServices.TimeStamp=2025.04.27-13.23.23
DumpGPUServices.LastCompileMethod=Unknown
GitSourceControl.TimeStamp=2025.04.27-13.23.23
GitSourceControl.LastCompileMethod=Unknown
N10XSourceCodeAccess.TimeStamp=2025.04.27-13.23.23
N10XSourceCodeAccess.LastCompileMethod=Unknown
PluginUtils.TimeStamp=2025.04.27-13.23.23
PluginUtils.LastCompileMethod=Unknown
RigLogicModule.TimeStamp=2025.04.27-13.23.18
RigLogicModule.LastCompileMethod=Unknown
RigLogicEditor.TimeStamp=2025.04.27-13.23.17
RigLogicEditor.LastCompileMethod=Unknown
RiderSourceCodeAccess.TimeStamp=2025.04.27-13.23.23
RiderSourceCodeAccess.LastCompileMethod=Unknown
UObjectPlugin.TimeStamp=2025.04.27-13.23.23
UObjectPlugin.LastCompileMethod=Unknown
SubversionSourceControl.TimeStamp=2025.04.27-13.23.23
SubversionSourceControl.LastCompileMethod=Unknown
VisualStudioSourceCodeAccess.TimeStamp=2025.04.27-13.23.23
VisualStudioSourceCodeAccess.LastCompileMethod=Unknown
BlendSpaceMotionAnalysis.TimeStamp=2025.04.27-13.23.12
BlendSpaceMotionAnalysis.LastCompileMethod=Unknown
VisualStudioCodeSourceCodeAccess.TimeStamp=2025.04.27-13.23.23
VisualStudioCodeSourceCodeAccess.LastCompileMethod=Unknown
ChangelistReview.TimeStamp=2025.04.27-13.23.24
ChangelistReview.LastCompileMethod=Unknown
ColorGradingEditor.TimeStamp=2025.04.27-13.23.24
ColorGradingEditor.LastCompileMethod=Unknown
CryptoKeys.TimeStamp=2025.04.27-13.23.24
CryptoKeys.LastCompileMethod=Unknown
CryptoKeysOpenSSL.TimeStamp=2025.04.27-13.23.24
CryptoKeysOpenSSL.LastCompileMethod=Unknown
CurveEditorTools.TimeStamp=2025.04.27-13.23.24
CurveEditorTools.LastCompileMethod=Unknown
EditorDebugTools.TimeStamp=2025.04.27-13.23.24
EditorDebugTools.LastCompileMethod=Unknown
EditorScriptingUtilities.TimeStamp=2025.04.27-13.23.24
EditorScriptingUtilities.LastCompileMethod=Unknown
BlueprintHeaderView.TimeStamp=2025.04.27-13.23.24
BlueprintHeaderView.LastCompileMethod=Unknown
MaterialAnalyzer.TimeStamp=2025.04.27-13.23.25
MaterialAnalyzer.LastCompileMethod=Unknown
MeshLODToolset.TimeStamp=2025.04.27-13.23.25
MeshLODToolset.LastCompileMethod=Unknown
MobileLauncherProfileWizard.TimeStamp=2025.04.27-13.23.25
MobileLauncherProfileWizard.LastCompileMethod=Unknown
ModelingToolsEditorMode.TimeStamp=2025.04.27-13.23.25
ModelingToolsEditorMode.LastCompileMethod=Unknown
PluginBrowser.TimeStamp=2025.04.27-13.23.25
PluginBrowser.LastCompileMethod=Unknown
SequencerAnimTools.TimeStamp=2025.04.27-13.23.26
SequencerAnimTools.LastCompileMethod=Unknown
SpeedTreeImporter.TimeStamp=2025.04.27-13.23.26
SpeedTreeImporter.LastCompileMethod=Unknown
StylusInput.TimeStamp=2025.04.27-13.23.26
StylusInput.LastCompileMethod=Unknown
StylusInputDebugWidget.TimeStamp=2025.04.27-13.23.26
StylusInputDebugWidget.LastCompileMethod=Unknown
UMGWidgetPreview.TimeStamp=2025.04.27-13.23.26
UMGWidgetPreview.LastCompileMethod=Unknown
UVEditor.TimeStamp=2025.04.27-13.23.26
UVEditor.LastCompileMethod=Unknown
UVEditorTools.TimeStamp=2025.04.27-13.23.26
UVEditorTools.LastCompileMethod=Unknown
UVEditorToolsEditorOnly.TimeStamp=2025.04.27-13.23.26
UVEditorToolsEditorOnly.LastCompileMethod=Unknown
WorldPartitionHLODUtilities.TimeStamp=2025.04.27-13.23.28
WorldPartitionHLODUtilities.LastCompileMethod=Unknown
DatasmithContentEditor.TimeStamp=2025.04.27-13.23.33
DatasmithContentEditor.LastCompileMethod=Unknown
VariantManager.TimeStamp=2025.04.27-13.23.38
VariantManager.LastCompileMethod=Unknown
VariantManagerContentEditor.TimeStamp=2025.04.27-13.23.38
VariantManagerContentEditor.LastCompileMethod=Unknown
GLTFCore.TimeStamp=2025.04.27-13.25.11
GLTFCore.LastCompileMethod=Unknown
InterchangeMessages.TimeStamp=2025.04.27-13.25.12
InterchangeMessages.LastCompileMethod=Unknown
InterchangeExport.TimeStamp=2025.04.27-13.25.11
InterchangeExport.LastCompileMethod=Unknown
InterchangeDispatcher.TimeStamp=2025.04.27-13.25.11
InterchangeDispatcher.LastCompileMethod=Unknown
InterchangeCommon.TimeStamp=2025.04.27-13.25.11
InterchangeCommon.LastCompileMethod=Unknown
InterchangeCommonParser.TimeStamp=2025.04.27-13.25.11
InterchangeCommonParser.LastCompileMethod=Unknown
InterchangeFbxParser.TimeStamp=2025.04.27-13.25.12
InterchangeFbxParser.LastCompileMethod=Unknown
InterchangeEditor.TimeStamp=2025.04.27-13.25.11
InterchangeEditor.LastCompileMethod=Unknown
InterchangeEditorPipelines.TimeStamp=2025.04.27-13.25.11
InterchangeEditorPipelines.LastCompileMethod=Unknown
InterchangeEditorUtilities.TimeStamp=2025.04.27-13.25.11
InterchangeEditorUtilities.LastCompileMethod=Unknown
TemplateSequence.TimeStamp=2025.04.27-13.25.23
TemplateSequence.LastCompileMethod=Unknown
NNEDenoiser.TimeStamp=2025.04.27-13.25.24
NNEDenoiser.LastCompileMethod=Unknown
ActorLayerUtilities.TimeStamp=2025.04.27-13.25.32
ActorLayerUtilities.LastCompileMethod=Unknown
ActorLayerUtilitiesEditor.TimeStamp=2025.04.27-13.25.32
ActorLayerUtilitiesEditor.LastCompileMethod=Unknown
AppleImageUtils.TimeStamp=2025.04.27-13.25.32
AppleImageUtils.LastCompileMethod=Unknown
AppleImageUtilsBlueprintSupport.TimeStamp=2025.04.27-13.25.32
AppleImageUtilsBlueprintSupport.LastCompileMethod=Unknown
SequencerScripting.TimeStamp=2025.04.27-13.25.23
SequencerScripting.LastCompileMethod=Unknown
SequencerScriptingEditor.TimeStamp=2025.04.27-13.25.23
SequencerScriptingEditor.LastCompileMethod=Unknown
AssetTags.TimeStamp=2025.04.27-13.25.32
AssetTags.LastCompileMethod=Unknown
AudioCapture.TimeStamp=2025.04.27-13.25.32
AudioCapture.LastCompileMethod=Unknown
AudioCaptureWasapi.TimeStamp=2025.04.27-13.20.01
AudioCaptureWasapi.LastCompileMethod=Unknown
AudioWidgetsEditor.TimeStamp=2025.04.27-13.25.32
AudioWidgetsEditor.LastCompileMethod=Unknown
ArchVisCharacter.TimeStamp=2025.04.27-13.25.32
ArchVisCharacter.LastCompileMethod=Unknown
ComputeFrameworkEditor.TimeStamp=2025.04.27-13.25.33
ComputeFrameworkEditor.LastCompileMethod=Unknown
GeometryCacheSequencer.TimeStamp=2025.04.27-13.25.34
GeometryCacheSequencer.LastCompileMethod=Unknown
GeometryCacheStreamer.TimeStamp=2025.04.27-13.25.34
GeometryCacheStreamer.LastCompileMethod=Unknown
GeometryCacheTracks.TimeStamp=2025.04.27-13.25.34
GeometryCacheTracks.LastCompileMethod=Unknown
GeometryAlgorithms.TimeStamp=2025.04.27-13.25.34
GeometryAlgorithms.LastCompileMethod=Unknown
DynamicMesh.TimeStamp=2025.04.27-13.25.34
DynamicMesh.LastCompileMethod=Unknown
MeshFileUtils.TimeStamp=2025.04.27-13.25.34
MeshFileUtils.LastCompileMethod=Unknown
HairStrandsDeformer.TimeStamp=2025.04.27-13.26.49
HairStrandsDeformer.LastCompileMethod=Unknown
HairStrandsRuntime.TimeStamp=2025.04.27-13.26.49
HairStrandsRuntime.LastCompileMethod=Unknown
HairStrandsEditor.TimeStamp=2025.04.27-13.26.49
HairStrandsEditor.LastCompileMethod=Unknown
HairCardGeneratorFramework.TimeStamp=2025.04.27-13.26.49
HairCardGeneratorFramework.LastCompileMethod=Unknown
GooglePAD.TimeStamp=2025.04.27-13.26.43
GooglePAD.LastCompileMethod=Unknown
AndroidPermission.TimeStamp=2025.04.27-13.25.32
AndroidPermission.LastCompileMethod=Unknown
InputDebugging.TimeStamp=2025.04.27-13.27.06
InputDebugging.LastCompileMethod=Unknown
InputDebuggingEditor.TimeStamp=2025.04.27-13.27.06
InputDebuggingEditor.LastCompileMethod=Unknown
MeshModelingTools.TimeStamp=2025.04.27-13.27.07
MeshModelingTools.LastCompileMethod=Unknown
MeshModelingToolsEditorOnly.TimeStamp=2025.04.27-13.27.07
MeshModelingToolsEditorOnly.LastCompileMethod=Unknown
ModelingComponents.TimeStamp=2025.04.27-13.27.07
ModelingComponents.LastCompileMethod=Unknown
GeometryFramework.TimeStamp=2025.04.27-13.20.05
GeometryFramework.LastCompileMethod=Unknown
ModelingComponentsEditorOnly.TimeStamp=2025.04.27-13.27.07
ModelingComponentsEditorOnly.LastCompileMethod=Unknown
ModelingOperators.TimeStamp=2025.04.27-13.27.07
ModelingOperators.LastCompileMethod=Unknown
ModelingOperatorsEditorOnly.TimeStamp=2025.04.27-13.27.07
ModelingOperatorsEditorOnly.LastCompileMethod=Unknown
ProceduralMeshComponentEditor.TimeStamp=2025.04.27-13.27.40
ProceduralMeshComponentEditor.LastCompileMethod=Unknown
StateTreeEditorModule.TimeStamp=2025.04.27-13.27.43
StateTreeEditorModule.LastCompileMethod=Unknown
MobilePatchingUtils.TimeStamp=2025.04.27-13.27.16
MobilePatchingUtils.LastCompileMethod=Unknown
UnrealUSDWrapper.TimeStamp=2025.04.27-13.27.44
UnrealUSDWrapper.LastCompileMethod=Unknown
USDUtilities.TimeStamp=2025.04.27-13.27.44
USDUtilities.LastCompileMethod=Unknown
USDClasses.TimeStamp=2025.04.27-13.27.44
USDClasses.LastCompileMethod=Unknown
SynthesisEditor.TimeStamp=2025.04.27-13.27.44
SynthesisEditor.LastCompileMethod=Unknown
BaseCharacterFXEditor.TimeStamp=2025.04.27-13.24.04
BaseCharacterFXEditor.LastCompileMethod=Unknown
MetaHumanSDKEditor.TimeStamp=2025.04.27-13.24.13
MetaHumanSDKEditor.LastCompileMethod=Unknown
MetaHumanSDKRuntime.TimeStamp=2025.04.27-13.24.13
MetaHumanSDKRuntime.LastCompileMethod=Unknown
LightMixer.TimeStamp=2025.04.27-13.23.25
LightMixer.LastCompileMethod=Unknown
ObjectMixerEditor.TimeStamp=2025.04.27-13.23.25
ObjectMixerEditor.LastCompileMethod=Unknown
PortableObjectFileDataSource.TimeStamp=2025.04.27-13.23.25
PortableObjectFileDataSource.LastCompileMethod=Unknown
XInputDevice.TimeStamp=2025.04.27-13.27.59
XInputDevice.LastCompileMethod=Unknown
ContentBrowserAssetDataSource.TimeStamp=2025.04.27-13.23.24
ContentBrowserAssetDataSource.LastCompileMethod=Unknown
CollectionManager.TimeStamp=2025.04.27-13.20.02
CollectionManager.LastCompileMethod=Unknown
ContentBrowserClassDataSource.TimeStamp=2025.04.27-13.23.24
ContentBrowserClassDataSource.LastCompileMethod=Unknown
ContentBrowserFileDataSource.TimeStamp=2025.04.27-13.23.24
ContentBrowserFileDataSource.LastCompileMethod=Unknown
SkeletalMeshModelingTools.TimeStamp=2025.04.27-13.23.38
SkeletalMeshModelingTools.LastCompileMethod=Unknown
SkeletalMeshEditor.TimeStamp=2025.04.27-13.20.09
SkeletalMeshEditor.LastCompileMethod=Unknown
ConcertSyncClient.TimeStamp=2025.04.27-13.23.22
ConcertSyncClient.LastCompileMethod=Unknown
Bridge.TimeStamp=2025.04.27-13.42.31
Bridge.LastCompileMethod=Unknown
MegascansPlugin.TimeStamp=2025.04.27-13.42.31
MegascansPlugin.LastCompileMethod=Unknown
CmdLinkServer.TimeStamp=2025.04.27-13.23.20
CmdLinkServer.LastCompileMethod=Unknown
Fab.TimeStamp=2025.04.27-13.42.28
Fab.LastCompileMethod=Unknown
DataflowEditor.TimeStamp=2025.04.27-13.24.06
DataflowEditor.LastCompileMethod=Unknown
TakesCore.TimeStamp=2025.04.27-13.28.08
TakesCore.LastCompileMethod=Unknown
TakeTrackRecorders.TimeStamp=2025.04.27-13.28.08
TakeTrackRecorders.LastCompileMethod=Unknown
TakeRecorderSources.TimeStamp=2025.04.27-13.28.08
TakeRecorderSources.LastCompileMethod=Unknown
CacheTrackRecorder.TimeStamp=2025.04.27-13.28.08
CacheTrackRecorder.LastCompileMethod=Unknown
AudioSynesthesiaEditor.TimeStamp=2025.04.27-13.25.32
AudioSynesthesiaEditor.LastCompileMethod=Unknown
ProfileVisualizer.TimeStamp=2025.04.27-13.20.08
ProfileVisualizer.LastCompileMethod=Unknown
ImageWriteQueue.TimeStamp=2025.04.27-13.20.05
ImageWriteQueue.LastCompileMethod=Unknown
TypedElementRuntime.TimeStamp=2025.04.27-13.20.11
TypedElementRuntime.LastCompileMethod=Unknown
LevelInstanceEditor.TimeStamp=2025.04.27-13.20.06
LevelInstanceEditor.LastCompileMethod=Unknown
AIModule.TimeStamp=2025.04.27-13.20.00
AIModule.LastCompileMethod=Unknown
NavigationSystem.TimeStamp=2025.04.27-13.20.07
NavigationSystem.LastCompileMethod=Unknown
AITestSuite.TimeStamp=2025.04.27-13.20.00
AITestSuite.LastCompileMethod=Unknown
GameplayDebugger.TimeStamp=2025.04.27-13.20.05
GameplayDebugger.LastCompileMethod=Unknown
MessagingRpc.TimeStamp=2025.04.27-13.20.07
MessagingRpc.LastCompileMethod=Unknown
PortalRpc.TimeStamp=2025.04.27-13.20.08
PortalRpc.LastCompileMethod=Unknown
PortalServices.TimeStamp=2025.04.27-13.20.08
PortalServices.LastCompileMethod=Unknown
AnalyticsET.TimeStamp=2025.04.27-13.20.00
AnalyticsET.LastCompileMethod=Unknown
LauncherPlatform.TimeStamp=2025.04.27-13.20.06
LauncherPlatform.LastCompileMethod=Unknown
AudioMixerXAudio2.TimeStamp=2025.04.27-13.20.01
AudioMixerXAudio2.LastCompileMethod=Unknown
AudioMixer.TimeStamp=2025.04.27-13.20.01
AudioMixer.LastCompileMethod=Unknown
AudioMixerCore.TimeStamp=2025.04.27-13.20.01
AudioMixerCore.LastCompileMethod=Unknown
StreamingPauseRendering.TimeStamp=2025.04.27-13.20.10
StreamingPauseRendering.LastCompileMethod=Unknown
MovieScene.TimeStamp=2025.04.27-13.20.07
MovieScene.LastCompileMethod=Unknown
MovieSceneTracks.TimeStamp=2025.04.27-13.20.07
MovieSceneTracks.LastCompileMethod=Unknown
CinematicCamera.TimeStamp=2025.04.27-13.20.02
CinematicCamera.LastCompileMethod=Unknown
SparseVolumeTexture.TimeStamp=2025.04.27-13.20.10
SparseVolumeTexture.LastCompileMethod=Unknown
Documentation.TimeStamp=2025.04.27-13.20.03
Documentation.LastCompileMethod=Unknown
OutputLog.TimeStamp=2025.04.27-13.20.07
OutputLog.LastCompileMethod=Unknown
SourceControlWindows.TimeStamp=2025.04.27-13.20.10
SourceControlWindows.LastCompileMethod=Unknown
SourceControlWindowExtender.TimeStamp=2025.04.27-13.20.10
SourceControlWindowExtender.LastCompileMethod=Unknown
UncontrolledChangelists.TimeStamp=2025.04.27-13.20.11
UncontrolledChangelists.LastCompileMethod=Unknown
StructViewer.TimeStamp=2025.04.27-13.20.10
StructViewer.LastCompileMethod=Unknown
GraphEditor.TimeStamp=2025.04.27-13.20.05
GraphEditor.LastCompileMethod=Unknown
Kismet.TimeStamp=2025.04.27-13.20.06
Kismet.LastCompileMethod=Unknown
KismetWidgets.TimeStamp=2025.04.27-13.20.06
KismetWidgets.LastCompileMethod=Unknown
Persona.TimeStamp=2025.04.27-13.20.07
Persona.LastCompileMethod=Unknown
AdvancedPreviewScene.TimeStamp=2025.04.27-13.20.00
AdvancedPreviewScene.LastCompileMethod=Unknown
AnimationBlueprintEditor.TimeStamp=2025.04.27-13.20.00
AnimationBlueprintEditor.LastCompileMethod=Unknown
PackagesDialog.TimeStamp=2025.04.27-13.20.07
PackagesDialog.LastCompileMethod=Unknown
DetailCustomizations.TimeStamp=2025.04.27-13.20.03
DetailCustomizations.LastCompileMethod=Unknown
ComponentVisualizers.TimeStamp=2025.04.27-13.20.02
ComponentVisualizers.LastCompileMethod=Unknown
Layers.TimeStamp=2025.04.27-13.20.06
Layers.LastCompileMethod=Unknown
AutomationWindow.TimeStamp=2025.04.27-13.20.01
AutomationWindow.LastCompileMethod=Unknown
AutomationController.TimeStamp=2025.04.27-13.20.01
AutomationController.LastCompileMethod=Unknown
DeviceManager.TimeStamp=2025.04.27-13.20.03
DeviceManager.LastCompileMethod=Unknown
ProfilerClient.TimeStamp=
ProfilerClient.LastCompileMethod=Unknown
SessionFrontend.TimeStamp=2025.04.27-13.20.09
SessionFrontend.LastCompileMethod=Unknown
ProjectLauncher.TimeStamp=2025.04.27-13.20.08
ProjectLauncher.LastCompileMethod=Unknown
SettingsEditor.TimeStamp=2025.04.27-13.20.09
SettingsEditor.LastCompileMethod=Unknown
EditorSettingsViewer.TimeStamp=2025.04.27-13.20.03
EditorSettingsViewer.LastCompileMethod=Unknown
InternationalizationSettings.TimeStamp=2025.04.27-13.20.05
InternationalizationSettings.LastCompileMethod=Unknown
ProjectSettingsViewer.TimeStamp=2025.04.27-13.20.08
ProjectSettingsViewer.LastCompileMethod=Unknown
ProjectTargetPlatformEditor.TimeStamp=2025.04.27-13.20.08
ProjectTargetPlatformEditor.LastCompileMethod=Unknown
Blutility.TimeStamp=2025.04.27-13.20.01
Blutility.LastCompileMethod=Unknown
XmlParser.TimeStamp=2025.04.27-13.20.12
XmlParser.LastCompileMethod=Unknown
UndoHistory.TimeStamp=2025.04.27-13.20.11
UndoHistory.LastCompileMethod=Unknown
DeviceProfileEditor.TimeStamp=2025.04.27-13.20.03
DeviceProfileEditor.LastCompileMethod=Unknown
HardwareTargeting.TimeStamp=2025.04.27-13.20.05
HardwareTargeting.LastCompileMethod=Unknown
LocalizationDashboard.TimeStamp=2025.04.27-13.20.06
LocalizationDashboard.LastCompileMethod=Unknown
LocalizationService.TimeStamp=2025.04.27-13.20.06
LocalizationService.LastCompileMethod=Unknown
MergeActors.TimeStamp=2025.04.27-13.20.07
MergeActors.LastCompileMethod=Unknown
InputBindingEditor.TimeStamp=2025.04.27-13.20.05
InputBindingEditor.LastCompileMethod=Unknown
EditorInteractiveToolsFramework.TimeStamp=2025.04.27-13.20.03
EditorInteractiveToolsFramework.LastCompileMethod=Unknown
InteractiveToolsFramework.TimeStamp=2025.04.27-13.20.05
InteractiveToolsFramework.LastCompileMethod=Unknown
TraceInsights.TimeStamp=2025.04.27-13.20.10
TraceInsights.LastCompileMethod=Unknown
TraceInsightsCore.TimeStamp=2025.04.27-13.20.10
TraceInsightsCore.LastCompileMethod=Unknown
StaticMeshEditor.TimeStamp=2025.04.27-13.20.10
StaticMeshEditor.LastCompileMethod=Unknown
EditorFramework.TimeStamp=2025.04.27-13.20.03
EditorFramework.LastCompileMethod=Unknown
EditorConfig.TimeStamp=2025.04.27-13.20.03
EditorConfig.LastCompileMethod=Unknown
DerivedDataEditor.TimeStamp=2025.04.27-13.20.03
DerivedDataEditor.LastCompileMethod=Unknown
CSVtoSVG.TimeStamp=2025.04.27-13.20.03
CSVtoSVG.LastCompileMethod=Unknown
VirtualizationEditor.TimeStamp=2025.04.27-13.20.12
VirtualizationEditor.LastCompileMethod=Unknown
AnimationSettings.TimeStamp=2025.04.27-13.20.00
AnimationSettings.LastCompileMethod=Unknown
GameplayDebuggerEditor.TimeStamp=2025.04.27-13.20.05
GameplayDebuggerEditor.LastCompileMethod=Unknown
RenderResourceViewer.TimeStamp=2025.04.27-13.20.09
RenderResourceViewer.LastCompileMethod=Unknown
UniversalObjectLocatorEditor.TimeStamp=2025.04.27-13.20.11
UniversalObjectLocatorEditor.LastCompileMethod=Unknown
StructUtilsEditor.TimeStamp=2025.04.27-13.20.10
StructUtilsEditor.LastCompileMethod=Unknown
StructUtilsTestSuite.TimeStamp=2025.04.27-13.20.10
StructUtilsTestSuite.LastCompileMethod=Unknown
AndroidRuntimeSettings.TimeStamp=2025.04.27-13.19.45
AndroidRuntimeSettings.LastCompileMethod=Unknown
IOSRuntimeSettings.TimeStamp=2025.04.27-13.19.50
IOSRuntimeSettings.LastCompileMethod=Unknown
MacPlatformEditor.TimeStamp=2025.04.27-13.20.06
MacPlatformEditor.LastCompileMethod=Unknown
WindowsPlatformEditor.TimeStamp=2025.04.27-13.20.12
WindowsPlatformEditor.LastCompileMethod=Unknown
AndroidPlatformEditor.TimeStamp=2025.04.27-13.19.45
AndroidPlatformEditor.LastCompileMethod=Unknown
AndroidDeviceDetection.TimeStamp=2025.04.27-13.19.45
AndroidDeviceDetection.LastCompileMethod=Unknown
PIEPreviewDeviceProfileSelector.TimeStamp=2025.04.27-13.20.08
PIEPreviewDeviceProfileSelector.LastCompileMethod=Unknown
IOSPlatformEditor.TimeStamp=2025.04.27-13.19.50
IOSPlatformEditor.LastCompileMethod=Unknown
LogVisualizer.TimeStamp=2025.04.27-13.20.06
LogVisualizer.LastCompileMethod=Unknown
WidgetRegistration.TimeStamp=2025.04.27-13.20.12
WidgetRegistration.LastCompileMethod=Unknown
ClothPainter.TimeStamp=2025.04.27-13.20.02
ClothPainter.LastCompileMethod=Unknown
ViewportInteraction.TimeStamp=2025.04.27-13.20.12
ViewportInteraction.LastCompileMethod=Unknown
EditorWidgets.TimeStamp=2025.04.27-13.20.03
EditorWidgets.LastCompileMethod=Unknown
ViewportSnapping.TimeStamp=2025.04.27-13.20.12
ViewportSnapping.LastCompileMethod=Unknown
MeshPaint.TimeStamp=2025.04.27-13.20.07
MeshPaint.LastCompileMethod=Unknown
PlacementMode.TimeStamp=2025.04.27-13.20.08
PlacementMode.LastCompileMethod=Unknown
SessionServices.TimeStamp=2025.04.27-13.20.09
SessionServices.LastCompileMethod=Unknown
CharacterAI.TimeStamp=2025.04.27-13.24.04
CharacterAI.LastCompileMethod=Unknown
FractureEngine.TimeStamp=2025.04.27-13.24.07
FractureEngine.LastCompileMethod=Unknown
PlanarCut.TimeStamp=2025.04.27-13.24.35
PlanarCut.LastCompileMethod=Unknown
AndroidMediaEditor.TimeStamp=2025.04.27-13.25.15
AndroidMediaEditor.LastCompileMethod=Unknown
AndroidMediaFactory.TimeStamp=2025.04.27-13.25.15
AndroidMediaFactory.LastCompileMethod=Unknown
AvfMediaEditor.TimeStamp=2025.04.27-13.25.15
AvfMediaEditor.LastCompileMethod=Unknown
AvfMediaFactory.TimeStamp=2025.04.27-13.25.15
AvfMediaFactory.LastCompileMethod=Unknown
ImgMediaEditor.TimeStamp=2025.04.27-13.25.16
ImgMediaEditor.LastCompileMethod=Unknown
ImgMediaFactory.TimeStamp=2025.04.27-13.25.16
ImgMediaFactory.LastCompileMethod=Unknown
OpenExrWrapper.TimeStamp=2025.04.27-13.25.16
OpenExrWrapper.LastCompileMethod=Unknown
MediaCompositingEditor.TimeStamp=2025.04.27-13.25.16
MediaCompositingEditor.LastCompileMethod=Unknown
SequenceRecorder.TimeStamp=2025.04.27-13.20.09
SequenceRecorder.LastCompileMethod=Unknown
MediaPlayerEditor.TimeStamp=2025.04.27-13.25.16
MediaPlayerEditor.LastCompileMethod=Unknown
WebMMedia.TimeStamp=2025.04.27-13.25.21
WebMMedia.LastCompileMethod=Unknown
WebMMediaEditor.TimeStamp=2025.04.27-13.25.21
WebMMediaEditor.LastCompileMethod=Unknown
WebMMediaFactory.TimeStamp=2025.04.27-13.25.21
WebMMediaFactory.LastCompileMethod=Unknown
WmfMediaEditor.TimeStamp=2025.04.27-13.25.21
WmfMediaEditor.LastCompileMethod=Unknown
WmfMediaFactory.TimeStamp=2025.04.27-13.25.21
WmfMediaFactory.LastCompileMethod=Unknown
OptimusEditor.TimeStamp=2025.04.27-13.23.15
OptimusEditor.LastCompileMethod=Unknown
SmartSnapping.TimeStamp=2025.04.27-13.23.10
SmartSnapping.LastCompileMethod=Unknown
LiveLinkMultiUser.TimeStamp=2025.04.27-13.23.16
LiveLinkMultiUser.LastCompileMethod=Unknown
IKRigEditor.TimeStamp=2025.04.27-13.23.16
IKRigEditor.LastCompileMethod=Unknown
ControlRigEditor.TimeStamp=2025.04.27-13.23.12
ControlRigEditor.LastCompileMethod=Unknown
GameplayCamerasEditor.TimeStamp=2025.04.27-13.23.19
GameplayCamerasEditor.LastCompileMethod=Unknown
CameraShakePreviewer.TimeStamp=2025.04.27-13.23.19
CameraShakePreviewer.LastCompileMethod=Unknown
EngineAssetDefinitions.TimeStamp=2025.04.27-13.23.24
EngineAssetDefinitions.LastCompileMethod=Unknown
GeometryMode.TimeStamp=2025.04.27-13.23.24
GeometryMode.LastCompileMethod=Unknown
BspMode.TimeStamp=2025.04.27-13.23.24
BspMode.LastCompileMethod=Unknown
TextureAlignMode.TimeStamp=2025.04.27-13.23.24
TextureAlignMode.LastCompileMethod=Unknown
ActorSequenceEditor.TimeStamp=2025.04.27-13.25.21
ActorSequenceEditor.LastCompileMethod=Unknown
LevelSequenceEditor.TimeStamp=2025.04.27-13.25.22
LevelSequenceEditor.LastCompileMethod=Unknown
TemplateSequenceEditor.TimeStamp=2025.04.27-13.25.23
TemplateSequenceEditor.LastCompileMethod=Unknown
AndroidFileServerEditor.TimeStamp=2025.04.27-13.25.32
AndroidFileServerEditor.LastCompileMethod=Unknown
AudioCaptureEditor.TimeStamp=2025.04.27-13.25.32
AudioCaptureEditor.LastCompileMethod=Unknown
GooglePADEditor.TimeStamp=2025.04.27-13.26.43
GooglePADEditor.LastCompileMethod=Unknown
ResonanceAudioEditor.TimeStamp=2025.04.27-13.27.40
ResonanceAudioEditor.LastCompileMethod=Unknown
RigVMEditor.TimeStamp=2025.04.27-13.27.41
RigVMEditor.LastCompileMethod=Unknown
WaveTableEditor.TimeStamp=2025.04.27-13.27.59
WaveTableEditor.LastCompileMethod=Unknown
ActorPickerMode.TimeStamp=2025.04.27-13.20.00
ActorPickerMode.LastCompileMethod=Unknown
SceneDepthPickerMode.TimeStamp=2025.04.27-13.20.09
SceneDepthPickerMode.LastCompileMethod=Unknown
LandscapeEditor.TimeStamp=2025.04.27-13.20.06
LandscapeEditor.LastCompileMethod=Unknown
FoliageEdit.TimeStamp=2025.04.27-13.20.05
FoliageEdit.LastCompileMethod=Unknown
VirtualTexturingEditor.TimeStamp=2025.04.27-13.20.12
VirtualTexturingEditor.LastCompileMethod=Unknown
AutomationWorker.TimeStamp=2025.04.27-13.20.01
AutomationWorker.LastCompileMethod=Unknown
SequenceRecorderSections.TimeStamp=2025.04.27-13.20.09
SequenceRecorderSections.LastCompileMethod=Unknown
StatsViewer.TimeStamp=2025.04.27-13.20.10
StatsViewer.LastCompileMethod=Unknown
DataLayerEditor.TimeStamp=2025.04.27-13.20.03
DataLayerEditor.LastCompileMethod=Unknown
GameProjectGeneration.TimeStamp=2025.04.27-13.20.05
GameProjectGeneration.LastCompileMethod=Unknown
UnsavedAssetsTracker.TimeStamp=2025.04.27-13.20.11
UnsavedAssetsTracker.LastCompileMethod=Unknown
StatusBar.TimeStamp=2025.04.27-13.20.10
StatusBar.LastCompileMethod=Unknown
AddContentDialog.TimeStamp=2025.04.27-13.20.00
AddContentDialog.LastCompileMethod=Unknown
WidgetCarousel.TimeStamp=2025.04.27-13.20.12
WidgetCarousel.LastCompileMethod=Unknown
SceneOutliner.TimeStamp=2025.04.27-13.20.09
SceneOutliner.LastCompileMethod=Unknown
SubobjectEditor.TimeStamp=2025.04.27-13.20.10
SubobjectEditor.LastCompileMethod=Unknown
HierarchicalLODOutliner.TimeStamp=2025.04.27-13.20.05
HierarchicalLODOutliner.LastCompileMethod=Unknown

[AssetEditorSubsystem]
CleanShutdown=False
DebuggerAttached=False

[/Script/Engine.WorldPartitionEditorPerProjectUserSettings]
bHideEditorDataLayers=False
bHideRuntimeDataLayers=False
bHideDataLayerActors=True
bHideUnloadedActors=False
bShowOnlySelectedActors=False
bHighlightSelectedDataLayers=True
bHideLevelInstanceContent=True
bDisableLoadingOfLastLoadedRegions=False
bBugItGoLoadRegion=False
bShowCellCoords=False
MinimapUnloadedOpacity=0.660000
PerWorldEditorSettings=(("/Game/ThirdPerson/Maps/ThirdPersonMap.ThirdPersonMap", ()))

[RootWindow]
ScreenPosition=X=1280.000 Y=684.000
WindowSize=X=1282.000 Y=722.000
InitiallyMaximized=True

[SlateAdditionalLayoutConfig]
Viewport 1.LayoutType=FourPanes2x2
FourPanes2x2.Viewport 1.Percentages0=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Percentages1=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Percentages2=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Percentages3=X=0.500 Y=0.500
FourPanes2x2.Viewport 1.Viewport0.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.Viewport1.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.Viewport2.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.Viewport3.TypeWithinLayout=Default
FourPanes2x2.Viewport 1.bIsMaximized=True
FourPanes2x2.Viewport 1.MaximizedViewport=FourPanes2x2.Viewport 1.Viewport1

[Directories2]
UNR=G:/Gamedev/RoughReality/Content/
BRUSH=G:/Gamedev/RoughReality/Content/
FBX=G:/Gamedev/RoughReality/Content/
FBXAnim=G:/Gamedev/RoughReality/Content/
GenericImport=G:/Gamedev/RoughReality/Content/
GenericExport=G:/Gamedev/RoughReality/Content/
GenericOpen=G:/Gamedev/RoughReality/Content/
GenericSave=G:/Gamedev/RoughReality/Content/
MeshImportExport=G:/Gamedev/RoughReality/Content/
WorldRoot=G:/Gamedev/RoughReality/Content/
Level=G:/Gamedev/RoughReality/Content/
Project=F:/Unreal/UE_5.5/

[Python]
LastDirectory=
RecentsFiles=F:/Unreal/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py

