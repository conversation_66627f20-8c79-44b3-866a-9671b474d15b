// Copyright Epic Games, Inc. All Rights Reserved.

#include "WeaponBase.h"
#include "Components/SkeletalMeshComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SceneComponent.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "Particles/ParticleSystemComponent.h"
#include "Sound/SoundBase.h"
#include "Animation/AnimMontage.h"
#include "TimerManager.h"
#include "../Characters/RookieCharacter.h"
#include "../DataAssets/WeaponDataAsset.h"

AWeaponBase::AWeaponBase()
{
	PrimaryActorTick.bCanEverTick = true;

	// Create components
	RootSceneComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootSceneComponent"));
	RootComponent = RootSceneComponent;

	WeaponMesh = CreateDefaultSubobject<USkeletalMeshComponent>(TEXT("WeaponMesh"));
	WeaponMesh->SetupAttachment(RootComponent);
	WeaponMesh->SetCollisionEnabled(ECollisionEnabled::NoCollision);

	MuzzleLocation = CreateDefaultSubobject<USceneComponent>(TEXT("MuzzleLocation"));
	MuzzleLocation->SetupAttachment(WeaponMesh);

	// Initialize weapon state
	CurrentState = EWeaponState::Idle;
	WeaponType = EWeaponType::Pistol;
	bCanFireAgain = true;
	bIsFiring = false;
	bIsReloading = false;
	bIsAutomatic = false;
}

void AWeaponBase::BeginPlay()
{
	Super::BeginPlay();

	// Initialize weapon stats from data asset
	if (WeaponData)
	{
		WeaponStats = WeaponData->WeaponStats;
		WeaponType = WeaponData->WeaponType;
		bIsAutomatic = WeaponData->bIsAutomatic;
		
		// Initialize ammo
		CurrentMagazine = WeaponStats.MagazineSize;
		CurrentAmmo = WeaponStats.MaxAmmo;
		
		OnAmmoChanged.Broadcast(CurrentAmmo);
	}
}

void AWeaponBase::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
	UpdateWeaponState(DeltaTime);
}

void AWeaponBase::StartFire()
{
	if (!CanFire()) return;

	bIsFiring = true;
	FireWeapon();

	// Handle automatic fire
	if (bIsAutomatic && CanFire())
	{
		float FireInterval = 60.0f / WeaponStats.FireRate; // Convert RPM to seconds
		GetWorldTimerManager().SetTimer(FireRateTimer, this, &AWeaponBase::FireWeapon, FireInterval, true);
	}
}

void AWeaponBase::StopFire()
{
	bIsFiring = false;
	GetWorldTimerManager().ClearTimer(FireRateTimer);
}

void AWeaponBase::Reload()
{
	if (!CanReload()) return;

	PerformReload();
}

void AWeaponBase::OnEquipped(ARookieCharacter* NewOwner)
{
	OwnerCharacter = NewOwner;
	CurrentState = EWeaponState::Equipping;
	
	// Attach to character
	if (OwnerCharacter)
	{
		// TODO: Attach to character's weapon socket
		UE_LOG(LogTemp, Log, TEXT("Weapon %s equipped by %s"), *GetName(), *OwnerCharacter->GetName());
	}
	
	CurrentState = EWeaponState::Idle;
}

void AWeaponBase::OnUnequipped()
{
	StopFire();
	CurrentState = EWeaponState::Unequipping;
	OwnerCharacter = nullptr;
	
	UE_LOG(LogTemp, Log, TEXT("Weapon %s unequipped"), *GetName());
	
	CurrentState = EWeaponState::Idle;
}

bool AWeaponBase::CanFire() const
{
	return CurrentState == EWeaponState::Idle && 
		   CurrentMagazine > 0 && 
		   bCanFireAgain && 
		   OwnerCharacter != nullptr;
}

bool AWeaponBase::CanReload() const
{
	return CurrentState == EWeaponState::Idle && 
		   CurrentMagazine < WeaponStats.MagazineSize && 
		   CurrentAmmo > 0 && 
		   !bIsReloading;
}

bool AWeaponBase::IsEmpty() const
{
	return CurrentMagazine <= 0 && CurrentAmmo <= 0;
}

bool AWeaponBase::NeedsReload() const
{
	return CurrentMagazine <= 0 && CurrentAmmo > 0;
}

float AWeaponBase::GetAmmoPercent() const
{
	return WeaponStats.MaxAmmo > 0 ? static_cast<float>(CurrentAmmo) / WeaponStats.MaxAmmo : 0.0f;
}

float AWeaponBase::GetMagazinePercent() const
{
	return WeaponStats.MagazineSize > 0 ? static_cast<float>(CurrentMagazine) / WeaponStats.MagazineSize : 0.0f;
}

FString AWeaponBase::GetWeaponName() const
{
	return WeaponData ? WeaponData->DisplayName.ToString() : GetName();
}

FGameplayTagContainer AWeaponBase::GetWeaponTags() const
{
	return WeaponData ? WeaponData->AssetTags : FGameplayTagContainer();
}

void AWeaponBase::FireWeapon()
{
	if (!CanFire()) return;

	// Consume ammo
	CurrentMagazine--;
	bCanFireAgain = false;

	// Perform line trace for hit detection
	if (OwnerCharacter)
	{
		FVector StartLocation = MuzzleLocation->GetComponentLocation();
		FVector ForwardVector = MuzzleLocation->GetForwardVector();
		
		// Add spread
		float SpreadRadians = FMath::DegreesToRadians(WeaponStats.SpreadAngle);
		ForwardVector = FMath::VRandCone(ForwardVector, SpreadRadians);
		
		FVector EndLocation = StartLocation + (ForwardVector * WeaponStats.Range);

		// Fire multiple projectiles for shotguns
		for (int32 i = 0; i < WeaponStats.ProjectileCount; i++)
		{
			FVector ProjectileDirection = ForwardVector;
			if (WeaponStats.ProjectileCount > 1)
			{
				// Add additional spread for multiple projectiles
				ProjectileDirection = FMath::VRandCone(ForwardVector, SpreadRadians * 2.0f);
			}
			
			FVector ProjectileEnd = StartLocation + (ProjectileDirection * WeaponStats.Range);
			FHitResult HitResult;
			
			if (PerformLineTrace(StartLocation, ProjectileEnd, HitResult))
			{
				if (HitResult.GetActor())
				{
					ApplyDamage(HitResult.GetActor(), WeaponStats.Damage, HitResult.Location);
				}
			}
		}
	}

	// Play effects
	PlayFireEffects();

	// Set fire rate timer
	float FireInterval = 60.0f / WeaponStats.FireRate;
	GetWorldTimerManager().SetTimer(FireRateTimer, this, &AWeaponBase::ResetFireRate, FireInterval, false);

	// Update state
	CurrentState = EWeaponState::Firing;
	OnWeaponFired.Broadcast(this);
	OnAmmoChanged.Broadcast(CurrentAmmo);

	UE_LOG(LogTemp, Log, TEXT("Weapon fired. Magazine: %d, Total Ammo: %d"), CurrentMagazine, CurrentAmmo);

	// Auto-reload if magazine is empty
	if (CurrentMagazine <= 0 && CurrentAmmo > 0)
	{
		Reload();
	}
}

void AWeaponBase::PerformReload()
{
	if (!CanReload()) return;

	bIsReloading = true;
	CurrentState = EWeaponState::Reloading;

	// Calculate ammo to reload
	int32 AmmoNeeded = WeaponStats.MagazineSize - CurrentMagazine;
	int32 AmmoToReload = FMath::Min(AmmoNeeded, CurrentAmmo);

	// Start reload timer
	GetWorldTimerManager().SetTimer(ReloadTimer, this, &AWeaponBase::CompleteReload, WeaponStats.ReloadTime, false);

	PlayReloadEffects();

	UE_LOG(LogTemp, Log, TEXT("Reloading weapon. Will reload %d rounds"), AmmoToReload);
}

void AWeaponBase::UpdateWeaponState(float DeltaTime)
{
	// Update weapon state based on current actions
	if (!bIsFiring && !bIsReloading && CurrentState != EWeaponState::Idle)
	{
		CurrentState = EWeaponState::Idle;
	}
}

bool AWeaponBase::PerformLineTrace(FVector Start, FVector End, FHitResult& OutHit)
{
	FCollisionQueryParams QueryParams;
	QueryParams.AddIgnoredActor(this);
	QueryParams.AddIgnoredActor(OwnerCharacter);
	QueryParams.bTraceComplex = true;

	return GetWorld()->LineTraceSingleByChannel(
		OutHit,
		Start,
		End,
		ECollisionChannel::ECC_Pawn,
		QueryParams
	);
}

void AWeaponBase::ApplyDamage(AActor* Target, float DamageAmount, const FVector& HitLocation)
{
	if (!Target) return;

	// Apply damage using Unreal's damage system
	UGameplayStatics::ApplyPointDamage(
		Target,
		DamageAmount,
		HitLocation,
		FHitResult(),
		OwnerCharacter ? OwnerCharacter->GetController() : nullptr,
		this,
		UDamageType::StaticClass()
	);

	UE_LOG(LogTemp, Log, TEXT("Applied %f damage to %s"), DamageAmount, *Target->GetName());
}

void AWeaponBase::PlayFireEffects()
{
	PlayMuzzleFlash();
	PlayFireSound();
}

void AWeaponBase::PlayReloadEffects()
{
	PlayReloadSound();
}

void AWeaponBase::PlayMuzzleFlash()
{
	if (MuzzleFlashEffect && MuzzleLocation)
	{
		UGameplayStatics::SpawnEmitterAtLocation(
			GetWorld(),
			MuzzleFlashEffect,
			MuzzleLocation->GetComponentTransform()
		);
	}
}

void AWeaponBase::PlayFireSound()
{
	if (FireSound)
	{
		UGameplayStatics::PlaySoundAtLocation(
			GetWorld(),
			FireSound,
			GetActorLocation()
		);
	}
}

void AWeaponBase::PlayReloadSound()
{
	if (ReloadSound)
	{
		UGameplayStatics::PlaySoundAtLocation(
			GetWorld(),
			ReloadSound,
			GetActorLocation()
		);
	}
}

void AWeaponBase::ResetFireRate()
{
	bCanFireAgain = true;
	CurrentState = EWeaponState::Idle;
}

void AWeaponBase::CompleteReload()
{
	// Calculate ammo to reload
	int32 AmmoNeeded = WeaponStats.MagazineSize - CurrentMagazine;
	int32 AmmoToReload = FMath::Min(AmmoNeeded, CurrentAmmo);

	// Transfer ammo
	CurrentAmmo -= AmmoToReload;
	CurrentMagazine += AmmoToReload;

	// Reset reload state
	bIsReloading = false;
	CurrentState = EWeaponState::Idle;

	OnWeaponReloaded.Broadcast(this);
	OnAmmoChanged.Broadcast(CurrentAmmo);

	UE_LOG(LogTemp, Log, TEXT("Reload complete. Magazine: %d, Total Ammo: %d"), CurrentMagazine, CurrentAmmo);
}

void AWeaponBase::UpdateLOD(float DistanceToPlayer)
{
	if (DistanceToPlayer > 5000.0f)
	{
		SetLODLevel(3); // Very low detail
	}
	else if (DistanceToPlayer > 2500.0f)
	{
		SetLODLevel(2); // Low detail
	}
	else if (DistanceToPlayer > 1000.0f)
	{
		SetLODLevel(1); // Medium detail
	}
	else
	{
		SetLODLevel(0); // High detail
	}
}

void AWeaponBase::SetLODLevel(int32 LODLevel)
{
	CurrentLODLevel = FMath::Clamp(LODLevel, 0, 3);

	// Adjust weapon mesh LOD
	if (WeaponMesh)
	{
		WeaponMesh->SetForcedLOD(CurrentLODLevel + 1); // UE LOD is 1-based
	}

	// Adjust tick frequency based on LOD
	switch (CurrentLODLevel)
	{
	case 0: // High detail
		PrimaryActorTick.TickInterval = 0.0f;
		break;
	case 1: // Medium detail
		PrimaryActorTick.TickInterval = 0.016f;
		break;
	case 2: // Low detail
		PrimaryActorTick.TickInterval = 0.033f;
		break;
	default: // Very low detail
		PrimaryActorTick.TickInterval = 0.05f;
		break;
	}
}

void AWeaponBase::PlayRandomMuzzleFlash()
{
	if (MuzzleFlashVariations.Num() > 0)
	{
		int32 RandomIndex = FMath::RandRange(0, MuzzleFlashVariations.Num() - 1);
		if (MuzzleFlashVariations[RandomIndex])
		{
			UGameplayStatics::SpawnEmitterAtLocation(
				GetWorld(),
				MuzzleFlashVariations[RandomIndex],
				MuzzleLocation->GetComponentLocation(),
				MuzzleLocation->GetComponentRotation()
			);
		}
	}
	else
	{
		// Fallback to single muzzle flash
		PlayMuzzleFlash();
	}
}

void AWeaponBase::PlayRandomFireSound()
{
	if (FireSoundVariations.Num() > 0)
	{
		int32 RandomIndex = FMath::RandRange(0, FireSoundVariations.Num() - 1);
		if (FireSoundVariations[RandomIndex])
		{
			UGameplayStatics::PlaySoundAtLocation(
				GetWorld(),
				FireSoundVariations[RandomIndex],
				GetActorLocation()
			);
		}
	}
	else
	{
		// Fallback to single fire sound
		PlayFireSound();
	}
}

void AWeaponBase::SpawnShellEjection()
{
	if (ShellEjectionEffect && MuzzleLocation)
	{
		FVector EjectionLocation = MuzzleLocation->GetComponentLocation();
		FRotator EjectionRotation = MuzzleLocation->GetComponentRotation();

		UGameplayStatics::SpawnEmitterAtLocation(
			GetWorld(),
			ShellEjectionEffect,
			EjectionLocation,
			EjectionRotation
		);
	}
}

void AWeaponBase::CreateBulletTracer(const FVector& StartLocation, const FVector& EndLocation)
{
	if (TracerEffect)
	{
		FVector Direction = (EndLocation - StartLocation).GetSafeNormal();
		FRotator TracerRotation = Direction.Rotation();

		UGameplayStatics::SpawnEmitterAtLocation(
			GetWorld(),
			TracerEffect,
			StartLocation,
			TracerRotation
		);
	}
}
