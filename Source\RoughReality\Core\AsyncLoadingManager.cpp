// Copyright Epic Games, Inc. All Rights Reserved.

#include "AsyncLoadingManager.h"
#include "Engine/World.h"
#include "Engine/AssetManager.h"
#include "Kismet/GameplayStatics.h"
#include "EngineUtils.h"

// Static member definition
AAsyncLoadingManager* AAsyncLoadingManager::Instance = nullptr;

AAsyncLoadingManager::AAsyncLoadingManager()
{
	PrimaryActorTick.bCanEverTick = true;
	PrimaryActorTick.TickInterval = 0.1f; // Tick 10 times per second

	bEnableAssetCaching = true;
	MaxCacheSize = 100;
	DefaultTimeout = 30.0f;
	bPreloadCommonAssets = true;
}

void AAsyncLoadingManager::BeginPlay()
{
	Super::BeginPlay();
	
	Instance = this;
	
	if (bPreloadCommonAssets)
	{
		PreloadCommonAssets();
	}
	
	UE_LOG(LogTemp, Log, TEXT("Async Loading Manager initialized"));
}

void AAsyncLoadingManager::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	ProcessLoadingQueue();
	CleanupCompletedLoads();
	UpdateLoadingProgress();
}

void AAsyncLoadingManager::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	// Cancel all active loading handles
	for (auto& HandlePair : ActiveLoadHandles)
	{
		if (HandlePair.Value.IsValid())
		{
			HandlePair.Value->CancelHandle();
		}
	}
	ActiveLoadHandles.Empty();
	
	Instance = nullptr;
	Super::EndPlay(EndPlayReason);
}

void AAsyncLoadingManager::LoadAssetAsync(const TSoftObjectPtr<UObject>& AssetToLoad, int32 Priority)
{
	if (AssetToLoad.IsNull()) return;

	FSoftObjectPath AssetPath = AssetToLoad.ToSoftObjectPath();
	
	// Check if already loaded or in cache
	if (UObject** CachedAsset = AssetCache.Find(AssetPath))
	{
		if (IsValid(*CachedAsset))
		{
			LoadingStats.CacheHits++;
			OnAssetLoaded.Broadcast(AssetToLoad);
			return;
		}
		else
		{
			// Remove invalid cached asset
			AssetCache.Remove(AssetPath);
		}
	}

	LoadingStats.CacheMisses++;
	LoadingStats.TotalAssetsRequested++;

	// Start async loading
	float StartTime = GetWorld()->GetTimeSeconds();
	LoadStartTimes.Add(AssetPath, StartTime);

	TArray<FSoftObjectPath> AssetsToLoad;
	AssetsToLoad.Add(AssetPath);

	TSharedPtr<FStreamableHandle> Handle = StreamableManager.RequestAsyncLoad(
		AssetsToLoad,
		FStreamableDelegate::CreateUFunction(this, FName("OnAssetLoadComplete")),
		Priority
	);

	if (Handle.IsValid())
	{
		FString LoadGroup = TEXT("SingleAsset_") + AssetPath.ToString();
		ActiveLoadHandles.Add(LoadGroup, Handle);
	}
}

UObject* AAsyncLoadingManager::LoadAssetSync(const TSoftObjectPtr<UObject>& AssetToLoad)
{
	if (AssetToLoad.IsNull()) return nullptr;

	FSoftObjectPath AssetPath = AssetToLoad.ToSoftObjectPath();
	
	// Check cache first
	if (UObject** CachedAsset = AssetCache.Find(AssetPath))
	{
		if (IsValid(*CachedAsset))
		{
			LoadingStats.CacheHits++;
			UpdateCacheAccess(AssetPath);
			return *CachedAsset;
		}
		else
		{
			AssetCache.Remove(AssetPath);
		}
	}

	LoadingStats.CacheMisses++;
	LoadingStats.TotalAssetsRequested++;

	// Load synchronously
	float StartTime = GetWorld()->GetTimeSeconds();
	UObject* LoadedAsset = StreamableManager.LoadSynchronous(AssetPath);
	float LoadTime = GetWorld()->GetTimeSeconds() - StartTime;

	if (LoadedAsset)
	{
		LoadingStats.AssetsLoaded++;
		LoadingStats.TotalLoadTime += LoadTime;
		LoadingStats.AverageLoadTime = LoadingStats.TotalLoadTime / LoadingStats.AssetsLoaded;

		// Add to cache
		if (bEnableAssetCaching)
		{
			if (AssetCache.Num() >= MaxCacheSize)
			{
				EvictOldestCacheEntry();
			}
			AssetCache.Add(AssetPath, LoadedAsset);
			UpdateCacheAccess(AssetPath);
		}

		OnAssetLoaded.Broadcast(AssetToLoad);
	}
	else
	{
		LoadingStats.AssetsFailed++;
		OnAssetLoadFailed.Broadcast(AssetToLoad);
	}

	return LoadedAsset;
}

bool AAsyncLoadingManager::IsAssetLoaded(const TSoftObjectPtr<UObject>& Asset) const
{
	if (Asset.IsNull()) return false;
	
	FSoftObjectPath AssetPath = Asset.ToSoftObjectPath();
	
	// Check cache
	if (UObject* const* CachedAsset = AssetCache.Find(AssetPath))
	{
		return IsValid(*CachedAsset);
	}
	
	// Check if loaded in memory
	return Asset.IsValid();
}

void AAsyncLoadingManager::UnloadAsset(const TSoftObjectPtr<UObject>& Asset)
{
	if (Asset.IsNull()) return;
	
	FSoftObjectPath AssetPath = Asset.ToSoftObjectPath();
	AssetCache.Remove(AssetPath);
	CacheAccessOrder.Remove(AssetPath);
}

void AAsyncLoadingManager::LoadAssetsAsync(const TArray<FAssetLoadRequest>& AssetRequests, const FString& GroupName)
{
	if (AssetRequests.Num() == 0) return;

	FLoadingGroup LoadGroup;
	LoadGroup.GroupName = GroupName;
	LoadGroup.AssetRequests = AssetRequests;
	LoadGroup.bLoadInBackground = true;
	LoadGroup.bShowProgress = true;
	LoadGroup.TimeoutSeconds = DefaultTimeout;

	LoadAssetGroup(LoadGroup);
}

void AAsyncLoadingManager::LoadAssetGroup(const FLoadingGroup& LoadGroup)
{
	if (LoadGroup.AssetRequests.Num() == 0) return;

	// Cancel existing group if it exists
	CancelLoadGroup(LoadGroup.GroupName);

	// Prepare assets to load
	TArray<FSoftObjectPath> AssetsToLoad;
	for (const FAssetLoadRequest& Request : LoadGroup.AssetRequests)
	{
		if (!Request.AssetToLoad.IsNull())
		{
			AssetsToLoad.Add(Request.AssetToLoad.ToSoftObjectPath());
		}
	}

	if (AssetsToLoad.Num() == 0) return;

	LoadingStats.TotalAssetsRequested += AssetsToLoad.Num();

	// Start async loading
	TSharedPtr<FStreamableHandle> Handle = StreamableManager.RequestAsyncLoad(
		AssetsToLoad,
		FStreamableDelegate::CreateUFunction(this, FName("OnAssetLoadComplete"))
	);

	if (Handle.IsValid())
	{
		ActiveLoadHandles.Add(LoadGroup.GroupName, Handle);
		LoadingGroups.Add(LoadGroup.GroupName, LoadGroup);
		GroupProgress.Add(LoadGroup.GroupName, 0.0f);
	}
}

void AAsyncLoadingManager::CancelLoadGroup(const FString& GroupName)
{
	if (TSharedPtr<FStreamableHandle>* Handle = ActiveLoadHandles.Find(GroupName))
	{
		if (Handle->IsValid())
		{
			(*Handle)->CancelHandle();
		}
	}

	ActiveLoadHandles.Remove(GroupName);
	LoadingGroups.Remove(GroupName);
	GroupProgress.Remove(GroupName);
}

void AAsyncLoadingManager::PreloadSectorAssets(int32 SectorIndex)
{
	const TArray<TSoftObjectPtr<UObject>>* SectorAssets = SectorAssetMaps.Find(SectorIndex);
	if (!SectorAssets) return;

	TArray<FAssetLoadRequest> AssetRequests;
	for (const TSoftObjectPtr<UObject>& Asset : *SectorAssets)
	{
		FAssetLoadRequest Request;
		Request.AssetToLoad = Asset;
		Request.Priority = 1; // High priority for sector assets
		Request.bIsRequired = true;
		Request.LoadGroup = FString::Printf(TEXT("Sector_%d"), SectorIndex);
		AssetRequests.Add(Request);
	}

	LoadAssetsAsync(AssetRequests, FString::Printf(TEXT("Sector_%d"), SectorIndex));
}

void AAsyncLoadingManager::PreloadWeaponAssets(const TArray<FString>& WeaponNames)
{
	TArray<FAssetLoadRequest> AssetRequests;
	
	for (const FString& WeaponName : WeaponNames)
	{
		// This would typically load weapon-specific assets
		// For now, just log the request
		UE_LOG(LogTemp, Log, TEXT("Preloading weapon assets for: %s"), *WeaponName);
	}

	if (AssetRequests.Num() > 0)
	{
		LoadAssetsAsync(AssetRequests, TEXT("WeaponAssets"));
	}
}

void AAsyncLoadingManager::PreloadCommonAssets()
{
	if (CommonAssets.Num() == 0) return;

	TArray<FAssetLoadRequest> AssetRequests;
	for (const TSoftObjectPtr<UObject>& Asset : CommonAssets)
	{
		FAssetLoadRequest Request;
		Request.AssetToLoad = Asset;
		Request.Priority = 2; // Medium priority for common assets
		Request.bIsRequired = false;
		Request.LoadGroup = TEXT("Common");
		AssetRequests.Add(Request);
	}

	LoadAssetsAsync(AssetRequests, TEXT("CommonAssets"));
}

void AAsyncLoadingManager::ClearAssetCache()
{
	AssetCache.Empty();
	CacheAccessOrder.Empty();
	LoadingStats.CacheHits = 0;
	LoadingStats.CacheMisses = 0;
	
	UE_LOG(LogTemp, Log, TEXT("Asset cache cleared"));
}

void AAsyncLoadingManager::TrimAssetCache()
{
	while (AssetCache.Num() > MaxCacheSize * 0.8f) // Trim to 80% of max size
	{
		EvictOldestCacheEntry();
	}
}

int32 AAsyncLoadingManager::GetCacheSize() const
{
	return AssetCache.Num();
}

float AAsyncLoadingManager::GetCacheHitRate() const
{
	int32 TotalRequests = LoadingStats.CacheHits + LoadingStats.CacheMisses;
	return TotalRequests > 0 ? (float)LoadingStats.CacheHits / TotalRequests : 0.0f;
}

float AAsyncLoadingManager::GetLoadingProgress(const FString& GroupName) const
{
	const float* Progress = GroupProgress.Find(GroupName);
	return Progress ? *Progress : 0.0f;
}

bool AAsyncLoadingManager::IsLoadingInProgress(const FString& GroupName) const
{
	return ActiveLoadHandles.Contains(GroupName);
}

TArray<FString> AAsyncLoadingManager::GetActiveLoadGroups() const
{
	TArray<FString> ActiveGroups;
	ActiveLoadHandles.GetKeys(ActiveGroups);
	return ActiveGroups;
}

void AAsyncLoadingManager::ForceGarbageCollection()
{
	GEngine->ForceGarbageCollection(true);
	UE_LOG(LogTemp, Log, TEXT("Forced garbage collection"));
}

int32 AAsyncLoadingManager::GetMemoryUsageMB() const
{
	FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
	return MemStats.UsedPhysical / (1024 * 1024);
}

void AAsyncLoadingManager::OptimizeMemoryUsage()
{
	TrimAssetCache();
	ForceGarbageCollection();
	
	UE_LOG(LogTemp, Log, TEXT("Memory usage optimized"));
}

FLoadingStatistics AAsyncLoadingManager::GetLoadingStatistics() const
{
	return LoadingStats;
}

void AAsyncLoadingManager::ResetStatistics()
{
	LoadingStats = FLoadingStatistics();
	UE_LOG(LogTemp, Log, TEXT("Loading statistics reset"));
}

void AAsyncLoadingManager::LogLoadingStatistics()
{
	UE_LOG(LogTemp, Log, TEXT("=== Loading Statistics ==="));
	UE_LOG(LogTemp, Log, TEXT("Total Assets Requested: %d"), LoadingStats.TotalAssetsRequested);
	UE_LOG(LogTemp, Log, TEXT("Assets Loaded: %d"), LoadingStats.AssetsLoaded);
	UE_LOG(LogTemp, Log, TEXT("Assets Failed: %d"), LoadingStats.AssetsFailed);
	UE_LOG(LogTemp, Log, TEXT("Total Load Time: %.2f seconds"), LoadingStats.TotalLoadTime);
	UE_LOG(LogTemp, Log, TEXT("Average Load Time: %.2f seconds"), LoadingStats.AverageLoadTime);
	UE_LOG(LogTemp, Log, TEXT("Cache Hits: %d"), LoadingStats.CacheHits);
	UE_LOG(LogTemp, Log, TEXT("Cache Misses: %d"), LoadingStats.CacheMisses);
	UE_LOG(LogTemp, Log, TEXT("Cache Hit Rate: %.1f%%"), GetCacheHitRate() * 100.0f);
}

AAsyncLoadingManager* AAsyncLoadingManager::GetAsyncLoadingManager(const UObject* WorldContext)
{
	if (Instance && IsValid(Instance))
	{
		return Instance;
	}

	// Try to find existing instance
	UWorld* World = GEngine->GetWorldFromContextObject(WorldContext, EGetWorldErrorMode::LogAndReturnNull);
	if (World)
	{
		for (TActorIterator<AAsyncLoadingManager> ActorItr(World); ActorItr; ++ActorItr)
		{
			Instance = *ActorItr;
			return Instance;
		}

		// Create new instance if none found
		Instance = World->SpawnActor<AAsyncLoadingManager>();
	}

	return Instance;
}

void AAsyncLoadingManager::ProcessLoadingQueue()
{
	// Update progress for active loads
	for (auto& HandlePair : ActiveLoadHandles)
	{
		if (HandlePair.Value.IsValid())
		{
			float Progress = HandlePair.Value->GetProgress();
			GroupProgress.FindOrAdd(HandlePair.Key) = Progress;
			
			if (Progress >= 1.0f)
			{
				OnLoadingComplete.Broadcast();
			}
		}
	}
}

void AAsyncLoadingManager::OnAssetLoadComplete()
{
	// This is a simplified callback - in a real implementation you'd track which assets completed
	LoadingStats.AssetsLoaded++;
	OnLoadingComplete.Broadcast();
	UE_LOG(LogTemp, Log, TEXT("Asset loading completed"));
}

void AAsyncLoadingManager::HandleAssetLoadFailed(const FSoftObjectPath& AssetPath)
{
	LoadingStats.AssetsFailed++;
	LoadStartTimes.Remove(AssetPath);
	
	TSoftObjectPtr<UObject> SoftPtr(AssetPath);
	OnAssetLoadFailed.Broadcast(SoftPtr);
}

void AAsyncLoadingManager::UpdateLoadingProgress()
{
	for (auto& ProgressPair : GroupProgress)
	{
		OnLoadingProgress.Broadcast(ProgressPair.Value, ProgressPair.Key);
	}
}

void AAsyncLoadingManager::CleanupCompletedLoads()
{
	TArray<FString> CompletedGroups;
	
	for (auto& HandlePair : ActiveLoadHandles)
	{
		if (!HandlePair.Value.IsValid() || HandlePair.Value->HasLoadCompleted())
		{
			CompletedGroups.Add(HandlePair.Key);
		}
	}
	
	for (const FString& GroupName : CompletedGroups)
	{
		ActiveLoadHandles.Remove(GroupName);
		LoadingGroups.Remove(GroupName);
		// Keep progress for a bit longer for UI purposes
	}
}

void AAsyncLoadingManager::UpdateCacheAccess(const FSoftObjectPath& AssetPath)
{
	// Move to end of access order (most recently used)
	CacheAccessOrder.Remove(AssetPath);
	CacheAccessOrder.Add(AssetPath);
}

void AAsyncLoadingManager::EvictOldestCacheEntry()
{
	if (CacheAccessOrder.Num() > 0)
	{
		FSoftObjectPath OldestAsset = CacheAccessOrder[0];
		AssetCache.Remove(OldestAsset);
		CacheAccessOrder.RemoveAt(0);
	}
}
