// Copyright Epic Games, Inc. All Rights Reserved.

#include "GameEventSystem.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "EngineUtils.h"

// Static member definitions
AGameEventSystem* AGameEventSystem::Instance = nullptr;

// Define common event tags - Initialize in BeginPlay to avoid static initialization issues
const FGameplayTag AGameEventSystem::EVENT_PLAYER_DAMAGED = FGameplayTag();
const FGameplayTag AGameEventSystem::EVENT_PLAYER_HEALED = FGameplayTag();
const FGameplayTag AGameEventSystem::EVENT_PLAYER_DIED = FGameplayTag();
const FGameplayTag AGameEventSystem::EVENT_ENEMY_KILLED = FGameplayTag();
const FGameplayTag AGameEventSystem::EVENT_WEAPON_FIRED = FGameplayTag();
const FGameplayTag AGameEventSystem::EVENT_WEAPON_RELOADED = FGameplayTag();
const FGameplayTag AGameEventSystem::EVENT_LEVEL_COMPLETED = FGameplayTag();
const FGameplayTag AGameEventSystem::EVENT_SECTOR_COMPLETED = FGameplayTag();
const FGameplayTag AGameEventSystem::EVENT_BULLET_TIME_ACTIVATED = FGameplayTag();
const FGameplayTag AGameEventSystem::EVENT_BULLET_TIME_DEACTIVATED = FGameplayTag();
const FGameplayTag AGameEventSystem::EVENT_TIME_REWIND = FGameplayTag();
const FGameplayTag AGameEventSystem::EVENT_DASH_PERFORMED = FGameplayTag();
const FGameplayTag AGameEventSystem::EVENT_UPGRADE_PURCHASED = FGameplayTag();
const FGameplayTag AGameEventSystem::EVENT_ACHIEVEMENT_UNLOCKED = FGameplayTag();

AGameEventSystem::AGameEventSystem()
{
	PrimaryActorTick.bCanEverTick = true;
	PrimaryActorTick.TickInterval = 0.1f; // Tick 10 times per second

	bEnableEventLogging = false;
	MaxEventHistorySize = 100;
	bAutoCleanupListeners = true;
	TotalEventsBroadcast = 0;
}

void AGameEventSystem::BeginPlay()
{
	Super::BeginPlay();
	
	Instance = this;
	
	// TODO: Register common event tags as valid once GameplayTags system is properly initialized
	// RegisterValidEventTag(EVENT_PLAYER_DAMAGED);
	// RegisterValidEventTag(EVENT_PLAYER_HEALED);
	// RegisterValidEventTag(EVENT_PLAYER_DIED);
	// RegisterValidEventTag(EVENT_ENEMY_KILLED);
	// RegisterValidEventTag(EVENT_WEAPON_FIRED);
	// RegisterValidEventTag(EVENT_WEAPON_RELOADED);
	// RegisterValidEventTag(EVENT_LEVEL_COMPLETED);
	// RegisterValidEventTag(EVENT_SECTOR_COMPLETED);
	// RegisterValidEventTag(EVENT_BULLET_TIME_ACTIVATED);
	// RegisterValidEventTag(EVENT_BULLET_TIME_DEACTIVATED);
	// RegisterValidEventTag(EVENT_TIME_REWIND);
	// RegisterValidEventTag(EVENT_DASH_PERFORMED);
	// RegisterValidEventTag(EVENT_UPGRADE_PURCHASED);
	// RegisterValidEventTag(EVENT_ACHIEVEMENT_UNLOCKED);
	
	UE_LOG(LogTemp, Log, TEXT("Game Event System initialized"));
}

void AGameEventSystem::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	// Cancel all delayed events
	for (auto& TimerPair : DelayedEventTimers)
	{
		GetWorldTimerManager().ClearTimer(TimerPair.Value);
	}
	DelayedEventTimers.Empty();
	
	// Clear all data
	EventListeners.Empty();
	EventHistory.Empty();
	EventQueue.Empty();
	
	Instance = nullptr;
	Super::EndPlay(EndPlayReason);
}

void AGameEventSystem::BroadcastEvent(const FGameEventData& EventData)
{
	if (!IsValidEventTag(EventData.EventTag))
	{
		UE_LOG(LogTemp, Warning, TEXT("Invalid event tag: %s"), *EventData.EventTag.ToString());
		return;
	}

	if (IsEventFiltered(EventData.EventTag))
	{
		return;
	}

	// Add to event queue for processing
	FGameEventData ProcessedEvent = EventData;
	ProcessedEvent.Timestamp = GetWorld()->GetTimeSeconds();
	EventQueue.Add(ProcessedEvent);

	// Process immediately
	ProcessEventQueue();
}

void AGameEventSystem::BroadcastEventByTag(const FGameplayTag& EventTag, AActor* EventInstigator, AActor* Target, const FVector& Location, float Magnitude)
{
	FGameEventData EventData(EventTag, EventInstigator, Target);
	EventData.Location = Location;
	EventData.Magnitude = Magnitude;
	
	BroadcastEvent(EventData);
}

void AGameEventSystem::BroadcastEventWithParameters(const FGameplayTag& EventTag, const TMap<FString, FString>& Parameters, AActor* EventInstigator)
{
	FGameEventData EventData(EventTag, EventInstigator);
	EventData.Parameters = Parameters;
	
	BroadcastEvent(EventData);
}

void AGameEventSystem::RegisterEventListener(UObject* Listener, const FGameplayTag& EventTag, const FGameEventDelegate& Delegate, int32 Priority)
{
	if (!Listener || !EventTag.IsValid())
	{
		UE_LOG(LogTemp, Warning, TEXT("Invalid listener or event tag for registration"));
		return;
	}

	FEventListener NewListener(Listener, EventTag, Delegate, Priority);
	
	TArray<FEventListener>& Listeners = EventListeners.FindOrAdd(EventTag);
	Listeners.Add(NewListener);
	
	// Sort by priority (higher priority first)
	SortListenersByPriority(Listeners);
	
	if (bEnableEventLogging)
	{
		UE_LOG(LogTemp, Log, TEXT("Registered listener for event: %s"), *EventTag.ToString());
	}
}

void AGameEventSystem::UnregisterEventListener(UObject* Listener, const FGameplayTag& EventTag)
{
	if (!Listener || !EventTag.IsValid()) return;

	TArray<FEventListener>* Listeners = EventListeners.Find(EventTag);
	if (!Listeners) return;

	for (int32 i = Listeners->Num() - 1; i >= 0; i--)
	{
		if ((*Listeners)[i].ListenerObject == Listener)
		{
			Listeners->RemoveAt(i);
		}
	}

	if (Listeners->Num() == 0)
	{
		EventListeners.Remove(EventTag);
	}
}

void AGameEventSystem::UnregisterAllListeners(UObject* Listener)
{
	if (!Listener) return;

	for (auto& ListenerPair : EventListeners)
	{
		TArray<FEventListener>& Listeners = ListenerPair.Value;
		
		for (int32 i = Listeners.Num() - 1; i >= 0; i--)
		{
			if (Listeners[i].ListenerObject == Listener)
			{
				Listeners.RemoveAt(i);
			}
		}
	}

	// Clean up empty listener arrays
	for (auto It = EventListeners.CreateIterator(); It; ++It)
	{
		if (It.Value().Num() == 0)
		{
			It.RemoveCurrent();
		}
	}
}

void AGameEventSystem::SetEventFilter(const FGameplayTag& EventTag, bool bEnabled)
{
	if (bEnabled)
	{
		FilteredEvents.Remove(EventTag);
	}
	else
	{
		FilteredEvents.Add(EventTag);
	}
}

bool AGameEventSystem::IsEventFiltered(const FGameplayTag& EventTag) const
{
	return FilteredEvents.Contains(EventTag);
}

TArray<FGameEventData> AGameEventSystem::GetEventHistory(const FGameplayTag& EventTag, int32 MaxEvents) const
{
	const TArray<FGameEventData>* History = EventHistory.Find(EventTag);
	if (!History) return TArray<FGameEventData>();

	TArray<FGameEventData> Result = *History;
	
	if (MaxEvents > 0 && Result.Num() > MaxEvents)
	{
		// Return the most recent events
		int32 StartIndex = Result.Num() - MaxEvents;
		Result.RemoveAt(0, StartIndex);
	}

	return Result;
}

void AGameEventSystem::ClearEventHistory()
{
	EventHistory.Empty();
	EventCounts.Empty();
	TotalEventsBroadcast = 0;
	
	UE_LOG(LogTemp, Log, TEXT("Event history cleared"));
}

int32 AGameEventSystem::GetEventCount(const FGameplayTag& EventTag) const
{
	const int32* Count = EventCounts.Find(EventTag);
	return Count ? *Count : 0;
}

void AGameEventSystem::EnableEventLogging(bool bEnable)
{
	bEnableEventLogging = bEnable;
	UE_LOG(LogTemp, Log, TEXT("Event logging %s"), bEnable ? TEXT("enabled") : TEXT("disabled"));
}

void AGameEventSystem::LogEventStatistics()
{
	UE_LOG(LogTemp, Log, TEXT("=== Event System Statistics ==="));
	UE_LOG(LogTemp, Log, TEXT("Total Events Broadcast: %d"), TotalEventsBroadcast);
	UE_LOG(LogTemp, Log, TEXT("Active Event Types: %d"), EventCounts.Num());
	UE_LOG(LogTemp, Log, TEXT("Registered Listeners: %d"), EventListeners.Num());
	
	for (const auto& CountPair : EventCounts)
	{
		UE_LOG(LogTemp, Log, TEXT("  %s: %d events"), *CountPair.Key.ToString(), CountPair.Value);
	}
}

TArray<FGameplayTag> AGameEventSystem::GetActiveEventTags() const
{
	TArray<FGameplayTag> ActiveTags;
	EventCounts.GetKeys(ActiveTags);
	return ActiveTags;
}

void AGameEventSystem::BroadcastEventDelayed(const FGameEventData& EventData, float Delay)
{
	if (Delay <= 0.0f)
	{
		BroadcastEvent(EventData);
		return;
	}

	FTimerHandle& TimerHandle = DelayedEventTimers.FindOrAdd(EventData.EventTag);
	GetWorldTimerManager().SetTimer(TimerHandle, 
		FTimerDelegate::CreateUFunction(this, FName("OnDelayedEventTimer"), EventData), 
		Delay, false);
}

void AGameEventSystem::CancelDelayedEvent(const FGameplayTag& EventTag)
{
	FTimerHandle* TimerHandle = DelayedEventTimers.Find(EventTag);
	if (TimerHandle && TimerHandle->IsValid())
	{
		GetWorldTimerManager().ClearTimer(*TimerHandle);
		DelayedEventTimers.Remove(EventTag);
	}
}

bool AGameEventSystem::IsValidEventTag(const FGameplayTag& EventTag) const
{
	return EventTag.IsValid() && (ValidEventTags.Num() == 0 || ValidEventTags.Contains(EventTag));
}

void AGameEventSystem::RegisterValidEventTag(const FGameplayTag& EventTag)
{
	if (EventTag.IsValid())
	{
		ValidEventTags.Add(EventTag);
	}
}

AGameEventSystem* AGameEventSystem::GetGameEventSystem(const UObject* WorldContext)
{
	if (Instance && IsValid(Instance))
	{
		return Instance;
	}

	// Try to find existing instance
	UWorld* World = GEngine->GetWorldFromContextObject(WorldContext, EGetWorldErrorMode::LogAndReturnNull);
	if (World)
	{
		for (TActorIterator<AGameEventSystem> ActorItr(World); ActorItr; ++ActorItr)
		{
			Instance = *ActorItr;
			return Instance;
		}

		// Create new instance if none found
		Instance = World->SpawnActor<AGameEventSystem>();
	}

	return Instance;
}

// Static helper functions
void AGameEventSystem::BroadcastPlayerDamaged(const UObject* WorldContext, AActor* Player, float Damage, AActor* DamageSource)
{
	if (AGameEventSystem* EventSystem = GetGameEventSystem(WorldContext))
	{
		FGameEventData EventData(EVENT_PLAYER_DAMAGED, DamageSource, Player);
		EventData.Magnitude = Damage;
		EventData.Location = Player ? Player->GetActorLocation() : FVector::ZeroVector;
		EventSystem->BroadcastEvent(EventData);
	}
}

void AGameEventSystem::BroadcastEnemyKilled(const UObject* WorldContext, AActor* Enemy, AActor* Killer, const FString& WeaponUsed)
{
	if (AGameEventSystem* EventSystem = GetGameEventSystem(WorldContext))
	{
		FGameEventData EventData(EVENT_ENEMY_KILLED, Killer, Enemy);
		EventData.Location = Enemy ? Enemy->GetActorLocation() : FVector::ZeroVector;
		EventData.Parameters.Add(TEXT("WeaponUsed"), WeaponUsed);
		EventSystem->BroadcastEvent(EventData);
	}
}

void AGameEventSystem::BroadcastWeaponFired(const UObject* WorldContext, AActor* Weapon, AActor* Shooter, const FVector& Location)
{
	if (AGameEventSystem* EventSystem = GetGameEventSystem(WorldContext))
	{
		FGameEventData EventData(EVENT_WEAPON_FIRED, Shooter, Weapon);
		EventData.Location = Location;
		EventSystem->BroadcastEvent(EventData);
	}
}

void AGameEventSystem::BroadcastLevelCompleted(const UObject* WorldContext, int32 SectorIndex, int32 LevelIndex, float CompletionTime)
{
	if (AGameEventSystem* EventSystem = GetGameEventSystem(WorldContext))
	{
		FGameEventData EventData(EVENT_LEVEL_COMPLETED);
		EventData.Parameters.Add(TEXT("SectorIndex"), FString::FromInt(SectorIndex));
		EventData.Parameters.Add(TEXT("LevelIndex"), FString::FromInt(LevelIndex));
		EventData.Parameters.Add(TEXT("CompletionTime"), FString::SanitizeFloat(CompletionTime));
		EventSystem->BroadcastEvent(EventData);
	}
}

void AGameEventSystem::BroadcastBulletTimeActivated(const UObject* WorldContext, AActor* Player, float Duration)
{
	if (AGameEventSystem* EventSystem = GetGameEventSystem(WorldContext))
	{
		FGameEventData EventData(EVENT_BULLET_TIME_ACTIVATED, Player);
		EventData.Magnitude = Duration;
		EventData.Location = Player ? Player->GetActorLocation() : FVector::ZeroVector;
		EventSystem->BroadcastEvent(EventData);
	}
}

void AGameEventSystem::BroadcastTimeRewind(const UObject* WorldContext, AActor* Player, const FVector& RewindLocation)
{
	if (AGameEventSystem* EventSystem = GetGameEventSystem(WorldContext))
	{
		FGameEventData EventData(EVENT_TIME_REWIND, Player);
		EventData.Location = RewindLocation;
		EventSystem->BroadcastEvent(EventData);
	}
}

void AGameEventSystem::ProcessEventQueue()
{
	for (const FGameEventData& EventData : EventQueue)
	{
		// Add to history
		TArray<FGameEventData>& History = EventHistory.FindOrAdd(EventData.EventTag);
		History.Add(EventData);
		
		// Limit history size
		if (History.Num() > MaxEventHistorySize)
		{
			History.RemoveAt(0, History.Num() - MaxEventHistorySize);
		}

		// Update event count
		int32& Count = EventCounts.FindOrAdd(EventData.EventTag);
		Count++;
		TotalEventsBroadcast++;

		// Notify listeners
		TArray<FEventListener>* Listeners = EventListeners.Find(EventData.EventTag);
		if (Listeners)
		{
			for (FEventListener& Listener : *Listeners)
			{
				if (Listener.bIsActive && IsValid(Listener.ListenerObject))
				{
					if (Listener.Delegate.IsBound())
					{
						Listener.Delegate.ExecuteIfBound(EventData);
					}
				}
			}
		}

		if (bEnableEventLogging)
		{
			UE_LOG(LogTemp, Log, TEXT("Event broadcast: %s"), *EventData.EventTag.ToString());
		}
	}

	EventQueue.Empty();

	// Cleanup invalid listeners if enabled
	if (bAutoCleanupListeners)
	{
		CleanupInvalidListeners();
	}
}

void AGameEventSystem::CleanupInvalidListeners()
{
	for (auto& ListenerPair : EventListeners)
	{
		TArray<FEventListener>& Listeners = ListenerPair.Value;
		
		for (int32 i = Listeners.Num() - 1; i >= 0; i--)
		{
			if (!IsValid(Listeners[i].ListenerObject))
			{
				Listeners.RemoveAt(i);
			}
		}
	}

	// Remove empty listener arrays
	for (auto It = EventListeners.CreateIterator(); It; ++It)
	{
		if (It.Value().Num() == 0)
		{
			It.RemoveCurrent();
		}
	}
}

void AGameEventSystem::SortListenersByPriority(TArray<FEventListener>& Listeners)
{
	Listeners.Sort([](const FEventListener& A, const FEventListener& B)
	{
		return A.Priority > B.Priority; // Higher priority first
	});
}

void AGameEventSystem::OnDelayedEventTimer(FGameEventData EventData)
{
	DelayedEventTimers.Remove(EventData.EventTag);
	BroadcastEvent(EventData);
}
