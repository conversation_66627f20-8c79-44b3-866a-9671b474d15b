// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "GameplayTagContainer.h"
#include "Engine/DataTable.h"
#include "GameAnalyticsManager.generated.h"

USTRUCT(BlueprintType)
struct FPlayerActionEvent
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadWrite, Category = "Event")
	FString EventType;

	UPROPERTY(BlueprintReadWrite, Category = "Event")
	FString EventCategory;

	UPROPERTY(BlueprintReadWrite, Category = "Event")
	FVector Location;

	UPROPERTY(BlueprintReadWrite, Category = "Event")
	float Timestamp;

	UPROPERTY(BlueprintReadWrite, Category = "Event")
	TMap<FString, FString> Parameters;

	FPlayerActionEvent()
	{
		EventType = TEXT("");
		EventCategory = TEXT("");
		Location = FVector::ZeroVector;
		Timestamp = 0.0f;
	}
};

USTRUCT(BlueprintType)
struct FHeatmapData
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadWrite, Category = "Heatmap")
	FVector Location;

	UPROPERTY(BlueprintReadWrite, Category = "Heatmap")
	FString EventType;

	UPROPERTY(BlueprintReadWrite, Category = "Heatmap")
	int32 Intensity;

	UPROPERTY(BlueprintReadWrite, Category = "Heatmap")
	float Timestamp;

	FHeatmapData()
	{
		Location = FVector::ZeroVector;
		EventType = TEXT("");
		Intensity = 1;
		Timestamp = 0.0f;
	}
};

USTRUCT(BlueprintType)
struct FWeaponAnalytics
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadWrite, Category = "Weapon Analytics")
	FString WeaponName;

	UPROPERTY(BlueprintReadWrite, Category = "Weapon Analytics")
	int32 ShotsFired;

	UPROPERTY(BlueprintReadWrite, Category = "Weapon Analytics")
	int32 ShotsHit;

	UPROPERTY(BlueprintReadWrite, Category = "Weapon Analytics")
	int32 Kills;

	UPROPERTY(BlueprintReadWrite, Category = "Weapon Analytics")
	float TotalDamageDealt;

	UPROPERTY(BlueprintReadWrite, Category = "Weapon Analytics")
	float TimeUsed;

	UPROPERTY(BlueprintReadWrite, Category = "Weapon Analytics")
	int32 ReloadCount;

	FWeaponAnalytics()
	{
		WeaponName = TEXT("");
		ShotsFired = 0;
		ShotsHit = 0;
		Kills = 0;
		TotalDamageDealt = 0.0f;
		TimeUsed = 0.0f;
		ReloadCount = 0;
	}
};

USTRUCT(BlueprintType)
struct FPerformanceMetrics
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadWrite, Category = "Performance")
	float AverageFrameRate;

	UPROPERTY(BlueprintReadWrite, Category = "Performance")
	float MinFrameRate;

	UPROPERTY(BlueprintReadWrite, Category = "Performance")
	float MaxFrameRate;

	UPROPERTY(BlueprintReadWrite, Category = "Performance")
	float AverageFrameTime;

	UPROPERTY(BlueprintReadWrite, Category = "Performance")
	int32 MemoryUsageMB;

	UPROPERTY(BlueprintReadWrite, Category = "Performance")
	int32 DrawCalls;

	UPROPERTY(BlueprintReadWrite, Category = "Performance")
	float LoadingTime;

	FPerformanceMetrics()
	{
		AverageFrameRate = 0.0f;
		MinFrameRate = 0.0f;
		MaxFrameRate = 0.0f;
		AverageFrameTime = 0.0f;
		MemoryUsageMB = 0;
		DrawCalls = 0;
		LoadingTime = 0.0f;
	}
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAnalyticsEvent, const FPlayerActionEvent&, Event);

/**
 * Game Analytics Manager for comprehensive data collection and analysis
 * Tracks player behavior, performance metrics, and gameplay patterns
 */
UCLASS(BlueprintType, Blueprintable)
class ROUGHREALITY_API AGameAnalyticsManager : public AActor
{
	GENERATED_BODY()

public:
	AGameAnalyticsManager();

protected:
	virtual void BeginPlay() override;
	virtual void Tick(float DeltaTime) override;
	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

public:
	/** Analytics Configuration */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Analytics Config")
	bool bEnableAnalytics = true;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Analytics Config")
	bool bEnableHeatmaps = true;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Analytics Config")
	bool bEnablePerformanceTracking = true;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Analytics Config")
	float EventBatchSize = 100.0f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Analytics Config")
	float FlushInterval = 30.0f; // Seconds

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Analytics Config")
	FString AnalyticsEndpoint = TEXT("https://analytics.roughreality.com/api/events");

	/** Events */
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnAnalyticsEvent OnAnalyticsEvent;

	/** Current Session Data */
	UPROPERTY(BlueprintReadOnly, Category = "Session Data")
	FString SessionID;

	UPROPERTY(BlueprintReadOnly, Category = "Session Data")
	float SessionStartTime;

	UPROPERTY(BlueprintReadOnly, Category = "Session Data")
	TArray<FPlayerActionEvent> PendingEvents;

	UPROPERTY(BlueprintReadOnly, Category = "Session Data")
	TArray<FHeatmapData> HeatmapData;

	UPROPERTY(BlueprintReadOnly, Category = "Session Data")
	TMap<FString, FWeaponAnalytics> WeaponAnalytics;

	UPROPERTY(BlueprintReadOnly, Category = "Session Data")
	FPerformanceMetrics CurrentPerformanceMetrics;

public:
	/** Event Logging */
	UFUNCTION(BlueprintCallable, Category = "Analytics")
	void LogPlayerAction(const FString& ActionType, const FString& Category, const FVector& Location, const TMap<FString, FString>& Parameters = TMap<FString, FString>());

	UFUNCTION(BlueprintCallable, Category = "Analytics")
	void LogWeaponFired(const FString& WeaponName, const FVector& Location, bool bHit, float Damage = 0.0f);

	UFUNCTION(BlueprintCallable, Category = "Analytics")
	void LogWeaponReload(const FString& WeaponName, const FVector& Location);

	UFUNCTION(BlueprintCallable, Category = "Analytics")
	void LogEnemyKill(const FString& EnemyType, const FString& WeaponUsed, const FVector& Location);

	UFUNCTION(BlueprintCallable, Category = "Analytics")
	void LogPlayerDeath(const FString& CauseOfDeath, const FVector& Location);

	UFUNCTION(BlueprintCallable, Category = "Analytics")
	void LogLevelComplete(int32 SectorIndex, int32 LevelIndex, float CompletionTime);

	UFUNCTION(BlueprintCallable, Category = "Analytics")
	void LogBulletTimeUsage(const FVector& Location, float Duration);

	UFUNCTION(BlueprintCallable, Category = "Analytics")
	void LogTimeRewind(const FVector& Location);

	/** Heatmap Data */
	UFUNCTION(BlueprintCallable, Category = "Heatmap")
	void RecordHeatmapEvent(const FString& EventType, const FVector& Location, int32 Intensity = 1);

	UFUNCTION(BlueprintCallable, Category = "Heatmap")
	TArray<FHeatmapData> GetHeatmapData(const FString& EventType) const;

	UFUNCTION(BlueprintCallable, Category = "Heatmap")
	void ClearHeatmapData();

	/** Weapon Analytics */
	UFUNCTION(BlueprintCallable, Category = "Weapon Analytics")
	void UpdateWeaponUsageTime(const FString& WeaponName, float DeltaTime);

	UFUNCTION(BlueprintCallable, Category = "Weapon Analytics")
	FWeaponAnalytics GetWeaponAnalytics(const FString& WeaponName) const;

	UFUNCTION(BlueprintCallable, Category = "Weapon Analytics")
	TArray<FWeaponAnalytics> GetAllWeaponAnalytics() const;

	/** Performance Tracking */
	UFUNCTION(BlueprintCallable, Category = "Performance")
	void UpdatePerformanceMetrics();

	UFUNCTION(BlueprintCallable, Category = "Performance")
	FPerformanceMetrics GetCurrentPerformanceMetrics() const;

	/** Data Management */
	UFUNCTION(BlueprintCallable, Category = "Data Management")
	void FlushAnalyticsData();

	UFUNCTION(BlueprintCallable, Category = "Data Management")
	void SaveAnalyticsToFile(const FString& Filename);

	UFUNCTION(BlueprintCallable, Category = "Data Management")
	void LoadAnalyticsFromFile(const FString& Filename);

	UFUNCTION(BlueprintCallable, Category = "Data Management")
	void ExportHeatmapToCSV(const FString& Filename);

	/** Session Management */
	UFUNCTION(BlueprintCallable, Category = "Session")
	void StartNewSession();

	UFUNCTION(BlueprintCallable, Category = "Session")
	void EndCurrentSession();

	UFUNCTION(BlueprintCallable, Category = "Session")
	float GetSessionDuration() const;

	/** Static Access */
	UFUNCTION(BlueprintCallable, Category = "Analytics", CallInEditor = true)
	static AGameAnalyticsManager* GetAnalyticsManager(const UObject* WorldContext);

protected:
	/** Internal Functions */
	void InitializeSession();
	void ProcessPendingEvents();
	void SendAnalyticsData(const TArray<FPlayerActionEvent>& Events);
	FString GenerateSessionID() const;
	void UpdateFrameRateTracking(float DeltaTime);

private:
	/** Singleton instance */
	static AGameAnalyticsManager* Instance;

	/** Performance tracking */
	TArray<float> FrameRateHistory;
	float LastFlushTime = 0.0f;
	float FrameRateAccumulator = 0.0f;
	int32 FrameCount = 0;

	/** File I/O */
	FString GetAnalyticsDirectory() const;
	bool EnsureAnalyticsDirectoryExists() const;
};
