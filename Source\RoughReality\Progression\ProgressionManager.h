// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "GameplayTagContainer.h"
#include "Engine/DataTable.h"
#include "ProgressionManager.generated.h"

class URoughSaveGame;

USTRUCT(BlueprintType)
struct FUpgradeNode : public FTableRowBase
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
	FName UpgradeID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
	FText DisplayName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
	FText Description;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
	TSoftObjectPtr<UTexture2D> Icon;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
	int32 Cost = 100;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
	int32 MaxLevel = 5;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
	TArray<FName> Prerequisites;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
	FGameplayTagContainer RequiredTags;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
	FGameplayTagContainer GrantedTags;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
	TMap<FString, float> StatModifiers;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
	bool bIsWeaponUpgrade = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
	FName TargetWeapon = NAME_None;

	FUpgradeNode()
	{
		UpgradeID = NAME_None;
		Cost = 100;
		MaxLevel = 5;
		bIsWeaponUpgrade = false;
		TargetWeapon = NAME_None;
	}
};

USTRUCT(BlueprintType)
struct FUnlockCondition
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Unlock")
	FName UnlockID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Unlock")
	FText DisplayName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Unlock")
	FText Description;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Unlock")
	int32 RequiredRuns = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Unlock")
	int32 RequiredKills = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Unlock")
	int32 RequiredSectorCompletions = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Unlock")
	float RequiredAccuracy = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Unlock")
	float RequiredCompletionTime = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Unlock")
	TArray<FName> RequiredWeaponKills;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Unlock")
	FGameplayTagContainer RequiredAchievements;

	FUnlockCondition()
	{
		UnlockID = NAME_None;
		RequiredRuns = 0;
		RequiredKills = 0;
		RequiredSectorCompletions = 0;
		RequiredAccuracy = 0.0f;
		RequiredCompletionTime = 0.0f;
	}
};

USTRUCT(BlueprintType)
struct FPrestigeLevel
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prestige")
	int32 Level = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prestige")
	int32 RequiredTeeth = 1000;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prestige")
	FText LevelName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prestige")
	FLinearColor LevelColor = FLinearColor::White;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prestige")
	TArray<FGameplayTag> UnlockedFeatures;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prestige")
	float GlobalDamageBonus = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prestige")
	float GlobalHealthBonus = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Prestige")
	int32 ExtraRewindCharges = 0;

	FPrestigeLevel()
	{
		Level = 0;
		RequiredTeeth = 1000;
		LevelColor = FLinearColor::White;
		GlobalDamageBonus = 0.0f;
		GlobalHealthBonus = 0.0f;
		ExtraRewindCharges = 0;
	}
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnUpgradePurchased, FName, UpgradeID, int32, NewLevel);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnUnlockAchieved, FName, UnlockID);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPrestigeLevelUp, int32, NewPrestigeLevel);

/**
 * Progression Manager for Rough Reality
 * Handles upgrade trees, unlocks, and meta-progression systems
 */
UCLASS(BlueprintType, Blueprintable)
class ROUGHREALITY_API AProgressionManager : public AActor
{
	GENERATED_BODY()

public:
	AProgressionManager();

protected:
	virtual void BeginPlay() override;

public:
	/** Data Tables */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Data")
	UDataTable* UpgradeDataTable;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Data")
	UDataTable* UnlockConditionsTable;

	/** Prestige System */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Prestige")
	TArray<FPrestigeLevel> PrestigeLevels;

	UPROPERTY(BlueprintReadOnly, Category = "Prestige")
	int32 CurrentPrestigeLevel = 0;

	/** Events */
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnUpgradePurchased OnUpgradePurchased;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnUnlockAchieved OnUnlockAchieved;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnPrestigeLevelUp OnPrestigeLevelUp;

public:
	/** Upgrade System */
	UFUNCTION(BlueprintCallable, Category = "Upgrades")
	bool CanPurchaseUpgrade(FName UpgradeID) const;

	UFUNCTION(BlueprintCallable, Category = "Upgrades")
	bool PurchaseUpgrade(FName UpgradeID);

	UFUNCTION(BlueprintCallable, Category = "Upgrades")
	int32 GetUpgradeLevel(FName UpgradeID) const;

	UFUNCTION(BlueprintCallable, Category = "Upgrades")
	int32 GetUpgradeCost(FName UpgradeID, int32 Level) const;

	UFUNCTION(BlueprintCallable, Category = "Upgrades")
	TArray<FUpgradeNode> GetAvailableUpgrades() const;

	UFUNCTION(BlueprintCallable, Category = "Upgrades")
	TArray<FUpgradeNode> GetPurchasedUpgrades() const;

	UFUNCTION(BlueprintCallable, Category = "Upgrades")
	FUpgradeNode GetUpgradeData(FName UpgradeID) const;

	/** Unlock System */
	UFUNCTION(BlueprintCallable, Category = "Unlocks")
	void CheckUnlockConditions();

	UFUNCTION(BlueprintCallable, Category = "Unlocks")
	bool IsUnlocked(FName UnlockID) const;

	UFUNCTION(BlueprintCallable, Category = "Unlocks")
	void ForceUnlock(FName UnlockID);

	UFUNCTION(BlueprintCallable, Category = "Unlocks")
	TArray<FUnlockCondition> GetPendingUnlocks() const;

	UFUNCTION(BlueprintCallable, Category = "Unlocks")
	float GetUnlockProgress(FName UnlockID) const;

	/** Prestige System */
	UFUNCTION(BlueprintCallable, Category = "Prestige")
	bool CanPrestige() const;

	UFUNCTION(BlueprintCallable, Category = "Prestige")
	void PerformPrestige();

	UFUNCTION(BlueprintCallable, Category = "Prestige")
	int32 GetPrestigeLevel() const { return CurrentPrestigeLevel; }

	UFUNCTION(BlueprintCallable, Category = "Prestige")
	FPrestigeLevel GetCurrentPrestigeData() const;

	UFUNCTION(BlueprintCallable, Category = "Prestige")
	FPrestigeLevel GetNextPrestigeData() const;

	UFUNCTION(BlueprintCallable, Category = "Prestige")
	float GetPrestigeProgress() const;

	/** Stat Calculations */
	UFUNCTION(BlueprintCallable, Category = "Stats")
	float GetTotalStatModifier(const FString& StatName) const;

	UFUNCTION(BlueprintCallable, Category = "Stats")
	float GetWeaponStatModifier(FName WeaponName, const FString& StatName) const;

	UFUNCTION(BlueprintCallable, Category = "Stats")
	TMap<FString, float> GetAllStatModifiers() const;

	/** Achievement System */
	UFUNCTION(BlueprintCallable, Category = "Achievements")
	void UnlockAchievement(const FGameplayTag& AchievementTag);

	UFUNCTION(BlueprintCallable, Category = "Achievements")
	bool HasAchievement(const FGameplayTag& AchievementTag) const;

	UFUNCTION(BlueprintCallable, Category = "Achievements")
	TArray<FGameplayTag> GetUnlockedAchievements() const;

	/** Economy */
	UFUNCTION(BlueprintCallable, Category = "Economy")
	int32 GetAvailableTeeth() const;

	UFUNCTION(BlueprintCallable, Category = "Economy")
	bool SpendTeeth(int32 Amount);

	UFUNCTION(BlueprintCallable, Category = "Economy")
	void AddTeeth(int32 Amount);

	/** Static Access */
	UFUNCTION(BlueprintCallable, Category = "Progression", CallInEditor = true)
	static AProgressionManager* GetProgressionManager(const UObject* WorldContext);

protected:
	/** Internal Functions */
	void InitializeProgressionData();
	void LoadProgressionState();
	void SaveProgressionState();
	bool CheckSingleUnlockCondition(const FUnlockCondition& Condition) const;
	void ApplyPrestigeBonuses();

private:
	/** Singleton instance */
	static AProgressionManager* Instance;

	/** Cached save game reference */
	UPROPERTY()
	URoughSaveGame* SaveGameRef;

	/** Cached upgrade data */
	TMap<FName, FUpgradeNode> UpgradeCache;
	TMap<FName, FUnlockCondition> UnlockCache;
};
