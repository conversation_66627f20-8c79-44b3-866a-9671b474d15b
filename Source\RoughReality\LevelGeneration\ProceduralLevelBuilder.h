// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "GameplayTagContainer.h"
#include "ProceduralLevelBuilder.generated.h"

class UTileDefinitionDataAsset;
class ALevelTile;

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnLevelGenerated, int32, SectorIndex, int32, LevelIndex);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnLevelGenerationFailed, FString, ErrorMessage);

UENUM(BlueprintType)
enum class ETileType : uint8
{
	Start,
	Combat,
	Shop,
	Boss,
	Transition,
	Special
};

USTRUCT(BlueprintType)
struct FTileSpawnInfo
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tile")
	UTileDefinitionDataAsset* TileDefinition;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tile")
	FVector Location;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tile")
	FRotator Rotation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tile")
	ETileType TileType;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tile")
	bool bIsFixed;

	FTileSpawnInfo()
	{
		TileDefinition = nullptr;
		Location = FVector::ZeroVector;
		Rotation = FRotator::ZeroRotator;
		TileType = ETileType::Combat;
		bIsFixed = false;
	}
};

USTRUCT(BlueprintType)
struct FLevelSectorConfiguration
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sector")
	FName SectorName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sector")
	FGameplayTagContainer SectorTags;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sector")
	int32 MinTiles = 5;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sector")
	int32 MaxTiles = 10;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sector")
	TArray<UTileDefinitionDataAsset*> AvailableTiles;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sector")
	TArray<FTileSpawnInfo> FixedTiles;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sector")
	float TileSpacing = 2000.0f;

	FLevelSectorConfiguration()
	{
		SectorName = NAME_None;
		MinTiles = 5;
		MaxTiles = 10;
		TileSpacing = 2000.0f;
	}
};

/**
 * Procedural Level Builder for Rough Reality
 * Generates levels using modular tiles with fixed landmarks
 */
UCLASS(BlueprintType, Blueprintable)
class ROUGHREALITY_API AProceduralLevelBuilder : public AActor
{
	GENERATED_BODY()

public:
	AProceduralLevelBuilder();

protected:
	virtual void BeginPlay() override;

public:
	/** Sector Configurations */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Level Generation")
	TArray<FLevelSectorConfiguration> SectorConfigurations;

	/** Current Level State */
	UPROPERTY(BlueprintReadOnly, Category = "Level State")
	int32 CurrentSector = 0;

	UPROPERTY(BlueprintReadOnly, Category = "Level State")
	int32 CurrentLevel = 0;

	UPROPERTY(BlueprintReadOnly, Category = "Level State")
	TArray<ALevelTile*> SpawnedTiles;

	/** Generation Settings */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Generation Settings")
	int32 RandomSeed = 0;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Generation Settings")
	bool bUseRandomSeed = true;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Generation Settings")
	float DefaultTileSpacing = 2000.0f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Generation Settings")
	FVector LevelOrigin = FVector::ZeroVector;

	/** Events */
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnLevelGenerated OnLevelGenerated;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnLevelGenerationFailed OnLevelGenerationFailed;

public:
	/** Main Generation Functions */
	UFUNCTION(BlueprintCallable, Category = "Level Generation")
	bool GenerateLevel(int32 SectorIndex, int32 LevelIndex);

	UFUNCTION(BlueprintCallable, Category = "Level Generation")
	void ClearCurrentLevel();

	UFUNCTION(BlueprintCallable, Category = "Level Generation")
	void RegenerateCurrentLevel();

	/** Sector Management */
	UFUNCTION(BlueprintCallable, Category = "Sector Management")
	bool IsSectorValid(int32 SectorIndex) const;

	UFUNCTION(BlueprintCallable, Category = "Sector Management")
	FLevelSectorConfiguration GetSectorConfiguration(int32 SectorIndex) const;

	UFUNCTION(BlueprintCallable, Category = "Sector Management")
	int32 GetSectorCount() const;

	/** Tile Management */
	UFUNCTION(BlueprintCallable, Category = "Tile Management")
	ALevelTile* SpawnTile(UTileDefinitionDataAsset* TileDefinition, const FVector& Location, const FRotator& Rotation);

	UFUNCTION(BlueprintCallable, Category = "Tile Management")
	void DestroyTile(ALevelTile* Tile);

	UFUNCTION(BlueprintCallable, Category = "Tile Management")
	TArray<ALevelTile*> GetTilesByType(ETileType TileType) const;

	/** Utility Functions */
	UFUNCTION(BlueprintCallable, Category = "Utility")
	FVector CalculateTilePosition(int32 TileIndex, float Spacing) const;

	UFUNCTION(BlueprintCallable, Category = "Utility")
	void SetRandomSeed(int32 NewSeed);

	UFUNCTION(BlueprintCallable, Category = "Utility")
	int32 GetCurrentRandomSeed() const;

	/** Validation */
	UFUNCTION(BlueprintCallable, Category = "Validation")
	bool ValidateLevel() const;

	UFUNCTION(BlueprintCallable, Category = "Validation")
	bool ValidateSectorConfiguration(const FLevelSectorConfiguration& SectorConfig) const;

protected:
	/** Internal Generation Logic */
	bool GenerateSectorLevel(const FLevelSectorConfiguration& SectorConfig, int32 LevelIndex);

	TArray<FTileSpawnInfo> PlanTileLayout(const FLevelSectorConfiguration& SectorConfig, int32 LevelIndex);
	
	bool SpawnPlannedTiles(const TArray<FTileSpawnInfo>& TileLayout);

	/** Tile Selection Logic */
	UTileDefinitionDataAsset* SelectRandomTile(const TArray<UTileDefinitionDataAsset*>& AvailableTiles, ETileType DesiredType) const;
	
	TArray<UTileDefinitionDataAsset*> FilterTilesByType(const TArray<UTileDefinitionDataAsset*>& Tiles, ETileType TileType) const;
	
	TArray<UTileDefinitionDataAsset*> FilterTilesByTags(const TArray<UTileDefinitionDataAsset*>& Tiles, const FGameplayTagContainer& RequiredTags) const;

	/** Layout Algorithms */
	TArray<FVector> GenerateLinearLayout(int32 TileCount, float Spacing) const;
	
	TArray<FVector> GenerateBranchingLayout(int32 TileCount, float Spacing) const;
	
	TArray<FVector> GenerateCircularLayout(int32 TileCount, float Spacing) const;

	/** Validation Helpers */
	bool ValidateTileConnections() const;
	
	bool CheckTileOverlaps() const;
	
	bool EnsurePathConnectivity() const;

private:
	/** Random number stream for consistent generation */
	FRandomStream RandomStream;

	/** Track generation state */
	bool bIsGenerating = false;
	
	/** Cache for performance */
	TMap<ETileType, TArray<UTileDefinitionDataAsset*>> TileTypeCache;
};
