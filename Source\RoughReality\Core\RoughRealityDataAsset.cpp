// Copyright Epic Games, Inc. All Rights Reserved.

#include "RoughRealityDataAsset.h"
#include "GameplayTagsManager.h"

URoughRealityDataAsset::URoughRealityDataAsset()
{
	AssetID = NAME_None;
	DisplayName = FText::GetEmpty();
	Description = FText::GetEmpty();
	bIsEnabled = true;
	Priority = 0;
}

bool URoughRealityDataAsset::HasTag(const FGameplayTag& Tag) const
{
	return AssetTags.HasTag(Tag);
}

bool URoughRealityDataAsset::HasAnyTag(const FGameplayTagContainer& Tags) const
{
	return AssetTags.HasAny(Tags);
}

bool URoughRealityDataAsset::HasAllTags(const FGameplayTagContainer& Tags) const
{
	return AssetTags.HasAll(Tags);
}

FString URoughRealityDataAsset::GetAssetIDString() const
{
	return AssetID.ToString();
}

bool URoughRealityDataAsset::ValidateAsset() const
{
	// Base validation - check if asset ID is set
	if (AssetID == NAME_None)
	{
		UE_LOG(LogTemp, Warning, TEXT("Data Asset %s has no AssetID set"), *GetName());
		return false;
	}

	// Check if display name is set
	if (DisplayName.IsEmpty())
	{
		UE_LOG(LogTemp, Warning, TEXT("Data Asset %s has no DisplayName set"), *GetName());
		return false;
	}

	return true;
}

#if WITH_EDITOR
void URoughRealityDataAsset::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
	Super::PostEditChangeProperty(PropertyChangedEvent);

	// Auto-generate AssetID from object name if not set
	if (PropertyChangedEvent.GetPropertyName() == GET_MEMBER_NAME_CHECKED(URoughRealityDataAsset, AssetID))
	{
		if (AssetID == NAME_None)
		{
			AssetID = FName(*GetName());
		}
	}

	// Validate the asset when properties change
	ValidateAsset();
}
#endif
