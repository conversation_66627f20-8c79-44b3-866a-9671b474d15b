// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RoughReality/Core/AsyncLoadingManager.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAsyncLoadingManager() {}

// Begin Cross Module References
COREUOBJECT_API UClass* Z_Construct_UClass_UObject_NoRegister();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FSoftObjectPath();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ROUGHREALITY_API UClass* Z_Construct_UClass_AAsyncLoadingManager();
ROUGHREALITY_API UClass* Z_Construct_UClass_AAsyncLoadingManager_NoRegister();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnAssetLoaded__DelegateSignature();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnAssetLoadFailed__DelegateSignature();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnLoadingComplete__DelegateSignature();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnLoadingProgress__DelegateSignature();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FAssetLoadRequest();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FLoadingGroup();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FLoadingStatistics();
UPackage* Z_Construct_UPackage__Script_RoughReality();
// End Cross Module References

// Begin Delegate FOnAssetLoaded
struct Z_Construct_UDelegateFunction_RoughReality_OnAssetLoaded__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnAssetLoaded_Parms
	{
		TSoftObjectPtr<UObject> LoadedAsset;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_LoadedAsset;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnAssetLoaded__DelegateSignature_Statics::NewProp_LoadedAsset = { "LoadedAsset", nullptr, (EPropertyFlags)0x0014000000000080, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnAssetLoaded_Parms, LoadedAsset), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnAssetLoaded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnAssetLoaded__DelegateSignature_Statics::NewProp_LoadedAsset,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnAssetLoaded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnAssetLoaded__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnAssetLoaded__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnAssetLoaded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnAssetLoaded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnAssetLoaded__DelegateSignature_Statics::_Script_RoughReality_eventOnAssetLoaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnAssetLoaded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnAssetLoaded__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnAssetLoaded__DelegateSignature_Statics::_Script_RoughReality_eventOnAssetLoaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnAssetLoaded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnAssetLoaded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnAssetLoaded_DelegateWrapper(const FMulticastScriptDelegate& OnAssetLoaded, const TSoftObjectPtr<UObject>& LoadedAsset)
{
	struct _Script_RoughReality_eventOnAssetLoaded_Parms
	{
		TSoftObjectPtr<UObject> LoadedAsset;
	};
	_Script_RoughReality_eventOnAssetLoaded_Parms Parms;
	Parms.LoadedAsset=LoadedAsset;
	OnAssetLoaded.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnAssetLoaded

// Begin Delegate FOnAssetLoadFailed
struct Z_Construct_UDelegateFunction_RoughReality_OnAssetLoadFailed__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnAssetLoadFailed_Parms
	{
		TSoftObjectPtr<UObject> FailedAsset;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_FailedAsset;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnAssetLoadFailed__DelegateSignature_Statics::NewProp_FailedAsset = { "FailedAsset", nullptr, (EPropertyFlags)0x0014000000000080, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnAssetLoadFailed_Parms, FailedAsset), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnAssetLoadFailed__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnAssetLoadFailed__DelegateSignature_Statics::NewProp_FailedAsset,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnAssetLoadFailed__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnAssetLoadFailed__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnAssetLoadFailed__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnAssetLoadFailed__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnAssetLoadFailed__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnAssetLoadFailed__DelegateSignature_Statics::_Script_RoughReality_eventOnAssetLoadFailed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnAssetLoadFailed__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnAssetLoadFailed__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnAssetLoadFailed__DelegateSignature_Statics::_Script_RoughReality_eventOnAssetLoadFailed_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnAssetLoadFailed__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnAssetLoadFailed__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnAssetLoadFailed_DelegateWrapper(const FMulticastScriptDelegate& OnAssetLoadFailed, const TSoftObjectPtr<UObject>& FailedAsset)
{
	struct _Script_RoughReality_eventOnAssetLoadFailed_Parms
	{
		TSoftObjectPtr<UObject> FailedAsset;
	};
	_Script_RoughReality_eventOnAssetLoadFailed_Parms Parms;
	Parms.FailedAsset=FailedAsset;
	OnAssetLoadFailed.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnAssetLoadFailed

// Begin Delegate FOnLoadingProgress
struct Z_Construct_UDelegateFunction_RoughReality_OnLoadingProgress__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnLoadingProgress_Parms
	{
		float Progress;
		FString CurrentAsset;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Progress;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CurrentAsset;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnLoadingProgress__DelegateSignature_Statics::NewProp_Progress = { "Progress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnLoadingProgress_Parms, Progress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnLoadingProgress__DelegateSignature_Statics::NewProp_CurrentAsset = { "CurrentAsset", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnLoadingProgress_Parms, CurrentAsset), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnLoadingProgress__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnLoadingProgress__DelegateSignature_Statics::NewProp_Progress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnLoadingProgress__DelegateSignature_Statics::NewProp_CurrentAsset,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnLoadingProgress__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnLoadingProgress__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnLoadingProgress__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnLoadingProgress__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnLoadingProgress__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnLoadingProgress__DelegateSignature_Statics::_Script_RoughReality_eventOnLoadingProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnLoadingProgress__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnLoadingProgress__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnLoadingProgress__DelegateSignature_Statics::_Script_RoughReality_eventOnLoadingProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnLoadingProgress__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnLoadingProgress__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnLoadingProgress_DelegateWrapper(const FMulticastScriptDelegate& OnLoadingProgress, float Progress, const FString& CurrentAsset)
{
	struct _Script_RoughReality_eventOnLoadingProgress_Parms
	{
		float Progress;
		FString CurrentAsset;
	};
	_Script_RoughReality_eventOnLoadingProgress_Parms Parms;
	Parms.Progress=Progress;
	Parms.CurrentAsset=CurrentAsset;
	OnLoadingProgress.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnLoadingProgress

// Begin Delegate FOnLoadingComplete
struct Z_Construct_UDelegateFunction_RoughReality_OnLoadingComplete__DelegateSignature_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnLoadingComplete__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnLoadingComplete__DelegateSignature", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnLoadingComplete__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnLoadingComplete__DelegateSignature_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnLoadingComplete__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnLoadingComplete__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnLoadingComplete_DelegateWrapper(const FMulticastScriptDelegate& OnLoadingComplete)
{
	OnLoadingComplete.ProcessMulticastDelegate<UObject>(NULL);
}
// End Delegate FOnLoadingComplete

// Begin ScriptStruct FAssetLoadRequest
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_AssetLoadRequest;
class UScriptStruct* FAssetLoadRequest::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_AssetLoadRequest.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_AssetLoadRequest.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAssetLoadRequest, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("AssetLoadRequest"));
	}
	return Z_Registration_Info_UScriptStruct_AssetLoadRequest.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FAssetLoadRequest>()
{
	return FAssetLoadRequest::StaticStruct();
}
struct Z_Construct_UScriptStruct_FAssetLoadRequest_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetToLoad_MetaData[] = {
		{ "Category", "Loading" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Priority_MetaData[] = {
		{ "Category", "Loading" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsRequired_MetaData[] = {
		{ "Category", "Loading" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadGroup_MetaData[] = {
		{ "Category", "Loading" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AssetToLoad;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Priority;
	static void NewProp_bIsRequired_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsRequired;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LoadGroup;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAssetLoadRequest>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_AssetToLoad = { "AssetToLoad", nullptr, (EPropertyFlags)0x0014000000000004, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAssetLoadRequest, AssetToLoad), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetToLoad_MetaData), NewProp_AssetToLoad_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAssetLoadRequest, Priority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Priority_MetaData), NewProp_Priority_MetaData) };
void Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_bIsRequired_SetBit(void* Obj)
{
	((FAssetLoadRequest*)Obj)->bIsRequired = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_bIsRequired = { "bIsRequired", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAssetLoadRequest), &Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_bIsRequired_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsRequired_MetaData), NewProp_bIsRequired_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_LoadGroup = { "LoadGroup", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAssetLoadRequest, LoadGroup), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadGroup_MetaData), NewProp_LoadGroup_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_AssetToLoad,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_bIsRequired,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_LoadGroup,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	&NewStructOps,
	"AssetLoadRequest",
	Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::PropPointers),
	sizeof(FAssetLoadRequest),
	alignof(FAssetLoadRequest),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAssetLoadRequest()
{
	if (!Z_Registration_Info_UScriptStruct_AssetLoadRequest.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_AssetLoadRequest.InnerSingleton, Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_AssetLoadRequest.InnerSingleton;
}
// End ScriptStruct FAssetLoadRequest

// Begin ScriptStruct FLoadingGroup
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_LoadingGroup;
class UScriptStruct* FLoadingGroup::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_LoadingGroup.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_LoadingGroup.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FLoadingGroup, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("LoadingGroup"));
	}
	return Z_Registration_Info_UScriptStruct_LoadingGroup.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FLoadingGroup>()
{
	return FLoadingGroup::StaticStruct();
}
struct Z_Construct_UScriptStruct_FLoadingGroup_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GroupName_MetaData[] = {
		{ "Category", "Loading Group" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetRequests_MetaData[] = {
		{ "Category", "Loading Group" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLoadInBackground_MetaData[] = {
		{ "Category", "Loading Group" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowProgress_MetaData[] = {
		{ "Category", "Loading Group" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeoutSeconds_MetaData[] = {
		{ "Category", "Loading Group" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_GroupName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AssetRequests_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AssetRequests;
	static void NewProp_bLoadInBackground_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLoadInBackground;
	static void NewProp_bShowProgress_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowProgress;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeoutSeconds;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FLoadingGroup>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FLoadingGroup_Statics::NewProp_GroupName = { "GroupName", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLoadingGroup, GroupName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GroupName_MetaData), NewProp_GroupName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FLoadingGroup_Statics::NewProp_AssetRequests_Inner = { "AssetRequests", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAssetLoadRequest, METADATA_PARAMS(0, nullptr) }; // 886331830
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FLoadingGroup_Statics::NewProp_AssetRequests = { "AssetRequests", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLoadingGroup, AssetRequests), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetRequests_MetaData), NewProp_AssetRequests_MetaData) }; // 886331830
void Z_Construct_UScriptStruct_FLoadingGroup_Statics::NewProp_bLoadInBackground_SetBit(void* Obj)
{
	((FLoadingGroup*)Obj)->bLoadInBackground = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FLoadingGroup_Statics::NewProp_bLoadInBackground = { "bLoadInBackground", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FLoadingGroup), &Z_Construct_UScriptStruct_FLoadingGroup_Statics::NewProp_bLoadInBackground_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLoadInBackground_MetaData), NewProp_bLoadInBackground_MetaData) };
void Z_Construct_UScriptStruct_FLoadingGroup_Statics::NewProp_bShowProgress_SetBit(void* Obj)
{
	((FLoadingGroup*)Obj)->bShowProgress = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FLoadingGroup_Statics::NewProp_bShowProgress = { "bShowProgress", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FLoadingGroup), &Z_Construct_UScriptStruct_FLoadingGroup_Statics::NewProp_bShowProgress_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowProgress_MetaData), NewProp_bShowProgress_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLoadingGroup_Statics::NewProp_TimeoutSeconds = { "TimeoutSeconds", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLoadingGroup, TimeoutSeconds), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeoutSeconds_MetaData), NewProp_TimeoutSeconds_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FLoadingGroup_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingGroup_Statics::NewProp_GroupName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingGroup_Statics::NewProp_AssetRequests_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingGroup_Statics::NewProp_AssetRequests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingGroup_Statics::NewProp_bLoadInBackground,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingGroup_Statics::NewProp_bShowProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingGroup_Statics::NewProp_TimeoutSeconds,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLoadingGroup_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FLoadingGroup_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	&NewStructOps,
	"LoadingGroup",
	Z_Construct_UScriptStruct_FLoadingGroup_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLoadingGroup_Statics::PropPointers),
	sizeof(FLoadingGroup),
	alignof(FLoadingGroup),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLoadingGroup_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FLoadingGroup_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FLoadingGroup()
{
	if (!Z_Registration_Info_UScriptStruct_LoadingGroup.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_LoadingGroup.InnerSingleton, Z_Construct_UScriptStruct_FLoadingGroup_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_LoadingGroup.InnerSingleton;
}
// End ScriptStruct FLoadingGroup

// Begin ScriptStruct FLoadingStatistics
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_LoadingStatistics;
class UScriptStruct* FLoadingStatistics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_LoadingStatistics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_LoadingStatistics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FLoadingStatistics, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("LoadingStatistics"));
	}
	return Z_Registration_Info_UScriptStruct_LoadingStatistics.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FLoadingStatistics>()
{
	return FLoadingStatistics::StaticStruct();
}
struct Z_Construct_UScriptStruct_FLoadingStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalAssetsRequested_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetsLoaded_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetsFailed_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalLoadTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageLoadTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CacheHits_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CacheMisses_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalAssetsRequested;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AssetsLoaded;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AssetsFailed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalLoadTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageLoadTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CacheHits;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CacheMisses;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FLoadingStatistics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FLoadingStatistics_Statics::NewProp_TotalAssetsRequested = { "TotalAssetsRequested", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLoadingStatistics, TotalAssetsRequested), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalAssetsRequested_MetaData), NewProp_TotalAssetsRequested_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FLoadingStatistics_Statics::NewProp_AssetsLoaded = { "AssetsLoaded", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLoadingStatistics, AssetsLoaded), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetsLoaded_MetaData), NewProp_AssetsLoaded_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FLoadingStatistics_Statics::NewProp_AssetsFailed = { "AssetsFailed", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLoadingStatistics, AssetsFailed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetsFailed_MetaData), NewProp_AssetsFailed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLoadingStatistics_Statics::NewProp_TotalLoadTime = { "TotalLoadTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLoadingStatistics, TotalLoadTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalLoadTime_MetaData), NewProp_TotalLoadTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLoadingStatistics_Statics::NewProp_AverageLoadTime = { "AverageLoadTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLoadingStatistics, AverageLoadTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageLoadTime_MetaData), NewProp_AverageLoadTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FLoadingStatistics_Statics::NewProp_CacheHits = { "CacheHits", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLoadingStatistics, CacheHits), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CacheHits_MetaData), NewProp_CacheHits_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FLoadingStatistics_Statics::NewProp_CacheMisses = { "CacheMisses", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLoadingStatistics, CacheMisses), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CacheMisses_MetaData), NewProp_CacheMisses_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FLoadingStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingStatistics_Statics::NewProp_TotalAssetsRequested,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingStatistics_Statics::NewProp_AssetsLoaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingStatistics_Statics::NewProp_AssetsFailed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingStatistics_Statics::NewProp_TotalLoadTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingStatistics_Statics::NewProp_AverageLoadTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingStatistics_Statics::NewProp_CacheHits,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingStatistics_Statics::NewProp_CacheMisses,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLoadingStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FLoadingStatistics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	&NewStructOps,
	"LoadingStatistics",
	Z_Construct_UScriptStruct_FLoadingStatistics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLoadingStatistics_Statics::PropPointers),
	sizeof(FLoadingStatistics),
	alignof(FLoadingStatistics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLoadingStatistics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FLoadingStatistics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FLoadingStatistics()
{
	if (!Z_Registration_Info_UScriptStruct_LoadingStatistics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_LoadingStatistics.InnerSingleton, Z_Construct_UScriptStruct_FLoadingStatistics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_LoadingStatistics.InnerSingleton;
}
// End ScriptStruct FLoadingStatistics

// Begin Class AAsyncLoadingManager Function CancelLoadGroup
struct Z_Construct_UFunction_AAsyncLoadingManager_CancelLoadGroup_Statics
{
	struct AsyncLoadingManager_eventCancelLoadGroup_Parms
	{
		FString GroupName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Batch Loading" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GroupName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_GroupName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_CancelLoadGroup_Statics::NewProp_GroupName = { "GroupName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AsyncLoadingManager_eventCancelLoadGroup_Parms, GroupName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GroupName_MetaData), NewProp_GroupName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAsyncLoadingManager_CancelLoadGroup_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_CancelLoadGroup_Statics::NewProp_GroupName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_CancelLoadGroup_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAsyncLoadingManager_CancelLoadGroup_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAsyncLoadingManager, nullptr, "CancelLoadGroup", nullptr, nullptr, Z_Construct_UFunction_AAsyncLoadingManager_CancelLoadGroup_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_CancelLoadGroup_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAsyncLoadingManager_CancelLoadGroup_Statics::AsyncLoadingManager_eventCancelLoadGroup_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_CancelLoadGroup_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAsyncLoadingManager_CancelLoadGroup_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AAsyncLoadingManager_CancelLoadGroup_Statics::AsyncLoadingManager_eventCancelLoadGroup_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAsyncLoadingManager_CancelLoadGroup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAsyncLoadingManager_CancelLoadGroup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAsyncLoadingManager::execCancelLoadGroup)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_GroupName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CancelLoadGroup(Z_Param_GroupName);
	P_NATIVE_END;
}
// End Class AAsyncLoadingManager Function CancelLoadGroup

// Begin Class AAsyncLoadingManager Function ClearAssetCache
struct Z_Construct_UFunction_AAsyncLoadingManager_ClearAssetCache_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cache" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cache Management */" },
#endif
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache Management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAsyncLoadingManager_ClearAssetCache_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAsyncLoadingManager, nullptr, "ClearAssetCache", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_ClearAssetCache_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAsyncLoadingManager_ClearAssetCache_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AAsyncLoadingManager_ClearAssetCache()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAsyncLoadingManager_ClearAssetCache_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAsyncLoadingManager::execClearAssetCache)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearAssetCache();
	P_NATIVE_END;
}
// End Class AAsyncLoadingManager Function ClearAssetCache

// Begin Class AAsyncLoadingManager Function ForceGarbageCollection
struct Z_Construct_UFunction_AAsyncLoadingManager_ForceGarbageCollection_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Memory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Memory Management */" },
#endif
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Memory Management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAsyncLoadingManager_ForceGarbageCollection_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAsyncLoadingManager, nullptr, "ForceGarbageCollection", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_ForceGarbageCollection_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAsyncLoadingManager_ForceGarbageCollection_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AAsyncLoadingManager_ForceGarbageCollection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAsyncLoadingManager_ForceGarbageCollection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAsyncLoadingManager::execForceGarbageCollection)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ForceGarbageCollection();
	P_NATIVE_END;
}
// End Class AAsyncLoadingManager Function ForceGarbageCollection

// Begin Class AAsyncLoadingManager Function GetActiveLoadGroups
struct Z_Construct_UFunction_AAsyncLoadingManager_GetActiveLoadGroups_Statics
{
	struct AsyncLoadingManager_eventGetActiveLoadGroups_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_GetActiveLoadGroups_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_GetActiveLoadGroups_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AsyncLoadingManager_eventGetActiveLoadGroups_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAsyncLoadingManager_GetActiveLoadGroups_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_GetActiveLoadGroups_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_GetActiveLoadGroups_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_GetActiveLoadGroups_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAsyncLoadingManager_GetActiveLoadGroups_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAsyncLoadingManager, nullptr, "GetActiveLoadGroups", nullptr, nullptr, Z_Construct_UFunction_AAsyncLoadingManager_GetActiveLoadGroups_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_GetActiveLoadGroups_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAsyncLoadingManager_GetActiveLoadGroups_Statics::AsyncLoadingManager_eventGetActiveLoadGroups_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_GetActiveLoadGroups_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAsyncLoadingManager_GetActiveLoadGroups_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AAsyncLoadingManager_GetActiveLoadGroups_Statics::AsyncLoadingManager_eventGetActiveLoadGroups_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAsyncLoadingManager_GetActiveLoadGroups()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAsyncLoadingManager_GetActiveLoadGroups_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAsyncLoadingManager::execGetActiveLoadGroups)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetActiveLoadGroups();
	P_NATIVE_END;
}
// End Class AAsyncLoadingManager Function GetActiveLoadGroups

// Begin Class AAsyncLoadingManager Function GetAsyncLoadingManager
struct Z_Construct_UFunction_AAsyncLoadingManager_GetAsyncLoadingManager_Statics
{
	struct AsyncLoadingManager_eventGetAsyncLoadingManager_Parms
	{
		const UObject* WorldContext;
		AAsyncLoadingManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Asset Loading" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Static Access */" },
#endif
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Static Access" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldContext_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldContext;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_GetAsyncLoadingManager_Statics::NewProp_WorldContext = { "WorldContext", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AsyncLoadingManager_eventGetAsyncLoadingManager_Parms, WorldContext), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldContext_MetaData), NewProp_WorldContext_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_GetAsyncLoadingManager_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AsyncLoadingManager_eventGetAsyncLoadingManager_Parms, ReturnValue), Z_Construct_UClass_AAsyncLoadingManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAsyncLoadingManager_GetAsyncLoadingManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_GetAsyncLoadingManager_Statics::NewProp_WorldContext,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_GetAsyncLoadingManager_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_GetAsyncLoadingManager_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAsyncLoadingManager_GetAsyncLoadingManager_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAsyncLoadingManager, nullptr, "GetAsyncLoadingManager", nullptr, nullptr, Z_Construct_UFunction_AAsyncLoadingManager_GetAsyncLoadingManager_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_GetAsyncLoadingManager_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAsyncLoadingManager_GetAsyncLoadingManager_Statics::AsyncLoadingManager_eventGetAsyncLoadingManager_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_GetAsyncLoadingManager_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAsyncLoadingManager_GetAsyncLoadingManager_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AAsyncLoadingManager_GetAsyncLoadingManager_Statics::AsyncLoadingManager_eventGetAsyncLoadingManager_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAsyncLoadingManager_GetAsyncLoadingManager()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAsyncLoadingManager_GetAsyncLoadingManager_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAsyncLoadingManager::execGetAsyncLoadingManager)
{
	P_GET_OBJECT(UObject,Z_Param_WorldContext);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(AAsyncLoadingManager**)Z_Param__Result=AAsyncLoadingManager::GetAsyncLoadingManager(Z_Param_WorldContext);
	P_NATIVE_END;
}
// End Class AAsyncLoadingManager Function GetAsyncLoadingManager

// Begin Class AAsyncLoadingManager Function GetCacheHitRate
struct Z_Construct_UFunction_AAsyncLoadingManager_GetCacheHitRate_Statics
{
	struct AsyncLoadingManager_eventGetCacheHitRate_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cache" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_GetCacheHitRate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AsyncLoadingManager_eventGetCacheHitRate_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAsyncLoadingManager_GetCacheHitRate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_GetCacheHitRate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_GetCacheHitRate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAsyncLoadingManager_GetCacheHitRate_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAsyncLoadingManager, nullptr, "GetCacheHitRate", nullptr, nullptr, Z_Construct_UFunction_AAsyncLoadingManager_GetCacheHitRate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_GetCacheHitRate_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAsyncLoadingManager_GetCacheHitRate_Statics::AsyncLoadingManager_eventGetCacheHitRate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_GetCacheHitRate_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAsyncLoadingManager_GetCacheHitRate_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AAsyncLoadingManager_GetCacheHitRate_Statics::AsyncLoadingManager_eventGetCacheHitRate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAsyncLoadingManager_GetCacheHitRate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAsyncLoadingManager_GetCacheHitRate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAsyncLoadingManager::execGetCacheHitRate)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetCacheHitRate();
	P_NATIVE_END;
}
// End Class AAsyncLoadingManager Function GetCacheHitRate

// Begin Class AAsyncLoadingManager Function GetCacheSize
struct Z_Construct_UFunction_AAsyncLoadingManager_GetCacheSize_Statics
{
	struct AsyncLoadingManager_eventGetCacheSize_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cache" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_GetCacheSize_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AsyncLoadingManager_eventGetCacheSize_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAsyncLoadingManager_GetCacheSize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_GetCacheSize_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_GetCacheSize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAsyncLoadingManager_GetCacheSize_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAsyncLoadingManager, nullptr, "GetCacheSize", nullptr, nullptr, Z_Construct_UFunction_AAsyncLoadingManager_GetCacheSize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_GetCacheSize_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAsyncLoadingManager_GetCacheSize_Statics::AsyncLoadingManager_eventGetCacheSize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_GetCacheSize_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAsyncLoadingManager_GetCacheSize_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AAsyncLoadingManager_GetCacheSize_Statics::AsyncLoadingManager_eventGetCacheSize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAsyncLoadingManager_GetCacheSize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAsyncLoadingManager_GetCacheSize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAsyncLoadingManager::execGetCacheSize)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetCacheSize();
	P_NATIVE_END;
}
// End Class AAsyncLoadingManager Function GetCacheSize

// Begin Class AAsyncLoadingManager Function GetLoadingProgress
struct Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingProgress_Statics
{
	struct AsyncLoadingManager_eventGetLoadingProgress_Parms
	{
		FString GroupName;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progress" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Progress Tracking */" },
#endif
		{ "CPP_Default_GroupName", "Default" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Progress Tracking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GroupName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_GroupName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingProgress_Statics::NewProp_GroupName = { "GroupName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AsyncLoadingManager_eventGetLoadingProgress_Parms, GroupName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GroupName_MetaData), NewProp_GroupName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AsyncLoadingManager_eventGetLoadingProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingProgress_Statics::NewProp_GroupName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingProgress_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAsyncLoadingManager, nullptr, "GetLoadingProgress", nullptr, nullptr, Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingProgress_Statics::AsyncLoadingManager_eventGetLoadingProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingProgress_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingProgress_Statics::AsyncLoadingManager_eventGetLoadingProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAsyncLoadingManager::execGetLoadingProgress)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_GroupName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetLoadingProgress(Z_Param_GroupName);
	P_NATIVE_END;
}
// End Class AAsyncLoadingManager Function GetLoadingProgress

// Begin Class AAsyncLoadingManager Function GetLoadingStatistics
struct Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingStatistics_Statics
{
	struct AsyncLoadingManager_eventGetLoadingStatistics_Parms
	{
		FLoadingStatistics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Statistics */" },
#endif
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Statistics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AsyncLoadingManager_eventGetLoadingStatistics_Parms, ReturnValue), Z_Construct_UScriptStruct_FLoadingStatistics, METADATA_PARAMS(0, nullptr) }; // 653947713
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingStatistics_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAsyncLoadingManager, nullptr, "GetLoadingStatistics", nullptr, nullptr, Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingStatistics_Statics::AsyncLoadingManager_eventGetLoadingStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingStatistics_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingStatistics_Statics::AsyncLoadingManager_eventGetLoadingStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAsyncLoadingManager::execGetLoadingStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FLoadingStatistics*)Z_Param__Result=P_THIS->GetLoadingStatistics();
	P_NATIVE_END;
}
// End Class AAsyncLoadingManager Function GetLoadingStatistics

// Begin Class AAsyncLoadingManager Function GetMemoryUsageMB
struct Z_Construct_UFunction_AAsyncLoadingManager_GetMemoryUsageMB_Statics
{
	struct AsyncLoadingManager_eventGetMemoryUsageMB_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_GetMemoryUsageMB_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AsyncLoadingManager_eventGetMemoryUsageMB_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAsyncLoadingManager_GetMemoryUsageMB_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_GetMemoryUsageMB_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_GetMemoryUsageMB_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAsyncLoadingManager_GetMemoryUsageMB_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAsyncLoadingManager, nullptr, "GetMemoryUsageMB", nullptr, nullptr, Z_Construct_UFunction_AAsyncLoadingManager_GetMemoryUsageMB_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_GetMemoryUsageMB_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAsyncLoadingManager_GetMemoryUsageMB_Statics::AsyncLoadingManager_eventGetMemoryUsageMB_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_GetMemoryUsageMB_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAsyncLoadingManager_GetMemoryUsageMB_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AAsyncLoadingManager_GetMemoryUsageMB_Statics::AsyncLoadingManager_eventGetMemoryUsageMB_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAsyncLoadingManager_GetMemoryUsageMB()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAsyncLoadingManager_GetMemoryUsageMB_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAsyncLoadingManager::execGetMemoryUsageMB)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetMemoryUsageMB();
	P_NATIVE_END;
}
// End Class AAsyncLoadingManager Function GetMemoryUsageMB

// Begin Class AAsyncLoadingManager Function IsAssetLoaded
struct Z_Construct_UFunction_AAsyncLoadingManager_IsAssetLoaded_Statics
{
	struct AsyncLoadingManager_eventIsAssetLoaded_Parms
	{
		TSoftObjectPtr<UObject> Asset;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Asset Loading" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Asset_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_Asset;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_IsAssetLoaded_Statics::NewProp_Asset = { "Asset", nullptr, (EPropertyFlags)0x0014000008000182, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AsyncLoadingManager_eventIsAssetLoaded_Parms, Asset), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Asset_MetaData), NewProp_Asset_MetaData) };
void Z_Construct_UFunction_AAsyncLoadingManager_IsAssetLoaded_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AsyncLoadingManager_eventIsAssetLoaded_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_IsAssetLoaded_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AsyncLoadingManager_eventIsAssetLoaded_Parms), &Z_Construct_UFunction_AAsyncLoadingManager_IsAssetLoaded_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAsyncLoadingManager_IsAssetLoaded_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_IsAssetLoaded_Statics::NewProp_Asset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_IsAssetLoaded_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_IsAssetLoaded_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAsyncLoadingManager_IsAssetLoaded_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAsyncLoadingManager, nullptr, "IsAssetLoaded", nullptr, nullptr, Z_Construct_UFunction_AAsyncLoadingManager_IsAssetLoaded_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_IsAssetLoaded_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAsyncLoadingManager_IsAssetLoaded_Statics::AsyncLoadingManager_eventIsAssetLoaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_IsAssetLoaded_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAsyncLoadingManager_IsAssetLoaded_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AAsyncLoadingManager_IsAssetLoaded_Statics::AsyncLoadingManager_eventIsAssetLoaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAsyncLoadingManager_IsAssetLoaded()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAsyncLoadingManager_IsAssetLoaded_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAsyncLoadingManager::execIsAssetLoaded)
{
	P_GET_SOFTOBJECT_REF(TSoftObjectPtr<UObject>,Z_Param_Out_Asset);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsAssetLoaded(Z_Param_Out_Asset);
	P_NATIVE_END;
}
// End Class AAsyncLoadingManager Function IsAssetLoaded

// Begin Class AAsyncLoadingManager Function IsLoadingInProgress
struct Z_Construct_UFunction_AAsyncLoadingManager_IsLoadingInProgress_Statics
{
	struct AsyncLoadingManager_eventIsLoadingInProgress_Parms
	{
		FString GroupName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progress" },
		{ "CPP_Default_GroupName", "Default" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GroupName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_GroupName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_IsLoadingInProgress_Statics::NewProp_GroupName = { "GroupName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AsyncLoadingManager_eventIsLoadingInProgress_Parms, GroupName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GroupName_MetaData), NewProp_GroupName_MetaData) };
void Z_Construct_UFunction_AAsyncLoadingManager_IsLoadingInProgress_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AsyncLoadingManager_eventIsLoadingInProgress_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_IsLoadingInProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AsyncLoadingManager_eventIsLoadingInProgress_Parms), &Z_Construct_UFunction_AAsyncLoadingManager_IsLoadingInProgress_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAsyncLoadingManager_IsLoadingInProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_IsLoadingInProgress_Statics::NewProp_GroupName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_IsLoadingInProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_IsLoadingInProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAsyncLoadingManager_IsLoadingInProgress_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAsyncLoadingManager, nullptr, "IsLoadingInProgress", nullptr, nullptr, Z_Construct_UFunction_AAsyncLoadingManager_IsLoadingInProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_IsLoadingInProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAsyncLoadingManager_IsLoadingInProgress_Statics::AsyncLoadingManager_eventIsLoadingInProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_IsLoadingInProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAsyncLoadingManager_IsLoadingInProgress_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AAsyncLoadingManager_IsLoadingInProgress_Statics::AsyncLoadingManager_eventIsLoadingInProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAsyncLoadingManager_IsLoadingInProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAsyncLoadingManager_IsLoadingInProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAsyncLoadingManager::execIsLoadingInProgress)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_GroupName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsLoadingInProgress(Z_Param_GroupName);
	P_NATIVE_END;
}
// End Class AAsyncLoadingManager Function IsLoadingInProgress

// Begin Class AAsyncLoadingManager Function LoadAssetAsync
struct Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetAsync_Statics
{
	struct AsyncLoadingManager_eventLoadAssetAsync_Parms
	{
		TSoftObjectPtr<UObject> AssetToLoad;
		int32 Priority;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Asset Loading" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Single Asset Loading */" },
#endif
		{ "CPP_Default_Priority", "0" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Single Asset Loading" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetToLoad_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AssetToLoad;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetAsync_Statics::NewProp_AssetToLoad = { "AssetToLoad", nullptr, (EPropertyFlags)0x0014000008000182, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AsyncLoadingManager_eventLoadAssetAsync_Parms, AssetToLoad), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetToLoad_MetaData), NewProp_AssetToLoad_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetAsync_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AsyncLoadingManager_eventLoadAssetAsync_Parms, Priority), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetAsync_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetAsync_Statics::NewProp_AssetToLoad,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetAsync_Statics::NewProp_Priority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetAsync_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetAsync_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAsyncLoadingManager, nullptr, "LoadAssetAsync", nullptr, nullptr, Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetAsync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetAsync_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetAsync_Statics::AsyncLoadingManager_eventLoadAssetAsync_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetAsync_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetAsync_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetAsync_Statics::AsyncLoadingManager_eventLoadAssetAsync_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetAsync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetAsync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAsyncLoadingManager::execLoadAssetAsync)
{
	P_GET_SOFTOBJECT_REF(TSoftObjectPtr<UObject>,Z_Param_Out_AssetToLoad);
	P_GET_PROPERTY(FIntProperty,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LoadAssetAsync(Z_Param_Out_AssetToLoad,Z_Param_Priority);
	P_NATIVE_END;
}
// End Class AAsyncLoadingManager Function LoadAssetAsync

// Begin Class AAsyncLoadingManager Function LoadAssetGroup
struct Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetGroup_Statics
{
	struct AsyncLoadingManager_eventLoadAssetGroup_Parms
	{
		FLoadingGroup LoadGroup;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Batch Loading" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadGroup_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_LoadGroup;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetGroup_Statics::NewProp_LoadGroup = { "LoadGroup", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AsyncLoadingManager_eventLoadAssetGroup_Parms, LoadGroup), Z_Construct_UScriptStruct_FLoadingGroup, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadGroup_MetaData), NewProp_LoadGroup_MetaData) }; // 3940392407
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetGroup_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetGroup_Statics::NewProp_LoadGroup,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetGroup_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetGroup_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAsyncLoadingManager, nullptr, "LoadAssetGroup", nullptr, nullptr, Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetGroup_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetGroup_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetGroup_Statics::AsyncLoadingManager_eventLoadAssetGroup_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetGroup_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetGroup_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetGroup_Statics::AsyncLoadingManager_eventLoadAssetGroup_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetGroup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetGroup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAsyncLoadingManager::execLoadAssetGroup)
{
	P_GET_STRUCT_REF(FLoadingGroup,Z_Param_Out_LoadGroup);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LoadAssetGroup(Z_Param_Out_LoadGroup);
	P_NATIVE_END;
}
// End Class AAsyncLoadingManager Function LoadAssetGroup

// Begin Class AAsyncLoadingManager Function LoadAssetsAsync
struct Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetsAsync_Statics
{
	struct AsyncLoadingManager_eventLoadAssetsAsync_Parms
	{
		TArray<FAssetLoadRequest> AssetRequests;
		FString GroupName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Batch Loading" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Batch Loading */" },
#endif
		{ "CPP_Default_GroupName", "Default" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Batch Loading" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetRequests_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GroupName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_AssetRequests_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AssetRequests;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GroupName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetsAsync_Statics::NewProp_AssetRequests_Inner = { "AssetRequests", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAssetLoadRequest, METADATA_PARAMS(0, nullptr) }; // 886331830
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetsAsync_Statics::NewProp_AssetRequests = { "AssetRequests", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AsyncLoadingManager_eventLoadAssetsAsync_Parms, AssetRequests), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetRequests_MetaData), NewProp_AssetRequests_MetaData) }; // 886331830
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetsAsync_Statics::NewProp_GroupName = { "GroupName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AsyncLoadingManager_eventLoadAssetsAsync_Parms, GroupName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GroupName_MetaData), NewProp_GroupName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetsAsync_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetsAsync_Statics::NewProp_AssetRequests_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetsAsync_Statics::NewProp_AssetRequests,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetsAsync_Statics::NewProp_GroupName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetsAsync_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetsAsync_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAsyncLoadingManager, nullptr, "LoadAssetsAsync", nullptr, nullptr, Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetsAsync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetsAsync_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetsAsync_Statics::AsyncLoadingManager_eventLoadAssetsAsync_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetsAsync_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetsAsync_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetsAsync_Statics::AsyncLoadingManager_eventLoadAssetsAsync_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetsAsync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetsAsync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAsyncLoadingManager::execLoadAssetsAsync)
{
	P_GET_TARRAY_REF(FAssetLoadRequest,Z_Param_Out_AssetRequests);
	P_GET_PROPERTY(FStrProperty,Z_Param_GroupName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LoadAssetsAsync(Z_Param_Out_AssetRequests,Z_Param_GroupName);
	P_NATIVE_END;
}
// End Class AAsyncLoadingManager Function LoadAssetsAsync

// Begin Class AAsyncLoadingManager Function LoadAssetSync
struct Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetSync_Statics
{
	struct AsyncLoadingManager_eventLoadAssetSync_Parms
	{
		TSoftObjectPtr<UObject> AssetToLoad;
		UObject* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Asset Loading" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetToLoad_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AssetToLoad;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetSync_Statics::NewProp_AssetToLoad = { "AssetToLoad", nullptr, (EPropertyFlags)0x0014000008000182, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AsyncLoadingManager_eventLoadAssetSync_Parms, AssetToLoad), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetToLoad_MetaData), NewProp_AssetToLoad_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetSync_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AsyncLoadingManager_eventLoadAssetSync_Parms, ReturnValue), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetSync_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetSync_Statics::NewProp_AssetToLoad,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetSync_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetSync_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetSync_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAsyncLoadingManager, nullptr, "LoadAssetSync", nullptr, nullptr, Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetSync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetSync_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetSync_Statics::AsyncLoadingManager_eventLoadAssetSync_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetSync_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetSync_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetSync_Statics::AsyncLoadingManager_eventLoadAssetSync_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetSync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetSync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAsyncLoadingManager::execLoadAssetSync)
{
	P_GET_SOFTOBJECT_REF(TSoftObjectPtr<UObject>,Z_Param_Out_AssetToLoad);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UObject**)Z_Param__Result=P_THIS->LoadAssetSync(Z_Param_Out_AssetToLoad);
	P_NATIVE_END;
}
// End Class AAsyncLoadingManager Function LoadAssetSync

// Begin Class AAsyncLoadingManager Function LogLoadingStatistics
struct Z_Construct_UFunction_AAsyncLoadingManager_LogLoadingStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAsyncLoadingManager_LogLoadingStatistics_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAsyncLoadingManager, nullptr, "LogLoadingStatistics", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_LogLoadingStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAsyncLoadingManager_LogLoadingStatistics_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AAsyncLoadingManager_LogLoadingStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAsyncLoadingManager_LogLoadingStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAsyncLoadingManager::execLogLoadingStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogLoadingStatistics();
	P_NATIVE_END;
}
// End Class AAsyncLoadingManager Function LogLoadingStatistics

// Begin Class AAsyncLoadingManager Function OnAssetLoadComplete
struct Z_Construct_UFunction_AAsyncLoadingManager_OnAssetLoadComplete_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAsyncLoadingManager_OnAssetLoadComplete_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAsyncLoadingManager, nullptr, "OnAssetLoadComplete", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_OnAssetLoadComplete_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAsyncLoadingManager_OnAssetLoadComplete_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AAsyncLoadingManager_OnAssetLoadComplete()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAsyncLoadingManager_OnAssetLoadComplete_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAsyncLoadingManager::execOnAssetLoadComplete)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnAssetLoadComplete();
	P_NATIVE_END;
}
// End Class AAsyncLoadingManager Function OnAssetLoadComplete

// Begin Class AAsyncLoadingManager Function OptimizeMemoryUsage
struct Z_Construct_UFunction_AAsyncLoadingManager_OptimizeMemoryUsage_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Memory" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAsyncLoadingManager_OptimizeMemoryUsage_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAsyncLoadingManager, nullptr, "OptimizeMemoryUsage", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_OptimizeMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAsyncLoadingManager_OptimizeMemoryUsage_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AAsyncLoadingManager_OptimizeMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAsyncLoadingManager_OptimizeMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAsyncLoadingManager::execOptimizeMemoryUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeMemoryUsage();
	P_NATIVE_END;
}
// End Class AAsyncLoadingManager Function OptimizeMemoryUsage

// Begin Class AAsyncLoadingManager Function PreloadCommonAssets
struct Z_Construct_UFunction_AAsyncLoadingManager_PreloadCommonAssets_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Preloading" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAsyncLoadingManager_PreloadCommonAssets_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAsyncLoadingManager, nullptr, "PreloadCommonAssets", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_PreloadCommonAssets_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAsyncLoadingManager_PreloadCommonAssets_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AAsyncLoadingManager_PreloadCommonAssets()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAsyncLoadingManager_PreloadCommonAssets_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAsyncLoadingManager::execPreloadCommonAssets)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PreloadCommonAssets();
	P_NATIVE_END;
}
// End Class AAsyncLoadingManager Function PreloadCommonAssets

// Begin Class AAsyncLoadingManager Function PreloadSectorAssets
struct Z_Construct_UFunction_AAsyncLoadingManager_PreloadSectorAssets_Statics
{
	struct AsyncLoadingManager_eventPreloadSectorAssets_Parms
	{
		int32 SectorIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Preloading" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Preloading */" },
#endif
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Preloading" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SectorIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_PreloadSectorAssets_Statics::NewProp_SectorIndex = { "SectorIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AsyncLoadingManager_eventPreloadSectorAssets_Parms, SectorIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAsyncLoadingManager_PreloadSectorAssets_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_PreloadSectorAssets_Statics::NewProp_SectorIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_PreloadSectorAssets_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAsyncLoadingManager_PreloadSectorAssets_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAsyncLoadingManager, nullptr, "PreloadSectorAssets", nullptr, nullptr, Z_Construct_UFunction_AAsyncLoadingManager_PreloadSectorAssets_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_PreloadSectorAssets_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAsyncLoadingManager_PreloadSectorAssets_Statics::AsyncLoadingManager_eventPreloadSectorAssets_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_PreloadSectorAssets_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAsyncLoadingManager_PreloadSectorAssets_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AAsyncLoadingManager_PreloadSectorAssets_Statics::AsyncLoadingManager_eventPreloadSectorAssets_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAsyncLoadingManager_PreloadSectorAssets()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAsyncLoadingManager_PreloadSectorAssets_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAsyncLoadingManager::execPreloadSectorAssets)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SectorIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PreloadSectorAssets(Z_Param_SectorIndex);
	P_NATIVE_END;
}
// End Class AAsyncLoadingManager Function PreloadSectorAssets

// Begin Class AAsyncLoadingManager Function PreloadWeaponAssets
struct Z_Construct_UFunction_AAsyncLoadingManager_PreloadWeaponAssets_Statics
{
	struct AsyncLoadingManager_eventPreloadWeaponAssets_Parms
	{
		TArray<FString> WeaponNames;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Preloading" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponNames_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_WeaponNames_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_WeaponNames;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_PreloadWeaponAssets_Statics::NewProp_WeaponNames_Inner = { "WeaponNames", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_PreloadWeaponAssets_Statics::NewProp_WeaponNames = { "WeaponNames", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AsyncLoadingManager_eventPreloadWeaponAssets_Parms, WeaponNames), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponNames_MetaData), NewProp_WeaponNames_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAsyncLoadingManager_PreloadWeaponAssets_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_PreloadWeaponAssets_Statics::NewProp_WeaponNames_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_PreloadWeaponAssets_Statics::NewProp_WeaponNames,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_PreloadWeaponAssets_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAsyncLoadingManager_PreloadWeaponAssets_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAsyncLoadingManager, nullptr, "PreloadWeaponAssets", nullptr, nullptr, Z_Construct_UFunction_AAsyncLoadingManager_PreloadWeaponAssets_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_PreloadWeaponAssets_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAsyncLoadingManager_PreloadWeaponAssets_Statics::AsyncLoadingManager_eventPreloadWeaponAssets_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_PreloadWeaponAssets_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAsyncLoadingManager_PreloadWeaponAssets_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AAsyncLoadingManager_PreloadWeaponAssets_Statics::AsyncLoadingManager_eventPreloadWeaponAssets_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAsyncLoadingManager_PreloadWeaponAssets()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAsyncLoadingManager_PreloadWeaponAssets_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAsyncLoadingManager::execPreloadWeaponAssets)
{
	P_GET_TARRAY_REF(FString,Z_Param_Out_WeaponNames);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PreloadWeaponAssets(Z_Param_Out_WeaponNames);
	P_NATIVE_END;
}
// End Class AAsyncLoadingManager Function PreloadWeaponAssets

// Begin Class AAsyncLoadingManager Function ResetStatistics
struct Z_Construct_UFunction_AAsyncLoadingManager_ResetStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAsyncLoadingManager_ResetStatistics_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAsyncLoadingManager, nullptr, "ResetStatistics", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_ResetStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAsyncLoadingManager_ResetStatistics_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AAsyncLoadingManager_ResetStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAsyncLoadingManager_ResetStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAsyncLoadingManager::execResetStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetStatistics();
	P_NATIVE_END;
}
// End Class AAsyncLoadingManager Function ResetStatistics

// Begin Class AAsyncLoadingManager Function TrimAssetCache
struct Z_Construct_UFunction_AAsyncLoadingManager_TrimAssetCache_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Cache" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAsyncLoadingManager_TrimAssetCache_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAsyncLoadingManager, nullptr, "TrimAssetCache", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_TrimAssetCache_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAsyncLoadingManager_TrimAssetCache_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AAsyncLoadingManager_TrimAssetCache()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAsyncLoadingManager_TrimAssetCache_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAsyncLoadingManager::execTrimAssetCache)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TrimAssetCache();
	P_NATIVE_END;
}
// End Class AAsyncLoadingManager Function TrimAssetCache

// Begin Class AAsyncLoadingManager Function UnloadAsset
struct Z_Construct_UFunction_AAsyncLoadingManager_UnloadAsset_Statics
{
	struct AsyncLoadingManager_eventUnloadAsset_Parms
	{
		TSoftObjectPtr<UObject> Asset;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Asset Loading" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Asset_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_Asset;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UFunction_AAsyncLoadingManager_UnloadAsset_Statics::NewProp_Asset = { "Asset", nullptr, (EPropertyFlags)0x0014000008000182, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AsyncLoadingManager_eventUnloadAsset_Parms, Asset), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Asset_MetaData), NewProp_Asset_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AAsyncLoadingManager_UnloadAsset_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AAsyncLoadingManager_UnloadAsset_Statics::NewProp_Asset,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_UnloadAsset_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AAsyncLoadingManager_UnloadAsset_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AAsyncLoadingManager, nullptr, "UnloadAsset", nullptr, nullptr, Z_Construct_UFunction_AAsyncLoadingManager_UnloadAsset_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_UnloadAsset_Statics::PropPointers), sizeof(Z_Construct_UFunction_AAsyncLoadingManager_UnloadAsset_Statics::AsyncLoadingManager_eventUnloadAsset_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AAsyncLoadingManager_UnloadAsset_Statics::Function_MetaDataParams), Z_Construct_UFunction_AAsyncLoadingManager_UnloadAsset_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AAsyncLoadingManager_UnloadAsset_Statics::AsyncLoadingManager_eventUnloadAsset_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AAsyncLoadingManager_UnloadAsset()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AAsyncLoadingManager_UnloadAsset_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AAsyncLoadingManager::execUnloadAsset)
{
	P_GET_SOFTOBJECT_REF(TSoftObjectPtr<UObject>,Z_Param_Out_Asset);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnloadAsset(Z_Param_Out_Asset);
	P_NATIVE_END;
}
// End Class AAsyncLoadingManager Function UnloadAsset

// Begin Class AAsyncLoadingManager
void AAsyncLoadingManager::StaticRegisterNativesAAsyncLoadingManager()
{
	UClass* Class = AAsyncLoadingManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CancelLoadGroup", &AAsyncLoadingManager::execCancelLoadGroup },
		{ "ClearAssetCache", &AAsyncLoadingManager::execClearAssetCache },
		{ "ForceGarbageCollection", &AAsyncLoadingManager::execForceGarbageCollection },
		{ "GetActiveLoadGroups", &AAsyncLoadingManager::execGetActiveLoadGroups },
		{ "GetAsyncLoadingManager", &AAsyncLoadingManager::execGetAsyncLoadingManager },
		{ "GetCacheHitRate", &AAsyncLoadingManager::execGetCacheHitRate },
		{ "GetCacheSize", &AAsyncLoadingManager::execGetCacheSize },
		{ "GetLoadingProgress", &AAsyncLoadingManager::execGetLoadingProgress },
		{ "GetLoadingStatistics", &AAsyncLoadingManager::execGetLoadingStatistics },
		{ "GetMemoryUsageMB", &AAsyncLoadingManager::execGetMemoryUsageMB },
		{ "IsAssetLoaded", &AAsyncLoadingManager::execIsAssetLoaded },
		{ "IsLoadingInProgress", &AAsyncLoadingManager::execIsLoadingInProgress },
		{ "LoadAssetAsync", &AAsyncLoadingManager::execLoadAssetAsync },
		{ "LoadAssetGroup", &AAsyncLoadingManager::execLoadAssetGroup },
		{ "LoadAssetsAsync", &AAsyncLoadingManager::execLoadAssetsAsync },
		{ "LoadAssetSync", &AAsyncLoadingManager::execLoadAssetSync },
		{ "LogLoadingStatistics", &AAsyncLoadingManager::execLogLoadingStatistics },
		{ "OnAssetLoadComplete", &AAsyncLoadingManager::execOnAssetLoadComplete },
		{ "OptimizeMemoryUsage", &AAsyncLoadingManager::execOptimizeMemoryUsage },
		{ "PreloadCommonAssets", &AAsyncLoadingManager::execPreloadCommonAssets },
		{ "PreloadSectorAssets", &AAsyncLoadingManager::execPreloadSectorAssets },
		{ "PreloadWeaponAssets", &AAsyncLoadingManager::execPreloadWeaponAssets },
		{ "ResetStatistics", &AAsyncLoadingManager::execResetStatistics },
		{ "TrimAssetCache", &AAsyncLoadingManager::execTrimAssetCache },
		{ "UnloadAsset", &AAsyncLoadingManager::execUnloadAsset },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(AAsyncLoadingManager);
UClass* Z_Construct_UClass_AAsyncLoadingManager_NoRegister()
{
	return AAsyncLoadingManager::StaticClass();
}
struct Z_Construct_UClass_AAsyncLoadingManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Async Loading Manager for Rough Reality\n * Handles efficient asset loading with progress tracking and caching\n */" },
#endif
		{ "IncludePath", "Core/AsyncLoadingManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Async Loading Manager for Rough Reality\nHandles efficient asset loading with progress tracking and caching" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAssetCaching_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Loading Configuration */" },
#endif
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Loading Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxCacheSize_MetaData[] = {
		{ "Category", "Configuration" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultTimeout_MetaData[] = {
		{ "Category", "Configuration" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreloadCommonAssets_MetaData[] = {
		{ "Category", "Configuration" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAssetLoaded_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Events */" },
#endif
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAssetLoadFailed_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLoadingProgress_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLoadingComplete_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadingStats_MetaData[] = {
		{ "Category", "Statistics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Loading Statistics */" },
#endif
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Loading Statistics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetCache_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Asset Cache */" },
#endif
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Asset Cache" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadingGroups_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Loading Groups */" },
#endif
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Loading Groups" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GroupProgress_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Loading Progress */" },
#endif
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Loading Progress" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CommonAssets_MetaData[] = {
		{ "Category", "Preloading" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Common Assets to Preload */" },
#endif
		{ "ModuleRelativePath", "Core/AsyncLoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Common Assets to Preload" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnableAssetCaching_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAssetCaching;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxCacheSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultTimeout;
	static void NewProp_bPreloadCommonAssets_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreloadCommonAssets;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAssetLoaded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAssetLoadFailed;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLoadingProgress;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLoadingComplete;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LoadingStats;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AssetCache_ValueProp;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AssetCache_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_AssetCache;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LoadingGroups_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LoadingGroups_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_LoadingGroups;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GroupProgress_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GroupProgress_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_GroupProgress;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_CommonAssets_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CommonAssets;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AAsyncLoadingManager_CancelLoadGroup, "CancelLoadGroup" }, // 2037996309
		{ &Z_Construct_UFunction_AAsyncLoadingManager_ClearAssetCache, "ClearAssetCache" }, // 2611569587
		{ &Z_Construct_UFunction_AAsyncLoadingManager_ForceGarbageCollection, "ForceGarbageCollection" }, // 1926880808
		{ &Z_Construct_UFunction_AAsyncLoadingManager_GetActiveLoadGroups, "GetActiveLoadGroups" }, // 894570506
		{ &Z_Construct_UFunction_AAsyncLoadingManager_GetAsyncLoadingManager, "GetAsyncLoadingManager" }, // 885940319
		{ &Z_Construct_UFunction_AAsyncLoadingManager_GetCacheHitRate, "GetCacheHitRate" }, // 2947283090
		{ &Z_Construct_UFunction_AAsyncLoadingManager_GetCacheSize, "GetCacheSize" }, // 190751097
		{ &Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingProgress, "GetLoadingProgress" }, // 2600597494
		{ &Z_Construct_UFunction_AAsyncLoadingManager_GetLoadingStatistics, "GetLoadingStatistics" }, // 2849788163
		{ &Z_Construct_UFunction_AAsyncLoadingManager_GetMemoryUsageMB, "GetMemoryUsageMB" }, // 349925927
		{ &Z_Construct_UFunction_AAsyncLoadingManager_IsAssetLoaded, "IsAssetLoaded" }, // 1087876366
		{ &Z_Construct_UFunction_AAsyncLoadingManager_IsLoadingInProgress, "IsLoadingInProgress" }, // 3007949415
		{ &Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetAsync, "LoadAssetAsync" }, // 2045998598
		{ &Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetGroup, "LoadAssetGroup" }, // 2239138843
		{ &Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetsAsync, "LoadAssetsAsync" }, // 1053514071
		{ &Z_Construct_UFunction_AAsyncLoadingManager_LoadAssetSync, "LoadAssetSync" }, // 90832807
		{ &Z_Construct_UFunction_AAsyncLoadingManager_LogLoadingStatistics, "LogLoadingStatistics" }, // 1332898235
		{ &Z_Construct_UFunction_AAsyncLoadingManager_OnAssetLoadComplete, "OnAssetLoadComplete" }, // 4237418708
		{ &Z_Construct_UFunction_AAsyncLoadingManager_OptimizeMemoryUsage, "OptimizeMemoryUsage" }, // 276954503
		{ &Z_Construct_UFunction_AAsyncLoadingManager_PreloadCommonAssets, "PreloadCommonAssets" }, // 50869174
		{ &Z_Construct_UFunction_AAsyncLoadingManager_PreloadSectorAssets, "PreloadSectorAssets" }, // 584884682
		{ &Z_Construct_UFunction_AAsyncLoadingManager_PreloadWeaponAssets, "PreloadWeaponAssets" }, // 1399427629
		{ &Z_Construct_UFunction_AAsyncLoadingManager_ResetStatistics, "ResetStatistics" }, // 682910471
		{ &Z_Construct_UFunction_AAsyncLoadingManager_TrimAssetCache, "TrimAssetCache" }, // 3090532111
		{ &Z_Construct_UFunction_AAsyncLoadingManager_UnloadAsset, "UnloadAsset" }, // 1685759804
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AAsyncLoadingManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_bEnableAssetCaching_SetBit(void* Obj)
{
	((AAsyncLoadingManager*)Obj)->bEnableAssetCaching = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_bEnableAssetCaching = { "bEnableAssetCaching", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAsyncLoadingManager), &Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_bEnableAssetCaching_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAssetCaching_MetaData), NewProp_bEnableAssetCaching_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_MaxCacheSize = { "MaxCacheSize", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAsyncLoadingManager, MaxCacheSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxCacheSize_MetaData), NewProp_MaxCacheSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_DefaultTimeout = { "DefaultTimeout", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAsyncLoadingManager, DefaultTimeout), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultTimeout_MetaData), NewProp_DefaultTimeout_MetaData) };
void Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_bPreloadCommonAssets_SetBit(void* Obj)
{
	((AAsyncLoadingManager*)Obj)->bPreloadCommonAssets = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_bPreloadCommonAssets = { "bPreloadCommonAssets", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AAsyncLoadingManager), &Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_bPreloadCommonAssets_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreloadCommonAssets_MetaData), NewProp_bPreloadCommonAssets_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_OnAssetLoaded = { "OnAssetLoaded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAsyncLoadingManager, OnAssetLoaded), Z_Construct_UDelegateFunction_RoughReality_OnAssetLoaded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAssetLoaded_MetaData), NewProp_OnAssetLoaded_MetaData) }; // 2257866693
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_OnAssetLoadFailed = { "OnAssetLoadFailed", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAsyncLoadingManager, OnAssetLoadFailed), Z_Construct_UDelegateFunction_RoughReality_OnAssetLoadFailed__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAssetLoadFailed_MetaData), NewProp_OnAssetLoadFailed_MetaData) }; // 1274947605
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_OnLoadingProgress = { "OnLoadingProgress", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAsyncLoadingManager, OnLoadingProgress), Z_Construct_UDelegateFunction_RoughReality_OnLoadingProgress__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLoadingProgress_MetaData), NewProp_OnLoadingProgress_MetaData) }; // 2274206526
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_OnLoadingComplete = { "OnLoadingComplete", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAsyncLoadingManager, OnLoadingComplete), Z_Construct_UDelegateFunction_RoughReality_OnLoadingComplete__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLoadingComplete_MetaData), NewProp_OnLoadingComplete_MetaData) }; // 1863612303
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_LoadingStats = { "LoadingStats", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAsyncLoadingManager, LoadingStats), Z_Construct_UScriptStruct_FLoadingStatistics, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadingStats_MetaData), NewProp_LoadingStats_MetaData) }; // 653947713
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_AssetCache_ValueProp = { "AssetCache", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_AssetCache_Key_KeyProp = { "AssetCache_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSoftObjectPath, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_AssetCache = { "AssetCache", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAsyncLoadingManager, AssetCache), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetCache_MetaData), NewProp_AssetCache_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_LoadingGroups_ValueProp = { "LoadingGroups", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FLoadingGroup, METADATA_PARAMS(0, nullptr) }; // 3940392407
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_LoadingGroups_Key_KeyProp = { "LoadingGroups_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_LoadingGroups = { "LoadingGroups", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAsyncLoadingManager, LoadingGroups), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadingGroups_MetaData), NewProp_LoadingGroups_MetaData) }; // 3940392407
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_GroupProgress_ValueProp = { "GroupProgress", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_GroupProgress_Key_KeyProp = { "GroupProgress_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_GroupProgress = { "GroupProgress", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAsyncLoadingManager, GroupProgress), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GroupProgress_MetaData), NewProp_GroupProgress_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_CommonAssets_Inner = { "CommonAssets", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_CommonAssets = { "CommonAssets", nullptr, (EPropertyFlags)0x0024080000000015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AAsyncLoadingManager, CommonAssets), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CommonAssets_MetaData), NewProp_CommonAssets_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AAsyncLoadingManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_bEnableAssetCaching,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_MaxCacheSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_DefaultTimeout,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_bPreloadCommonAssets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_OnAssetLoaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_OnAssetLoadFailed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_OnLoadingProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_OnLoadingComplete,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_LoadingStats,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_AssetCache_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_AssetCache_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_AssetCache,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_LoadingGroups_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_LoadingGroups_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_LoadingGroups,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_GroupProgress_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_GroupProgress_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_GroupProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_CommonAssets_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AAsyncLoadingManager_Statics::NewProp_CommonAssets,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAsyncLoadingManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AAsyncLoadingManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AAsyncLoadingManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AAsyncLoadingManager_Statics::ClassParams = {
	&AAsyncLoadingManager::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AAsyncLoadingManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AAsyncLoadingManager_Statics::PropPointers),
	0,
	0x009000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AAsyncLoadingManager_Statics::Class_MetaDataParams), Z_Construct_UClass_AAsyncLoadingManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AAsyncLoadingManager()
{
	if (!Z_Registration_Info_UClass_AAsyncLoadingManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AAsyncLoadingManager.OuterSingleton, Z_Construct_UClass_AAsyncLoadingManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AAsyncLoadingManager.OuterSingleton;
}
template<> ROUGHREALITY_API UClass* StaticClass<AAsyncLoadingManager>()
{
	return AAsyncLoadingManager::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AAsyncLoadingManager);
AAsyncLoadingManager::~AAsyncLoadingManager() {}
// End Class AAsyncLoadingManager

// Begin Registration
struct Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_AsyncLoadingManager_h_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAssetLoadRequest::StaticStruct, Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewStructOps, TEXT("AssetLoadRequest"), &Z_Registration_Info_UScriptStruct_AssetLoadRequest, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAssetLoadRequest), 886331830U) },
		{ FLoadingGroup::StaticStruct, Z_Construct_UScriptStruct_FLoadingGroup_Statics::NewStructOps, TEXT("LoadingGroup"), &Z_Registration_Info_UScriptStruct_LoadingGroup, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FLoadingGroup), 3940392407U) },
		{ FLoadingStatistics::StaticStruct, Z_Construct_UScriptStruct_FLoadingStatistics_Statics::NewStructOps, TEXT("LoadingStatistics"), &Z_Registration_Info_UScriptStruct_LoadingStatistics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FLoadingStatistics), 653947713U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AAsyncLoadingManager, AAsyncLoadingManager::StaticClass, TEXT("AAsyncLoadingManager"), &Z_Registration_Info_UClass_AAsyncLoadingManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AAsyncLoadingManager), 176367198U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_AsyncLoadingManager_h_1260049284(TEXT("/Script/RoughReality"),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_AsyncLoadingManager_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_AsyncLoadingManager_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_AsyncLoadingManager_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_AsyncLoadingManager_h_Statics::ScriptStructInfo),
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
