// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Core/RoughRealityDataAsset.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
struct FGameplayTag;
struct FGameplayTagContainer;
#ifdef ROUGHREALITY_RoughRealityDataAsset_generated_h
#error "RoughRealityDataAsset.generated.h already included, missing '#pragma once' in RoughRealityDataAsset.h"
#endif
#define ROUGHREALITY_RoughRealityDataAsset_generated_h

#define FID_RoughReality_Source_RoughReality_Core_RoughRealityDataAsset_h_17_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execValidateAsset); \
	DECLARE_FUNCTION(execGetAssetIDString); \
	DECLARE_FUNCTION(execHasAllTags); \
	DECLARE_FUNCTION(execHasAnyTag); \
	DECLARE_FUNCTION(execHasTag);


#define FID_RoughReality_Source_RoughReality_Core_RoughRealityDataAsset_h_17_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURoughRealityDataAsset(); \
	friend struct Z_Construct_UClass_URoughRealityDataAsset_Statics; \
public: \
	DECLARE_CLASS(URoughRealityDataAsset, UDataAsset, COMPILED_IN_FLAGS(CLASS_Abstract), CASTCLASS_None, TEXT("/Script/RoughReality"), NO_API) \
	DECLARE_SERIALIZER(URoughRealityDataAsset)


#define FID_RoughReality_Source_RoughReality_Core_RoughRealityDataAsset_h_17_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	URoughRealityDataAsset(URoughRealityDataAsset&&); \
	URoughRealityDataAsset(const URoughRealityDataAsset&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URoughRealityDataAsset); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URoughRealityDataAsset); \
	DEFINE_ABSTRACT_DEFAULT_CONSTRUCTOR_CALL(URoughRealityDataAsset) \
	NO_API virtual ~URoughRealityDataAsset();


#define FID_RoughReality_Source_RoughReality_Core_RoughRealityDataAsset_h_14_PROLOG
#define FID_RoughReality_Source_RoughReality_Core_RoughRealityDataAsset_h_17_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_RoughReality_Source_RoughReality_Core_RoughRealityDataAsset_h_17_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_Core_RoughRealityDataAsset_h_17_INCLASS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_Core_RoughRealityDataAsset_h_17_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ROUGHREALITY_API UClass* StaticClass<class URoughRealityDataAsset>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_RoughReality_Source_RoughReality_Core_RoughRealityDataAsset_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
