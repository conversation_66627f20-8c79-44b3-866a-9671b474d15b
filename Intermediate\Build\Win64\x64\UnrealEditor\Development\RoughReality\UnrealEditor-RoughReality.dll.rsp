/MANIFEST:EMBED
/MANIFESTINPUT:"..\Build\Windows\Resources\Default-Win64.manifest"
/NOLOGO
/DEBUG:FULL
/errorReport:prompt
/MACHINE:x64
/SUBSYSTEM:WINDOWS
/FIXED:No
/NXCOMPAT
/STACK:12000000
/DELAY:UNLOAD
/DLL
/PDBALTPATH:%_PDB%
/d2:-ExtendedWarningInfo
/OPT:NOREF
/OPT:NOICF
/INCREMENTAL:NO
/ignore:4199
/ignore:4099
/ALTERNATENAME:__imp___std_init_once_begin_initialize=__imp_InitOnceBeginInitialize
/ALTERNATENAME:__imp___std_init_once_complete=__imp_InitOnceComplete
/DELAYLOAD:"d3d12.dll"
/DELAYLOAD:"DBGHELP.DLL"
/LIBPATH:"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.38.33130\lib\x64"
/LIBPATH:"C:\Program Files (x86)\Windows Kits\10\lib\10.0.22621.0\ucrt\x64"
/LIBPATH:"C:\Program Files (x86)\Windows Kits\10\lib\10.0.22621.0\um\x64"
/NODEFAULTLIB:"LIBCMT"
/NODEFAULTLIB:"LIBCPMT"
/NODEFAULTLIB:"LIBCMTD"
/NODEFAULTLIB:"LIBCPMTD"
/NODEFAULTLIB:"MSVCRTD"
/NODEFAULTLIB:"MSVCPRTD"
/NODEFAULTLIB:"LIBC"
/NODEFAULTLIB:"LIBCP"
/NODEFAULTLIB:"LIBCD"
/NODEFAULTLIB:"LIBCPD"
/FUNCTIONPADMIN:6
/NOIMPLIB
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\RoughRealityEditor\Development\UnrealEd\SharedPCH.UnrealEd.Project.ValApi.Cpp20.h.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\Module.RoughReality.1.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\Module.RoughReality.2.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\Module.RoughReality.3.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\Module.RoughReality.4.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\Module.RoughReality.5.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\RoughReality.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\RoughRealityCharacter.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\RoughRealityGameMode.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\GameAnalyticsManager.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\RookieCharacter.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\AsyncLoadingManager.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\GameEventSystem.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\GameplayLoopManager.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\ObjectPoolManager.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\RoughRealityDataAsset.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\RoughRealityGameModeBase.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\CharacterStatsDataAsset.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\TileDefinitionDataAsset.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\WeaponDataAsset.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\LevelTile.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\ProceduralLevelBuilder.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\ProgressionManager.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\RoughSaveGame.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\VisualEffectsManager.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\WeaponBase.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\Default.rc2.res"
"..\Plugins\Runtime\GameplayAbilities\Intermediate\Build\Win64\x64\UnrealEditor\Development\GameplayAbilities\UnrealEditor-GameplayAbilities.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\GameplayTags\UnrealEditor-GameplayTags.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\GameplayTasks\UnrealEditor-GameplayTasks.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\Core\UnrealEditor-Core.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\CoreUObject\UnrealEditor-CoreUObject.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\Engine\UnrealEditor-Engine.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\InputCore\UnrealEditor-InputCore.lib"
"..\Plugins\EnhancedInput\Intermediate\Build\Win64\x64\UnrealEditor\Development\EnhancedInput\UnrealEditor-EnhancedInput.lib"
"..\Plugins\Runtime\CommonUI\Intermediate\Build\Win64\x64\UnrealEditor\Development\CommonUI\UnrealEditor-CommonUI.lib"
"..\Plugins\Runtime\ModularGameplay\Intermediate\Build\Win64\x64\UnrealEditor\Development\ModularGameplay\UnrealEditor-ModularGameplay.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\AIModule\UnrealEditor-AIModule.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\NavigationSystem\UnrealEditor-NavigationSystem.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\UMG\UnrealEditor-UMG.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\Slate\UnrealEditor-Slate.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\SlateCore\UnrealEditor-SlateCore.lib"
"delayimp.lib"
"wininet.lib"
"rpcrt4.lib"
"ws2_32.lib"
"dbghelp.lib"
"comctl32.lib"
"Winmm.lib"
"kernel32.lib"
"user32.lib"
"gdi32.lib"
"winspool.lib"
"comdlg32.lib"
"advapi32.lib"
"shell32.lib"
"ole32.lib"
"oleaut32.lib"
"uuid.lib"
"odbc32.lib"
"odbccp32.lib"
"netapi32.lib"
"iphlpapi.lib"
"setupapi.lib"
"synchronization.lib"
"dwmapi.lib"
"imm32.lib"
/OUT:"G:\Gamedev\RoughReality\Binaries\Win64\UnrealEditor-RoughReality.dll"
/PDB:"G:\Gamedev\RoughReality\Binaries\Win64\UnrealEditor-RoughReality.pdb"
/ignore:4078