// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Character.h"
#include "InputActionValue.h"
#include "GameplayTagContainer.h"
#include "Components/TimelineComponent.h"
#include "RookieCharacter.generated.h"

class USpringArmComponent;
class UCameraComponent;
class UInputMappingContext;
class UInputAction;
class AWeaponBase;
class UCharacterStatsDataAsset;
class UTimelineComponent;
class UCurveFloat;

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnHealthChanged, float, NewHealthPercent);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnBulletTimeChanged, float, NewBulletTimePercent);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnRewindChargesChanged, int32, NewCharges);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnWeaponChanged, AWeaponBase*, NewWeapon);

USTRUCT(BlueprintType)
struct FTimeSnapshot
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadOnly, Category = "Time Rewind")
	FVector Location;

	UPROPERTY(BlueprintReadOnly, Category = "Time Rewind")
	FRotator Rotation;

	UPROPERTY(BlueprintReadOnly, Category = "Time Rewind")
	FVector Velocity;

	UPROPERTY(BlueprintReadOnly, Category = "Time Rewind")
	float Health;

	UPROPERTY(BlueprintReadOnly, Category = "Time Rewind")
	float Timestamp;

	FTimeSnapshot()
	{
		Location = FVector::ZeroVector;
		Rotation = FRotator::ZeroRotator;
		Velocity = FVector::ZeroVector;
		Health = 100.0f;
		Timestamp = 0.0f;
	}
};

/**
 * Enhanced Rookie Character with Max Payne-style mechanics
 * Features bullet-time, time rewind, dash, and advanced combat
 */
UCLASS(BlueprintType, Blueprintable)
class ROUGHREALITY_API ARookieCharacter : public ACharacter
{
	GENERATED_BODY()

public:
	ARookieCharacter();

protected:
	virtual void BeginPlay() override;
	virtual void Tick(float DeltaTime) override;
	virtual void SetupPlayerInputComponent(UInputComponent* PlayerInputComponent) override;
	virtual void NotifyControllerChanged() override;

	/** Camera Components */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Camera", meta = (AllowPrivateAccess = "true"))
	USpringArmComponent* CameraBoom;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Camera", meta = (AllowPrivateAccess = "true"))
	UCameraComponent* FollowCamera;

	/** Input */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input", meta = (AllowPrivateAccess = "true"))
	UInputMappingContext* DefaultMappingContext;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input", meta = (AllowPrivateAccess = "true"))
	UInputAction* MoveAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input", meta = (AllowPrivateAccess = "true"))
	UInputAction* LookAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input", meta = (AllowPrivateAccess = "true"))
	UInputAction* JumpAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input", meta = (AllowPrivateAccess = "true"))
	UInputAction* FireAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input", meta = (AllowPrivateAccess = "true"))
	UInputAction* BulletTimeAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input", meta = (AllowPrivateAccess = "true"))
	UInputAction* RewindAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input", meta = (AllowPrivateAccess = "true"))
	UInputAction* DashAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input", meta = (AllowPrivateAccess = "true"))
	UInputAction* InteractAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input", meta = (AllowPrivateAccess = "true"))
	UInputAction* WeaponSwitchAction;

public:
	/** Character Stats */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Character Stats")
	UCharacterStatsDataAsset* CharacterStats;

	UPROPERTY(BlueprintReadOnly, Category = "Character Stats")
	float CurrentHealth = 100.0f;

	UPROPERTY(BlueprintReadOnly, Category = "Character Stats")
	float MaxHealth = 100.0f;

	/** Bullet Time System */
	UPROPERTY(BlueprintReadOnly, Category = "Bullet Time")
	float BulletTimeEnergy = 100.0f;

	UPROPERTY(BlueprintReadOnly, Category = "Bullet Time")
	float MaxBulletTimeEnergy = 100.0f;

	UPROPERTY(BlueprintReadOnly, Category = "Bullet Time")
	bool bIsBulletTimeActive = false;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Bullet Time")
	float BulletTimeScale = 0.3f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Bullet Time")
	float BulletTimeDrainRate = 25.0f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Bullet Time")
	float BulletTimeRechargeRate = 15.0f;

	/** Time Rewind System */
	UPROPERTY(BlueprintReadOnly, Category = "Time Rewind")
	int32 RewindCharges = 3;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Time Rewind")
	int32 MaxRewindCharges = 3;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Time Rewind")
	float SnapshotInterval = 0.2f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Time Rewind")
	float RewindDuration = 3.0f;

	UPROPERTY(BlueprintReadOnly, Category = "Time Rewind")
	TArray<FTimeSnapshot> TimeSnapshots;

	/** Dash System */
	UPROPERTY(BlueprintReadOnly, Category = "Dash")
	bool bCanDash = true;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Dash")
	float DashDistance = 1000.0f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Dash")
	float DashCooldown = 2.0f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Dash")
	float DashDuration = 0.3f;

	/** Weapon System */
	UPROPERTY(BlueprintReadOnly, Category = "Weapons")
	AWeaponBase* CurrentWeapon;

	UPROPERTY(BlueprintReadOnly, Category = "Weapons")
	TArray<AWeaponBase*> WeaponInventory;

	UPROPERTY(BlueprintReadOnly, Category = "Weapons")
	int32 CurrentWeaponIndex = 0;

public:
	/** Events */
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnHealthChanged OnHealthChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnBulletTimeChanged OnBulletTimeChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnRewindChargesChanged OnRewindChargesChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnWeaponChanged OnWeaponChanged;

public:
	/** Input Handlers */
	void Move(const FInputActionValue& Value);
	void Look(const FInputActionValue& Value);
	void StartFire(const FInputActionValue& Value);
	void StopFire(const FInputActionValue& Value);
	void StartBulletTime(const FInputActionValue& Value);
	void StopBulletTime(const FInputActionValue& Value);
	void PerformRewind(const FInputActionValue& Value);
	void PerformDash(const FInputActionValue& Value);
	void Interact(const FInputActionValue& Value);
	void SwitchWeapon(const FInputActionValue& Value);

	/** Health System */
	// Override base class virtual function
	virtual float TakeDamage(float DamageAmount, const FDamageEvent& DamageEvent, AController* EventInstigator, AActor* DamageCauser) override;

	// Simplified Blueprint-callable version
	UFUNCTION(BlueprintCallable, Category = "Health")
	void TakeDamageSimple(float DamageAmount);

	UFUNCTION(BlueprintCallable, Category = "Health")
	void Heal(float HealAmount);

	UFUNCTION(BlueprintCallable, Category = "Health")
	bool IsDead() const;

	UFUNCTION(BlueprintCallable, Category = "Health")
	float GetHealthPercent() const;

	/** Bullet Time */
	UFUNCTION(BlueprintCallable, Category = "Bullet Time")
	void ActivateBulletTime();

	UFUNCTION(BlueprintCallable, Category = "Bullet Time")
	void DeactivateBulletTime();

	UFUNCTION(BlueprintCallable, Category = "Bullet Time")
	bool CanUseBulletTime() const;

	UFUNCTION(BlueprintCallable, Category = "Bullet Time")
	float GetBulletTimePercent() const;

	/** Time Rewind */
	UFUNCTION(BlueprintCallable, Category = "Time Rewind")
	void PerformTimeRewind();

	UFUNCTION(BlueprintCallable, Category = "Time Rewind")
	bool CanRewind() const;

	UFUNCTION(BlueprintCallable, Category = "Time Rewind")
	void AddRewindCharge();

	/** Dash */
	UFUNCTION(BlueprintCallable, Category = "Dash")
	void PerformDashMovement();

	UFUNCTION(BlueprintCallable, Category = "Dash")
	bool CanPerformDash() const;

	/** Weapon Management */
	UFUNCTION(BlueprintCallable, Category = "Weapons")
	void EquipWeapon(AWeaponBase* NewWeapon);

	UFUNCTION(BlueprintCallable, Category = "Weapons")
	void AddWeaponToInventory(AWeaponBase* Weapon);

	UFUNCTION(BlueprintCallable, Category = "Weapons")
	void SwitchToNextWeapon();

	UFUNCTION(BlueprintCallable, Category = "Weapons")
	void SwitchToPreviousWeapon();

	/** Integration with Enhanced Systems */
	UFUNCTION(BlueprintCallable, Category = "Integration")
	void RegisterWithGameSystems();

	UFUNCTION(BlueprintCallable, Category = "Integration")
	void UnregisterFromGameSystems();

	/** Performance Optimization */
	UFUNCTION(BlueprintCallable, Category = "Performance")
	void UpdatePerformanceSettings(int32 QualityLevel);

	UFUNCTION(BlueprintCallable, Category = "Performance")
	void SetLODLevel(int32 LODLevel);

	/** Analytics Integration */
	UFUNCTION(BlueprintCallable, Category = "Analytics")
	void RecordPlayerAction(const FString& ActionType, const TMap<FString, FString>& Parameters);

	/** Getters */
	FORCEINLINE USpringArmComponent* GetCameraBoom() const { return CameraBoom; }
	FORCEINLINE UCameraComponent* GetFollowCamera() const { return FollowCamera; }

protected:
	/** Internal Systems */
	void UpdateBulletTime(float DeltaTime);
	void UpdateTimeSnapshots(float DeltaTime);
	void UpdateDashCooldown(float DeltaTime);

	/** Timeline Components */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Timeline")
	UTimelineComponent* DashTimeline;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Timeline")
	UCurveFloat* DashCurve;

	/** Timeline Functions */
	UFUNCTION()
	void DashTimelineUpdate(float Value);

	UFUNCTION()
	void DashTimelineFinished();

private:
	float LastSnapshotTime = 0.0f;
	float DashCooldownTimer = 0.0f;
	bool bIsDashing = false;
	FVector DashStartLocation;
	FVector DashTargetLocation;
};
