// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RoughReality/RoughRealityGameMode.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeRoughRealityGameMode() {}

// Begin Cross Module References
ENGINE_API UClass* Z_Construct_UClass_AGameModeBase();
ROUGHREALITY_API UClass* Z_Construct_UClass_ARoughRealityGameMode();
ROUGHREALITY_API UClass* Z_Construct_UClass_ARoughRealityGameMode_NoRegister();
UPackage* Z_Construct_UPackage__Script_RoughReality();
// End Cross Module References

// Begin Class ARoughRealityGameMode
void ARoughRealityGameMode::StaticRegisterNativesARoughRealityGameMode()
{
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(ARoughRealityGameMode);
UClass* Z_Construct_UClass_ARoughRealityGameMode_NoRegister()
{
	return ARoughRealityGameMode::StaticClass();
}
struct Z_Construct_UClass_ARoughRealityGameMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "HideCategories", "Info Rendering MovementReplication Replication Actor Input Movement Collision Rendering HLOD WorldPartition DataLayers Transformation" },
		{ "IncludePath", "RoughRealityGameMode.h" },
		{ "ModuleRelativePath", "RoughRealityGameMode.h" },
		{ "ShowCategories", "Input|MouseInput Input|TouchInput" },
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ARoughRealityGameMode>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_ARoughRealityGameMode_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AGameModeBase,
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ARoughRealityGameMode_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ARoughRealityGameMode_Statics::ClassParams = {
	&ARoughRealityGameMode::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	0,
	0,
	0x008802ACu,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ARoughRealityGameMode_Statics::Class_MetaDataParams), Z_Construct_UClass_ARoughRealityGameMode_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ARoughRealityGameMode()
{
	if (!Z_Registration_Info_UClass_ARoughRealityGameMode.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ARoughRealityGameMode.OuterSingleton, Z_Construct_UClass_ARoughRealityGameMode_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ARoughRealityGameMode.OuterSingleton;
}
template<> ROUGHREALITY_API UClass* StaticClass<ARoughRealityGameMode>()
{
	return ARoughRealityGameMode::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ARoughRealityGameMode);
ARoughRealityGameMode::~ARoughRealityGameMode() {}
// End Class ARoughRealityGameMode

// Begin Registration
struct Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_RoughRealityGameMode_h_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ARoughRealityGameMode, ARoughRealityGameMode::StaticClass, TEXT("ARoughRealityGameMode"), &Z_Registration_Info_UClass_ARoughRealityGameMode, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ARoughRealityGameMode), 1984238020U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_RoughRealityGameMode_h_1621961381(TEXT("/Script/RoughReality"),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_RoughRealityGameMode_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_RoughRealityGameMode_h_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
