// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Core/GameplayLoopManager.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
struct FSectorConfiguration;
#ifdef ROUGHREALITY_GameplayLoopManager_generated_h
#error "GameplayLoopManager.generated.h already included, missing '#pragma once' in GameplayLoopManager.h"
#endif
#define ROUGHREALITY_GameplayLoopManager_generated_h

#define FID_RoughReality_Source_RoughReality_Core_GameplayLoopManager_h_14_DELEGATE \
ROUGHREALITY_API void FOnRunStarted_DelegateWrapper(const FMulticastScriptDelegate& OnRunStarted, int32 RunNumber);


#define FID_RoughReality_Source_RoughReality_Core_GameplayLoopManager_h_15_DELEGATE \
ROUGHREALITY_API void FOnRunCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnRunCompleted, bool bSuccess, int32 RunNumber);


#define FID_RoughReality_Source_RoughReality_Core_GameplayLoopManager_h_16_DELEGATE \
ROUGHREALITY_API void FOnSectorChanged_DelegateWrapper(const FMulticastScriptDelegate& OnSectorChanged, int32 NewSector, int32 OldSector);


#define FID_RoughReality_Source_RoughReality_Core_GameplayLoopManager_h_17_DELEGATE \
ROUGHREALITY_API void FOnDifficultyScaled_DelegateWrapper(const FMulticastScriptDelegate& OnDifficultyScaled, float NewDifficultyMultiplier);


#define FID_RoughReality_Source_RoughReality_Core_GameplayLoopManager_h_41_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FRunStatistics_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FRunStatistics>();

#define FID_RoughReality_Source_RoughReality_Core_GameplayLoopManager_h_89_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSectorConfiguration_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FSectorConfiguration>();

#define FID_RoughReality_Source_RoughReality_Core_GameplayLoopManager_h_138_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execUnloadUnusedAssets); \
	DECLARE_FUNCTION(execPreloadSectorAssets); \
	DECLARE_FUNCTION(execIsRunInProgress); \
	DECLARE_FUNCTION(execGetRunProgress); \
	DECLARE_FUNCTION(execGetTotalSectors); \
	DECLARE_FUNCTION(execGetSectorConfiguration); \
	DECLARE_FUNCTION(execGetCurrentSectorConfiguration); \
	DECLARE_FUNCTION(execUpdateAccuracy); \
	DECLARE_FUNCTION(execRecordTeethCollected); \
	DECLARE_FUNCTION(execRecordRewindUsage); \
	DECLARE_FUNCTION(execRecordBulletTimeUsage); \
	DECLARE_FUNCTION(execRecordWeaponUsage); \
	DECLARE_FUNCTION(execRecordEnemyKill); \
	DECLARE_FUNCTION(execUpdateDifficultyScaling); \
	DECLARE_FUNCTION(execCalculateDifficultyMultiplier); \
	DECLARE_FUNCTION(execCanAdvanceToSector); \
	DECLARE_FUNCTION(execAdvanceToNextSector); \
	DECLARE_FUNCTION(execAdvanceToNextLevel); \
	DECLARE_FUNCTION(execAbandonCurrentRun); \
	DECLARE_FUNCTION(execCompleteCurrentRun); \
	DECLARE_FUNCTION(execStartNewRun);


#define FID_RoughReality_Source_RoughReality_Core_GameplayLoopManager_h_138_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAGameplayLoopManager(); \
	friend struct Z_Construct_UClass_AGameplayLoopManager_Statics; \
public: \
	DECLARE_CLASS(AGameplayLoopManager, AGameStateBase, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/RoughReality"), NO_API) \
	DECLARE_SERIALIZER(AGameplayLoopManager)


#define FID_RoughReality_Source_RoughReality_Core_GameplayLoopManager_h_138_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	AGameplayLoopManager(AGameplayLoopManager&&); \
	AGameplayLoopManager(const AGameplayLoopManager&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AGameplayLoopManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AGameplayLoopManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AGameplayLoopManager) \
	NO_API virtual ~AGameplayLoopManager();


#define FID_RoughReality_Source_RoughReality_Core_GameplayLoopManager_h_135_PROLOG
#define FID_RoughReality_Source_RoughReality_Core_GameplayLoopManager_h_138_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_RoughReality_Source_RoughReality_Core_GameplayLoopManager_h_138_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_Core_GameplayLoopManager_h_138_INCLASS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_Core_GameplayLoopManager_h_138_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ROUGHREALITY_API UClass* StaticClass<class AGameplayLoopManager>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_RoughReality_Source_RoughReality_Core_GameplayLoopManager_h


#define FOREACH_ENUM_ERUNSTATE(op) \
	op(ERunState::NotStarted) \
	op(ERunState::InProgress) \
	op(ERunState::Completed) \
	op(ERunState::Failed) \
	op(ERunState::Abandoned) 

enum class ERunState : uint8;
template<> struct TIsUEnumClass<ERunState> { enum { Value = true }; };
template<> ROUGHREALITY_API UEnum* StaticEnum<ERunState>();

#define FOREACH_ENUM_ESECTORTYPE(op) \
	op(ESectorType::DilapidatedCity) \
	op(ESectorType::StationTunnels) \
	op(ESectorType::BelaVegas) \
	op(ESectorType::IndustrialDistrict) 

enum class ESectorType : uint8;
template<> struct TIsUEnumClass<ESectorType> { enum { Value = true }; };
template<> ROUGHREALITY_API UEnum* StaticEnum<ESectorType>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
