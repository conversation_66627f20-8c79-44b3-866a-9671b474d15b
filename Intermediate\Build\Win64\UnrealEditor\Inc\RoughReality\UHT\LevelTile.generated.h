// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "LevelGeneration/LevelTile.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class AActor;
class ALevelTile;
class UTileDefinitionDataAsset;
enum class ETileType : uint8;
#ifdef ROUGHREALITY_LevelTile_generated_h
#error "LevelTile.generated.h already included, missing '#pragma once' in LevelTile.h"
#endif
#define ROUGHREALITY_LevelTile_generated_h

#define FID_RoughReality_Source_RoughReality_LevelGeneration_LevelTile_h_16_DELEGATE \
ROUGHREALITY_API void FOnTileActivated_DelegateWrapper(const FMulticastScriptDelegate& OnTileActivated, ALevelTile* Tile);


#define FID_RoughReality_Source_RoughReality_LevelGeneration_LevelTile_h_17_DELEGATE \
ROUGHREALITY_API void FOnTileDeactivated_DelegateWrapper(const FMulticastScriptDelegate& OnTileDeactivated, ALevelTile* Tile);


#define FID_RoughReality_Source_RoughReality_LevelGeneration_LevelTile_h_18_DELEGATE \
ROUGHREALITY_API void FOnTileCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnTileCompleted, ALevelTile* Tile);


#define FID_RoughReality_Source_RoughReality_LevelGeneration_LevelTile_h_27_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execApplyTileLighting); \
	DECLARE_FUNCTION(execStopAmbientSound); \
	DECLARE_FUNCTION(execPlayAmbientSound); \
	DECLARE_FUNCTION(execOnEnemyDestroyed); \
	DECLARE_FUNCTION(execGetAliveEnemies); \
	DECLARE_FUNCTION(execGetAliveEnemyCount); \
	DECLARE_FUNCTION(execGetExitLocation); \
	DECLARE_FUNCTION(execGetEntranceLocation); \
	DECLARE_FUNCTION(execGetConnectionPointLocations); \
	DECLARE_FUNCTION(execGetTileBounds); \
	DECLARE_FUNCTION(execGetTileSize); \
	DECLARE_FUNCTION(execIsCompleted); \
	DECLARE_FUNCTION(execIsActive); \
	DECLARE_FUNCTION(execGetTileType); \
	DECLARE_FUNCTION(execCheckCompletionConditions); \
	DECLARE_FUNCTION(execCompleteTile); \
	DECLARE_FUNCTION(execSpawnInteractables); \
	DECLARE_FUNCTION(execSpawnItems); \
	DECLARE_FUNCTION(execSpawnEnemies); \
	DECLARE_FUNCTION(execClearTileContent); \
	DECLARE_FUNCTION(execSpawnTileContent); \
	DECLARE_FUNCTION(execDeactivateTile); \
	DECLARE_FUNCTION(execActivateTile); \
	DECLARE_FUNCTION(execInitializeTile);


#define FID_RoughReality_Source_RoughReality_LevelGeneration_LevelTile_h_27_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesALevelTile(); \
	friend struct Z_Construct_UClass_ALevelTile_Statics; \
public: \
	DECLARE_CLASS(ALevelTile, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/RoughReality"), NO_API) \
	DECLARE_SERIALIZER(ALevelTile)


#define FID_RoughReality_Source_RoughReality_LevelGeneration_LevelTile_h_27_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	ALevelTile(ALevelTile&&); \
	ALevelTile(const ALevelTile&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ALevelTile); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ALevelTile); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ALevelTile) \
	NO_API virtual ~ALevelTile();


#define FID_RoughReality_Source_RoughReality_LevelGeneration_LevelTile_h_24_PROLOG
#define FID_RoughReality_Source_RoughReality_LevelGeneration_LevelTile_h_27_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_RoughReality_Source_RoughReality_LevelGeneration_LevelTile_h_27_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_LevelGeneration_LevelTile_h_27_INCLASS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_LevelGeneration_LevelTile_h_27_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ROUGHREALITY_API UClass* StaticClass<class ALevelTile>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_RoughReality_Source_RoughReality_LevelGeneration_LevelTile_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
