// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "LevelGeneration/ProceduralLevelBuilder.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class ALevelTile;
class UTileDefinitionDataAsset;
enum class ETileType : uint8;
struct FLevelSectorConfiguration;
#ifdef ROUGHREALITY_ProceduralLevelBuilder_generated_h
#error "ProceduralLevelBuilder.generated.h already included, missing '#pragma once' in ProceduralLevelBuilder.h"
#endif
#define ROUGHREALITY_ProceduralLevelBuilder_generated_h

#define FID_RoughReality_Source_RoughReality_LevelGeneration_ProceduralLevelBuilder_h_13_DELEGATE \
ROUGHREALITY_API void FOnLevelGenerated_DelegateWrapper(const FMulticastScriptDelegate& OnLevelGenerated, int32 SectorIndex, int32 LevelIndex);


#define FID_RoughReality_Source_RoughReality_LevelGeneration_ProceduralLevelBuilder_h_14_DELEGATE \
ROUGHREALITY_API void FOnLevelGenerationFailed_DelegateWrapper(const FMulticastScriptDelegate& OnLevelGenerationFailed, const FString& ErrorMessage);


#define FID_RoughReality_Source_RoughReality_LevelGeneration_ProceduralLevelBuilder_h_30_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTileSpawnInfo_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FTileSpawnInfo>();

#define FID_RoughReality_Source_RoughReality_LevelGeneration_ProceduralLevelBuilder_h_60_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FLevelSectorConfiguration>();

#define FID_RoughReality_Source_RoughReality_LevelGeneration_ProceduralLevelBuilder_h_99_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execValidateSectorConfiguration); \
	DECLARE_FUNCTION(execValidateLevel); \
	DECLARE_FUNCTION(execGetCurrentRandomSeed); \
	DECLARE_FUNCTION(execSetRandomSeed); \
	DECLARE_FUNCTION(execCalculateTilePosition); \
	DECLARE_FUNCTION(execGetTilesByType); \
	DECLARE_FUNCTION(execDestroyTile); \
	DECLARE_FUNCTION(execSpawnTile); \
	DECLARE_FUNCTION(execGetSectorCount); \
	DECLARE_FUNCTION(execGetSectorConfiguration); \
	DECLARE_FUNCTION(execIsSectorValid); \
	DECLARE_FUNCTION(execRegenerateCurrentLevel); \
	DECLARE_FUNCTION(execClearCurrentLevel); \
	DECLARE_FUNCTION(execGenerateLevel);


#define FID_RoughReality_Source_RoughReality_LevelGeneration_ProceduralLevelBuilder_h_99_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAProceduralLevelBuilder(); \
	friend struct Z_Construct_UClass_AProceduralLevelBuilder_Statics; \
public: \
	DECLARE_CLASS(AProceduralLevelBuilder, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/RoughReality"), NO_API) \
	DECLARE_SERIALIZER(AProceduralLevelBuilder)


#define FID_RoughReality_Source_RoughReality_LevelGeneration_ProceduralLevelBuilder_h_99_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	AProceduralLevelBuilder(AProceduralLevelBuilder&&); \
	AProceduralLevelBuilder(const AProceduralLevelBuilder&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AProceduralLevelBuilder); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AProceduralLevelBuilder); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AProceduralLevelBuilder) \
	NO_API virtual ~AProceduralLevelBuilder();


#define FID_RoughReality_Source_RoughReality_LevelGeneration_ProceduralLevelBuilder_h_96_PROLOG
#define FID_RoughReality_Source_RoughReality_LevelGeneration_ProceduralLevelBuilder_h_99_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_RoughReality_Source_RoughReality_LevelGeneration_ProceduralLevelBuilder_h_99_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_LevelGeneration_ProceduralLevelBuilder_h_99_INCLASS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_LevelGeneration_ProceduralLevelBuilder_h_99_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ROUGHREALITY_API UClass* StaticClass<class AProceduralLevelBuilder>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_RoughReality_Source_RoughReality_LevelGeneration_ProceduralLevelBuilder_h


#define FOREACH_ENUM_ETILETYPE(op) \
	op(ETileType::Start) \
	op(ETileType::Combat) \
	op(ETileType::Shop) \
	op(ETileType::Boss) \
	op(ETileType::Transition) \
	op(ETileType::Special) 

enum class ETileType : uint8;
template<> struct TIsUEnumClass<ETileType> { enum { Value = true }; };
template<> ROUGHREALITY_API UEnum* StaticEnum<ETileType>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
