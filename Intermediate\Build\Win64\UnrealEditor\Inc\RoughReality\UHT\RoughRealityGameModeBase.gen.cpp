// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RoughReality/Core/RoughRealityGameModeBase.h"
#include "Runtime/GameplayTags/Classes/GameplayTagContainer.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeRoughRealityGameModeBase() {}

// Begin Cross Module References
ENGINE_API UClass* Z_Construct_UClass_AGameModeBase();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTag();
ROUGHREALITY_API UClass* Z_Construct_UClass_AProceduralLevelBuilder_NoRegister();
ROUGHREALITY_API UClass* Z_Construct_UClass_ARookieCharacter_NoRegister();
ROUGHREALITY_API UClass* Z_Construct_UClass_ARoughRealityGameModeBase();
ROUGHREALITY_API UClass* Z_Construct_UClass_ARoughRealityGameModeBase_NoRegister();
ROUGHREALITY_API UClass* Z_Construct_UClass_URoughSaveGame_NoRegister();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnGameStateChanged__DelegateSignature();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnPlayerDied__DelegateSignature();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature();
UPackage* Z_Construct_UPackage__Script_RoughReality();
// End Cross Module References

// Begin Delegate FOnGameStateChanged
struct Z_Construct_UDelegateFunction_RoughReality_OnGameStateChanged__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnGameStateChanged_Parms
	{
		FGameplayTag NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnGameStateChanged__DelegateSignature_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnGameStateChanged_Parms, NewState), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(0, nullptr) }; // 1298103297
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnGameStateChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnGameStateChanged__DelegateSignature_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnGameStateChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnGameStateChanged__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnGameStateChanged__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnGameStateChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnGameStateChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnGameStateChanged__DelegateSignature_Statics::_Script_RoughReality_eventOnGameStateChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnGameStateChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnGameStateChanged__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnGameStateChanged__DelegateSignature_Statics::_Script_RoughReality_eventOnGameStateChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnGameStateChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnGameStateChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnGameStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnGameStateChanged, FGameplayTag NewState)
{
	struct _Script_RoughReality_eventOnGameStateChanged_Parms
	{
		FGameplayTag NewState;
	};
	_Script_RoughReality_eventOnGameStateChanged_Parms Parms;
	Parms.NewState=NewState;
	OnGameStateChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnGameStateChanged

// Begin Delegate FOnPlayerDied
struct Z_Construct_UDelegateFunction_RoughReality_OnPlayerDied__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnPlayerDied_Parms
	{
		ARookieCharacter* DeadPlayer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DeadPlayer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnPlayerDied__DelegateSignature_Statics::NewProp_DeadPlayer = { "DeadPlayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnPlayerDied_Parms, DeadPlayer), Z_Construct_UClass_ARookieCharacter_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnPlayerDied__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnPlayerDied__DelegateSignature_Statics::NewProp_DeadPlayer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnPlayerDied__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnPlayerDied__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnPlayerDied__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnPlayerDied__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnPlayerDied__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnPlayerDied__DelegateSignature_Statics::_Script_RoughReality_eventOnPlayerDied_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnPlayerDied__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnPlayerDied__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnPlayerDied__DelegateSignature_Statics::_Script_RoughReality_eventOnPlayerDied_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnPlayerDied__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnPlayerDied__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPlayerDied_DelegateWrapper(const FMulticastScriptDelegate& OnPlayerDied, ARookieCharacter* DeadPlayer)
{
	struct _Script_RoughReality_eventOnPlayerDied_Parms
	{
		ARookieCharacter* DeadPlayer;
	};
	_Script_RoughReality_eventOnPlayerDied_Parms Parms;
	Parms.DeadPlayer=DeadPlayer;
	OnPlayerDied.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnPlayerDied

// Begin Delegate FOnRunCompleted
struct Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnRunCompleted_Parms
	{
		bool bSuccess;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((_Script_RoughReality_eventOnRunCompleted_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(_Script_RoughReality_eventOnRunCompleted_Parms), &Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnRunCompleted__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::_Script_RoughReality_eventOnRunCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::_Script_RoughReality_eventOnRunCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnRunCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnRunCompleted, bool bSuccess)
{
	struct _Script_RoughReality_eventOnRunCompleted_Parms
	{
		bool bSuccess;
	};
	_Script_RoughReality_eventOnRunCompleted_Parms Parms;
	Parms.bSuccess=bSuccess ? true : false;
	OnRunCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnRunCompleted

// Begin Class ARoughRealityGameModeBase Function AddTeeth
struct Z_Construct_UFunction_ARoughRealityGameModeBase_AddTeeth_Statics
{
	struct RoughRealityGameModeBase_eventAddTeeth_Parms
	{
		int32 Amount;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Currency" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Currency management */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Currency management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Amount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ARoughRealityGameModeBase_AddTeeth_Statics::NewProp_Amount = { "Amount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughRealityGameModeBase_eventAddTeeth_Parms, Amount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARoughRealityGameModeBase_AddTeeth_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARoughRealityGameModeBase_AddTeeth_Statics::NewProp_Amount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_AddTeeth_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARoughRealityGameModeBase_AddTeeth_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARoughRealityGameModeBase, nullptr, "AddTeeth", nullptr, nullptr, Z_Construct_UFunction_ARoughRealityGameModeBase_AddTeeth_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_AddTeeth_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARoughRealityGameModeBase_AddTeeth_Statics::RoughRealityGameModeBase_eventAddTeeth_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_AddTeeth_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARoughRealityGameModeBase_AddTeeth_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ARoughRealityGameModeBase_AddTeeth_Statics::RoughRealityGameModeBase_eventAddTeeth_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARoughRealityGameModeBase_AddTeeth()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARoughRealityGameModeBase_AddTeeth_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARoughRealityGameModeBase::execAddTeeth)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Amount);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddTeeth(Z_Param_Amount);
	P_NATIVE_END;
}
// End Class ARoughRealityGameModeBase Function AddTeeth

// Begin Class ARoughRealityGameModeBase Function AdvanceToNextLevel
struct Z_Construct_UFunction_ARoughRealityGameModeBase_AdvanceToNextLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Roguelike" },
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARoughRealityGameModeBase_AdvanceToNextLevel_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARoughRealityGameModeBase, nullptr, "AdvanceToNextLevel", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_AdvanceToNextLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARoughRealityGameModeBase_AdvanceToNextLevel_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ARoughRealityGameModeBase_AdvanceToNextLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARoughRealityGameModeBase_AdvanceToNextLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARoughRealityGameModeBase::execAdvanceToNextLevel)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AdvanceToNextLevel();
	P_NATIVE_END;
}
// End Class ARoughRealityGameModeBase Function AdvanceToNextLevel

// Begin Class ARoughRealityGameModeBase Function AdvanceToNextSector
struct Z_Construct_UFunction_ARoughRealityGameModeBase_AdvanceToNextSector_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Roguelike" },
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARoughRealityGameModeBase_AdvanceToNextSector_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARoughRealityGameModeBase, nullptr, "AdvanceToNextSector", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_AdvanceToNextSector_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARoughRealityGameModeBase_AdvanceToNextSector_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ARoughRealityGameModeBase_AdvanceToNextSector()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARoughRealityGameModeBase_AdvanceToNextSector_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARoughRealityGameModeBase::execAdvanceToNextSector)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AdvanceToNextSector();
	P_NATIVE_END;
}
// End Class ARoughRealityGameModeBase Function AdvanceToNextSector

// Begin Class ARoughRealityGameModeBase Function EndCurrentRun
struct Z_Construct_UFunction_ARoughRealityGameModeBase_EndCurrentRun_Statics
{
	struct RoughRealityGameModeBase_eventEndCurrentRun_Parms
	{
		bool bSuccess;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Roguelike" },
		{ "CPP_Default_bSuccess", "false" },
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARoughRealityGameModeBase_EndCurrentRun_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((RoughRealityGameModeBase_eventEndCurrentRun_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARoughRealityGameModeBase_EndCurrentRun_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RoughRealityGameModeBase_eventEndCurrentRun_Parms), &Z_Construct_UFunction_ARoughRealityGameModeBase_EndCurrentRun_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARoughRealityGameModeBase_EndCurrentRun_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARoughRealityGameModeBase_EndCurrentRun_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_EndCurrentRun_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARoughRealityGameModeBase_EndCurrentRun_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARoughRealityGameModeBase, nullptr, "EndCurrentRun", nullptr, nullptr, Z_Construct_UFunction_ARoughRealityGameModeBase_EndCurrentRun_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_EndCurrentRun_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARoughRealityGameModeBase_EndCurrentRun_Statics::RoughRealityGameModeBase_eventEndCurrentRun_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_EndCurrentRun_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARoughRealityGameModeBase_EndCurrentRun_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ARoughRealityGameModeBase_EndCurrentRun_Statics::RoughRealityGameModeBase_eventEndCurrentRun_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARoughRealityGameModeBase_EndCurrentRun()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARoughRealityGameModeBase_EndCurrentRun_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARoughRealityGameModeBase::execEndCurrentRun)
{
	P_GET_UBOOL(Z_Param_bSuccess);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EndCurrentRun(Z_Param_bSuccess);
	P_NATIVE_END;
}
// End Class ARoughRealityGameModeBase Function EndCurrentRun

// Begin Class ARoughRealityGameModeBase Function GetTotalTeeth
struct Z_Construct_UFunction_ARoughRealityGameModeBase_GetTotalTeeth_Statics
{
	struct RoughRealityGameModeBase_eventGetTotalTeeth_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Currency" },
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ARoughRealityGameModeBase_GetTotalTeeth_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughRealityGameModeBase_eventGetTotalTeeth_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARoughRealityGameModeBase_GetTotalTeeth_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARoughRealityGameModeBase_GetTotalTeeth_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_GetTotalTeeth_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARoughRealityGameModeBase_GetTotalTeeth_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARoughRealityGameModeBase, nullptr, "GetTotalTeeth", nullptr, nullptr, Z_Construct_UFunction_ARoughRealityGameModeBase_GetTotalTeeth_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_GetTotalTeeth_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARoughRealityGameModeBase_GetTotalTeeth_Statics::RoughRealityGameModeBase_eventGetTotalTeeth_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_GetTotalTeeth_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARoughRealityGameModeBase_GetTotalTeeth_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ARoughRealityGameModeBase_GetTotalTeeth_Statics::RoughRealityGameModeBase_eventGetTotalTeeth_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARoughRealityGameModeBase_GetTotalTeeth()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARoughRealityGameModeBase_GetTotalTeeth_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARoughRealityGameModeBase::execGetTotalTeeth)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetTotalTeeth();
	P_NATIVE_END;
}
// End Class ARoughRealityGameModeBase Function GetTotalTeeth

// Begin Class ARoughRealityGameModeBase Function HandlePlayerDeath
struct Z_Construct_UFunction_ARoughRealityGameModeBase_HandlePlayerDeath_Statics
{
	struct RoughRealityGameModeBase_eventHandlePlayerDeath_Parms
	{
		ARookieCharacter* DeadPlayer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Player" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Player management */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DeadPlayer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARoughRealityGameModeBase_HandlePlayerDeath_Statics::NewProp_DeadPlayer = { "DeadPlayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughRealityGameModeBase_eventHandlePlayerDeath_Parms, DeadPlayer), Z_Construct_UClass_ARookieCharacter_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARoughRealityGameModeBase_HandlePlayerDeath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARoughRealityGameModeBase_HandlePlayerDeath_Statics::NewProp_DeadPlayer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_HandlePlayerDeath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARoughRealityGameModeBase_HandlePlayerDeath_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARoughRealityGameModeBase, nullptr, "HandlePlayerDeath", nullptr, nullptr, Z_Construct_UFunction_ARoughRealityGameModeBase_HandlePlayerDeath_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_HandlePlayerDeath_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARoughRealityGameModeBase_HandlePlayerDeath_Statics::RoughRealityGameModeBase_eventHandlePlayerDeath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_HandlePlayerDeath_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARoughRealityGameModeBase_HandlePlayerDeath_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ARoughRealityGameModeBase_HandlePlayerDeath_Statics::RoughRealityGameModeBase_eventHandlePlayerDeath_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARoughRealityGameModeBase_HandlePlayerDeath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARoughRealityGameModeBase_HandlePlayerDeath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARoughRealityGameModeBase::execHandlePlayerDeath)
{
	P_GET_OBJECT(ARookieCharacter,Z_Param_DeadPlayer);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->HandlePlayerDeath(Z_Param_DeadPlayer);
	P_NATIVE_END;
}
// End Class ARoughRealityGameModeBase Function HandlePlayerDeath

// Begin Class ARoughRealityGameModeBase Function IsInGameState
struct Z_Construct_UFunction_ARoughRealityGameModeBase_IsInGameState_Statics
{
	struct RoughRealityGameModeBase_eventIsInGameState_Parms
	{
		FGameplayTag State;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Game State" },
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_State_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_State;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARoughRealityGameModeBase_IsInGameState_Statics::NewProp_State = { "State", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughRealityGameModeBase_eventIsInGameState_Parms, State), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_State_MetaData), NewProp_State_MetaData) }; // 1298103297
void Z_Construct_UFunction_ARoughRealityGameModeBase_IsInGameState_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RoughRealityGameModeBase_eventIsInGameState_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARoughRealityGameModeBase_IsInGameState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RoughRealityGameModeBase_eventIsInGameState_Parms), &Z_Construct_UFunction_ARoughRealityGameModeBase_IsInGameState_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARoughRealityGameModeBase_IsInGameState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARoughRealityGameModeBase_IsInGameState_Statics::NewProp_State,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARoughRealityGameModeBase_IsInGameState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_IsInGameState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARoughRealityGameModeBase_IsInGameState_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARoughRealityGameModeBase, nullptr, "IsInGameState", nullptr, nullptr, Z_Construct_UFunction_ARoughRealityGameModeBase_IsInGameState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_IsInGameState_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARoughRealityGameModeBase_IsInGameState_Statics::RoughRealityGameModeBase_eventIsInGameState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_IsInGameState_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARoughRealityGameModeBase_IsInGameState_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ARoughRealityGameModeBase_IsInGameState_Statics::RoughRealityGameModeBase_eventIsInGameState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARoughRealityGameModeBase_IsInGameState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARoughRealityGameModeBase_IsInGameState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARoughRealityGameModeBase::execIsInGameState)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_State);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInGameState(Z_Param_Out_State);
	P_NATIVE_END;
}
// End Class ARoughRealityGameModeBase Function IsInGameState

// Begin Class ARoughRealityGameModeBase Function LoadGameData
struct Z_Construct_UFunction_ARoughRealityGameModeBase_LoadGameData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARoughRealityGameModeBase_LoadGameData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARoughRealityGameModeBase, nullptr, "LoadGameData", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_LoadGameData_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARoughRealityGameModeBase_LoadGameData_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ARoughRealityGameModeBase_LoadGameData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARoughRealityGameModeBase_LoadGameData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARoughRealityGameModeBase::execLoadGameData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LoadGameData();
	P_NATIVE_END;
}
// End Class ARoughRealityGameModeBase Function LoadGameData

// Begin Class ARoughRealityGameModeBase Function ResetSaveData
struct Z_Construct_UFunction_ARoughRealityGameModeBase_ResetSaveData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARoughRealityGameModeBase_ResetSaveData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARoughRealityGameModeBase, nullptr, "ResetSaveData", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_ResetSaveData_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARoughRealityGameModeBase_ResetSaveData_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ARoughRealityGameModeBase_ResetSaveData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARoughRealityGameModeBase_ResetSaveData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARoughRealityGameModeBase::execResetSaveData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetSaveData();
	P_NATIVE_END;
}
// End Class ARoughRealityGameModeBase Function ResetSaveData

// Begin Class ARoughRealityGameModeBase Function RespawnPlayer
struct Z_Construct_UFunction_ARoughRealityGameModeBase_RespawnPlayer_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Player" },
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARoughRealityGameModeBase_RespawnPlayer_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARoughRealityGameModeBase, nullptr, "RespawnPlayer", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_RespawnPlayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARoughRealityGameModeBase_RespawnPlayer_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ARoughRealityGameModeBase_RespawnPlayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARoughRealityGameModeBase_RespawnPlayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARoughRealityGameModeBase::execRespawnPlayer)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RespawnPlayer();
	P_NATIVE_END;
}
// End Class ARoughRealityGameModeBase Function RespawnPlayer

// Begin Class ARoughRealityGameModeBase Function SaveGameData
struct Z_Construct_UFunction_ARoughRealityGameModeBase_SaveGameData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Save/Load system */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Save/Load system" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARoughRealityGameModeBase_SaveGameData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARoughRealityGameModeBase, nullptr, "SaveGameData", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_SaveGameData_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARoughRealityGameModeBase_SaveGameData_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ARoughRealityGameModeBase_SaveGameData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARoughRealityGameModeBase_SaveGameData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARoughRealityGameModeBase::execSaveGameData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SaveGameData();
	P_NATIVE_END;
}
// End Class ARoughRealityGameModeBase Function SaveGameData

// Begin Class ARoughRealityGameModeBase Function SetGameState
struct Z_Construct_UFunction_ARoughRealityGameModeBase_SetGameState_Statics
{
	struct RoughRealityGameModeBase_eventSetGameState_Parms
	{
		FGameplayTag NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Game State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Game state management */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Game state management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewState_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ARoughRealityGameModeBase_SetGameState_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughRealityGameModeBase_eventSetGameState_Parms, NewState), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewState_MetaData), NewProp_NewState_MetaData) }; // 1298103297
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARoughRealityGameModeBase_SetGameState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARoughRealityGameModeBase_SetGameState_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_SetGameState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARoughRealityGameModeBase_SetGameState_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARoughRealityGameModeBase, nullptr, "SetGameState", nullptr, nullptr, Z_Construct_UFunction_ARoughRealityGameModeBase_SetGameState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_SetGameState_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARoughRealityGameModeBase_SetGameState_Statics::RoughRealityGameModeBase_eventSetGameState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_SetGameState_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARoughRealityGameModeBase_SetGameState_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ARoughRealityGameModeBase_SetGameState_Statics::RoughRealityGameModeBase_eventSetGameState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARoughRealityGameModeBase_SetGameState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARoughRealityGameModeBase_SetGameState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARoughRealityGameModeBase::execSetGameState)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_NewState);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetGameState(Z_Param_Out_NewState);
	P_NATIVE_END;
}
// End Class ARoughRealityGameModeBase Function SetGameState

// Begin Class ARoughRealityGameModeBase Function SpendTeeth
struct Z_Construct_UFunction_ARoughRealityGameModeBase_SpendTeeth_Statics
{
	struct RoughRealityGameModeBase_eventSpendTeeth_Parms
	{
		int32 Amount;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Currency" },
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Amount;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ARoughRealityGameModeBase_SpendTeeth_Statics::NewProp_Amount = { "Amount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughRealityGameModeBase_eventSpendTeeth_Parms, Amount), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ARoughRealityGameModeBase_SpendTeeth_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RoughRealityGameModeBase_eventSpendTeeth_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARoughRealityGameModeBase_SpendTeeth_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RoughRealityGameModeBase_eventSpendTeeth_Parms), &Z_Construct_UFunction_ARoughRealityGameModeBase_SpendTeeth_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARoughRealityGameModeBase_SpendTeeth_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARoughRealityGameModeBase_SpendTeeth_Statics::NewProp_Amount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARoughRealityGameModeBase_SpendTeeth_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_SpendTeeth_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARoughRealityGameModeBase_SpendTeeth_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARoughRealityGameModeBase, nullptr, "SpendTeeth", nullptr, nullptr, Z_Construct_UFunction_ARoughRealityGameModeBase_SpendTeeth_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_SpendTeeth_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARoughRealityGameModeBase_SpendTeeth_Statics::RoughRealityGameModeBase_eventSpendTeeth_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_SpendTeeth_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARoughRealityGameModeBase_SpendTeeth_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ARoughRealityGameModeBase_SpendTeeth_Statics::RoughRealityGameModeBase_eventSpendTeeth_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARoughRealityGameModeBase_SpendTeeth()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARoughRealityGameModeBase_SpendTeeth_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARoughRealityGameModeBase::execSpendTeeth)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Amount);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SpendTeeth(Z_Param_Amount);
	P_NATIVE_END;
}
// End Class ARoughRealityGameModeBase Function SpendTeeth

// Begin Class ARoughRealityGameModeBase Function StartNewRun
struct Z_Construct_UFunction_ARoughRealityGameModeBase_StartNewRun_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Roguelike" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Roguelike progression */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Roguelike progression" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARoughRealityGameModeBase_StartNewRun_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARoughRealityGameModeBase, nullptr, "StartNewRun", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARoughRealityGameModeBase_StartNewRun_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARoughRealityGameModeBase_StartNewRun_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ARoughRealityGameModeBase_StartNewRun()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARoughRealityGameModeBase_StartNewRun_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARoughRealityGameModeBase::execStartNewRun)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartNewRun();
	P_NATIVE_END;
}
// End Class ARoughRealityGameModeBase Function StartNewRun

// Begin Class ARoughRealityGameModeBase
void ARoughRealityGameModeBase::StaticRegisterNativesARoughRealityGameModeBase()
{
	UClass* Class = ARoughRealityGameModeBase::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddTeeth", &ARoughRealityGameModeBase::execAddTeeth },
		{ "AdvanceToNextLevel", &ARoughRealityGameModeBase::execAdvanceToNextLevel },
		{ "AdvanceToNextSector", &ARoughRealityGameModeBase::execAdvanceToNextSector },
		{ "EndCurrentRun", &ARoughRealityGameModeBase::execEndCurrentRun },
		{ "GetTotalTeeth", &ARoughRealityGameModeBase::execGetTotalTeeth },
		{ "HandlePlayerDeath", &ARoughRealityGameModeBase::execHandlePlayerDeath },
		{ "IsInGameState", &ARoughRealityGameModeBase::execIsInGameState },
		{ "LoadGameData", &ARoughRealityGameModeBase::execLoadGameData },
		{ "ResetSaveData", &ARoughRealityGameModeBase::execResetSaveData },
		{ "RespawnPlayer", &ARoughRealityGameModeBase::execRespawnPlayer },
		{ "SaveGameData", &ARoughRealityGameModeBase::execSaveGameData },
		{ "SetGameState", &ARoughRealityGameModeBase::execSetGameState },
		{ "SpendTeeth", &ARoughRealityGameModeBase::execSpendTeeth },
		{ "StartNewRun", &ARoughRealityGameModeBase::execStartNewRun },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(ARoughRealityGameModeBase);
UClass* Z_Construct_UClass_ARoughRealityGameModeBase_NoRegister()
{
	return ARoughRealityGameModeBase::StaticClass();
}
struct Z_Construct_UClass_ARoughRealityGameModeBase_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Core game mode for Rough Reality\n * Manages game flow, roguelike mechanics, and level progression\n */" },
#endif
		{ "HideCategories", "Info Rendering MovementReplication Replication Actor Input Movement Collision Rendering HLOD WorldPartition DataLayers Transformation" },
		{ "IncludePath", "Core/RoughRealityGameModeBase.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
		{ "ShowCategories", "Input|MouseInput Input|TouchInput" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Core game mode for Rough Reality\nManages game flow, roguelike mechanics, and level progression" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentGameState_MetaData[] = {
		{ "Category", "Game State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current game state tag */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current game state tag" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentRun_MetaData[] = {
		{ "Category", "Roguelike" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current run number */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current run number" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentSector_MetaData[] = {
		{ "Category", "Roguelike" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current sector index */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current sector index" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentLevel_MetaData[] = {
		{ "Category", "Roguelike" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current level within sector */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current level within sector" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeethThisRun_MetaData[] = {
		{ "Category", "Roguelike" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Total teeth (currency) collected this run */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Total teeth (currency) collected this run" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LevelBuilder_MetaData[] = {
		{ "Category", "Level Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Reference to the procedural level builder */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reference to the procedural level builder" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SaveGame_MetaData[] = {
		{ "Category", "Save System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Reference to the save game */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Reference to the save game" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnGameStateChanged_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Events */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPlayerDied_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnRunCompleted_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MenuState_MetaData[] = {
		{ "Category", "Game State Tags" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Game state tags */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Game state tags" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayingState_MetaData[] = {
		{ "Category", "Game State Tags" },
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PausedState_MetaData[] = {
		{ "Category", "Game State Tags" },
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GameOverState_MetaData[] = {
		{ "Category", "Game State Tags" },
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VictoryState_MetaData[] = {
		{ "Category", "Game State Tags" },
		{ "ModuleRelativePath", "Core/RoughRealityGameModeBase.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentGameState;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentRun;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentSector;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeethThisRun;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LevelBuilder;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SaveGame;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnGameStateChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPlayerDied;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnRunCompleted;
	static const UECodeGen_Private::FStructPropertyParams NewProp_MenuState;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayingState;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PausedState;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GameOverState;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VictoryState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ARoughRealityGameModeBase_AddTeeth, "AddTeeth" }, // 3748168444
		{ &Z_Construct_UFunction_ARoughRealityGameModeBase_AdvanceToNextLevel, "AdvanceToNextLevel" }, // 2704866720
		{ &Z_Construct_UFunction_ARoughRealityGameModeBase_AdvanceToNextSector, "AdvanceToNextSector" }, // 2633756085
		{ &Z_Construct_UFunction_ARoughRealityGameModeBase_EndCurrentRun, "EndCurrentRun" }, // 4169068043
		{ &Z_Construct_UFunction_ARoughRealityGameModeBase_GetTotalTeeth, "GetTotalTeeth" }, // 4247497421
		{ &Z_Construct_UFunction_ARoughRealityGameModeBase_HandlePlayerDeath, "HandlePlayerDeath" }, // 991415434
		{ &Z_Construct_UFunction_ARoughRealityGameModeBase_IsInGameState, "IsInGameState" }, // 3919988796
		{ &Z_Construct_UFunction_ARoughRealityGameModeBase_LoadGameData, "LoadGameData" }, // 1762371374
		{ &Z_Construct_UFunction_ARoughRealityGameModeBase_ResetSaveData, "ResetSaveData" }, // 2010628579
		{ &Z_Construct_UFunction_ARoughRealityGameModeBase_RespawnPlayer, "RespawnPlayer" }, // 835722198
		{ &Z_Construct_UFunction_ARoughRealityGameModeBase_SaveGameData, "SaveGameData" }, // 32508958
		{ &Z_Construct_UFunction_ARoughRealityGameModeBase_SetGameState, "SetGameState" }, // 4247721477
		{ &Z_Construct_UFunction_ARoughRealityGameModeBase_SpendTeeth, "SpendTeeth" }, // 2218804730
		{ &Z_Construct_UFunction_ARoughRealityGameModeBase_StartNewRun, "StartNewRun" }, // 2441981235
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ARoughRealityGameModeBase>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_CurrentGameState = { "CurrentGameState", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARoughRealityGameModeBase, CurrentGameState), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentGameState_MetaData), NewProp_CurrentGameState_MetaData) }; // 1298103297
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_CurrentRun = { "CurrentRun", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARoughRealityGameModeBase, CurrentRun), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentRun_MetaData), NewProp_CurrentRun_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_CurrentSector = { "CurrentSector", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARoughRealityGameModeBase, CurrentSector), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentSector_MetaData), NewProp_CurrentSector_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_CurrentLevel = { "CurrentLevel", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARoughRealityGameModeBase, CurrentLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentLevel_MetaData), NewProp_CurrentLevel_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_TeethThisRun = { "TeethThisRun", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARoughRealityGameModeBase, TeethThisRun), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeethThisRun_MetaData), NewProp_TeethThisRun_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_LevelBuilder = { "LevelBuilder", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARoughRealityGameModeBase, LevelBuilder), Z_Construct_UClass_AProceduralLevelBuilder_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LevelBuilder_MetaData), NewProp_LevelBuilder_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_SaveGame = { "SaveGame", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARoughRealityGameModeBase, SaveGame), Z_Construct_UClass_URoughSaveGame_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SaveGame_MetaData), NewProp_SaveGame_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_OnGameStateChanged = { "OnGameStateChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARoughRealityGameModeBase, OnGameStateChanged), Z_Construct_UDelegateFunction_RoughReality_OnGameStateChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnGameStateChanged_MetaData), NewProp_OnGameStateChanged_MetaData) }; // 2094308501
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_OnPlayerDied = { "OnPlayerDied", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARoughRealityGameModeBase, OnPlayerDied), Z_Construct_UDelegateFunction_RoughReality_OnPlayerDied__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPlayerDied_MetaData), NewProp_OnPlayerDied_MetaData) }; // 3139496749
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_OnRunCompleted = { "OnRunCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARoughRealityGameModeBase, OnRunCompleted), Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnRunCompleted_MetaData), NewProp_OnRunCompleted_MetaData) }; // 3545251381
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_MenuState = { "MenuState", nullptr, (EPropertyFlags)0x0020080000010001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARoughRealityGameModeBase, MenuState), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MenuState_MetaData), NewProp_MenuState_MetaData) }; // 1298103297
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_PlayingState = { "PlayingState", nullptr, (EPropertyFlags)0x0020080000010001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARoughRealityGameModeBase, PlayingState), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayingState_MetaData), NewProp_PlayingState_MetaData) }; // 1298103297
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_PausedState = { "PausedState", nullptr, (EPropertyFlags)0x0020080000010001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARoughRealityGameModeBase, PausedState), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PausedState_MetaData), NewProp_PausedState_MetaData) }; // 1298103297
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_GameOverState = { "GameOverState", nullptr, (EPropertyFlags)0x0020080000010001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARoughRealityGameModeBase, GameOverState), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GameOverState_MetaData), NewProp_GameOverState_MetaData) }; // 1298103297
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_VictoryState = { "VictoryState", nullptr, (EPropertyFlags)0x0020080000010001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARoughRealityGameModeBase, VictoryState), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VictoryState_MetaData), NewProp_VictoryState_MetaData) }; // 1298103297
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ARoughRealityGameModeBase_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_CurrentGameState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_CurrentRun,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_CurrentSector,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_CurrentLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_TeethThisRun,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_LevelBuilder,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_SaveGame,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_OnGameStateChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_OnPlayerDied,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_OnRunCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_MenuState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_PlayingState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_PausedState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_GameOverState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARoughRealityGameModeBase_Statics::NewProp_VictoryState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ARoughRealityGameModeBase_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ARoughRealityGameModeBase_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AGameModeBase,
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ARoughRealityGameModeBase_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ARoughRealityGameModeBase_Statics::ClassParams = {
	&ARoughRealityGameModeBase::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ARoughRealityGameModeBase_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ARoughRealityGameModeBase_Statics::PropPointers),
	0,
	0x009002ACu,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ARoughRealityGameModeBase_Statics::Class_MetaDataParams), Z_Construct_UClass_ARoughRealityGameModeBase_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ARoughRealityGameModeBase()
{
	if (!Z_Registration_Info_UClass_ARoughRealityGameModeBase.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ARoughRealityGameModeBase.OuterSingleton, Z_Construct_UClass_ARoughRealityGameModeBase_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ARoughRealityGameModeBase.OuterSingleton;
}
template<> ROUGHREALITY_API UClass* StaticClass<ARoughRealityGameModeBase>()
{
	return ARoughRealityGameModeBase::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ARoughRealityGameModeBase);
ARoughRealityGameModeBase::~ARoughRealityGameModeBase() {}
// End Class ARoughRealityGameModeBase

// Begin Registration
struct Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_RoughRealityGameModeBase_h_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ARoughRealityGameModeBase, ARoughRealityGameModeBase::StaticClass, TEXT("ARoughRealityGameModeBase"), &Z_Registration_Info_UClass_ARoughRealityGameModeBase, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ARoughRealityGameModeBase), 1849432922U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_RoughRealityGameModeBase_h_2990398231(TEXT("/Script/RoughReality"),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_RoughRealityGameModeBase_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_RoughRealityGameModeBase_h_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
