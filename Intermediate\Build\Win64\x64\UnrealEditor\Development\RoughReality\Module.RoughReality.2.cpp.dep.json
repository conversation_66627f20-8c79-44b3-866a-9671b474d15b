{"Version": "1.2", "Data": {"Source": "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\roughreality\\module.roughreality.2.cpp", "ProvidedModule": "", "PCH": "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\x64\\roughrealityeditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.cpp20.h.pch", "Includes": ["g:\\gamedev\\roughreality\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\roughreality\\definitions.roughreality.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\gameplayloopmanager.gen.cpp", "g:\\gamedev\\roughreality\\source\\roughreality\\core\\gameplayloopmanager.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\gamestatebase.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\gamemodebase.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\engine\\serverstatreplicator.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\serverstatreplicator.generated.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gamemodebase.generated.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gamestatebase.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\engine\\streamablemanager.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\coreuobject\\public\\misc\\packageaccesstracking.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\sourcelocation.h", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.38.33130\\include\\source_location", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\sourcelocationutils.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\gameplayloopmanager.generated.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\leveltile.gen.cpp", "g:\\gamedev\\roughreality\\source\\roughreality\\levelgeneration\\leveltile.h", "g:\\gamedev\\roughreality\\source\\roughreality\\levelgeneration\\procedurallevelbuilder.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\procedurallevelbuilder.generated.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\leveltile.generated.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\objectpoolmanager.gen.cpp", "g:\\gamedev\\roughreality\\source\\roughreality\\core\\objectpoolmanager.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\objectpoolmanager.generated.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\procedurallevelbuilder.gen.cpp"], "ImportedModules": [], "ImportedHeaderUnits": []}}