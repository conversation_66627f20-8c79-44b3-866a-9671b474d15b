// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "DataAssets/TileDefinitionDataAsset.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
struct FGameplayTag;
struct FGameplayTagContainer;
struct FTileConnectionPoint;
struct FTileSpawnPoint;
#ifdef ROUGHREALITY_TileDefinitionDataAsset_generated_h
#error "TileDefinitionDataAsset.generated.h already included, missing '#pragma once' in TileDefinitionDataAsset.h"
#endif
#define ROUGHREALITY_TileDefinitionDataAsset_generated_h

#define FID_RoughReality_Source_RoughReality_DataAssets_TileDefinitionDataAsset_h_17_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTileConnectionPoint_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FTileConnectionPoint>();

#define FID_RoughReality_Source_RoughReality_DataAssets_TileDefinitionDataAsset_h_46_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTileSpawnPoint_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FTileSpawnPoint>();

#define FID_RoughReality_Source_RoughReality_DataAssets_TileDefinitionDataAsset_h_78_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetRandomSpawnLocation); \
	DECLARE_FUNCTION(execGetConnectionPointCount); \
	DECLARE_FUNCTION(execHasExitPoint); \
	DECLARE_FUNCTION(execHasEntrancePoint); \
	DECLARE_FUNCTION(execGetTileTypeString); \
	DECLARE_FUNCTION(execGetSpawnPointsByTag); \
	DECLARE_FUNCTION(execGetConnectionPointsByTag); \
	DECLARE_FUNCTION(execIsCompatibleWithAdjacent); \
	DECLARE_FUNCTION(execCanSpawnAtLevel); \
	DECLARE_FUNCTION(execCanSpawnInSector);


#define FID_RoughReality_Source_RoughReality_DataAssets_TileDefinitionDataAsset_h_78_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUTileDefinitionDataAsset(); \
	friend struct Z_Construct_UClass_UTileDefinitionDataAsset_Statics; \
public: \
	DECLARE_CLASS(UTileDefinitionDataAsset, URoughRealityDataAsset, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/RoughReality"), NO_API) \
	DECLARE_SERIALIZER(UTileDefinitionDataAsset)


#define FID_RoughReality_Source_RoughReality_DataAssets_TileDefinitionDataAsset_h_78_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UTileDefinitionDataAsset(UTileDefinitionDataAsset&&); \
	UTileDefinitionDataAsset(const UTileDefinitionDataAsset&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UTileDefinitionDataAsset); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UTileDefinitionDataAsset); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UTileDefinitionDataAsset) \
	NO_API virtual ~UTileDefinitionDataAsset();


#define FID_RoughReality_Source_RoughReality_DataAssets_TileDefinitionDataAsset_h_75_PROLOG
#define FID_RoughReality_Source_RoughReality_DataAssets_TileDefinitionDataAsset_h_78_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_RoughReality_Source_RoughReality_DataAssets_TileDefinitionDataAsset_h_78_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_DataAssets_TileDefinitionDataAsset_h_78_INCLASS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_DataAssets_TileDefinitionDataAsset_h_78_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ROUGHREALITY_API UClass* StaticClass<class UTileDefinitionDataAsset>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_RoughReality_Source_RoughReality_DataAssets_TileDefinitionDataAsset_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
