/NOLOGO
/errorReport:prompt
/MACHINE:x64
/SUBSYSTEM:WINDOWS
/DEF
/NAME:"UnrealEditor-RoughReality.dll"
/IGNORE:4221
/NODEFAULTLIB
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\RoughRealityEditor\Development\UnrealEd\SharedPCH.UnrealEd.Project.ValApi.Cpp20.h.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\RoughReality.init.gen.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\RoughRealityCharacter.gen.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\RoughRealityGameMode.gen.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\RoughReality.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\RoughRealityCharacter.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\RoughRealityGameMode.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\PerModuleInline.gen.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\Default.rc2.res"
/OUT:"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\UnrealEditor-RoughReality.lib"