/NOLOGO
/errorReport:prompt
/MACHINE:x64
/SUBSYSTEM:WINDOWS
/DEF
/NAME:"UnrealEditor-RoughReality.dll"
/IGNORE:4221
/NODEFAULTLIB
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\RoughRealityEditor\Development\UnrealEd\SharedPCH.UnrealEd.Project.ValApi.Cpp20.h.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\Module.RoughReality.1.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\Module.RoughReality.2.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\Module.RoughReality.3.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\Module.RoughReality.4.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\Module.RoughReality.5.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\RoughReality.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\RoughRealityCharacter.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\RoughRealityGameMode.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\GameAnalyticsManager.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\RookieCharacter.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\AsyncLoadingManager.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\GameEventSystem.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\GameplayLoopManager.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\ObjectPoolManager.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\RoughRealityDataAsset.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\RoughRealityGameModeBase.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\CharacterStatsDataAsset.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\TileDefinitionDataAsset.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\WeaponDataAsset.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\LevelTile.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\ProceduralLevelBuilder.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\ProgressionManager.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\RoughSaveGame.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\VisualEffectsManager.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\WeaponBase.cpp.obj"
"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\Default.rc2.res"
/OUT:"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\UnrealEditor-RoughReality.lib"