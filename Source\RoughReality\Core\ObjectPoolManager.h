// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Engine/World.h"
#include "ObjectPoolManager.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnObjectSpawned, AActor*, SpawnedActor, FName, PoolName);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnObjectReturned, AActor*, ReturnedActor, FName, PoolName);

USTRUCT(BlueprintType)
struct FObjectPoolConfiguration
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pool Config")
	FName PoolName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pool Config")
	TSoftClassPtr<AActor> ActorClass;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pool Config")
	int32 InitialPoolSize = 10;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pool Config")
	int32 MaxPoolSize = 50;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pool Config")
	bool bCanGrowPool = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pool Config")
	bool bPrewarmPool = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pool Config")
	float AutoReturnTime = 0.0f; // 0 = manual return only

	FObjectPoolConfiguration()
	{
		PoolName = NAME_None;
		InitialPoolSize = 10;
		MaxPoolSize = 50;
		bCanGrowPool = true;
		bPrewarmPool = true;
		AutoReturnTime = 0.0f;
	}
};

USTRUCT(BlueprintType)
struct FPooledObjectInfo
{
	GENERATED_BODY()

	UPROPERTY()
	AActor* Actor;

	UPROPERTY()
	bool bInUse;

	UPROPERTY()
	float SpawnTime;

	UPROPERTY()
	FName PoolName;

	FPooledObjectInfo()
	{
		Actor = nullptr;
		bInUse = false;
		SpawnTime = 0.0f;
		PoolName = NAME_None;
	}

	FPooledObjectInfo(AActor* InActor, FName InPoolName)
	{
		Actor = InActor;
		bInUse = false;
		SpawnTime = 0.0f;
		PoolName = InPoolName;
	}
};

/**
 * Object Pool Manager for efficient actor spawning and management
 * Reduces garbage collection overhead and improves performance
 */
UCLASS(BlueprintType, Blueprintable)
class ROUGHREALITY_API AObjectPoolManager : public AActor
{
	GENERATED_BODY()

public:
	AObjectPoolManager();

protected:
	virtual void BeginPlay() override;
	virtual void Tick(float DeltaTime) override;
	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

public:
	/** Pool Configurations */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Pool Configuration")
	TArray<FObjectPoolConfiguration> PoolConfigurations;

	/** Events */
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnObjectSpawned OnObjectSpawned;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnObjectReturned OnObjectReturned;

	/** Statistics */
	UPROPERTY(BlueprintReadOnly, Category = "Statistics")
	int32 TotalObjectsSpawned = 0;

	UPROPERTY(BlueprintReadOnly, Category = "Statistics")
	int32 TotalObjectsReturned = 0;

	UPROPERTY(BlueprintReadOnly, Category = "Statistics")
	int32 TotalPoolHits = 0;

	UPROPERTY(BlueprintReadOnly, Category = "Statistics")
	int32 TotalPoolMisses = 0;

public:
	/** Pool Management */
	UFUNCTION(BlueprintCallable, Category = "Object Pool")
	void InitializePool(FName PoolName, const FObjectPoolConfiguration& Config);

	UFUNCTION(BlueprintCallable, Category = "Object Pool")
	void InitializeAllPools();

	UFUNCTION(BlueprintCallable, Category = "Object Pool")
	void ClearPool(FName PoolName);

	UFUNCTION(BlueprintCallable, Category = "Object Pool")
	void ClearAllPools();

	/** Object Spawning */
	UFUNCTION(BlueprintCallable, Category = "Object Pool")
	AActor* SpawnPooledObject(FName PoolName, const FVector& Location, const FRotator& Rotation, AActor* Owner = nullptr);

	UFUNCTION(BlueprintCallable, Category = "Object Pool")
	void ReturnPooledObject(AActor* Object);

	UFUNCTION(BlueprintCallable, Category = "Object Pool")
	void ReturnPooledObjectDelayed(AActor* Object, float Delay);

	/** Pool Queries */
	UFUNCTION(BlueprintCallable, Category = "Object Pool")
	int32 GetPoolSize(FName PoolName) const;

	UFUNCTION(BlueprintCallable, Category = "Object Pool")
	int32 GetAvailableObjects(FName PoolName) const;

	UFUNCTION(BlueprintCallable, Category = "Object Pool")
	int32 GetObjectsInUse(FName PoolName) const;

	UFUNCTION(BlueprintCallable, Category = "Object Pool")
	bool IsPoolInitialized(FName PoolName) const;

	UFUNCTION(BlueprintCallable, Category = "Object Pool")
	float GetPoolHitRate() const;

	/** Utility Functions */
	UFUNCTION(BlueprintCallable, Category = "Object Pool")
	void PrewarmPool(FName PoolName, int32 Count);

	UFUNCTION(BlueprintCallable, Category = "Object Pool")
	void TrimPool(FName PoolName, int32 TargetSize);

	UFUNCTION(BlueprintCallable, Category = "Object Pool")
	void OptimizeAllPools();

	/** Static Access */
	UFUNCTION(BlueprintCallable, Category = "Object Pool", CallInEditor = true)
	static AObjectPoolManager* GetObjectPoolManager(const UObject* WorldContext);

protected:
	/** Internal Pool Management */
	AActor* CreatePooledObject(FName PoolName, UClass* ActorClass);
	void ReturnObjectToPool(AActor* Object, FName PoolName);
	FPooledObjectInfo* FindPooledObjectInfo(AActor* Object);
	void ProcessAutoReturns(float DeltaTime);

	/** Pool Storage */
	UPROPERTY()
	TMap<FName, TArray<FPooledObjectInfo>> ObjectPools;

	UPROPERTY()
	TMap<AActor*, FName> ActiveObjects; // Maps active objects to their pool names

	/** Configuration Cache */
	UPROPERTY()
	TMap<FName, FObjectPoolConfiguration> PoolConfigCache;

	/** Timer Handles */
	TMap<AActor*, FTimerHandle> DelayedReturnTimers;

	/** Performance Tracking */
	float LastOptimizationTime = 0.0f;
	static constexpr float OptimizationInterval = 30.0f; // Optimize every 30 seconds

private:
	/** Singleton instance */
	static AObjectPoolManager* Instance;

	/** Delayed return callback */
	UFUNCTION()
	void OnDelayedReturn(AActor* Object);
};
