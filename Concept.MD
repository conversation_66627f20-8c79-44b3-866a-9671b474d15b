# Rough Reality – Modular Game System Design (Unreal Engine 5, C++)

## 1. Game Concept

**Title:** Rough Reality
**Genre:** Action/Survival, Roguelike Structure
**Perspective:** Third-Person Shooter
**Platform:** PC and Consoles
**Engine:** Unreal Engine 5.3+
**Team:** Small Indie Team
**Inspirations:** <PERSON>, The Running Man, Manhunt
**Setting:** Dystopian city of Havencross

*Summary:* A fast-paced, narrative-driven action-survival roguelike game featuring intense combat, procedural level generation with fixed landmarks, time manipulation, and a sadistic Narrator.

---

## 2. Core Systems Setup

### Required Plugins & Systems

* **Enhanced Input** for flexible designer-friendly controls
* **Common UI** for HUD and menus
* **Modular Gameplay Features** for clean architecture
* **Gameplay Tags** for dynamic game logic
* **DataAssets** to decouple design from code
* **Optional:** Gameplay Ability System for complex upgrades and skills

---

## 3. Input System (Enhanced Input)

### Input Mapping Context: `IMC_Rookie`

Actions:

* `IA_Move`: Vector2D
* `IA_Look`: Vector2D
* `IA_Jump`, `IA_Fire`, `IA_BulletTime`, `IA_Rewind`, `IA_Dash`: Trigger

Bound inside `SetupPlayerInputComponent` using `UEnhancedInputComponent`.

---

## 4. Procedural Level Design

### Class: `AProceduralLevelBuilder`

* Procedural map generator using modular tiles
* Supports both randomized tiles and fixed setpiece locations
* Controlled via `UDataAsset_TileDefinition` containing tags, rules, and type info
* Roguelike structure: maps reset on death, with procedural variation each run

### Sector Flow Example

```plaintext
Start Room (Fixed)
→ 2-4 Random Combat Tiles
→ Grinder Shop (Fixed)
→ More Random Tiles
→ Boss Arena (Fixed)
```

Each tile is modular and can be previewed and arranged by level designers using visual Blueprint tools. Predefined anchor points ensure fixed locations like shops, story scenes, and boss rooms always spawn in coherent progression.

---

## 5. Character: Rookie

### Class: `ARookieCharacter`

Core abilities:

* Movement, dash, jump
* Bullet Time (`SetGlobalTimeDilation`)
* Time Rewind with gameplay snapshots
* Fire weapons

Health and upgrades handled via `UCharacterStatsDataAsset`.

---

## 6. Time Mechanics

### Bullet Time

Slows game world to 0.3x for precision shooting.

### Time Rewind

Stores snapshots of position and state every 0.2s.
Restores state on rewind with retro visual effect.
Uses per-arena limited charges defined by difficulty.

---

## 7. Weapons

### Base Class: `AWeaponBase`

* Muzzle FX, fire rate, ammo logic
* Uses `UWeaponDataAsset` for stats

Weapons:

* Venom .50 (Pistol)
* Rapidfire Cannon (Assault Rifle)
* Shatterstorm (Shotgun)
* Twin-Tails Shotgun (Super Shotgun)
* Atlas RPG (Rocket Launcher)
* Chainblaze (Chaingun)

---

## 8. AI System

### Behavior Trees

* Patrol → Detect → Engage → Flank
* Data-driven via Blackboard

### AI Types

* Kingpin's Enforcers: aggressive mobs
* Nazi Rats: tactical, use explosives
* Crimson Cultists: stealth and ambush
* Hunter's Pack: heavily armed with dogs

### Boss AI

Each boss uses `ABossBase` and unique phase logic with `UBossPhaseDataAsset`.

---

## 9. UI System (Common UI)

### Root Widget: `WBP_RootHUD`

Screens:

* `WBP_HUDCombat`
* `WBP_UpgradeShop`
* `WBP_PauseMenu`

Elements:

* Health bar
* Weapon + ammo
* Bullet time meter
* Rewind counter

---

## 10. Upgrade Shop (Grinder)

### Class: `AGrinderShop`

* Interactive actor with upgrade menu
* Uses `UUpgradeDataAsset`
* UI: `WBP_GrinderMenu` with purchase confirmation

---

## 11. Save/Progression

### Save Game Class: `URoughSaveGame`

* Stores unlocked gear, total teeth (currency)
* Only persistent unlocks are saved; levels reset each run
* Procedural generation seeded per run; no mid-run saves

---

## 12. World Overview

### Setting: Havencross

* Dystopian city, divided into themed procedural sectors

### Sectors:

1. **Dilapidated City** – Ruled by Kingpin Richards
2. **Station Tunnels** – Nazi Rats’ domain
3. **Bela Vegas District** – Controlled by Crimson Order
4. **Abandoned Industrial District** – Hunter’s territory

Each sector combines procedural tiles with handcrafted setpieces to balance variety and narrative structure.

---

## 13. Narrative

### Characters:

* **Rookie:** Silent protagonist
* **Narrator:** Sadistic observer
* **Overlords:** Sector bosses with strong personalities

### Themes:

* Psychological manipulation, dystopian survival, bloodsport entertainment

---

## 14. Visuals

* High contrast, neon + decay
* Stylized enemy and character design
* Sector-based color palettes

---

## 15. Audio

* Dynamic combat music
* Narrator voiceovers
* Environmentally reactive SFX

---

## 16. Controls

* WASD: Move
* Mouse: Aim
* Space: Jump
* LMB: Fire
* RShift: Dash
* E: Interact
* 1-4: Weapon switch

---

## 17. Development Roadmap

### Milestone 1: Concept & Pre-Production

* Game design doc, concept art, prototype

### Milestone 2: Alpha (3–6 Months)

* Core gameplay systems, first sectors
* AI and narrative integration

### Milestone 3: Beta (7–10 Months)

* All sectors, full narrative, polish

### Milestone 4: Release

* Launch on PC/consoles, updates, DLC

---

## 18. Folder Structure

```plaintext
/Blueprints/Characters
/Blueprints/UI
/Blueprints/LevelGeneration
/C++/Weapons
/C++/Core
/C++/AI
/DataAssets/Enemies
/DataAssets/Weapons
/DataAssets/Tiles
```

---

Let me know if you want a matching GitHub structure, Blueprint class examples, or Behavior Tree setups.
