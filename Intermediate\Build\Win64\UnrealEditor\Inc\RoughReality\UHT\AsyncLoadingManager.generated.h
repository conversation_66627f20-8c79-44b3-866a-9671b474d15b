// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Core/AsyncLoadingManager.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class AAsyncLoadingManager;
class UObject;
struct FAssetLoadRequest;
struct FLoadingGroup;
struct FLoadingStatistics;
#ifdef ROUGHREALITY_AsyncLoadingManager_generated_h
#error "AsyncLoadingManager.generated.h already included, missing '#pragma once' in AsyncLoadingManager.h"
#endif
#define ROUGHREALITY_AsyncLoadingManager_generated_h

#define FID_RoughReality_Source_RoughReality_Core_AsyncLoadingManager_h_11_DELEGATE \
ROUGHREALITY_API void FOnAssetLoaded_DelegateWrapper(const FMulticastScriptDelegate& OnAssetLoaded, const TSoftObjectPtr<UObject>& LoadedAsset);


#define FID_RoughReality_Source_RoughReality_Core_AsyncLoadingManager_h_12_DELEGATE \
ROUGHREALITY_API void FOnAssetLoadFailed_DelegateWrapper(const FMulticastScriptDelegate& OnAssetLoadFailed, const TSoftObjectPtr<UObject>& FailedAsset);


#define FID_RoughReality_Source_RoughReality_Core_AsyncLoadingManager_h_13_DELEGATE \
ROUGHREALITY_API void FOnLoadingProgress_DelegateWrapper(const FMulticastScriptDelegate& OnLoadingProgress, float Progress, const FString& CurrentAsset);


#define FID_RoughReality_Source_RoughReality_Core_AsyncLoadingManager_h_14_DELEGATE \
ROUGHREALITY_API void FOnLoadingComplete_DelegateWrapper(const FMulticastScriptDelegate& OnLoadingComplete);


#define FID_RoughReality_Source_RoughReality_Core_AsyncLoadingManager_h_19_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAssetLoadRequest_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FAssetLoadRequest>();

#define FID_RoughReality_Source_RoughReality_Core_AsyncLoadingManager_h_52_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FLoadingGroup_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FLoadingGroup>();

#define FID_RoughReality_Source_RoughReality_Core_AsyncLoadingManager_h_81_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FLoadingStatistics_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FLoadingStatistics>();

#define FID_RoughReality_Source_RoughReality_Core_AsyncLoadingManager_h_123_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnAssetLoadComplete); \
	DECLARE_FUNCTION(execGetAsyncLoadingManager); \
	DECLARE_FUNCTION(execLogLoadingStatistics); \
	DECLARE_FUNCTION(execResetStatistics); \
	DECLARE_FUNCTION(execGetLoadingStatistics); \
	DECLARE_FUNCTION(execOptimizeMemoryUsage); \
	DECLARE_FUNCTION(execGetMemoryUsageMB); \
	DECLARE_FUNCTION(execForceGarbageCollection); \
	DECLARE_FUNCTION(execGetActiveLoadGroups); \
	DECLARE_FUNCTION(execIsLoadingInProgress); \
	DECLARE_FUNCTION(execGetLoadingProgress); \
	DECLARE_FUNCTION(execGetCacheHitRate); \
	DECLARE_FUNCTION(execGetCacheSize); \
	DECLARE_FUNCTION(execTrimAssetCache); \
	DECLARE_FUNCTION(execClearAssetCache); \
	DECLARE_FUNCTION(execPreloadCommonAssets); \
	DECLARE_FUNCTION(execPreloadWeaponAssets); \
	DECLARE_FUNCTION(execPreloadSectorAssets); \
	DECLARE_FUNCTION(execCancelLoadGroup); \
	DECLARE_FUNCTION(execLoadAssetGroup); \
	DECLARE_FUNCTION(execLoadAssetsAsync); \
	DECLARE_FUNCTION(execUnloadAsset); \
	DECLARE_FUNCTION(execIsAssetLoaded); \
	DECLARE_FUNCTION(execLoadAssetSync); \
	DECLARE_FUNCTION(execLoadAssetAsync);


#define FID_RoughReality_Source_RoughReality_Core_AsyncLoadingManager_h_123_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAAsyncLoadingManager(); \
	friend struct Z_Construct_UClass_AAsyncLoadingManager_Statics; \
public: \
	DECLARE_CLASS(AAsyncLoadingManager, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/RoughReality"), NO_API) \
	DECLARE_SERIALIZER(AAsyncLoadingManager)


#define FID_RoughReality_Source_RoughReality_Core_AsyncLoadingManager_h_123_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	AAsyncLoadingManager(AAsyncLoadingManager&&); \
	AAsyncLoadingManager(const AAsyncLoadingManager&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AAsyncLoadingManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AAsyncLoadingManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AAsyncLoadingManager) \
	NO_API virtual ~AAsyncLoadingManager();


#define FID_RoughReality_Source_RoughReality_Core_AsyncLoadingManager_h_120_PROLOG
#define FID_RoughReality_Source_RoughReality_Core_AsyncLoadingManager_h_123_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_RoughReality_Source_RoughReality_Core_AsyncLoadingManager_h_123_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_Core_AsyncLoadingManager_h_123_INCLASS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_Core_AsyncLoadingManager_h_123_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ROUGHREALITY_API UClass* StaticClass<class AAsyncLoadingManager>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_RoughReality_Source_RoughReality_Core_AsyncLoadingManager_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
