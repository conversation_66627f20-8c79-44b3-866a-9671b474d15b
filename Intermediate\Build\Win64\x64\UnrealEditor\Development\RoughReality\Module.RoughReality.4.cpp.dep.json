{"Version": "1.2", "Data": {"Source": "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\roughreality\\module.roughreality.4.cpp", "ProvidedModule": "", "PCH": "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\x64\\roughrealityeditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.cpp20.h.pch", "Includes": ["g:\\gamedev\\roughreality\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\roughreality\\definitions.roughreality.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\roughsavegame.gen.cpp", "g:\\gamedev\\roughreality\\source\\roughreality\\savesystem\\roughsavegame.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\savegame.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\savegame.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\roughsavegame.generated.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\tiledefinitiondataasset.gen.cpp", "g:\\gamedev\\roughreality\\source\\roughreality\\dataassets\\tiledefinitiondataasset.h", "g:\\gamedev\\roughreality\\source\\roughreality\\core\\roughrealitydataasset.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\roughrealitydataasset.generated.h", "g:\\gamedev\\roughreality\\source\\roughreality\\levelgeneration\\procedurallevelbuilder.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\procedurallevelbuilder.generated.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\tiledefinitiondataasset.generated.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\visualeffectsmanager.gen.cpp", "g:\\gamedev\\roughreality\\source\\roughreality\\vfx\\visualeffectsmanager.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\components\\postprocesscomponent.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\components\\shapecomponent.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\shapecomponent.generated.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\postprocesscomponent.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\public\\materials\\materialparametercollection.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\materialparametercollection.generated.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\visualeffectsmanager.generated.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\weaponbase.gen.cpp", "g:\\gamedev\\roughreality\\source\\roughreality\\weapons\\weaponbase.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\weaponbase.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}