// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/SaveGame.h"
#include "GameplayTagContainer.h"
#include "RoughSaveGame.generated.h"

USTRUCT(BlueprintType)
struct FUnlockedWeapon
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon")
	FName WeaponID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon")
	bool bIsUnlocked = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon")
	int32 UpgradeLevel = 0;

	FUnlockedWeapon()
	{
		WeaponID = NAME_None;
		bIsUnlocked = false;
		UpgradeLevel = 0;
	}

	FUnlockedWeapon(FName InWeaponID, bool bInIsUnlocked = false, int32 InUpgradeLevel = 0)
		: WeaponID(InWeaponID), bIsUnlocked(bInIsUnlocked), UpgradeLevel(InUpgradeLevel)
	{
	}
};

USTRUCT(BlueprintType)
struct FUnlockedUpgrade
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
	FName UpgradeID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
	bool bIsUnlocked = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
	int32 Level = 0;

	FUnlockedUpgrade()
	{
		UpgradeID = NAME_None;
		bIsUnlocked = false;
		Level = 0;
	}

	FUnlockedUpgrade(FName InUpgradeID, bool bInIsUnlocked = false, int32 InLevel = 0)
		: UpgradeID(InUpgradeID), bIsUnlocked(bInIsUnlocked), Level(InLevel)
	{
	}
};

USTRUCT(BlueprintType)
struct FGameStatistics
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
	int32 TotalRuns = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
	int32 SuccessfulRuns = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
	int32 TotalKills = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
	int32 TotalDeaths = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
	float TotalPlayTime = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
	int32 HighestSectorReached = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
	int32 MostTeethInSingleRun = 0;

	FGameStatistics()
	{
		TotalRuns = 0;
		SuccessfulRuns = 0;
		TotalKills = 0;
		TotalDeaths = 0;
		TotalPlayTime = 0.0f;
		HighestSectorReached = 0;
		MostTeethInSingleRun = 0;
	}
};

/**
 * Save game class for Rough Reality
 * Stores persistent progression data between runs
 */
UCLASS(BlueprintType)
class ROUGHREALITY_API URoughSaveGame : public USaveGame
{
	GENERATED_BODY()

public:
	URoughSaveGame();

	/** Total teeth (currency) accumulated across all runs */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Currency")
	int32 TotalTeeth = 0;

	/** Unlocked weapons */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression")
	TArray<FUnlockedWeapon> UnlockedWeapons;

	/** Unlocked upgrades */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression")
	TArray<FUnlockedUpgrade> UnlockedUpgrades;

	/** Unlocked gameplay tags (for various unlocks) */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression")
	FGameplayTagContainer UnlockedTags;

	/** Game statistics */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
	FGameStatistics Statistics;

	/** Game settings */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
	float MasterVolume = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
	float SFXVolume = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
	float MusicVolume = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
	int32 GraphicsQuality = 2; // 0=Low, 1=Medium, 2=High, 3=Ultra

public:
	/** Initialize default values */
	UFUNCTION(BlueprintCallable, Category = "Save Game")
	void InitializeDefaults();

	/** Weapon management */
	UFUNCTION(BlueprintCallable, Category = "Weapons")
	bool IsWeaponUnlocked(FName WeaponID) const;

	UFUNCTION(BlueprintCallable, Category = "Weapons")
	void UnlockWeapon(FName WeaponID);

	UFUNCTION(BlueprintCallable, Category = "Weapons")
	int32 GetWeaponUpgradeLevel(FName WeaponID) const;

	UFUNCTION(BlueprintCallable, Category = "Weapons")
	void UpgradeWeapon(FName WeaponID);

	/** Upgrade management */
	UFUNCTION(BlueprintCallable, Category = "Upgrades")
	bool IsUpgradeUnlocked(FName UpgradeID) const;

	UFUNCTION(BlueprintCallable, Category = "Upgrades")
	void UnlockUpgrade(FName UpgradeID);

	UFUNCTION(BlueprintCallable, Category = "Upgrades")
	int32 GetUpgradeLevel(FName UpgradeID) const;

	UFUNCTION(BlueprintCallable, Category = "Upgrades")
	void UpgradeUpgrade(FName UpgradeID);

	/** Tag management */
	UFUNCTION(BlueprintCallable, Category = "Tags")
	bool HasUnlockedTag(const FGameplayTag& Tag) const;

	UFUNCTION(BlueprintCallable, Category = "Tags")
	void UnlockTag(const FGameplayTag& Tag);

	/** Statistics */
	UFUNCTION(BlueprintCallable, Category = "Statistics")
	void IncrementRuns();

	UFUNCTION(BlueprintCallable, Category = "Statistics")
	void IncrementSuccessfulRuns();

	UFUNCTION(BlueprintCallable, Category = "Statistics")
	void AddKills(int32 KillCount);

	UFUNCTION(BlueprintCallable, Category = "Statistics")
	void IncrementDeaths();

	UFUNCTION(BlueprintCallable, Category = "Statistics")
	void AddPlayTime(float DeltaTime);

	UFUNCTION(BlueprintCallable, Category = "Statistics")
	void UpdateHighestSector(int32 SectorIndex);

	UFUNCTION(BlueprintCallable, Category = "Statistics")
	void UpdateMostTeethInRun(int32 TeethCount);

	/** Enhanced Save System */
	UFUNCTION(BlueprintCallable, Category = "Save System")
	bool ValidateSaveData() const;

	UFUNCTION(BlueprintCallable, Category = "Save System")
	FString GetSaveDataChecksum() const;

	UFUNCTION(BlueprintCallable, Category = "Save System")
	void CreateBackup();

	UFUNCTION(BlueprintCallable, Category = "Save System")
	bool RestoreFromBackup();

	UFUNCTION(BlueprintCallable, Category = "Save System")
	TMap<FString, FString> GetSaveMetadata() const;

	/** Cloud Save Support */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cloud Save")
	FString CloudSaveID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cloud Save")
	FDateTime LastCloudSync;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cloud Save")
	bool bCloudSaveEnabled = false;

private:
	/** Helper function to find weapon data */
	FUnlockedWeapon* FindWeaponData(FName WeaponID);
	const FUnlockedWeapon* FindWeaponData(FName WeaponID) const;

	/** Helper function to find upgrade data */
	FUnlockedUpgrade* FindUpgradeData(FName UpgradeID);
	const FUnlockedUpgrade* FindUpgradeData(FName UpgradeID) const;
};
