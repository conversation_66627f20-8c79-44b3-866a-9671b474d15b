// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RoughRealityGameMode.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef ROUGHREALITY_RoughRealityGameMode_generated_h
#error "RoughRealityGameMode.generated.h already included, missing '#pragma once' in RoughRealityGameMode.h"
#endif
#define ROUGHREALITY_RoughRealityGameMode_generated_h

#define FID_RoughReality_Source_RoughReality_RoughRealityGameMode_h_12_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesARoughRealityGameMode(); \
	friend struct Z_Construct_UClass_ARoughRealityGameMode_Statics; \
public: \
	DECLARE_CLASS(ARoughRealityGameMode, AGameModeBase, COMPILED_IN_FLAGS(0 | CLASS_Transient | CLASS_Config), CASTCLASS_None, TEXT("/Script/RoughReality"), ROUGHREALITY_API) \
	DECLARE_SERIALIZER(ARoughRealityGameMode)


#define FID_RoughReality_Source_RoughReality_RoughRealityGameMode_h_12_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	ARoughRealityGameMode(ARoughRealityGameMode&&); \
	ARoughRealityGameMode(const ARoughRealityGameMode&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(ROUGHREALITY_API, ARoughRealityGameMode); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ARoughRealityGameMode); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ARoughRealityGameMode) \
	ROUGHREALITY_API virtual ~ARoughRealityGameMode();


#define FID_RoughReality_Source_RoughReality_RoughRealityGameMode_h_9_PROLOG
#define FID_RoughReality_Source_RoughReality_RoughRealityGameMode_h_12_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_RoughReality_Source_RoughReality_RoughRealityGameMode_h_12_INCLASS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_RoughRealityGameMode_h_12_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ROUGHREALITY_API UClass* StaticClass<class ARoughRealityGameMode>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_RoughReality_Source_RoughReality_RoughRealityGameMode_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
