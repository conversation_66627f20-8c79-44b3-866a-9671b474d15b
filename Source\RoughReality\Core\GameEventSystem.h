// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "GameplayTagContainer.h"
#include "GameEventSystem.generated.h"

USTRUCT(BlueprintType)
struct FGameEventData
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadWrite, Category = "Event")
	FGameplayTag EventTag;

	UPROPERTY(BlueprintReadWrite, Category = "Event")
	AActor* Instigator;

	UPROPERTY(BlueprintReadWrite, Category = "Event")
	AActor* Target;

	UPROPERTY(BlueprintReadWrite, Category = "Event")
	FVector Location;

	UPROPERTY(BlueprintReadWrite, Category = "Event")
	float Magnitude;

	UPROPERTY(BlueprintReadWrite, Category = "Event")
	TMap<FString, FString> Parameters;

	UPROPERTY(BlueprintReadWrite, Category = "Event")
	float Timestamp;

	FGameEventData()
	{
		EventTag = FGameplayTag::EmptyTag;
		Instigator = nullptr;
		Target = nullptr;
		Location = FVector::ZeroVector;
		Magnitude = 0.0f;
		Timestamp = 0.0f;
	}

	FGameEventData(const FGameplayTag& InEventTag, AActor* InInstigator = nullptr, AActor* InTarget = nullptr)
	{
		EventTag = InEventTag;
		Instigator = InInstigator;
		Target = InTarget;
		Location = FVector::ZeroVector;
		Magnitude = 0.0f;
		Timestamp = 0.0f;
	}
};

DECLARE_DYNAMIC_DELEGATE_OneParam(FGameEventDelegate, const FGameEventData&, EventData);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FGameEventMulticastDelegate, const FGameEventData&, EventData);

USTRUCT(BlueprintType)
struct FEventListener
{
	GENERATED_BODY()

	UPROPERTY()
	UObject* ListenerObject;

	UPROPERTY()
	FGameplayTag EventTag;

	UPROPERTY()
	FGameEventDelegate Delegate;

	UPROPERTY()
	bool bIsActive;

	UPROPERTY()
	int32 Priority;

	FEventListener()
	{
		ListenerObject = nullptr;
		EventTag = FGameplayTag::EmptyTag;
		bIsActive = true;
		Priority = 0;
	}

	FEventListener(UObject* InListener, const FGameplayTag& InEventTag, const FGameEventDelegate& InDelegate, int32 InPriority = 0)
	{
		ListenerObject = InListener;
		EventTag = InEventTag;
		Delegate = InDelegate;
		bIsActive = true;
		Priority = InPriority;
	}
};

/**
 * Centralized Game Event System for Rough Reality
 * Provides decoupled communication between game systems
 */
UCLASS(BlueprintType, Blueprintable)
class ROUGHREALITY_API AGameEventSystem : public AActor
{
	GENERATED_BODY()

public:
	AGameEventSystem();

protected:
	virtual void BeginPlay() override;
	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

public:
	/** Event Broadcasting */
	UFUNCTION(BlueprintCallable, Category = "Game Events")
	void BroadcastEvent(const FGameEventData& EventData);

	UFUNCTION(BlueprintCallable, Category = "Game Events")
	void BroadcastEventByTag(const FGameplayTag& EventTag, AActor* Instigator = nullptr, AActor* Target = nullptr, const FVector& Location = FVector::ZeroVector, float Magnitude = 0.0f);

	UFUNCTION(BlueprintCallable, Category = "Game Events")
	void BroadcastEventWithParameters(const FGameplayTag& EventTag, const TMap<FString, FString>& Parameters, AActor* Instigator = nullptr);

	/** Event Listening */
	UFUNCTION(BlueprintCallable, Category = "Game Events")
	void RegisterEventListener(UObject* Listener, const FGameplayTag& EventTag, const FGameEventDelegate& Delegate, int32 Priority = 0);

	UFUNCTION(BlueprintCallable, Category = "Game Events")
	void UnregisterEventListener(UObject* Listener, const FGameplayTag& EventTag);

	UFUNCTION(BlueprintCallable, Category = "Game Events")
	void UnregisterAllListeners(UObject* Listener);

	/** Event Filtering */
	UFUNCTION(BlueprintCallable, Category = "Game Events")
	void SetEventFilter(const FGameplayTag& EventTag, bool bEnabled);

	UFUNCTION(BlueprintCallable, Category = "Game Events")
	bool IsEventFiltered(const FGameplayTag& EventTag) const;

	/** Event History */
	UFUNCTION(BlueprintCallable, Category = "Game Events")
	TArray<FGameEventData> GetEventHistory(const FGameplayTag& EventTag, int32 MaxEvents = 10) const;

	UFUNCTION(BlueprintCallable, Category = "Game Events")
	void ClearEventHistory();

	UFUNCTION(BlueprintCallable, Category = "Game Events")
	int32 GetEventCount(const FGameplayTag& EventTag) const;

	/** Event Debugging */
	UFUNCTION(BlueprintCallable, Category = "Game Events")
	void EnableEventLogging(bool bEnable);

	UFUNCTION(BlueprintCallable, Category = "Game Events")
	void LogEventStatistics();

	UFUNCTION(BlueprintCallable, Category = "Game Events")
	TArray<FGameplayTag> GetActiveEventTags() const;

	/** Delayed Events */
	UFUNCTION(BlueprintCallable, Category = "Game Events")
	void BroadcastEventDelayed(const FGameEventData& EventData, float Delay);

	UFUNCTION(BlueprintCallable, Category = "Game Events")
	void CancelDelayedEvent(const FGameplayTag& EventTag);

	/** Event Validation */
	UFUNCTION(BlueprintCallable, Category = "Game Events")
	bool IsValidEventTag(const FGameplayTag& EventTag) const;

	UFUNCTION(BlueprintCallable, Category = "Game Events")
	void RegisterValidEventTag(const FGameplayTag& EventTag);

	/** Static Access */
	UFUNCTION(BlueprintCallable, Category = "Game Events", CallInEditor = true)
	static AGameEventSystem* GetGameEventSystem(const UObject* WorldContext);

	/** Common Game Events - Static helpers */
	UFUNCTION(BlueprintCallable, Category = "Common Events")
	static void BroadcastPlayerDamaged(const UObject* WorldContext, AActor* Player, float Damage, AActor* DamageSource);

	UFUNCTION(BlueprintCallable, Category = "Common Events")
	static void BroadcastEnemyKilled(const UObject* WorldContext, AActor* Enemy, AActor* Killer, const FString& WeaponUsed);

	UFUNCTION(BlueprintCallable, Category = "Common Events")
	static void BroadcastWeaponFired(const UObject* WorldContext, AActor* Weapon, AActor* Shooter, const FVector& Location);

	UFUNCTION(BlueprintCallable, Category = "Common Events")
	static void BroadcastLevelCompleted(const UObject* WorldContext, int32 SectorIndex, int32 LevelIndex, float CompletionTime);

	UFUNCTION(BlueprintCallable, Category = "Common Events")
	static void BroadcastBulletTimeActivated(const UObject* WorldContext, AActor* Player, float Duration);

	UFUNCTION(BlueprintCallable, Category = "Common Events")
	static void BroadcastTimeRewind(const UObject* WorldContext, AActor* Player, const FVector& RewindLocation);

protected:
	/** Internal Functions */
	void ProcessEventQueue();
	void CleanupInvalidListeners();
	void SortListenersByPriority(TArray<FEventListener>& Listeners);

	/** Event Storage */
	UPROPERTY()
	TMap<FGameplayTag, TArray<FEventListener>> EventListeners;

	UPROPERTY()
	TMap<FGameplayTag, TArray<FGameEventData>> EventHistory;

	UPROPERTY()
	TSet<FGameplayTag> FilteredEvents;

	UPROPERTY()
	TSet<FGameplayTag> ValidEventTags;

	/** Event Queue for delayed processing */
	UPROPERTY()
	TArray<FGameEventData> EventQueue;

	/** Configuration */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Configuration")
	bool bEnableEventLogging = false;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Configuration")
	int32 MaxEventHistorySize = 100;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Configuration")
	bool bAutoCleanupListeners = true;

	/** Statistics */
	UPROPERTY(BlueprintReadOnly, Category = "Statistics")
	int32 TotalEventsBroadcast = 0;

	UPROPERTY(BlueprintReadOnly, Category = "Statistics")
	TMap<FGameplayTag, int32> EventCounts;

	/** Delayed Event Timers */
	TMap<FGameplayTag, FTimerHandle> DelayedEventTimers;

private:
	/** Singleton instance */
	static AGameEventSystem* Instance;

	/** Delayed event callback */
	UFUNCTION()
	void OnDelayedEventTimer(FGameEventData EventData);

	/** Common event tags */
	static const FGameplayTag EVENT_PLAYER_DAMAGED;
	static const FGameplayTag EVENT_PLAYER_HEALED;
	static const FGameplayTag EVENT_PLAYER_DIED;
	static const FGameplayTag EVENT_ENEMY_KILLED;
	static const FGameplayTag EVENT_WEAPON_FIRED;
	static const FGameplayTag EVENT_WEAPON_RELOADED;
	static const FGameplayTag EVENT_LEVEL_COMPLETED;
	static const FGameplayTag EVENT_SECTOR_COMPLETED;
	static const FGameplayTag EVENT_BULLET_TIME_ACTIVATED;
	static const FGameplayTag EVENT_BULLET_TIME_DEACTIVATED;
	static const FGameplayTag EVENT_TIME_REWIND;
	static const FGameplayTag EVENT_DASH_PERFORMED;
	static const FGameplayTag EVENT_UPGRADE_PURCHASED;
	static const FGameplayTag EVENT_ACHIEVEMENT_UNLOCKED;
};
