// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RoughReality/DataAssets/WeaponDataAsset.h"
#include "RoughReality/Weapons/WeaponBase.h"
#include "Runtime/GameplayTags/Classes/GameplayTagContainer.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeWeaponDataAsset() {}

// Begin Cross Module References
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
ENGINE_API UClass* Z_Construct_UClass_UAnimMontage_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UParticleSystem_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USkeletalMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USoundBase_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
ROUGHREALITY_API UClass* Z_Construct_UClass_URoughRealityDataAsset();
ROUGHREALITY_API UClass* Z_Construct_UClass_UWeaponDataAsset();
ROUGHREALITY_API UClass* Z_Construct_UClass_UWeaponDataAsset_NoRegister();
ROUGHREALITY_API UEnum* Z_Construct_UEnum_RoughReality_EWeaponType();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FWeaponStats();
UPackage* Z_Construct_UPackage__Script_RoughReality();
// End Cross Module References

// Begin Class UWeaponDataAsset Function CanUpgrade
struct Z_Construct_UFunction_UWeaponDataAsset_CanUpgrade_Statics
{
	struct WeaponDataAsset_eventCanUpgrade_Parms
	{
		int32 CurrentLevel;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Upgrades" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentLevel;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UWeaponDataAsset_CanUpgrade_Statics::NewProp_CurrentLevel = { "CurrentLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponDataAsset_eventCanUpgrade_Parms, CurrentLevel), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UWeaponDataAsset_CanUpgrade_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((WeaponDataAsset_eventCanUpgrade_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UWeaponDataAsset_CanUpgrade_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WeaponDataAsset_eventCanUpgrade_Parms), &Z_Construct_UFunction_UWeaponDataAsset_CanUpgrade_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponDataAsset_CanUpgrade_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponDataAsset_CanUpgrade_Statics::NewProp_CurrentLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponDataAsset_CanUpgrade_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponDataAsset_CanUpgrade_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponDataAsset_CanUpgrade_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponDataAsset, nullptr, "CanUpgrade", nullptr, nullptr, Z_Construct_UFunction_UWeaponDataAsset_CanUpgrade_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponDataAsset_CanUpgrade_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponDataAsset_CanUpgrade_Statics::WeaponDataAsset_eventCanUpgrade_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponDataAsset_CanUpgrade_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponDataAsset_CanUpgrade_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponDataAsset_CanUpgrade_Statics::WeaponDataAsset_eventCanUpgrade_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponDataAsset_CanUpgrade()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponDataAsset_CanUpgrade_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponDataAsset::execCanUpgrade)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_CurrentLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanUpgrade(Z_Param_CurrentLevel);
	P_NATIVE_END;
}
// End Class UWeaponDataAsset Function CanUpgrade

// Begin Class UWeaponDataAsset Function GetUpgradeCost
struct Z_Construct_UFunction_UWeaponDataAsset_GetUpgradeCost_Statics
{
	struct WeaponDataAsset_eventGetUpgradeCost_Parms
	{
		int32 UpgradeLevel;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Upgrades" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_UpgradeLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UWeaponDataAsset_GetUpgradeCost_Statics::NewProp_UpgradeLevel = { "UpgradeLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponDataAsset_eventGetUpgradeCost_Parms, UpgradeLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UWeaponDataAsset_GetUpgradeCost_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponDataAsset_eventGetUpgradeCost_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponDataAsset_GetUpgradeCost_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponDataAsset_GetUpgradeCost_Statics::NewProp_UpgradeLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponDataAsset_GetUpgradeCost_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponDataAsset_GetUpgradeCost_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponDataAsset_GetUpgradeCost_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponDataAsset, nullptr, "GetUpgradeCost", nullptr, nullptr, Z_Construct_UFunction_UWeaponDataAsset_GetUpgradeCost_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponDataAsset_GetUpgradeCost_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponDataAsset_GetUpgradeCost_Statics::WeaponDataAsset_eventGetUpgradeCost_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponDataAsset_GetUpgradeCost_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponDataAsset_GetUpgradeCost_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponDataAsset_GetUpgradeCost_Statics::WeaponDataAsset_eventGetUpgradeCost_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponDataAsset_GetUpgradeCost()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponDataAsset_GetUpgradeCost_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponDataAsset::execGetUpgradeCost)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_UpgradeLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetUpgradeCost(Z_Param_UpgradeLevel);
	P_NATIVE_END;
}
// End Class UWeaponDataAsset Function GetUpgradeCost

// Begin Class UWeaponDataAsset Function GetUpgradedStats
struct Z_Construct_UFunction_UWeaponDataAsset_GetUpgradedStats_Statics
{
	struct WeaponDataAsset_eventGetUpgradedStats_Parms
	{
		int32 UpgradeLevel;
		FWeaponStats ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Upgrades" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get upgraded weapon stats */" },
#endif
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get upgraded weapon stats" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_UpgradeLevel;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UWeaponDataAsset_GetUpgradedStats_Statics::NewProp_UpgradeLevel = { "UpgradeLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponDataAsset_eventGetUpgradedStats_Parms, UpgradeLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UWeaponDataAsset_GetUpgradedStats_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponDataAsset_eventGetUpgradedStats_Parms, ReturnValue), Z_Construct_UScriptStruct_FWeaponStats, METADATA_PARAMS(0, nullptr) }; // 797466482
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponDataAsset_GetUpgradedStats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponDataAsset_GetUpgradedStats_Statics::NewProp_UpgradeLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponDataAsset_GetUpgradedStats_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponDataAsset_GetUpgradedStats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponDataAsset_GetUpgradedStats_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponDataAsset, nullptr, "GetUpgradedStats", nullptr, nullptr, Z_Construct_UFunction_UWeaponDataAsset_GetUpgradedStats_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponDataAsset_GetUpgradedStats_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponDataAsset_GetUpgradedStats_Statics::WeaponDataAsset_eventGetUpgradedStats_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponDataAsset_GetUpgradedStats_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponDataAsset_GetUpgradedStats_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponDataAsset_GetUpgradedStats_Statics::WeaponDataAsset_eventGetUpgradedStats_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponDataAsset_GetUpgradedStats()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponDataAsset_GetUpgradedStats_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponDataAsset::execGetUpgradedStats)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_UpgradeLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FWeaponStats*)Z_Param__Result=P_THIS->GetUpgradedStats(Z_Param_UpgradeLevel);
	P_NATIVE_END;
}
// End Class UWeaponDataAsset Function GetUpgradedStats

// Begin Class UWeaponDataAsset Function GetWeaponTierColor
struct Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTierColor_Statics
{
	struct WeaponDataAsset_eventGetWeaponTierColor_Parms
	{
		FLinearColor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTierColor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponDataAsset_eventGetWeaponTierColor_Parms, ReturnValue), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTierColor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTierColor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTierColor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTierColor_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponDataAsset, nullptr, "GetWeaponTierColor", nullptr, nullptr, Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTierColor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTierColor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTierColor_Statics::WeaponDataAsset_eventGetWeaponTierColor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTierColor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTierColor_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTierColor_Statics::WeaponDataAsset_eventGetWeaponTierColor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTierColor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTierColor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponDataAsset::execGetWeaponTierColor)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FLinearColor*)Z_Param__Result=P_THIS->GetWeaponTierColor();
	P_NATIVE_END;
}
// End Class UWeaponDataAsset Function GetWeaponTierColor

// Begin Class UWeaponDataAsset Function GetWeaponTypeString
struct Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTypeString_Statics
{
	struct WeaponDataAsset_eventGetWeaponTypeString_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Utility Functions */" },
#endif
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility Functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTypeString_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponDataAsset_eventGetWeaponTypeString_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTypeString_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTypeString_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTypeString_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTypeString_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponDataAsset, nullptr, "GetWeaponTypeString", nullptr, nullptr, Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTypeString_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTypeString_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTypeString_Statics::WeaponDataAsset_eventGetWeaponTypeString_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTypeString_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTypeString_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTypeString_Statics::WeaponDataAsset_eventGetWeaponTypeString_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTypeString()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTypeString_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponDataAsset::execGetWeaponTypeString)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetWeaponTypeString();
	P_NATIVE_END;
}
// End Class UWeaponDataAsset Function GetWeaponTypeString

// Begin Class UWeaponDataAsset Function MeetsUnlockRequirements
struct Z_Construct_UFunction_UWeaponDataAsset_MeetsUnlockRequirements_Statics
{
	struct WeaponDataAsset_eventMeetsUnlockRequirements_Parms
	{
		FGameplayTagContainer PlayerTags;
		int32 PlayerSectorProgress;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerTags_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerTags;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerSectorProgress;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UWeaponDataAsset_MeetsUnlockRequirements_Statics::NewProp_PlayerTags = { "PlayerTags", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponDataAsset_eventMeetsUnlockRequirements_Parms, PlayerTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerTags_MetaData), NewProp_PlayerTags_MetaData) }; // 3352185621
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UWeaponDataAsset_MeetsUnlockRequirements_Statics::NewProp_PlayerSectorProgress = { "PlayerSectorProgress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponDataAsset_eventMeetsUnlockRequirements_Parms, PlayerSectorProgress), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UWeaponDataAsset_MeetsUnlockRequirements_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((WeaponDataAsset_eventMeetsUnlockRequirements_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UWeaponDataAsset_MeetsUnlockRequirements_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WeaponDataAsset_eventMeetsUnlockRequirements_Parms), &Z_Construct_UFunction_UWeaponDataAsset_MeetsUnlockRequirements_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponDataAsset_MeetsUnlockRequirements_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponDataAsset_MeetsUnlockRequirements_Statics::NewProp_PlayerTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponDataAsset_MeetsUnlockRequirements_Statics::NewProp_PlayerSectorProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponDataAsset_MeetsUnlockRequirements_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponDataAsset_MeetsUnlockRequirements_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponDataAsset_MeetsUnlockRequirements_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponDataAsset, nullptr, "MeetsUnlockRequirements", nullptr, nullptr, Z_Construct_UFunction_UWeaponDataAsset_MeetsUnlockRequirements_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponDataAsset_MeetsUnlockRequirements_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponDataAsset_MeetsUnlockRequirements_Statics::WeaponDataAsset_eventMeetsUnlockRequirements_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponDataAsset_MeetsUnlockRequirements_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponDataAsset_MeetsUnlockRequirements_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponDataAsset_MeetsUnlockRequirements_Statics::WeaponDataAsset_eventMeetsUnlockRequirements_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponDataAsset_MeetsUnlockRequirements()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponDataAsset_MeetsUnlockRequirements_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponDataAsset::execMeetsUnlockRequirements)
{
	P_GET_STRUCT_REF(FGameplayTagContainer,Z_Param_Out_PlayerTags);
	P_GET_PROPERTY(FIntProperty,Z_Param_PlayerSectorProgress);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->MeetsUnlockRequirements(Z_Param_Out_PlayerTags,Z_Param_PlayerSectorProgress);
	P_NATIVE_END;
}
// End Class UWeaponDataAsset Function MeetsUnlockRequirements

// Begin Class UWeaponDataAsset
void UWeaponDataAsset::StaticRegisterNativesUWeaponDataAsset()
{
	UClass* Class = UWeaponDataAsset::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CanUpgrade", &UWeaponDataAsset::execCanUpgrade },
		{ "GetUpgradeCost", &UWeaponDataAsset::execGetUpgradeCost },
		{ "GetUpgradedStats", &UWeaponDataAsset::execGetUpgradedStats },
		{ "GetWeaponTierColor", &UWeaponDataAsset::execGetWeaponTierColor },
		{ "GetWeaponTypeString", &UWeaponDataAsset::execGetWeaponTypeString },
		{ "MeetsUnlockRequirements", &UWeaponDataAsset::execMeetsUnlockRequirements },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UWeaponDataAsset);
UClass* Z_Construct_UClass_UWeaponDataAsset_NoRegister()
{
	return UWeaponDataAsset::StaticClass();
}
struct Z_Construct_UClass_UWeaponDataAsset_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Data Asset for weapon configuration\n * Contains all stats, visuals, and audio for weapons\n */" },
#endif
		{ "IncludePath", "DataAssets/WeaponDataAsset.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data Asset for weapon configuration\nContains all stats, visuals, and audio for weapons" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponType_MetaData[] = {
		{ "Category", "Weapon Type" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Weapon Type */" },
#endif
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weapon Type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponStats_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Weapon Statistics */" },
#endif
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weapon Statistics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsAutomatic_MetaData[] = {
		{ "Category", "Fire Mode" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Fire Mode */" },
#endif
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Fire Mode" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanBurstFire_MetaData[] = {
		{ "Category", "Fire Mode" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BurstCount_MetaData[] = {
		{ "Category", "Fire Mode" },
		{ "EditCondition", "bCanBurstFire" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponMesh_MetaData[] = {
		{ "Category", "Visuals" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Visual Assets */" },
#endif
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visual Assets" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldMesh_MetaData[] = {
		{ "Category", "Visuals" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponIcon_MetaData[] = {
		{ "Category", "Visuals" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AmmoIcon_MetaData[] = {
		{ "Category", "Visuals" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MuzzleFlashEffect_MetaData[] = {
		{ "Category", "Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Effects */" },
#endif
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Effects" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ImpactEffect_MetaData[] = {
		{ "Category", "Effects" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TracerEffect_MetaData[] = {
		{ "Category", "Effects" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShellEjectEffect_MetaData[] = {
		{ "Category", "Effects" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FireSound_MetaData[] = {
		{ "Category", "Audio" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Audio */" },
#endif
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReloadSound_MetaData[] = {
		{ "Category", "Audio" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EmptySound_MetaData[] = {
		{ "Category", "Audio" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EquipSound_MetaData[] = {
		{ "Category", "Audio" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnequipSound_MetaData[] = {
		{ "Category", "Audio" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FireAnimation_MetaData[] = {
		{ "Category", "Animation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Animations */" },
#endif
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Animations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReloadAnimation_MetaData[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EquipAnimation_MetaData[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnequipAnimation_MetaData[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxUpgradeLevel_MetaData[] = {
		{ "Category", "Upgrades" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Upgrade System */" },
#endif
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Upgrade System" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpgradeCosts_MetaData[] = {
		{ "Category", "Upgrades" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageUpgradeMultiplier_MetaData[] = {
		{ "Category", "Upgrades" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FireRateUpgradeMultiplier_MetaData[] = {
		{ "Category", "Upgrades" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccuracyUpgradeMultiplier_MetaData[] = {
		{ "Category", "Upgrades" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MagazineSizeUpgradeAmount_MetaData[] = {
		{ "Category", "Upgrades" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnlockCost_MetaData[] = {
		{ "Category", "Unlock" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Unlock Requirements */" },
#endif
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Unlock Requirements" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredTags_MetaData[] = {
		{ "Category", "Unlock" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredSectorProgress_MetaData[] = {
		{ "Category", "Unlock" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponTier_MetaData[] = {
		{ "Category", "Classification" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Rarity and Classification */" },
#endif
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Rarity and Classification" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsStartingWeapon_MetaData[] = {
		{ "Category", "Classification" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 1-5, higher is better\n" },
#endif
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "1-5, higher is better" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsUnlockable_MetaData[] = {
		{ "Category", "Classification" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanPenetrate_MetaData[] = {
		{ "Category", "Special" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Special Properties */" },
#endif
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Special Properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PenetrationCount_MetaData[] = {
		{ "Category", "Special" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasExplosiveDamage_MetaData[] = {
		{ "Category", "Special" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExplosionRadius_MetaData[] = {
		{ "Category", "Special" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExplosionDamage_MetaData[] = {
		{ "Category", "Special" },
		{ "ModuleRelativePath", "DataAssets/WeaponDataAsset.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_WeaponType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_WeaponType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WeaponStats;
	static void NewProp_bIsAutomatic_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsAutomatic;
	static void NewProp_bCanBurstFire_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanBurstFire;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BurstCount;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_WeaponMesh;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_WorldMesh;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_WeaponIcon;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AmmoIcon;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_MuzzleFlashEffect;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ImpactEffect;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_TracerEffect;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ShellEjectEffect;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_FireSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ReloadSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_EmptySound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_EquipSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_UnequipSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_FireAnimation;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ReloadAnimation;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_EquipAnimation;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_UnequipAnimation;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxUpgradeLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_UpgradeCosts_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_UpgradeCosts;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageUpgradeMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FireRateUpgradeMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AccuracyUpgradeMultiplier;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MagazineSizeUpgradeAmount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_UnlockCost;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RequiredTags;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RequiredSectorProgress;
	static const UECodeGen_Private::FIntPropertyParams NewProp_WeaponTier;
	static void NewProp_bIsStartingWeapon_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsStartingWeapon;
	static void NewProp_bIsUnlockable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsUnlockable;
	static void NewProp_bCanPenetrate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanPenetrate;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PenetrationCount;
	static void NewProp_bHasExplosiveDamage_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasExplosiveDamage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExplosionRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ExplosionDamage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UWeaponDataAsset_CanUpgrade, "CanUpgrade" }, // 341550627
		{ &Z_Construct_UFunction_UWeaponDataAsset_GetUpgradeCost, "GetUpgradeCost" }, // 2080375633
		{ &Z_Construct_UFunction_UWeaponDataAsset_GetUpgradedStats, "GetUpgradedStats" }, // 452839730
		{ &Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTierColor, "GetWeaponTierColor" }, // 3606945651
		{ &Z_Construct_UFunction_UWeaponDataAsset_GetWeaponTypeString, "GetWeaponTypeString" }, // 1553344984
		{ &Z_Construct_UFunction_UWeaponDataAsset_MeetsUnlockRequirements, "MeetsUnlockRequirements" }, // 901343552
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UWeaponDataAsset>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_WeaponType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_WeaponType = { "WeaponType", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, WeaponType), Z_Construct_UEnum_RoughReality_EWeaponType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponType_MetaData), NewProp_WeaponType_MetaData) }; // 5347314
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_WeaponStats = { "WeaponStats", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, WeaponStats), Z_Construct_UScriptStruct_FWeaponStats, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponStats_MetaData), NewProp_WeaponStats_MetaData) }; // 797466482
void Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_bIsAutomatic_SetBit(void* Obj)
{
	((UWeaponDataAsset*)Obj)->bIsAutomatic = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_bIsAutomatic = { "bIsAutomatic", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UWeaponDataAsset), &Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_bIsAutomatic_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsAutomatic_MetaData), NewProp_bIsAutomatic_MetaData) };
void Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_bCanBurstFire_SetBit(void* Obj)
{
	((UWeaponDataAsset*)Obj)->bCanBurstFire = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_bCanBurstFire = { "bCanBurstFire", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UWeaponDataAsset), &Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_bCanBurstFire_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanBurstFire_MetaData), NewProp_bCanBurstFire_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_BurstCount = { "BurstCount", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, BurstCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BurstCount_MetaData), NewProp_BurstCount_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_WeaponMesh = { "WeaponMesh", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, WeaponMesh), Z_Construct_UClass_USkeletalMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponMesh_MetaData), NewProp_WeaponMesh_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_WorldMesh = { "WorldMesh", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, WorldMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldMesh_MetaData), NewProp_WorldMesh_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_WeaponIcon = { "WeaponIcon", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, WeaponIcon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponIcon_MetaData), NewProp_WeaponIcon_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_AmmoIcon = { "AmmoIcon", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, AmmoIcon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AmmoIcon_MetaData), NewProp_AmmoIcon_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_MuzzleFlashEffect = { "MuzzleFlashEffect", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, MuzzleFlashEffect), Z_Construct_UClass_UParticleSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MuzzleFlashEffect_MetaData), NewProp_MuzzleFlashEffect_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_ImpactEffect = { "ImpactEffect", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, ImpactEffect), Z_Construct_UClass_UParticleSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ImpactEffect_MetaData), NewProp_ImpactEffect_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_TracerEffect = { "TracerEffect", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, TracerEffect), Z_Construct_UClass_UParticleSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TracerEffect_MetaData), NewProp_TracerEffect_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_ShellEjectEffect = { "ShellEjectEffect", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, ShellEjectEffect), Z_Construct_UClass_UParticleSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShellEjectEffect_MetaData), NewProp_ShellEjectEffect_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_FireSound = { "FireSound", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, FireSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FireSound_MetaData), NewProp_FireSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_ReloadSound = { "ReloadSound", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, ReloadSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReloadSound_MetaData), NewProp_ReloadSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_EmptySound = { "EmptySound", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, EmptySound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EmptySound_MetaData), NewProp_EmptySound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_EquipSound = { "EquipSound", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, EquipSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EquipSound_MetaData), NewProp_EquipSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_UnequipSound = { "UnequipSound", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, UnequipSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnequipSound_MetaData), NewProp_UnequipSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_FireAnimation = { "FireAnimation", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, FireAnimation), Z_Construct_UClass_UAnimMontage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FireAnimation_MetaData), NewProp_FireAnimation_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_ReloadAnimation = { "ReloadAnimation", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, ReloadAnimation), Z_Construct_UClass_UAnimMontage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReloadAnimation_MetaData), NewProp_ReloadAnimation_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_EquipAnimation = { "EquipAnimation", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, EquipAnimation), Z_Construct_UClass_UAnimMontage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EquipAnimation_MetaData), NewProp_EquipAnimation_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_UnequipAnimation = { "UnequipAnimation", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, UnequipAnimation), Z_Construct_UClass_UAnimMontage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnequipAnimation_MetaData), NewProp_UnequipAnimation_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_MaxUpgradeLevel = { "MaxUpgradeLevel", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, MaxUpgradeLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxUpgradeLevel_MetaData), NewProp_MaxUpgradeLevel_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_UpgradeCosts_Inner = { "UpgradeCosts", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_UpgradeCosts = { "UpgradeCosts", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, UpgradeCosts), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpgradeCosts_MetaData), NewProp_UpgradeCosts_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_DamageUpgradeMultiplier = { "DamageUpgradeMultiplier", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, DamageUpgradeMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageUpgradeMultiplier_MetaData), NewProp_DamageUpgradeMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_FireRateUpgradeMultiplier = { "FireRateUpgradeMultiplier", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, FireRateUpgradeMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FireRateUpgradeMultiplier_MetaData), NewProp_FireRateUpgradeMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_AccuracyUpgradeMultiplier = { "AccuracyUpgradeMultiplier", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, AccuracyUpgradeMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccuracyUpgradeMultiplier_MetaData), NewProp_AccuracyUpgradeMultiplier_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_MagazineSizeUpgradeAmount = { "MagazineSizeUpgradeAmount", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, MagazineSizeUpgradeAmount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MagazineSizeUpgradeAmount_MetaData), NewProp_MagazineSizeUpgradeAmount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_UnlockCost = { "UnlockCost", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, UnlockCost), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnlockCost_MetaData), NewProp_UnlockCost_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_RequiredTags = { "RequiredTags", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, RequiredTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredTags_MetaData), NewProp_RequiredTags_MetaData) }; // 3352185621
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_RequiredSectorProgress = { "RequiredSectorProgress", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, RequiredSectorProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredSectorProgress_MetaData), NewProp_RequiredSectorProgress_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_WeaponTier = { "WeaponTier", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, WeaponTier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponTier_MetaData), NewProp_WeaponTier_MetaData) };
void Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_bIsStartingWeapon_SetBit(void* Obj)
{
	((UWeaponDataAsset*)Obj)->bIsStartingWeapon = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_bIsStartingWeapon = { "bIsStartingWeapon", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UWeaponDataAsset), &Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_bIsStartingWeapon_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsStartingWeapon_MetaData), NewProp_bIsStartingWeapon_MetaData) };
void Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_bIsUnlockable_SetBit(void* Obj)
{
	((UWeaponDataAsset*)Obj)->bIsUnlockable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_bIsUnlockable = { "bIsUnlockable", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UWeaponDataAsset), &Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_bIsUnlockable_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsUnlockable_MetaData), NewProp_bIsUnlockable_MetaData) };
void Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_bCanPenetrate_SetBit(void* Obj)
{
	((UWeaponDataAsset*)Obj)->bCanPenetrate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_bCanPenetrate = { "bCanPenetrate", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UWeaponDataAsset), &Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_bCanPenetrate_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanPenetrate_MetaData), NewProp_bCanPenetrate_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_PenetrationCount = { "PenetrationCount", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, PenetrationCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PenetrationCount_MetaData), NewProp_PenetrationCount_MetaData) };
void Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_bHasExplosiveDamage_SetBit(void* Obj)
{
	((UWeaponDataAsset*)Obj)->bHasExplosiveDamage = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_bHasExplosiveDamage = { "bHasExplosiveDamage", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UWeaponDataAsset), &Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_bHasExplosiveDamage_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasExplosiveDamage_MetaData), NewProp_bHasExplosiveDamage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_ExplosionRadius = { "ExplosionRadius", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, ExplosionRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExplosionRadius_MetaData), NewProp_ExplosionRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_ExplosionDamage = { "ExplosionDamage", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponDataAsset, ExplosionDamage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExplosionDamage_MetaData), NewProp_ExplosionDamage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UWeaponDataAsset_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_WeaponType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_WeaponType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_WeaponStats,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_bIsAutomatic,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_bCanBurstFire,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_BurstCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_WeaponMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_WorldMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_WeaponIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_AmmoIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_MuzzleFlashEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_ImpactEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_TracerEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_ShellEjectEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_FireSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_ReloadSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_EmptySound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_EquipSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_UnequipSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_FireAnimation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_ReloadAnimation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_EquipAnimation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_UnequipAnimation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_MaxUpgradeLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_UpgradeCosts_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_UpgradeCosts,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_DamageUpgradeMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_FireRateUpgradeMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_AccuracyUpgradeMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_MagazineSizeUpgradeAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_UnlockCost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_RequiredTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_RequiredSectorProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_WeaponTier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_bIsStartingWeapon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_bIsUnlockable,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_bCanPenetrate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_PenetrationCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_bHasExplosiveDamage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_ExplosionRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponDataAsset_Statics::NewProp_ExplosionDamage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UWeaponDataAsset_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UWeaponDataAsset_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URoughRealityDataAsset,
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UWeaponDataAsset_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UWeaponDataAsset_Statics::ClassParams = {
	&UWeaponDataAsset::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UWeaponDataAsset_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UWeaponDataAsset_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UWeaponDataAsset_Statics::Class_MetaDataParams), Z_Construct_UClass_UWeaponDataAsset_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UWeaponDataAsset()
{
	if (!Z_Registration_Info_UClass_UWeaponDataAsset.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UWeaponDataAsset.OuterSingleton, Z_Construct_UClass_UWeaponDataAsset_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UWeaponDataAsset.OuterSingleton;
}
template<> ROUGHREALITY_API UClass* StaticClass<UWeaponDataAsset>()
{
	return UWeaponDataAsset::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UWeaponDataAsset);
UWeaponDataAsset::~UWeaponDataAsset() {}
// End Class UWeaponDataAsset

// Begin Registration
struct Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_DataAssets_WeaponDataAsset_h_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UWeaponDataAsset, UWeaponDataAsset::StaticClass, TEXT("UWeaponDataAsset"), &Z_Registration_Info_UClass_UWeaponDataAsset, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UWeaponDataAsset), 854114762U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_DataAssets_WeaponDataAsset_h_3792321500(TEXT("/Script/RoughReality"),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_DataAssets_WeaponDataAsset_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_DataAssets_WeaponDataAsset_h_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
