// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "GameplayTagContainer.h"
#include "ProceduralLevelBuilder.h"
#include "LevelTile.generated.h"

class UTileDefinitionDataAsset;
class UStaticMeshComponent;
class USceneComponent;
class UAudioComponent;

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnTileActivated, ALevelTile*, Tile);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnTileDeactivated, ALevelTile*, Tile);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnTileCompleted, ALevelTile*, Tile);

/**
 * Level Tile Actor - Represents a single tile in the procedurally generated level
 * Handles spawning of enemies, items, and other content based on tile definition
 */
UCLASS(BlueprintType, Blueprintable)
class ROUGHREALITY_API ALevelTile : public AActor
{
	GENERATED_BODY()

public:
	ALevelTile();

protected:
	virtual void BeginPlay() override;
	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

	/** Components */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	USceneComponent* RootSceneComponent;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UStaticMeshComponent* TileMesh;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UAudioComponent* AmbientAudio;

public:
	/** Tile Configuration */
	UPROPERTY(BlueprintReadOnly, Category = "Tile")
	UTileDefinitionDataAsset* TileDefinition;

	UPROPERTY(BlueprintReadOnly, Category = "Tile")
	ETileType TileType = ETileType::Combat;

	UPROPERTY(BlueprintReadOnly, Category = "Tile")
	bool bIsActive = false;

	UPROPERTY(BlueprintReadOnly, Category = "Tile")
	bool bIsCompleted = false;

	/** Spawned Content */
	UPROPERTY(BlueprintReadOnly, Category = "Content")
	TArray<AActor*> SpawnedEnemies;

	UPROPERTY(BlueprintReadOnly, Category = "Content")
	TArray<AActor*> SpawnedItems;

	UPROPERTY(BlueprintReadOnly, Category = "Content")
	TArray<AActor*> SpawnedInteractables;

	/** Events */
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnTileActivated OnTileActivated;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnTileDeactivated OnTileDeactivated;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnTileCompleted OnTileCompleted;

public:
	/** Initialization */
	UFUNCTION(BlueprintCallable, Category = "Tile")
	void InitializeTile(UTileDefinitionDataAsset* InTileDefinition);

	/** Activation */
	UFUNCTION(BlueprintCallable, Category = "Tile")
	void ActivateTile();

	UFUNCTION(BlueprintCallable, Category = "Tile")
	void DeactivateTile();

	/** Content Spawning */
	UFUNCTION(BlueprintCallable, Category = "Content")
	void SpawnTileContent();

	UFUNCTION(BlueprintCallable, Category = "Content")
	void ClearTileContent();

	UFUNCTION(BlueprintCallable, Category = "Content")
	void SpawnEnemies();

	UFUNCTION(BlueprintCallable, Category = "Content")
	void SpawnItems();

	UFUNCTION(BlueprintCallable, Category = "Content")
	void SpawnInteractables();

	/** Completion */
	UFUNCTION(BlueprintCallable, Category = "Tile")
	void CompleteTile();

	UFUNCTION(BlueprintCallable, Category = "Tile")
	bool CheckCompletionConditions();

	/** Queries */
	UFUNCTION(BlueprintCallable, Category = "Tile")
	ETileType GetTileType() const { return TileType; }

	UFUNCTION(BlueprintCallable, Category = "Tile")
	bool IsActive() const { return bIsActive; }

	UFUNCTION(BlueprintCallable, Category = "Tile")
	bool IsCompleted() const { return bIsCompleted; }

	UFUNCTION(BlueprintCallable, Category = "Tile")
	FVector GetTileSize() const;

	UFUNCTION(BlueprintCallable, Category = "Tile")
	FVector GetTileBounds() const;

	/** Connection Points */
	UFUNCTION(BlueprintCallable, Category = "Connections")
	TArray<FVector> GetConnectionPointLocations() const;

	UFUNCTION(BlueprintCallable, Category = "Connections")
	FVector GetEntranceLocation() const;

	UFUNCTION(BlueprintCallable, Category = "Connections")
	FVector GetExitLocation() const;

	/** Content Management */
	UFUNCTION(BlueprintCallable, Category = "Content")
	int32 GetAliveEnemyCount() const;

	UFUNCTION(BlueprintCallable, Category = "Content")
	TArray<AActor*> GetAliveEnemies() const;

	UFUNCTION(BlueprintCallable, Category = "Content")
	void OnEnemyDestroyed(AActor* DestroyedEnemy);

	/** Audio */
	UFUNCTION(BlueprintCallable, Category = "Audio")
	void PlayAmbientSound();

	UFUNCTION(BlueprintCallable, Category = "Audio")
	void StopAmbientSound();

	/** Lighting */
	UFUNCTION(BlueprintCallable, Category = "Lighting")
	void ApplyTileLighting();

protected:
	/** Internal Functions */
	void SetupTileMesh();
	void SetupTileAudio();
	AActor* SpawnActorAtPoint(const FTileSpawnPoint& SpawnPoint, const FVector& WorldLocation);
	FVector ConvertRelativeToWorldLocation(const FVector& RelativeLocation) const;
	bool ShouldSpawnAtPoint(const FTileSpawnPoint& SpawnPoint) const;

	/** Completion tracking */
	void UpdateCompletionStatus();

private:
	/** Track if content has been spawned */
	bool bContentSpawned = false;

	/** Track enemies for completion */
	int32 InitialEnemyCount = 0;
};
