// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "DataAssets/CharacterStatsDataAsset.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef ROUGHREALITY_CharacterStatsDataAsset_generated_h
#error "CharacterStatsDataAsset.generated.h already included, missing '#pragma once' in CharacterStatsDataAsset.h"
#endif
#define ROUGHREALITY_CharacterStatsDataAsset_generated_h

#define FID_RoughReality_Source_RoughReality_DataAssets_CharacterStatsDataAsset_h_16_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetEffectiveRewindCharges); \
	DECLARE_FUNCTION(execGetEffectiveBulletTimeEnergy); \
	DECLARE_FUNCTION(execGetEffectiveDashDistance); \
	DECLARE_FUNCTION(execGetEffectiveMaxHealth);


#define FID_RoughReality_Source_RoughReality_DataAssets_CharacterStatsDataAsset_h_16_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUCharacterStatsDataAsset(); \
	friend struct Z_Construct_UClass_UCharacterStatsDataAsset_Statics; \
public: \
	DECLARE_CLASS(UCharacterStatsDataAsset, URoughRealityDataAsset, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/RoughReality"), NO_API) \
	DECLARE_SERIALIZER(UCharacterStatsDataAsset)


#define FID_RoughReality_Source_RoughReality_DataAssets_CharacterStatsDataAsset_h_16_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UCharacterStatsDataAsset(UCharacterStatsDataAsset&&); \
	UCharacterStatsDataAsset(const UCharacterStatsDataAsset&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UCharacterStatsDataAsset); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UCharacterStatsDataAsset); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UCharacterStatsDataAsset) \
	NO_API virtual ~UCharacterStatsDataAsset();


#define FID_RoughReality_Source_RoughReality_DataAssets_CharacterStatsDataAsset_h_13_PROLOG
#define FID_RoughReality_Source_RoughReality_DataAssets_CharacterStatsDataAsset_h_16_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_RoughReality_Source_RoughReality_DataAssets_CharacterStatsDataAsset_h_16_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_DataAssets_CharacterStatsDataAsset_h_16_INCLASS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_DataAssets_CharacterStatsDataAsset_h_16_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ROUGHREALITY_API UClass* StaticClass<class UCharacterStatsDataAsset>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_RoughReality_Source_RoughReality_DataAssets_CharacterStatsDataAsset_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
