// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeRoughReality_init() {}
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_RoughReality;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_RoughReality()
	{
		if (!Z_Registration_Info_UPackage__Script_RoughReality.OuterSingleton)
		{
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/RoughReality",
				nullptr,
				0,
				PKG_CompiledIn | 0x00000000,
				0x57F838F0,
				0xBC71AD8A,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_RoughReality.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_RoughReality.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_RoughReality(Z_Construct_UPackage__Script_RoughReality, TEXT("/Script/RoughReality"), Z_Registration_Info_UPackage__Script_RoughReality, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x57F838F0, 0xBC71AD8A));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
