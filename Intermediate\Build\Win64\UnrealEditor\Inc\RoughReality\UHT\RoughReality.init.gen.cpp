// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeRoughReality_init() {}
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_GameEventDelegate__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_GameEventMulticastDelegate__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnAmmoChanged__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnAnalyticsEvent__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnAssetLoaded__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnAssetLoadFailed__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnBulletTimeChanged__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnDifficultyScaled__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnGameStateChanged__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnHealthChanged__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerated__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerationFailed__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnLoadingComplete__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnLoadingProgress__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnObjectReturned__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnObjectSpawned__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnPlayerDied__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnPrestigeLevelUp__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnRewindChargesChanged__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnRunStarted__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnSectorChanged__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnTileActivated__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnTileCompleted__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnTileDeactivated__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnUnlockAchieved__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnUpgradePurchased__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnVisualStateChanged__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnWeaponChanged__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnWeaponFired__DelegateSignature();
	ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnWeaponReloaded__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_RoughReality;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_RoughReality()
	{
		if (!Z_Registration_Info_UPackage__Script_RoughReality.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_GameEventDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_GameEventMulticastDelegate__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnAmmoChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnAnalyticsEvent__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnAssetLoaded__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnAssetLoadFailed__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnBulletTimeChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnDifficultyScaled__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnGameStateChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnHealthChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerationFailed__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnLoadingComplete__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnLoadingProgress__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnObjectReturned__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnObjectSpawned__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnPlayerDied__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnPrestigeLevelUp__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnRewindChargesChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnRunStarted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnSectorChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnTileActivated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnTileCompleted__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnTileDeactivated__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnUnlockAchieved__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnUpgradePurchased__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnVisualStateChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnWeaponChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnWeaponFired__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_RoughReality_OnWeaponReloaded__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/RoughReality",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x72CD6BAF,
				0x6095DC93,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_RoughReality.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_RoughReality.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_RoughReality(Z_Construct_UPackage__Script_RoughReality, TEXT("/Script/RoughReality"), Z_Registration_Info_UPackage__Script_RoughReality, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x72CD6BAF, 0x6095DC93));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
