// Copyright Epic Games, Inc. All Rights Reserved.

#include "ProceduralLevelBuilder.h"
#include "Engine/World.h"
#include "Kismet/KismetMathLibrary.h"
#include "../DataAssets/TileDefinitionDataAsset.h"
#include "LevelTile.h"

AProceduralLevelBuilder::AProceduralLevelBuilder()
{
	PrimaryActorTick.bCanEverTick = false;

	// Initialize default values
	CurrentSector = 0;
	CurrentLevel = 0;
	RandomSeed = 0;
	bUseRandomSeed = true;
	DefaultTileSpacing = 2000.0f;
	LevelOrigin = FVector::ZeroVector;
	bIsGenerating = false;

	// Initialize random stream
	if (bUseRandomSeed)
	{
		RandomSeed = FMath::Rand();
	}
	RandomStream.Initialize(RandomSeed);
}

void AProceduralLevelBuilder::BeginPlay()
{
	Super::BeginPlay();

	// Initialize tile type cache for performance
	TileTypeCache.Empty();
	
	UE_LOG(LogTemp, Log, TEXT("Procedural Level Builder initialized with seed: %d"), RandomSeed);
}

bool AProceduralLevelBuilder::GenerateLevel(int32 SectorIndex, int32 LevelIndex)
{
	if (bIsGenerating)
	{
		UE_LOG(LogTemp, Warning, TEXT("Level generation already in progress"));
		return false;
	}

	if (!IsSectorValid(SectorIndex))
	{
		FString ErrorMsg = FString::Printf(TEXT("Invalid sector index: %d"), SectorIndex);
		UE_LOG(LogTemp, Error, TEXT("%s"), *ErrorMsg);
		OnLevelGenerationFailed.Broadcast(ErrorMsg);
		return false;
	}

	bIsGenerating = true;
	CurrentSector = SectorIndex;
	CurrentLevel = LevelIndex;

	// Clear existing level
	ClearCurrentLevel();

	// Get sector configuration
	FLevelSectorConfiguration SectorConfig = GetSectorConfiguration(SectorIndex);

	// Generate the level
	bool bSuccess = GenerateSectorLevel(SectorConfig, LevelIndex);

	bIsGenerating = false;

	if (bSuccess)
	{
		OnLevelGenerated.Broadcast(SectorIndex, LevelIndex);
		UE_LOG(LogTemp, Log, TEXT("Level generated successfully: Sector %d, Level %d"), SectorIndex, LevelIndex);
	}
	else
	{
		FString ErrorMsg = FString::Printf(TEXT("Failed to generate level: Sector %d, Level %d"), SectorIndex, LevelIndex);
		OnLevelGenerationFailed.Broadcast(ErrorMsg);
	}

	return bSuccess;
}

void AProceduralLevelBuilder::ClearCurrentLevel()
{
	// Destroy all spawned tiles
	for (ALevelTile* Tile : SpawnedTiles)
	{
		if (IsValid(Tile))
		{
			Tile->Destroy();
		}
	}
	SpawnedTiles.Empty();

	UE_LOG(LogTemp, Log, TEXT("Current level cleared"));
}

void AProceduralLevelBuilder::RegenerateCurrentLevel()
{
	GenerateLevel(CurrentSector, CurrentLevel);
}

bool AProceduralLevelBuilder::IsSectorValid(int32 SectorIndex) const
{
	return SectorIndex >= 0 && SectorIndex < SectorConfigurations.Num();
}

FLevelSectorConfiguration AProceduralLevelBuilder::GetSectorConfiguration(int32 SectorIndex) const
{
	if (IsSectorValid(SectorIndex))
	{
		return SectorConfigurations[SectorIndex];
	}
	return FLevelSectorConfiguration();
}

int32 AProceduralLevelBuilder::GetSectorCount() const
{
	return SectorConfigurations.Num();
}

ALevelTile* AProceduralLevelBuilder::SpawnTile(UTileDefinitionDataAsset* TileDefinition, const FVector& Location, const FRotator& Rotation)
{
	if (!TileDefinition)
	{
		UE_LOG(LogTemp, Error, TEXT("Cannot spawn tile: TileDefinition is null"));
		return nullptr;
	}

	// Spawn the tile actor
	FActorSpawnParameters SpawnParams;
	SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

	ALevelTile* NewTile = GetWorld()->SpawnActor<ALevelTile>(ALevelTile::StaticClass(), Location, Rotation, SpawnParams);
	
	if (NewTile)
	{
		// Initialize the tile with its definition
		NewTile->InitializeTile(TileDefinition);
		SpawnedTiles.Add(NewTile);
		
		UE_LOG(LogTemp, Log, TEXT("Spawned tile: %s at location %s"), 
			*TileDefinition->GetAssetIDString(), *Location.ToString());
	}

	return NewTile;
}

void AProceduralLevelBuilder::DestroyTile(ALevelTile* Tile)
{
	if (IsValid(Tile))
	{
		SpawnedTiles.Remove(Tile);
		Tile->Destroy();
	}
}

TArray<ALevelTile*> AProceduralLevelBuilder::GetTilesByType(ETileType TileType) const
{
	TArray<ALevelTile*> FilteredTiles;
	
	for (ALevelTile* Tile : SpawnedTiles)
	{
		if (IsValid(Tile) && Tile->GetTileType() == TileType)
		{
			FilteredTiles.Add(Tile);
		}
	}
	
	return FilteredTiles;
}

FVector AProceduralLevelBuilder::CalculateTilePosition(int32 TileIndex, float Spacing) const
{
	// Simple linear layout for now
	return LevelOrigin + FVector(TileIndex * Spacing, 0.0f, 0.0f);
}

void AProceduralLevelBuilder::SetRandomSeed(int32 NewSeed)
{
	RandomSeed = NewSeed;
	RandomStream.Initialize(RandomSeed);
	UE_LOG(LogTemp, Log, TEXT("Random seed set to: %d"), RandomSeed);
}

int32 AProceduralLevelBuilder::GetCurrentRandomSeed() const
{
	return RandomSeed;
}

bool AProceduralLevelBuilder::ValidateLevel() const
{
	// Basic validation - ensure we have tiles
	if (SpawnedTiles.Num() == 0)
	{
		UE_LOG(LogTemp, Warning, TEXT("Level validation failed: No tiles spawned"));
		return false;
	}

	// Check for start and end tiles
	bool bHasStart = GetTilesByType(ETileType::Start).Num() > 0;
	bool bHasEnd = GetTilesByType(ETileType::Boss).Num() > 0 || GetTilesByType(ETileType::Transition).Num() > 0;

	if (!bHasStart)
	{
		UE_LOG(LogTemp, Warning, TEXT("Level validation failed: No start tile found"));
		return false;
	}

	if (!bHasEnd)
	{
		UE_LOG(LogTemp, Warning, TEXT("Level validation failed: No end tile found"));
		return false;
	}

	return true;
}

bool AProceduralLevelBuilder::ValidateSectorConfiguration(const FLevelSectorConfiguration& SectorConfig) const
{
	if (SectorConfig.AvailableTiles.Num() == 0)
	{
		UE_LOG(LogTemp, Error, TEXT("Sector configuration invalid: No available tiles"));
		return false;
	}

	if (SectorConfig.MinTiles <= 0 || SectorConfig.MaxTiles <= 0)
	{
		UE_LOG(LogTemp, Error, TEXT("Sector configuration invalid: Invalid tile count limits"));
		return false;
	}

	if (SectorConfig.MinTiles > SectorConfig.MaxTiles)
	{
		UE_LOG(LogTemp, Error, TEXT("Sector configuration invalid: MinTiles > MaxTiles"));
		return false;
	}

	return true;
}

bool AProceduralLevelBuilder::GenerateSectorLevel(const FLevelSectorConfiguration& SectorConfig, int32 LevelIndex)
{
	if (!ValidateSectorConfiguration(SectorConfig))
	{
		return false;
	}

	// Plan the tile layout
	TArray<FTileSpawnInfo> TileLayout = PlanTileLayout(SectorConfig, LevelIndex);
	
	if (TileLayout.Num() == 0)
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to plan tile layout"));
		return false;
	}

	// Spawn the planned tiles
	bool bSuccess = SpawnPlannedTiles(TileLayout);
	
	if (bSuccess)
	{
		bSuccess = ValidateLevel();
	}

	return bSuccess;
}

TArray<FTileSpawnInfo> AProceduralLevelBuilder::PlanTileLayout(const FLevelSectorConfiguration& SectorConfig, int32 LevelIndex)
{
	TArray<FTileSpawnInfo> PlannedTiles;

	// Determine number of tiles to generate
	int32 TileCount = RandomStream.RandRange(SectorConfig.MinTiles, SectorConfig.MaxTiles);
	
	// Generate positions
	TArray<FVector> TilePositions = GenerateLinearLayout(TileCount, SectorConfig.TileSpacing);

	// Add fixed tiles first
	for (const FTileSpawnInfo& FixedTile : SectorConfig.FixedTiles)
	{
		PlannedTiles.Add(FixedTile);
	}

	// Fill remaining positions with random tiles
	for (int32 i = PlannedTiles.Num(); i < TileCount && i < TilePositions.Num(); i++)
	{
		FTileSpawnInfo NewTile;
		NewTile.Location = TilePositions[i];
		NewTile.Rotation = FRotator::ZeroRotator;
		NewTile.TileType = ETileType::Combat; // Default to combat
		NewTile.bIsFixed = false;
		
		// Select appropriate tile type based on position
		if (i == 0)
		{
			NewTile.TileType = ETileType::Start;
		}
		else if (i == TileCount - 1)
		{
			NewTile.TileType = ETileType::Boss;
		}
		else if (i == TileCount / 2)
		{
			NewTile.TileType = ETileType::Shop;
		}

		// Select random tile of the appropriate type
		NewTile.TileDefinition = SelectRandomTile(SectorConfig.AvailableTiles, NewTile.TileType);
		
		if (NewTile.TileDefinition)
		{
			PlannedTiles.Add(NewTile);
		}
	}

	return PlannedTiles;
}

bool AProceduralLevelBuilder::SpawnPlannedTiles(const TArray<FTileSpawnInfo>& TileLayout)
{
	bool bAllSuccessful = true;

	for (const FTileSpawnInfo& TileInfo : TileLayout)
	{
		ALevelTile* SpawnedTile = SpawnTile(TileInfo.TileDefinition, TileInfo.Location, TileInfo.Rotation);
		if (!SpawnedTile)
		{
			bAllSuccessful = false;
			UE_LOG(LogTemp, Error, TEXT("Failed to spawn tile at location %s"), *TileInfo.Location.ToString());
		}
	}

	return bAllSuccessful;
}

UTileDefinitionDataAsset* AProceduralLevelBuilder::SelectRandomTile(const TArray<UTileDefinitionDataAsset*>& AvailableTiles, ETileType DesiredType) const
{
	TArray<UTileDefinitionDataAsset*> FilteredTiles = FilterTilesByType(AvailableTiles, DesiredType);
	
	if (FilteredTiles.Num() == 0)
	{
		// Fallback to any available tile
		FilteredTiles = AvailableTiles;
	}

	if (FilteredTiles.Num() > 0)
	{
		int32 RandomIndex = RandomStream.RandRange(0, FilteredTiles.Num() - 1);
		return FilteredTiles[RandomIndex];
	}

	return nullptr;
}

TArray<UTileDefinitionDataAsset*> AProceduralLevelBuilder::FilterTilesByType(const TArray<UTileDefinitionDataAsset*>& Tiles, ETileType TileType) const
{
	TArray<UTileDefinitionDataAsset*> FilteredTiles;
	
	for (UTileDefinitionDataAsset* Tile : Tiles)
	{
		if (Tile && Tile->TileType == TileType)
		{
			FilteredTiles.Add(Tile);
		}
	}
	
	return FilteredTiles;
}

TArray<FVector> AProceduralLevelBuilder::GenerateLinearLayout(int32 TileCount, float Spacing) const
{
	TArray<FVector> Positions;
	
	for (int32 i = 0; i < TileCount; i++)
	{
		FVector Position = LevelOrigin + FVector(i * Spacing, 0.0f, 0.0f);
		Positions.Add(Position);
	}
	
	return Positions;
}
