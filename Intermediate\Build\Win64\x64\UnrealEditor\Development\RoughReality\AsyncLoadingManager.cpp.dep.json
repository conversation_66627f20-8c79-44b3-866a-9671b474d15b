{"Version": "1.2", "Data": {"Source": "g:\\gamedev\\roughreality\\source\\roughreality\\core\\asyncloadingmanager.cpp", "ProvidedModule": "", "PCH": "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\x64\\roughrealityeditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.cpp20.h.pch", "Includes": ["g:\\gamedev\\roughreality\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\roughreality\\definitions.roughreality.h", "g:\\gamedev\\roughreality\\source\\roughreality\\core\\asyncloadingmanager.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\engine\\streamablemanager.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\coreuobject\\public\\misc\\packageaccesstracking.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\sourcelocation.h", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.38.33130\\include\\source_location", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\sourcelocationutils.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\engine\\assetmanager.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\engine\\assetmanagertypes.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\assetmanagertypes.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\coreuobject\\public\\misc\\assetregistryinterface.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\assetregistrymodule.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\iassetregistry.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\assetregistry\\uht\\iassetregistry.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformchunkinstall.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\engine\\contentencryptionconfig.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\assetmanager.generated.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\asyncloadingmanager.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystatics.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetsystemlibrary.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\kismetsystemlibrary.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\sound\\dialoguetypes.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\dialoguetypes.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystaticstypes.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystaticstypes.generated.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystatics.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\public\\engineutils.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}