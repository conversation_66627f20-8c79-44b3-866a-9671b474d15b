// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RoughReality/RoughRealityCharacter.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeRoughRealityCharacter() {}

// Begin Cross Module References
ROUGHREALITY_API UClass* Z_Construct_UClass_ADEPRECATED_ARoughRealityCharacter();
ROUGHREALITY_API UClass* Z_Construct_UClass_ADEPRECATED_ARoughRealityCharacter_NoRegister();
ROUGHREALITY_API UClass* Z_Construct_UClass_ARookieCharacter();
UPackage* Z_Construct_UPackage__Script_RoughReality();
// End Cross Module References

// Begin Class ADEPRECATED_ARoughRealityCharacter
void ADEPRECATED_ARoughRealityCharacter::StaticRegisterNativesADEPRECATED_ARoughRealityCharacter()
{
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(ADEPRECATED_ARoughRealityCharacter);
UClass* Z_Construct_UClass_ADEPRECATED_ARoughRealityCharacter_NoRegister()
{
	return ADEPRECATED_ARoughRealityCharacter::StaticClass();
}
struct Z_Construct_UClass_ADEPRECATED_ARoughRealityCharacter_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Legacy character class - redirects to the enhanced RookieCharacter\n * This maintains compatibility with existing Blueprint references\n */" },
#endif
		{ "HideCategories", "Navigation" },
		{ "IncludePath", "RoughRealityCharacter.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "RoughRealityCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Legacy character class - redirects to the enhanced RookieCharacter\nThis maintains compatibility with existing Blueprint references" },
#endif
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ADEPRECATED_ARoughRealityCharacter>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_ADEPRECATED_ARoughRealityCharacter_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_ARookieCharacter,
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ADEPRECATED_ARoughRealityCharacter_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ADEPRECATED_ARoughRealityCharacter_Statics::ClassParams = {
	&ADEPRECATED_ARoughRealityCharacter::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	0,
	0,
	0x028002A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ADEPRECATED_ARoughRealityCharacter_Statics::Class_MetaDataParams), Z_Construct_UClass_ADEPRECATED_ARoughRealityCharacter_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ADEPRECATED_ARoughRealityCharacter()
{
	if (!Z_Registration_Info_UClass_ADEPRECATED_ARoughRealityCharacter.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ADEPRECATED_ARoughRealityCharacter.OuterSingleton, Z_Construct_UClass_ADEPRECATED_ARoughRealityCharacter_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ADEPRECATED_ARoughRealityCharacter.OuterSingleton;
}
template<> ROUGHREALITY_API UClass* StaticClass<ADEPRECATED_ARoughRealityCharacter>()
{
	return ADEPRECATED_ARoughRealityCharacter::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ADEPRECATED_ARoughRealityCharacter);
ADEPRECATED_ARoughRealityCharacter::~ADEPRECATED_ARoughRealityCharacter() {}
// End Class ADEPRECATED_ARoughRealityCharacter

// Begin Registration
struct Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_RoughRealityCharacter_h_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ADEPRECATED_ARoughRealityCharacter, ADEPRECATED_ARoughRealityCharacter::StaticClass, TEXT("ADEPRECATED_ARoughRealityCharacter"), &Z_Registration_Info_UClass_ADEPRECATED_ARoughRealityCharacter, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ADEPRECATED_ARoughRealityCharacter), 2989811079U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_RoughRealityCharacter_h_1547833804(TEXT("/Script/RoughReality"),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_RoughRealityCharacter_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_RoughRealityCharacter_h_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
