// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RoughReality/Characters/RookieCharacter.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeRookieCharacter() {}

// Begin Cross Module References
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_ACharacter();
ENGINE_API UClass* Z_Construct_UClass_UCameraComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UCurveFloat_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USpringArmComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTimelineComponent_NoRegister();
ENHANCEDINPUT_API UClass* Z_Construct_UClass_UInputAction_NoRegister();
ENHANCEDINPUT_API UClass* Z_Construct_UClass_UInputMappingContext_NoRegister();
ROUGHREALITY_API UClass* Z_Construct_UClass_ARookieCharacter();
ROUGHREALITY_API UClass* Z_Construct_UClass_ARookieCharacter_NoRegister();
ROUGHREALITY_API UClass* Z_Construct_UClass_AWeaponBase_NoRegister();
ROUGHREALITY_API UClass* Z_Construct_UClass_UCharacterStatsDataAsset_NoRegister();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnBulletTimeChanged__DelegateSignature();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnHealthChanged__DelegateSignature();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnRewindChargesChanged__DelegateSignature();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnWeaponChanged__DelegateSignature();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FTimeSnapshot();
UPackage* Z_Construct_UPackage__Script_RoughReality();
// End Cross Module References

// Begin Delegate FOnHealthChanged
struct Z_Construct_UDelegateFunction_RoughReality_OnHealthChanged__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnHealthChanged_Parms
	{
		float NewHealthPercent;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewHealthPercent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnHealthChanged__DelegateSignature_Statics::NewProp_NewHealthPercent = { "NewHealthPercent", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnHealthChanged_Parms, NewHealthPercent), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnHealthChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnHealthChanged__DelegateSignature_Statics::NewProp_NewHealthPercent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnHealthChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnHealthChanged__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnHealthChanged__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnHealthChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnHealthChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnHealthChanged__DelegateSignature_Statics::_Script_RoughReality_eventOnHealthChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnHealthChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnHealthChanged__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnHealthChanged__DelegateSignature_Statics::_Script_RoughReality_eventOnHealthChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnHealthChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnHealthChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnHealthChanged_DelegateWrapper(const FMulticastScriptDelegate& OnHealthChanged, float NewHealthPercent)
{
	struct _Script_RoughReality_eventOnHealthChanged_Parms
	{
		float NewHealthPercent;
	};
	_Script_RoughReality_eventOnHealthChanged_Parms Parms;
	Parms.NewHealthPercent=NewHealthPercent;
	OnHealthChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnHealthChanged

// Begin Delegate FOnBulletTimeChanged
struct Z_Construct_UDelegateFunction_RoughReality_OnBulletTimeChanged__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnBulletTimeChanged_Parms
	{
		float NewBulletTimePercent;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewBulletTimePercent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnBulletTimeChanged__DelegateSignature_Statics::NewProp_NewBulletTimePercent = { "NewBulletTimePercent", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnBulletTimeChanged_Parms, NewBulletTimePercent), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnBulletTimeChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnBulletTimeChanged__DelegateSignature_Statics::NewProp_NewBulletTimePercent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnBulletTimeChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnBulletTimeChanged__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnBulletTimeChanged__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnBulletTimeChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnBulletTimeChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnBulletTimeChanged__DelegateSignature_Statics::_Script_RoughReality_eventOnBulletTimeChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnBulletTimeChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnBulletTimeChanged__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnBulletTimeChanged__DelegateSignature_Statics::_Script_RoughReality_eventOnBulletTimeChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnBulletTimeChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnBulletTimeChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnBulletTimeChanged_DelegateWrapper(const FMulticastScriptDelegate& OnBulletTimeChanged, float NewBulletTimePercent)
{
	struct _Script_RoughReality_eventOnBulletTimeChanged_Parms
	{
		float NewBulletTimePercent;
	};
	_Script_RoughReality_eventOnBulletTimeChanged_Parms Parms;
	Parms.NewBulletTimePercent=NewBulletTimePercent;
	OnBulletTimeChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnBulletTimeChanged

// Begin Delegate FOnRewindChargesChanged
struct Z_Construct_UDelegateFunction_RoughReality_OnRewindChargesChanged__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnRewindChargesChanged_Parms
	{
		int32 NewCharges;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewCharges;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnRewindChargesChanged__DelegateSignature_Statics::NewProp_NewCharges = { "NewCharges", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnRewindChargesChanged_Parms, NewCharges), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnRewindChargesChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnRewindChargesChanged__DelegateSignature_Statics::NewProp_NewCharges,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnRewindChargesChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnRewindChargesChanged__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnRewindChargesChanged__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnRewindChargesChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnRewindChargesChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnRewindChargesChanged__DelegateSignature_Statics::_Script_RoughReality_eventOnRewindChargesChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnRewindChargesChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnRewindChargesChanged__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnRewindChargesChanged__DelegateSignature_Statics::_Script_RoughReality_eventOnRewindChargesChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnRewindChargesChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnRewindChargesChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnRewindChargesChanged_DelegateWrapper(const FMulticastScriptDelegate& OnRewindChargesChanged, int32 NewCharges)
{
	struct _Script_RoughReality_eventOnRewindChargesChanged_Parms
	{
		int32 NewCharges;
	};
	_Script_RoughReality_eventOnRewindChargesChanged_Parms Parms;
	Parms.NewCharges=NewCharges;
	OnRewindChargesChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnRewindChargesChanged

// Begin Delegate FOnWeaponChanged
struct Z_Construct_UDelegateFunction_RoughReality_OnWeaponChanged__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnWeaponChanged_Parms
	{
		AWeaponBase* NewWeapon;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NewWeapon;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnWeaponChanged__DelegateSignature_Statics::NewProp_NewWeapon = { "NewWeapon", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnWeaponChanged_Parms, NewWeapon), Z_Construct_UClass_AWeaponBase_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnWeaponChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnWeaponChanged__DelegateSignature_Statics::NewProp_NewWeapon,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnWeaponChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnWeaponChanged__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnWeaponChanged__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnWeaponChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnWeaponChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnWeaponChanged__DelegateSignature_Statics::_Script_RoughReality_eventOnWeaponChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnWeaponChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnWeaponChanged__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnWeaponChanged__DelegateSignature_Statics::_Script_RoughReality_eventOnWeaponChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnWeaponChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnWeaponChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnWeaponChanged_DelegateWrapper(const FMulticastScriptDelegate& OnWeaponChanged, AWeaponBase* NewWeapon)
{
	struct _Script_RoughReality_eventOnWeaponChanged_Parms
	{
		AWeaponBase* NewWeapon;
	};
	_Script_RoughReality_eventOnWeaponChanged_Parms Parms;
	Parms.NewWeapon=NewWeapon;
	OnWeaponChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnWeaponChanged

// Begin ScriptStruct FTimeSnapshot
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_TimeSnapshot;
class UScriptStruct* FTimeSnapshot::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_TimeSnapshot.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_TimeSnapshot.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FTimeSnapshot, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("TimeSnapshot"));
	}
	return Z_Registration_Info_UScriptStruct_TimeSnapshot.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FTimeSnapshot>()
{
	return FTimeSnapshot::StaticStruct();
}
struct Z_Construct_UScriptStruct_FTimeSnapshot_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "Category", "Time Rewind" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "Category", "Time Rewind" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Velocity_MetaData[] = {
		{ "Category", "Time Rewind" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Health_MetaData[] = {
		{ "Category", "Time Rewind" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Timestamp_MetaData[] = {
		{ "Category", "Time Rewind" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Velocity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Health;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Timestamp;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FTimeSnapshot>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTimeSnapshot_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTimeSnapshot, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTimeSnapshot_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTimeSnapshot, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTimeSnapshot_Statics::NewProp_Velocity = { "Velocity", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTimeSnapshot, Velocity), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Velocity_MetaData), NewProp_Velocity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTimeSnapshot_Statics::NewProp_Health = { "Health", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTimeSnapshot, Health), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Health_MetaData), NewProp_Health_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTimeSnapshot_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTimeSnapshot, Timestamp), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Timestamp_MetaData), NewProp_Timestamp_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FTimeSnapshot_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTimeSnapshot_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTimeSnapshot_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTimeSnapshot_Statics::NewProp_Velocity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTimeSnapshot_Statics::NewProp_Health,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTimeSnapshot_Statics::NewProp_Timestamp,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTimeSnapshot_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FTimeSnapshot_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	&NewStructOps,
	"TimeSnapshot",
	Z_Construct_UScriptStruct_FTimeSnapshot_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTimeSnapshot_Statics::PropPointers),
	sizeof(FTimeSnapshot),
	alignof(FTimeSnapshot),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTimeSnapshot_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FTimeSnapshot_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FTimeSnapshot()
{
	if (!Z_Registration_Info_UScriptStruct_TimeSnapshot.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_TimeSnapshot.InnerSingleton, Z_Construct_UScriptStruct_FTimeSnapshot_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_TimeSnapshot.InnerSingleton;
}
// End ScriptStruct FTimeSnapshot

// Begin Class ARookieCharacter Function ActivateBulletTime
struct Z_Construct_UFunction_ARookieCharacter_ActivateBulletTime_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Bullet Time" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Bullet Time */" },
#endif
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Bullet Time" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARookieCharacter_ActivateBulletTime_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARookieCharacter, nullptr, "ActivateBulletTime", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_ActivateBulletTime_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARookieCharacter_ActivateBulletTime_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ARookieCharacter_ActivateBulletTime()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARookieCharacter_ActivateBulletTime_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARookieCharacter::execActivateBulletTime)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ActivateBulletTime();
	P_NATIVE_END;
}
// End Class ARookieCharacter Function ActivateBulletTime

// Begin Class ARookieCharacter Function AddRewindCharge
struct Z_Construct_UFunction_ARookieCharacter_AddRewindCharge_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Time Rewind" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARookieCharacter_AddRewindCharge_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARookieCharacter, nullptr, "AddRewindCharge", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_AddRewindCharge_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARookieCharacter_AddRewindCharge_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ARookieCharacter_AddRewindCharge()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARookieCharacter_AddRewindCharge_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARookieCharacter::execAddRewindCharge)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddRewindCharge();
	P_NATIVE_END;
}
// End Class ARookieCharacter Function AddRewindCharge

// Begin Class ARookieCharacter Function AddWeaponToInventory
struct Z_Construct_UFunction_ARookieCharacter_AddWeaponToInventory_Statics
{
	struct RookieCharacter_eventAddWeaponToInventory_Parms
	{
		AWeaponBase* Weapon;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapons" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Weapon;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARookieCharacter_AddWeaponToInventory_Statics::NewProp_Weapon = { "Weapon", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RookieCharacter_eventAddWeaponToInventory_Parms, Weapon), Z_Construct_UClass_AWeaponBase_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARookieCharacter_AddWeaponToInventory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARookieCharacter_AddWeaponToInventory_Statics::NewProp_Weapon,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_AddWeaponToInventory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARookieCharacter_AddWeaponToInventory_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARookieCharacter, nullptr, "AddWeaponToInventory", nullptr, nullptr, Z_Construct_UFunction_ARookieCharacter_AddWeaponToInventory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_AddWeaponToInventory_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARookieCharacter_AddWeaponToInventory_Statics::RookieCharacter_eventAddWeaponToInventory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_AddWeaponToInventory_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARookieCharacter_AddWeaponToInventory_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ARookieCharacter_AddWeaponToInventory_Statics::RookieCharacter_eventAddWeaponToInventory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARookieCharacter_AddWeaponToInventory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARookieCharacter_AddWeaponToInventory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARookieCharacter::execAddWeaponToInventory)
{
	P_GET_OBJECT(AWeaponBase,Z_Param_Weapon);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddWeaponToInventory(Z_Param_Weapon);
	P_NATIVE_END;
}
// End Class ARookieCharacter Function AddWeaponToInventory

// Begin Class ARookieCharacter Function CanPerformDash
struct Z_Construct_UFunction_ARookieCharacter_CanPerformDash_Statics
{
	struct RookieCharacter_eventCanPerformDash_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dash" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARookieCharacter_CanPerformDash_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RookieCharacter_eventCanPerformDash_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARookieCharacter_CanPerformDash_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RookieCharacter_eventCanPerformDash_Parms), &Z_Construct_UFunction_ARookieCharacter_CanPerformDash_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARookieCharacter_CanPerformDash_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARookieCharacter_CanPerformDash_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_CanPerformDash_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARookieCharacter_CanPerformDash_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARookieCharacter, nullptr, "CanPerformDash", nullptr, nullptr, Z_Construct_UFunction_ARookieCharacter_CanPerformDash_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_CanPerformDash_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARookieCharacter_CanPerformDash_Statics::RookieCharacter_eventCanPerformDash_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_CanPerformDash_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARookieCharacter_CanPerformDash_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ARookieCharacter_CanPerformDash_Statics::RookieCharacter_eventCanPerformDash_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARookieCharacter_CanPerformDash()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARookieCharacter_CanPerformDash_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARookieCharacter::execCanPerformDash)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanPerformDash();
	P_NATIVE_END;
}
// End Class ARookieCharacter Function CanPerformDash

// Begin Class ARookieCharacter Function CanRewind
struct Z_Construct_UFunction_ARookieCharacter_CanRewind_Statics
{
	struct RookieCharacter_eventCanRewind_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Time Rewind" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARookieCharacter_CanRewind_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RookieCharacter_eventCanRewind_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARookieCharacter_CanRewind_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RookieCharacter_eventCanRewind_Parms), &Z_Construct_UFunction_ARookieCharacter_CanRewind_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARookieCharacter_CanRewind_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARookieCharacter_CanRewind_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_CanRewind_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARookieCharacter_CanRewind_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARookieCharacter, nullptr, "CanRewind", nullptr, nullptr, Z_Construct_UFunction_ARookieCharacter_CanRewind_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_CanRewind_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARookieCharacter_CanRewind_Statics::RookieCharacter_eventCanRewind_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_CanRewind_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARookieCharacter_CanRewind_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ARookieCharacter_CanRewind_Statics::RookieCharacter_eventCanRewind_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARookieCharacter_CanRewind()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARookieCharacter_CanRewind_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARookieCharacter::execCanRewind)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanRewind();
	P_NATIVE_END;
}
// End Class ARookieCharacter Function CanRewind

// Begin Class ARookieCharacter Function CanUseBulletTime
struct Z_Construct_UFunction_ARookieCharacter_CanUseBulletTime_Statics
{
	struct RookieCharacter_eventCanUseBulletTime_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Bullet Time" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARookieCharacter_CanUseBulletTime_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RookieCharacter_eventCanUseBulletTime_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARookieCharacter_CanUseBulletTime_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RookieCharacter_eventCanUseBulletTime_Parms), &Z_Construct_UFunction_ARookieCharacter_CanUseBulletTime_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARookieCharacter_CanUseBulletTime_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARookieCharacter_CanUseBulletTime_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_CanUseBulletTime_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARookieCharacter_CanUseBulletTime_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARookieCharacter, nullptr, "CanUseBulletTime", nullptr, nullptr, Z_Construct_UFunction_ARookieCharacter_CanUseBulletTime_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_CanUseBulletTime_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARookieCharacter_CanUseBulletTime_Statics::RookieCharacter_eventCanUseBulletTime_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_CanUseBulletTime_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARookieCharacter_CanUseBulletTime_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ARookieCharacter_CanUseBulletTime_Statics::RookieCharacter_eventCanUseBulletTime_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARookieCharacter_CanUseBulletTime()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARookieCharacter_CanUseBulletTime_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARookieCharacter::execCanUseBulletTime)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanUseBulletTime();
	P_NATIVE_END;
}
// End Class ARookieCharacter Function CanUseBulletTime

// Begin Class ARookieCharacter Function DashTimelineFinished
struct Z_Construct_UFunction_ARookieCharacter_DashTimelineFinished_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARookieCharacter_DashTimelineFinished_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARookieCharacter, nullptr, "DashTimelineFinished", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_DashTimelineFinished_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARookieCharacter_DashTimelineFinished_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ARookieCharacter_DashTimelineFinished()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARookieCharacter_DashTimelineFinished_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARookieCharacter::execDashTimelineFinished)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DashTimelineFinished();
	P_NATIVE_END;
}
// End Class ARookieCharacter Function DashTimelineFinished

// Begin Class ARookieCharacter Function DashTimelineUpdate
struct Z_Construct_UFunction_ARookieCharacter_DashTimelineUpdate_Statics
{
	struct RookieCharacter_eventDashTimelineUpdate_Parms
	{
		float Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timeline Functions */" },
#endif
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timeline Functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARookieCharacter_DashTimelineUpdate_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RookieCharacter_eventDashTimelineUpdate_Parms, Value), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARookieCharacter_DashTimelineUpdate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARookieCharacter_DashTimelineUpdate_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_DashTimelineUpdate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARookieCharacter_DashTimelineUpdate_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARookieCharacter, nullptr, "DashTimelineUpdate", nullptr, nullptr, Z_Construct_UFunction_ARookieCharacter_DashTimelineUpdate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_DashTimelineUpdate_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARookieCharacter_DashTimelineUpdate_Statics::RookieCharacter_eventDashTimelineUpdate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_DashTimelineUpdate_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARookieCharacter_DashTimelineUpdate_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ARookieCharacter_DashTimelineUpdate_Statics::RookieCharacter_eventDashTimelineUpdate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARookieCharacter_DashTimelineUpdate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARookieCharacter_DashTimelineUpdate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARookieCharacter::execDashTimelineUpdate)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DashTimelineUpdate(Z_Param_Value);
	P_NATIVE_END;
}
// End Class ARookieCharacter Function DashTimelineUpdate

// Begin Class ARookieCharacter Function DeactivateBulletTime
struct Z_Construct_UFunction_ARookieCharacter_DeactivateBulletTime_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Bullet Time" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARookieCharacter_DeactivateBulletTime_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARookieCharacter, nullptr, "DeactivateBulletTime", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_DeactivateBulletTime_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARookieCharacter_DeactivateBulletTime_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ARookieCharacter_DeactivateBulletTime()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARookieCharacter_DeactivateBulletTime_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARookieCharacter::execDeactivateBulletTime)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DeactivateBulletTime();
	P_NATIVE_END;
}
// End Class ARookieCharacter Function DeactivateBulletTime

// Begin Class ARookieCharacter Function EquipWeapon
struct Z_Construct_UFunction_ARookieCharacter_EquipWeapon_Statics
{
	struct RookieCharacter_eventEquipWeapon_Parms
	{
		AWeaponBase* NewWeapon;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapons" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Weapon Management */" },
#endif
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weapon Management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NewWeapon;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ARookieCharacter_EquipWeapon_Statics::NewProp_NewWeapon = { "NewWeapon", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RookieCharacter_eventEquipWeapon_Parms, NewWeapon), Z_Construct_UClass_AWeaponBase_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARookieCharacter_EquipWeapon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARookieCharacter_EquipWeapon_Statics::NewProp_NewWeapon,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_EquipWeapon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARookieCharacter_EquipWeapon_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARookieCharacter, nullptr, "EquipWeapon", nullptr, nullptr, Z_Construct_UFunction_ARookieCharacter_EquipWeapon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_EquipWeapon_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARookieCharacter_EquipWeapon_Statics::RookieCharacter_eventEquipWeapon_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_EquipWeapon_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARookieCharacter_EquipWeapon_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ARookieCharacter_EquipWeapon_Statics::RookieCharacter_eventEquipWeapon_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARookieCharacter_EquipWeapon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARookieCharacter_EquipWeapon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARookieCharacter::execEquipWeapon)
{
	P_GET_OBJECT(AWeaponBase,Z_Param_NewWeapon);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EquipWeapon(Z_Param_NewWeapon);
	P_NATIVE_END;
}
// End Class ARookieCharacter Function EquipWeapon

// Begin Class ARookieCharacter Function GetBulletTimePercent
struct Z_Construct_UFunction_ARookieCharacter_GetBulletTimePercent_Statics
{
	struct RookieCharacter_eventGetBulletTimePercent_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Bullet Time" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARookieCharacter_GetBulletTimePercent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RookieCharacter_eventGetBulletTimePercent_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARookieCharacter_GetBulletTimePercent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARookieCharacter_GetBulletTimePercent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_GetBulletTimePercent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARookieCharacter_GetBulletTimePercent_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARookieCharacter, nullptr, "GetBulletTimePercent", nullptr, nullptr, Z_Construct_UFunction_ARookieCharacter_GetBulletTimePercent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_GetBulletTimePercent_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARookieCharacter_GetBulletTimePercent_Statics::RookieCharacter_eventGetBulletTimePercent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_GetBulletTimePercent_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARookieCharacter_GetBulletTimePercent_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ARookieCharacter_GetBulletTimePercent_Statics::RookieCharacter_eventGetBulletTimePercent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARookieCharacter_GetBulletTimePercent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARookieCharacter_GetBulletTimePercent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARookieCharacter::execGetBulletTimePercent)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetBulletTimePercent();
	P_NATIVE_END;
}
// End Class ARookieCharacter Function GetBulletTimePercent

// Begin Class ARookieCharacter Function GetHealthPercent
struct Z_Construct_UFunction_ARookieCharacter_GetHealthPercent_Statics
{
	struct RookieCharacter_eventGetHealthPercent_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Health" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARookieCharacter_GetHealthPercent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RookieCharacter_eventGetHealthPercent_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARookieCharacter_GetHealthPercent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARookieCharacter_GetHealthPercent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_GetHealthPercent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARookieCharacter_GetHealthPercent_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARookieCharacter, nullptr, "GetHealthPercent", nullptr, nullptr, Z_Construct_UFunction_ARookieCharacter_GetHealthPercent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_GetHealthPercent_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARookieCharacter_GetHealthPercent_Statics::RookieCharacter_eventGetHealthPercent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_GetHealthPercent_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARookieCharacter_GetHealthPercent_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ARookieCharacter_GetHealthPercent_Statics::RookieCharacter_eventGetHealthPercent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARookieCharacter_GetHealthPercent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARookieCharacter_GetHealthPercent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARookieCharacter::execGetHealthPercent)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetHealthPercent();
	P_NATIVE_END;
}
// End Class ARookieCharacter Function GetHealthPercent

// Begin Class ARookieCharacter Function Heal
struct Z_Construct_UFunction_ARookieCharacter_Heal_Statics
{
	struct RookieCharacter_eventHeal_Parms
	{
		float HealAmount;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Health" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealAmount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARookieCharacter_Heal_Statics::NewProp_HealAmount = { "HealAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RookieCharacter_eventHeal_Parms, HealAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARookieCharacter_Heal_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARookieCharacter_Heal_Statics::NewProp_HealAmount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_Heal_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARookieCharacter_Heal_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARookieCharacter, nullptr, "Heal", nullptr, nullptr, Z_Construct_UFunction_ARookieCharacter_Heal_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_Heal_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARookieCharacter_Heal_Statics::RookieCharacter_eventHeal_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_Heal_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARookieCharacter_Heal_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ARookieCharacter_Heal_Statics::RookieCharacter_eventHeal_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARookieCharacter_Heal()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARookieCharacter_Heal_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARookieCharacter::execHeal)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_HealAmount);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Heal(Z_Param_HealAmount);
	P_NATIVE_END;
}
// End Class ARookieCharacter Function Heal

// Begin Class ARookieCharacter Function IsDead
struct Z_Construct_UFunction_ARookieCharacter_IsDead_Statics
{
	struct RookieCharacter_eventIsDead_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Health" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ARookieCharacter_IsDead_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RookieCharacter_eventIsDead_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ARookieCharacter_IsDead_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RookieCharacter_eventIsDead_Parms), &Z_Construct_UFunction_ARookieCharacter_IsDead_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARookieCharacter_IsDead_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARookieCharacter_IsDead_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_IsDead_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARookieCharacter_IsDead_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARookieCharacter, nullptr, "IsDead", nullptr, nullptr, Z_Construct_UFunction_ARookieCharacter_IsDead_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_IsDead_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARookieCharacter_IsDead_Statics::RookieCharacter_eventIsDead_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_IsDead_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARookieCharacter_IsDead_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ARookieCharacter_IsDead_Statics::RookieCharacter_eventIsDead_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARookieCharacter_IsDead()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARookieCharacter_IsDead_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARookieCharacter::execIsDead)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsDead();
	P_NATIVE_END;
}
// End Class ARookieCharacter Function IsDead

// Begin Class ARookieCharacter Function PerformDashMovement
struct Z_Construct_UFunction_ARookieCharacter_PerformDashMovement_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Dash" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dash */" },
#endif
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dash" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARookieCharacter_PerformDashMovement_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARookieCharacter, nullptr, "PerformDashMovement", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_PerformDashMovement_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARookieCharacter_PerformDashMovement_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ARookieCharacter_PerformDashMovement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARookieCharacter_PerformDashMovement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARookieCharacter::execPerformDashMovement)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PerformDashMovement();
	P_NATIVE_END;
}
// End Class ARookieCharacter Function PerformDashMovement

// Begin Class ARookieCharacter Function PerformTimeRewind
struct Z_Construct_UFunction_ARookieCharacter_PerformTimeRewind_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Time Rewind" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Time Rewind */" },
#endif
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Time Rewind" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARookieCharacter_PerformTimeRewind_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARookieCharacter, nullptr, "PerformTimeRewind", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_PerformTimeRewind_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARookieCharacter_PerformTimeRewind_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ARookieCharacter_PerformTimeRewind()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARookieCharacter_PerformTimeRewind_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARookieCharacter::execPerformTimeRewind)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PerformTimeRewind();
	P_NATIVE_END;
}
// End Class ARookieCharacter Function PerformTimeRewind

// Begin Class ARookieCharacter Function RecordPlayerAction
struct Z_Construct_UFunction_ARookieCharacter_RecordPlayerAction_Statics
{
	struct RookieCharacter_eventRecordPlayerAction_Parms
	{
		FString ActionType;
		TMap<FString,FString> Parameters;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Analytics Integration */" },
#endif
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Analytics Integration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Parameters;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_ARookieCharacter_RecordPlayerAction_Statics::NewProp_ActionType = { "ActionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RookieCharacter_eventRecordPlayerAction_Parms, ActionType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionType_MetaData), NewProp_ActionType_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_ARookieCharacter_RecordPlayerAction_Statics::NewProp_Parameters_ValueProp = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_ARookieCharacter_RecordPlayerAction_Statics::NewProp_Parameters_Key_KeyProp = { "Parameters_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_ARookieCharacter_RecordPlayerAction_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RookieCharacter_eventRecordPlayerAction_Parms, Parameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARookieCharacter_RecordPlayerAction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARookieCharacter_RecordPlayerAction_Statics::NewProp_ActionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARookieCharacter_RecordPlayerAction_Statics::NewProp_Parameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARookieCharacter_RecordPlayerAction_Statics::NewProp_Parameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARookieCharacter_RecordPlayerAction_Statics::NewProp_Parameters,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_RecordPlayerAction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARookieCharacter_RecordPlayerAction_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARookieCharacter, nullptr, "RecordPlayerAction", nullptr, nullptr, Z_Construct_UFunction_ARookieCharacter_RecordPlayerAction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_RecordPlayerAction_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARookieCharacter_RecordPlayerAction_Statics::RookieCharacter_eventRecordPlayerAction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_RecordPlayerAction_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARookieCharacter_RecordPlayerAction_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ARookieCharacter_RecordPlayerAction_Statics::RookieCharacter_eventRecordPlayerAction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARookieCharacter_RecordPlayerAction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARookieCharacter_RecordPlayerAction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARookieCharacter::execRecordPlayerAction)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActionType);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_Parameters);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RecordPlayerAction(Z_Param_ActionType,Z_Param_Out_Parameters);
	P_NATIVE_END;
}
// End Class ARookieCharacter Function RecordPlayerAction

// Begin Class ARookieCharacter Function RegisterWithGameSystems
struct Z_Construct_UFunction_ARookieCharacter_RegisterWithGameSystems_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Integration with Enhanced Systems */" },
#endif
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Integration with Enhanced Systems" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARookieCharacter_RegisterWithGameSystems_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARookieCharacter, nullptr, "RegisterWithGameSystems", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_RegisterWithGameSystems_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARookieCharacter_RegisterWithGameSystems_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ARookieCharacter_RegisterWithGameSystems()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARookieCharacter_RegisterWithGameSystems_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARookieCharacter::execRegisterWithGameSystems)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterWithGameSystems();
	P_NATIVE_END;
}
// End Class ARookieCharacter Function RegisterWithGameSystems

// Begin Class ARookieCharacter Function SetLODLevel
struct Z_Construct_UFunction_ARookieCharacter_SetLODLevel_Statics
{
	struct RookieCharacter_eventSetLODLevel_Parms
	{
		int32 LODLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_LODLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ARookieCharacter_SetLODLevel_Statics::NewProp_LODLevel = { "LODLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RookieCharacter_eventSetLODLevel_Parms, LODLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARookieCharacter_SetLODLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARookieCharacter_SetLODLevel_Statics::NewProp_LODLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_SetLODLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARookieCharacter_SetLODLevel_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARookieCharacter, nullptr, "SetLODLevel", nullptr, nullptr, Z_Construct_UFunction_ARookieCharacter_SetLODLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_SetLODLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARookieCharacter_SetLODLevel_Statics::RookieCharacter_eventSetLODLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_SetLODLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARookieCharacter_SetLODLevel_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ARookieCharacter_SetLODLevel_Statics::RookieCharacter_eventSetLODLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARookieCharacter_SetLODLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARookieCharacter_SetLODLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARookieCharacter::execSetLODLevel)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_LODLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetLODLevel(Z_Param_LODLevel);
	P_NATIVE_END;
}
// End Class ARookieCharacter Function SetLODLevel

// Begin Class ARookieCharacter Function SwitchToNextWeapon
struct Z_Construct_UFunction_ARookieCharacter_SwitchToNextWeapon_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapons" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARookieCharacter_SwitchToNextWeapon_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARookieCharacter, nullptr, "SwitchToNextWeapon", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_SwitchToNextWeapon_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARookieCharacter_SwitchToNextWeapon_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ARookieCharacter_SwitchToNextWeapon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARookieCharacter_SwitchToNextWeapon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARookieCharacter::execSwitchToNextWeapon)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SwitchToNextWeapon();
	P_NATIVE_END;
}
// End Class ARookieCharacter Function SwitchToNextWeapon

// Begin Class ARookieCharacter Function SwitchToPreviousWeapon
struct Z_Construct_UFunction_ARookieCharacter_SwitchToPreviousWeapon_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapons" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARookieCharacter_SwitchToPreviousWeapon_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARookieCharacter, nullptr, "SwitchToPreviousWeapon", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_SwitchToPreviousWeapon_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARookieCharacter_SwitchToPreviousWeapon_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ARookieCharacter_SwitchToPreviousWeapon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARookieCharacter_SwitchToPreviousWeapon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARookieCharacter::execSwitchToPreviousWeapon)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SwitchToPreviousWeapon();
	P_NATIVE_END;
}
// End Class ARookieCharacter Function SwitchToPreviousWeapon

// Begin Class ARookieCharacter Function TakeDamageSimple
struct Z_Construct_UFunction_ARookieCharacter_TakeDamageSimple_Statics
{
	struct RookieCharacter_eventTakeDamageSimple_Parms
	{
		float DamageAmount;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Health" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Simplified Blueprint-callable version\n" },
#endif
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Simplified Blueprint-callable version" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageAmount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ARookieCharacter_TakeDamageSimple_Statics::NewProp_DamageAmount = { "DamageAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RookieCharacter_eventTakeDamageSimple_Parms, DamageAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARookieCharacter_TakeDamageSimple_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARookieCharacter_TakeDamageSimple_Statics::NewProp_DamageAmount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_TakeDamageSimple_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARookieCharacter_TakeDamageSimple_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARookieCharacter, nullptr, "TakeDamageSimple", nullptr, nullptr, Z_Construct_UFunction_ARookieCharacter_TakeDamageSimple_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_TakeDamageSimple_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARookieCharacter_TakeDamageSimple_Statics::RookieCharacter_eventTakeDamageSimple_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_TakeDamageSimple_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARookieCharacter_TakeDamageSimple_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ARookieCharacter_TakeDamageSimple_Statics::RookieCharacter_eventTakeDamageSimple_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARookieCharacter_TakeDamageSimple()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARookieCharacter_TakeDamageSimple_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARookieCharacter::execTakeDamageSimple)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DamageAmount);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TakeDamageSimple(Z_Param_DamageAmount);
	P_NATIVE_END;
}
// End Class ARookieCharacter Function TakeDamageSimple

// Begin Class ARookieCharacter Function UnregisterFromGameSystems
struct Z_Construct_UFunction_ARookieCharacter_UnregisterFromGameSystems_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Integration" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARookieCharacter_UnregisterFromGameSystems_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARookieCharacter, nullptr, "UnregisterFromGameSystems", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_UnregisterFromGameSystems_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARookieCharacter_UnregisterFromGameSystems_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ARookieCharacter_UnregisterFromGameSystems()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARookieCharacter_UnregisterFromGameSystems_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARookieCharacter::execUnregisterFromGameSystems)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnregisterFromGameSystems();
	P_NATIVE_END;
}
// End Class ARookieCharacter Function UnregisterFromGameSystems

// Begin Class ARookieCharacter Function UpdatePerformanceSettings
struct Z_Construct_UFunction_ARookieCharacter_UpdatePerformanceSettings_Statics
{
	struct RookieCharacter_eventUpdatePerformanceSettings_Parms
	{
		int32 QualityLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Performance Optimization */" },
#endif
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance Optimization" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_QualityLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ARookieCharacter_UpdatePerformanceSettings_Statics::NewProp_QualityLevel = { "QualityLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RookieCharacter_eventUpdatePerformanceSettings_Parms, QualityLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ARookieCharacter_UpdatePerformanceSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ARookieCharacter_UpdatePerformanceSettings_Statics::NewProp_QualityLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_UpdatePerformanceSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ARookieCharacter_UpdatePerformanceSettings_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ARookieCharacter, nullptr, "UpdatePerformanceSettings", nullptr, nullptr, Z_Construct_UFunction_ARookieCharacter_UpdatePerformanceSettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_UpdatePerformanceSettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_ARookieCharacter_UpdatePerformanceSettings_Statics::RookieCharacter_eventUpdatePerformanceSettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ARookieCharacter_UpdatePerformanceSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_ARookieCharacter_UpdatePerformanceSettings_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ARookieCharacter_UpdatePerformanceSettings_Statics::RookieCharacter_eventUpdatePerformanceSettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ARookieCharacter_UpdatePerformanceSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ARookieCharacter_UpdatePerformanceSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ARookieCharacter::execUpdatePerformanceSettings)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_QualityLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePerformanceSettings(Z_Param_QualityLevel);
	P_NATIVE_END;
}
// End Class ARookieCharacter Function UpdatePerformanceSettings

// Begin Class ARookieCharacter
void ARookieCharacter::StaticRegisterNativesARookieCharacter()
{
	UClass* Class = ARookieCharacter::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ActivateBulletTime", &ARookieCharacter::execActivateBulletTime },
		{ "AddRewindCharge", &ARookieCharacter::execAddRewindCharge },
		{ "AddWeaponToInventory", &ARookieCharacter::execAddWeaponToInventory },
		{ "CanPerformDash", &ARookieCharacter::execCanPerformDash },
		{ "CanRewind", &ARookieCharacter::execCanRewind },
		{ "CanUseBulletTime", &ARookieCharacter::execCanUseBulletTime },
		{ "DashTimelineFinished", &ARookieCharacter::execDashTimelineFinished },
		{ "DashTimelineUpdate", &ARookieCharacter::execDashTimelineUpdate },
		{ "DeactivateBulletTime", &ARookieCharacter::execDeactivateBulletTime },
		{ "EquipWeapon", &ARookieCharacter::execEquipWeapon },
		{ "GetBulletTimePercent", &ARookieCharacter::execGetBulletTimePercent },
		{ "GetHealthPercent", &ARookieCharacter::execGetHealthPercent },
		{ "Heal", &ARookieCharacter::execHeal },
		{ "IsDead", &ARookieCharacter::execIsDead },
		{ "PerformDashMovement", &ARookieCharacter::execPerformDashMovement },
		{ "PerformTimeRewind", &ARookieCharacter::execPerformTimeRewind },
		{ "RecordPlayerAction", &ARookieCharacter::execRecordPlayerAction },
		{ "RegisterWithGameSystems", &ARookieCharacter::execRegisterWithGameSystems },
		{ "SetLODLevel", &ARookieCharacter::execSetLODLevel },
		{ "SwitchToNextWeapon", &ARookieCharacter::execSwitchToNextWeapon },
		{ "SwitchToPreviousWeapon", &ARookieCharacter::execSwitchToPreviousWeapon },
		{ "TakeDamageSimple", &ARookieCharacter::execTakeDamageSimple },
		{ "UnregisterFromGameSystems", &ARookieCharacter::execUnregisterFromGameSystems },
		{ "UpdatePerformanceSettings", &ARookieCharacter::execUpdatePerformanceSettings },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(ARookieCharacter);
UClass* Z_Construct_UClass_ARookieCharacter_NoRegister()
{
	return ARookieCharacter::StaticClass();
}
struct Z_Construct_UClass_ARookieCharacter_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Enhanced Rookie Character with Max Payne-style mechanics\n * Features bullet-time, time rewind, dash, and advanced combat\n */" },
#endif
		{ "HideCategories", "Navigation" },
		{ "IncludePath", "Characters/RookieCharacter.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enhanced Rookie Character with Max Payne-style mechanics\nFeatures bullet-time, time rewind, dash, and advanced combat" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CameraBoom_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Camera" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Camera Components */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Camera Components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FollowCamera_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Camera" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultMappingContext_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Input */" },
#endif
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Input" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MoveAction_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Input" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LookAction_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Input" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_JumpAction_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Input" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FireAction_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Input" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BulletTimeAction_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Input" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewindAction_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Input" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashAction_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Input" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractAction_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Input" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponSwitchAction_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Input" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CharacterStats_MetaData[] = {
		{ "Category", "Character Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Character Stats */" },
#endif
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Character Stats" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentHealth_MetaData[] = {
		{ "Category", "Character Stats" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHealth_MetaData[] = {
		{ "Category", "Character Stats" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BulletTimeEnergy_MetaData[] = {
		{ "Category", "Bullet Time" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Bullet Time System */" },
#endif
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Bullet Time System" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxBulletTimeEnergy_MetaData[] = {
		{ "Category", "Bullet Time" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsBulletTimeActive_MetaData[] = {
		{ "Category", "Bullet Time" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BulletTimeScale_MetaData[] = {
		{ "Category", "Bullet Time" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BulletTimeDrainRate_MetaData[] = {
		{ "Category", "Bullet Time" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BulletTimeRechargeRate_MetaData[] = {
		{ "Category", "Bullet Time" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewindCharges_MetaData[] = {
		{ "Category", "Time Rewind" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Time Rewind System */" },
#endif
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Time Rewind System" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxRewindCharges_MetaData[] = {
		{ "Category", "Time Rewind" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SnapshotInterval_MetaData[] = {
		{ "Category", "Time Rewind" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewindDuration_MetaData[] = {
		{ "Category", "Time Rewind" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeSnapshots_MetaData[] = {
		{ "Category", "Time Rewind" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanDash_MetaData[] = {
		{ "Category", "Dash" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dash System */" },
#endif
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dash System" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashDistance_MetaData[] = {
		{ "Category", "Dash" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashCooldown_MetaData[] = {
		{ "Category", "Dash" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashDuration_MetaData[] = {
		{ "Category", "Dash" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentWeapon_MetaData[] = {
		{ "Category", "Weapons" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Weapon System */" },
#endif
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weapon System" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponInventory_MetaData[] = {
		{ "Category", "Weapons" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentWeaponIndex_MetaData[] = {
		{ "Category", "Weapons" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnHealthChanged_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Events */" },
#endif
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnBulletTimeChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnRewindChargesChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnWeaponChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashTimeline_MetaData[] = {
		{ "Category", "Timeline" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Timeline Components */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Timeline Components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashCurve_MetaData[] = {
		{ "Category", "Timeline" },
		{ "ModuleRelativePath", "Characters/RookieCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CameraBoom;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FollowCamera;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DefaultMappingContext;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MoveAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LookAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_JumpAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FireAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BulletTimeAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RewindAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DashAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WeaponSwitchAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CharacterStats;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentHealth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHealth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BulletTimeEnergy;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxBulletTimeEnergy;
	static void NewProp_bIsBulletTimeActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsBulletTimeActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BulletTimeScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BulletTimeDrainRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BulletTimeRechargeRate;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RewindCharges;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxRewindCharges;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SnapshotInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RewindDuration;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TimeSnapshots_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TimeSnapshots;
	static void NewProp_bCanDash_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanDash;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DashDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DashCooldown;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DashDuration;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CurrentWeapon;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WeaponInventory_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_WeaponInventory;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentWeaponIndex;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnHealthChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnBulletTimeChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnRewindChargesChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnWeaponChanged;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DashTimeline;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DashCurve;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ARookieCharacter_ActivateBulletTime, "ActivateBulletTime" }, // **********
		{ &Z_Construct_UFunction_ARookieCharacter_AddRewindCharge, "AddRewindCharge" }, // **********
		{ &Z_Construct_UFunction_ARookieCharacter_AddWeaponToInventory, "AddWeaponToInventory" }, // **********
		{ &Z_Construct_UFunction_ARookieCharacter_CanPerformDash, "CanPerformDash" }, // 533276630
		{ &Z_Construct_UFunction_ARookieCharacter_CanRewind, "CanRewind" }, // 3940600967
		{ &Z_Construct_UFunction_ARookieCharacter_CanUseBulletTime, "CanUseBulletTime" }, // 1522482850
		{ &Z_Construct_UFunction_ARookieCharacter_DashTimelineFinished, "DashTimelineFinished" }, // 299916351
		{ &Z_Construct_UFunction_ARookieCharacter_DashTimelineUpdate, "DashTimelineUpdate" }, // **********
		{ &Z_Construct_UFunction_ARookieCharacter_DeactivateBulletTime, "DeactivateBulletTime" }, // **********
		{ &Z_Construct_UFunction_ARookieCharacter_EquipWeapon, "EquipWeapon" }, // **********
		{ &Z_Construct_UFunction_ARookieCharacter_GetBulletTimePercent, "GetBulletTimePercent" }, // 641478946
		{ &Z_Construct_UFunction_ARookieCharacter_GetHealthPercent, "GetHealthPercent" }, // **********
		{ &Z_Construct_UFunction_ARookieCharacter_Heal, "Heal" }, // **********
		{ &Z_Construct_UFunction_ARookieCharacter_IsDead, "IsDead" }, // **********
		{ &Z_Construct_UFunction_ARookieCharacter_PerformDashMovement, "PerformDashMovement" }, // **********
		{ &Z_Construct_UFunction_ARookieCharacter_PerformTimeRewind, "PerformTimeRewind" }, // 989368655
		{ &Z_Construct_UFunction_ARookieCharacter_RecordPlayerAction, "RecordPlayerAction" }, // **********
		{ &Z_Construct_UFunction_ARookieCharacter_RegisterWithGameSystems, "RegisterWithGameSystems" }, // 752483986
		{ &Z_Construct_UFunction_ARookieCharacter_SetLODLevel, "SetLODLevel" }, // **********
		{ &Z_Construct_UFunction_ARookieCharacter_SwitchToNextWeapon, "SwitchToNextWeapon" }, // **********
		{ &Z_Construct_UFunction_ARookieCharacter_SwitchToPreviousWeapon, "SwitchToPreviousWeapon" }, // **********
		{ &Z_Construct_UFunction_ARookieCharacter_TakeDamageSimple, "TakeDamageSimple" }, // **********
		{ &Z_Construct_UFunction_ARookieCharacter_UnregisterFromGameSystems, "UnregisterFromGameSystems" }, // 232385035
		{ &Z_Construct_UFunction_ARookieCharacter_UpdatePerformanceSettings, "UpdatePerformanceSettings" }, // 3729244852
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ARookieCharacter>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_CameraBoom = { "CameraBoom", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, CameraBoom), Z_Construct_UClass_USpringArmComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CameraBoom_MetaData), NewProp_CameraBoom_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_FollowCamera = { "FollowCamera", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, FollowCamera), Z_Construct_UClass_UCameraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FollowCamera_MetaData), NewProp_FollowCamera_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_DefaultMappingContext = { "DefaultMappingContext", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, DefaultMappingContext), Z_Construct_UClass_UInputMappingContext_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultMappingContext_MetaData), NewProp_DefaultMappingContext_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_MoveAction = { "MoveAction", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, MoveAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MoveAction_MetaData), NewProp_MoveAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_LookAction = { "LookAction", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, LookAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LookAction_MetaData), NewProp_LookAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_JumpAction = { "JumpAction", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, JumpAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_JumpAction_MetaData), NewProp_JumpAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_FireAction = { "FireAction", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, FireAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FireAction_MetaData), NewProp_FireAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_BulletTimeAction = { "BulletTimeAction", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, BulletTimeAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BulletTimeAction_MetaData), NewProp_BulletTimeAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_RewindAction = { "RewindAction", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, RewindAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewindAction_MetaData), NewProp_RewindAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_DashAction = { "DashAction", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, DashAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashAction_MetaData), NewProp_DashAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_InteractAction = { "InteractAction", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, InteractAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractAction_MetaData), NewProp_InteractAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_WeaponSwitchAction = { "WeaponSwitchAction", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, WeaponSwitchAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponSwitchAction_MetaData), NewProp_WeaponSwitchAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_CharacterStats = { "CharacterStats", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, CharacterStats), Z_Construct_UClass_UCharacterStatsDataAsset_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CharacterStats_MetaData), NewProp_CharacterStats_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_CurrentHealth = { "CurrentHealth", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, CurrentHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentHealth_MetaData), NewProp_CurrentHealth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_MaxHealth = { "MaxHealth", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, MaxHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHealth_MetaData), NewProp_MaxHealth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_BulletTimeEnergy = { "BulletTimeEnergy", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, BulletTimeEnergy), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BulletTimeEnergy_MetaData), NewProp_BulletTimeEnergy_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_MaxBulletTimeEnergy = { "MaxBulletTimeEnergy", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, MaxBulletTimeEnergy), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxBulletTimeEnergy_MetaData), NewProp_MaxBulletTimeEnergy_MetaData) };
void Z_Construct_UClass_ARookieCharacter_Statics::NewProp_bIsBulletTimeActive_SetBit(void* Obj)
{
	((ARookieCharacter*)Obj)->bIsBulletTimeActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_bIsBulletTimeActive = { "bIsBulletTimeActive", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARookieCharacter), &Z_Construct_UClass_ARookieCharacter_Statics::NewProp_bIsBulletTimeActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsBulletTimeActive_MetaData), NewProp_bIsBulletTimeActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_BulletTimeScale = { "BulletTimeScale", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, BulletTimeScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BulletTimeScale_MetaData), NewProp_BulletTimeScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_BulletTimeDrainRate = { "BulletTimeDrainRate", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, BulletTimeDrainRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BulletTimeDrainRate_MetaData), NewProp_BulletTimeDrainRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_BulletTimeRechargeRate = { "BulletTimeRechargeRate", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, BulletTimeRechargeRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BulletTimeRechargeRate_MetaData), NewProp_BulletTimeRechargeRate_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_RewindCharges = { "RewindCharges", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, RewindCharges), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewindCharges_MetaData), NewProp_RewindCharges_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_MaxRewindCharges = { "MaxRewindCharges", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, MaxRewindCharges), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxRewindCharges_MetaData), NewProp_MaxRewindCharges_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_SnapshotInterval = { "SnapshotInterval", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, SnapshotInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SnapshotInterval_MetaData), NewProp_SnapshotInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_RewindDuration = { "RewindDuration", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, RewindDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewindDuration_MetaData), NewProp_RewindDuration_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_TimeSnapshots_Inner = { "TimeSnapshots", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTimeSnapshot, METADATA_PARAMS(0, nullptr) }; // 1602514274
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_TimeSnapshots = { "TimeSnapshots", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, TimeSnapshots), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeSnapshots_MetaData), NewProp_TimeSnapshots_MetaData) }; // 1602514274
void Z_Construct_UClass_ARookieCharacter_Statics::NewProp_bCanDash_SetBit(void* Obj)
{
	((ARookieCharacter*)Obj)->bCanDash = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_bCanDash = { "bCanDash", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ARookieCharacter), &Z_Construct_UClass_ARookieCharacter_Statics::NewProp_bCanDash_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanDash_MetaData), NewProp_bCanDash_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_DashDistance = { "DashDistance", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, DashDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashDistance_MetaData), NewProp_DashDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_DashCooldown = { "DashCooldown", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, DashCooldown), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashCooldown_MetaData), NewProp_DashCooldown_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_DashDuration = { "DashDuration", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, DashDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashDuration_MetaData), NewProp_DashDuration_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_CurrentWeapon = { "CurrentWeapon", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, CurrentWeapon), Z_Construct_UClass_AWeaponBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentWeapon_MetaData), NewProp_CurrentWeapon_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_WeaponInventory_Inner = { "WeaponInventory", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AWeaponBase_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_WeaponInventory = { "WeaponInventory", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, WeaponInventory), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponInventory_MetaData), NewProp_WeaponInventory_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_CurrentWeaponIndex = { "CurrentWeaponIndex", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, CurrentWeaponIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentWeaponIndex_MetaData), NewProp_CurrentWeaponIndex_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_OnHealthChanged = { "OnHealthChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, OnHealthChanged), Z_Construct_UDelegateFunction_RoughReality_OnHealthChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnHealthChanged_MetaData), NewProp_OnHealthChanged_MetaData) }; // 860266913
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_OnBulletTimeChanged = { "OnBulletTimeChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, OnBulletTimeChanged), Z_Construct_UDelegateFunction_RoughReality_OnBulletTimeChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnBulletTimeChanged_MetaData), NewProp_OnBulletTimeChanged_MetaData) }; // **********
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_OnRewindChargesChanged = { "OnRewindChargesChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, OnRewindChargesChanged), Z_Construct_UDelegateFunction_RoughReality_OnRewindChargesChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnRewindChargesChanged_MetaData), NewProp_OnRewindChargesChanged_MetaData) }; // 1531066048
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_OnWeaponChanged = { "OnWeaponChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, OnWeaponChanged), Z_Construct_UDelegateFunction_RoughReality_OnWeaponChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnWeaponChanged_MetaData), NewProp_OnWeaponChanged_MetaData) }; // 899887133
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_DashTimeline = { "DashTimeline", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, DashTimeline), Z_Construct_UClass_UTimelineComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashTimeline_MetaData), NewProp_DashTimeline_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ARookieCharacter_Statics::NewProp_DashCurve = { "DashCurve", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ARookieCharacter, DashCurve), Z_Construct_UClass_UCurveFloat_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashCurve_MetaData), NewProp_DashCurve_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ARookieCharacter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_CameraBoom,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_FollowCamera,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_DefaultMappingContext,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_MoveAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_LookAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_JumpAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_FireAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_BulletTimeAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_RewindAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_DashAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_InteractAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_WeaponSwitchAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_CharacterStats,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_CurrentHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_MaxHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_BulletTimeEnergy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_MaxBulletTimeEnergy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_bIsBulletTimeActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_BulletTimeScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_BulletTimeDrainRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_BulletTimeRechargeRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_RewindCharges,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_MaxRewindCharges,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_SnapshotInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_RewindDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_TimeSnapshots_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_TimeSnapshots,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_bCanDash,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_DashDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_DashCooldown,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_DashDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_CurrentWeapon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_WeaponInventory_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_WeaponInventory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_CurrentWeaponIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_OnHealthChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_OnBulletTimeChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_OnRewindChargesChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_OnWeaponChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_DashTimeline,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ARookieCharacter_Statics::NewProp_DashCurve,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ARookieCharacter_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ARookieCharacter_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_ACharacter,
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ARookieCharacter_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ARookieCharacter_Statics::ClassParams = {
	&ARookieCharacter::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ARookieCharacter_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ARookieCharacter_Statics::PropPointers),
	0,
	0x009000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ARookieCharacter_Statics::Class_MetaDataParams), Z_Construct_UClass_ARookieCharacter_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ARookieCharacter()
{
	if (!Z_Registration_Info_UClass_ARookieCharacter.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ARookieCharacter.OuterSingleton, Z_Construct_UClass_ARookieCharacter_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ARookieCharacter.OuterSingleton;
}
template<> ROUGHREALITY_API UClass* StaticClass<ARookieCharacter>()
{
	return ARookieCharacter::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ARookieCharacter);
ARookieCharacter::~ARookieCharacter() {}
// End Class ARookieCharacter

// Begin Registration
struct Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Characters_RookieCharacter_h_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FTimeSnapshot::StaticStruct, Z_Construct_UScriptStruct_FTimeSnapshot_Statics::NewStructOps, TEXT("TimeSnapshot"), &Z_Registration_Info_UScriptStruct_TimeSnapshot, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FTimeSnapshot), 1602514274U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ARookieCharacter, ARookieCharacter::StaticClass, TEXT("ARookieCharacter"), &Z_Registration_Info_UClass_ARookieCharacter, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ARookieCharacter), 988191472U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Characters_RookieCharacter_h_3608500782(TEXT("/Script/RoughReality"),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Characters_RookieCharacter_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Characters_RookieCharacter_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Characters_RookieCharacter_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Characters_RookieCharacter_h_Statics::ScriptStructInfo),
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
