// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Analytics/GameAnalyticsManager.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
 
class AGameAnalyticsManager;
class UObject;
struct FHeatmapData;
struct FPerformanceMetrics;
struct FPlayerActionEvent;
struct FWeaponAnalytics;
#ifdef ROUGHREALITY_GameAnalyticsManager_generated_h
#error "GameAnalyticsManager.generated.h already included, missing '#pragma once' in GameAnalyticsManager.h"
#endif
#define ROUGHREALITY_GameAnalyticsManager_generated_h

#define FID_RoughReality_Source_RoughReality_Analytics_GameAnalyticsManager_h_14_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPlayerActionEvent_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FPlayerActionEvent>();

#define FID_RoughReality_Source_RoughReality_Analytics_GameAnalyticsManager_h_43_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FHeatmapData_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FHeatmapData>();

#define FID_RoughReality_Source_RoughReality_Analytics_GameAnalyticsManager_h_69_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FWeaponAnalytics_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FWeaponAnalytics>();

#define FID_RoughReality_Source_RoughReality_Analytics_GameAnalyticsManager_h_107_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPerformanceMetrics_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FPerformanceMetrics>();

#define FID_RoughReality_Source_RoughReality_Analytics_GameAnalyticsManager_h_142_DELEGATE \
ROUGHREALITY_API void FOnAnalyticsEvent_DelegateWrapper(const FMulticastScriptDelegate& OnAnalyticsEvent, FPlayerActionEvent const& Event);


#define FID_RoughReality_Source_RoughReality_Analytics_GameAnalyticsManager_h_151_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetAnalyticsManager); \
	DECLARE_FUNCTION(execGetSessionDuration); \
	DECLARE_FUNCTION(execEndCurrentSession); \
	DECLARE_FUNCTION(execStartNewSession); \
	DECLARE_FUNCTION(execExportHeatmapToCSV); \
	DECLARE_FUNCTION(execLoadAnalyticsFromFile); \
	DECLARE_FUNCTION(execSaveAnalyticsToFile); \
	DECLARE_FUNCTION(execFlushAnalyticsData); \
	DECLARE_FUNCTION(execGetCurrentPerformanceMetrics); \
	DECLARE_FUNCTION(execUpdatePerformanceMetrics); \
	DECLARE_FUNCTION(execGetAllWeaponAnalytics); \
	DECLARE_FUNCTION(execGetWeaponAnalytics); \
	DECLARE_FUNCTION(execUpdateWeaponUsageTime); \
	DECLARE_FUNCTION(execClearHeatmapData); \
	DECLARE_FUNCTION(execGetHeatmapData); \
	DECLARE_FUNCTION(execRecordHeatmapEvent); \
	DECLARE_FUNCTION(execLogTimeRewind); \
	DECLARE_FUNCTION(execLogBulletTimeUsage); \
	DECLARE_FUNCTION(execLogLevelComplete); \
	DECLARE_FUNCTION(execLogPlayerDeath); \
	DECLARE_FUNCTION(execLogEnemyKill); \
	DECLARE_FUNCTION(execLogWeaponReload); \
	DECLARE_FUNCTION(execLogWeaponFired); \
	DECLARE_FUNCTION(execLogPlayerAction);


#define FID_RoughReality_Source_RoughReality_Analytics_GameAnalyticsManager_h_151_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAGameAnalyticsManager(); \
	friend struct Z_Construct_UClass_AGameAnalyticsManager_Statics; \
public: \
	DECLARE_CLASS(AGameAnalyticsManager, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/RoughReality"), NO_API) \
	DECLARE_SERIALIZER(AGameAnalyticsManager)


#define FID_RoughReality_Source_RoughReality_Analytics_GameAnalyticsManager_h_151_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	AGameAnalyticsManager(AGameAnalyticsManager&&); \
	AGameAnalyticsManager(const AGameAnalyticsManager&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AGameAnalyticsManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AGameAnalyticsManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AGameAnalyticsManager) \
	NO_API virtual ~AGameAnalyticsManager();


#define FID_RoughReality_Source_RoughReality_Analytics_GameAnalyticsManager_h_148_PROLOG
#define FID_RoughReality_Source_RoughReality_Analytics_GameAnalyticsManager_h_151_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_RoughReality_Source_RoughReality_Analytics_GameAnalyticsManager_h_151_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_Analytics_GameAnalyticsManager_h_151_INCLASS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_Analytics_GameAnalyticsManager_h_151_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ROUGHREALITY_API UClass* StaticClass<class AGameAnalyticsManager>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_RoughReality_Source_RoughReality_Analytics_GameAnalyticsManager_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
