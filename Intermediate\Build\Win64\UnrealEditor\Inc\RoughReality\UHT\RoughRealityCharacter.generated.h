// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RoughRealityCharacter.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef ROUGHREALITY_RoughRealityCharacter_generated_h
#error "RoughRealityCharacter.generated.h already included, missing '#pragma once' in RoughRealityCharacter.h"
#endif
#define ROUGHREALITY_RoughRealityCharacter_generated_h

#define FID_RoughReality_Source_RoughReality_RoughRealityCharacter_h_21_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesARoughRealityCharacter(); \
	friend struct Z_Construct_UClass_ARoughRealityCharacter_Statics; \
public: \
	DECLARE_CLASS(ARoughRealityCharacter, ACharacter, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/RoughReality"), NO_API) \
	DECLARE_SERIALIZER(ARoughRealityCharacter)


#define FID_RoughReality_Source_RoughReality_RoughRealityCharacter_h_21_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	ARoughRealityCharacter(ARoughRealityCharacter&&); \
	ARoughRealityCharacter(const ARoughRealityCharacter&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ARoughRealityCharacter); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ARoughRealityCharacter); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ARoughRealityCharacter) \
	NO_API virtual ~ARoughRealityCharacter();


#define FID_RoughReality_Source_RoughReality_RoughRealityCharacter_h_18_PROLOG
#define FID_RoughReality_Source_RoughReality_RoughRealityCharacter_h_21_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_RoughReality_Source_RoughReality_RoughRealityCharacter_h_21_INCLASS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_RoughRealityCharacter_h_21_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ROUGHREALITY_API UClass* StaticClass<class ARoughRealityCharacter>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_RoughReality_Source_RoughReality_RoughRealityCharacter_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
