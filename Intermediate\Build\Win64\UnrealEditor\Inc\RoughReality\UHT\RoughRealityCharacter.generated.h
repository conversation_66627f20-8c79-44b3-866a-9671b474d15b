// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "RoughRealityCharacter.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef ROUGHREALITY_RoughRealityCharacter_generated_h
#error "RoughRealityCharacter.generated.h already included, missing '#pragma once' in RoughRealityCharacter.h"
#endif
#define ROUGHREALITY_RoughRealityCharacter_generated_h

#define FID_RoughReality_Source_RoughReality_RoughRealityCharacter_h_18_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesADEPRECATED_ARoughRealityCharacter(); \
	friend struct Z_Construct_UClass_ADEPRECATED_ARoughRealityCharacter_Statics; \
public: \
	DECLARE_CLASS(ADEPRECATED_ARoughRealityCharacter, ARookieCharacter, COMPILED_IN_FLAGS(0 | CLASS_Config | CLASS_Deprecated), CASTCLASS_None, TEXT("/Script/RoughReality"), NO_API) \
	DECLARE_SERIALIZER(ADEPRECATED_ARoughRealityCharacter)


#define FID_RoughReality_Source_RoughReality_RoughRealityCharacter_h_18_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	ADEPRECATED_ARoughRealityCharacter(ADEPRECATED_ARoughRealityCharacter&&); \
	ADEPRECATED_ARoughRealityCharacter(const ADEPRECATED_ARoughRealityCharacter&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ADEPRECATED_ARoughRealityCharacter); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ADEPRECATED_ARoughRealityCharacter); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ADEPRECATED_ARoughRealityCharacter) \
	NO_API virtual ~ADEPRECATED_ARoughRealityCharacter();


#define FID_RoughReality_Source_RoughReality_RoughRealityCharacter_h_15_PROLOG
#define FID_RoughReality_Source_RoughReality_RoughRealityCharacter_h_18_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_RoughReality_Source_RoughReality_RoughRealityCharacter_h_18_INCLASS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_RoughRealityCharacter_h_18_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ROUGHREALITY_API UClass* StaticClass<class ADEPRECATED_ARoughRealityCharacter>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_RoughReality_Source_RoughReality_RoughRealityCharacter_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
