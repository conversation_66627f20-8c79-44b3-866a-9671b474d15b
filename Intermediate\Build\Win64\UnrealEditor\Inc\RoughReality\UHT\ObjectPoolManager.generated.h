// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Core/ObjectPoolManager.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class AActor;
class AObjectPoolManager;
class UObject;
struct FObjectPoolConfiguration;
#ifdef ROUGHREALITY_ObjectPoolManager_generated_h
#error "ObjectPoolManager.generated.h already included, missing '#pragma once' in ObjectPoolManager.h"
#endif
#define ROUGHREALITY_ObjectPoolManager_generated_h

#define FID_RoughReality_Source_RoughReality_Core_ObjectPoolManager_h_10_DELEGATE \
ROUGHREALITY_API void FOnObjectSpawned_DelegateWrapper(const FMulticastScriptDelegate& OnObjectSpawned, AActor* SpawnedActor, FName PoolName);


#define FID_RoughReality_Source_RoughReality_Core_ObjectPoolManager_h_11_DELEGATE \
ROUGHREALITY_API void FOnObjectReturned_DelegateWrapper(const FMulticastScriptDelegate& OnObjectReturned, AActor* ReturnedActor, FName PoolName);


#define FID_RoughReality_Source_RoughReality_Core_ObjectPoolManager_h_16_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FObjectPoolConfiguration>();

#define FID_RoughReality_Source_RoughReality_Core_ObjectPoolManager_h_53_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPooledObjectInfo_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FPooledObjectInfo>();

#define FID_RoughReality_Source_RoughReality_Core_ObjectPoolManager_h_91_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnDelayedReturn); \
	DECLARE_FUNCTION(execGetObjectPoolManager); \
	DECLARE_FUNCTION(execOptimizeAllPools); \
	DECLARE_FUNCTION(execTrimPool); \
	DECLARE_FUNCTION(execPrewarmPool); \
	DECLARE_FUNCTION(execGetPoolHitRate); \
	DECLARE_FUNCTION(execIsPoolInitialized); \
	DECLARE_FUNCTION(execGetObjectsInUse); \
	DECLARE_FUNCTION(execGetAvailableObjects); \
	DECLARE_FUNCTION(execGetPoolSize); \
	DECLARE_FUNCTION(execReturnPooledObjectDelayed); \
	DECLARE_FUNCTION(execReturnPooledObject); \
	DECLARE_FUNCTION(execSpawnPooledObject); \
	DECLARE_FUNCTION(execClearAllPools); \
	DECLARE_FUNCTION(execClearPool); \
	DECLARE_FUNCTION(execInitializeAllPools); \
	DECLARE_FUNCTION(execInitializePool);


#define FID_RoughReality_Source_RoughReality_Core_ObjectPoolManager_h_91_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAObjectPoolManager(); \
	friend struct Z_Construct_UClass_AObjectPoolManager_Statics; \
public: \
	DECLARE_CLASS(AObjectPoolManager, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/RoughReality"), NO_API) \
	DECLARE_SERIALIZER(AObjectPoolManager)


#define FID_RoughReality_Source_RoughReality_Core_ObjectPoolManager_h_91_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	AObjectPoolManager(AObjectPoolManager&&); \
	AObjectPoolManager(const AObjectPoolManager&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AObjectPoolManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AObjectPoolManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AObjectPoolManager) \
	NO_API virtual ~AObjectPoolManager();


#define FID_RoughReality_Source_RoughReality_Core_ObjectPoolManager_h_88_PROLOG
#define FID_RoughReality_Source_RoughReality_Core_ObjectPoolManager_h_91_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_RoughReality_Source_RoughReality_Core_ObjectPoolManager_h_91_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_Core_ObjectPoolManager_h_91_INCLASS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_Core_ObjectPoolManager_h_91_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ROUGHREALITY_API UClass* StaticClass<class AObjectPoolManager>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_RoughReality_Source_RoughReality_Core_ObjectPoolManager_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
