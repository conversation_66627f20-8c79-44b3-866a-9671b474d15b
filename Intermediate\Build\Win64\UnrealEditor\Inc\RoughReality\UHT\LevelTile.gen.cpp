// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RoughReality/LevelGeneration/LevelTile.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeLevelTile() {}

// Begin Cross Module References
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UAudioComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USceneComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
ROUGHREALITY_API UClass* Z_Construct_UClass_ALevelTile();
ROUGHREALITY_API UClass* Z_Construct_UClass_ALevelTile_NoRegister();
ROUGHREALITY_API UClass* Z_Construct_UClass_UTileDefinitionDataAsset_NoRegister();
ROUGHREALITY_API UEnum* Z_Construct_UEnum_RoughReality_ETileType();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnTileActivated__DelegateSignature();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnTileCompleted__DelegateSignature();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnTileDeactivated__DelegateSignature();
UPackage* Z_Construct_UPackage__Script_RoughReality();
// End Cross Module References

// Begin Delegate FOnTileActivated
struct Z_Construct_UDelegateFunction_RoughReality_OnTileActivated__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnTileActivated_Parms
	{
		ALevelTile* Tile;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Tile;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnTileActivated__DelegateSignature_Statics::NewProp_Tile = { "Tile", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnTileActivated_Parms, Tile), Z_Construct_UClass_ALevelTile_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnTileActivated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnTileActivated__DelegateSignature_Statics::NewProp_Tile,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnTileActivated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnTileActivated__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnTileActivated__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnTileActivated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnTileActivated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnTileActivated__DelegateSignature_Statics::_Script_RoughReality_eventOnTileActivated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnTileActivated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnTileActivated__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnTileActivated__DelegateSignature_Statics::_Script_RoughReality_eventOnTileActivated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnTileActivated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnTileActivated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnTileActivated_DelegateWrapper(const FMulticastScriptDelegate& OnTileActivated, ALevelTile* Tile)
{
	struct _Script_RoughReality_eventOnTileActivated_Parms
	{
		ALevelTile* Tile;
	};
	_Script_RoughReality_eventOnTileActivated_Parms Parms;
	Parms.Tile=Tile;
	OnTileActivated.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnTileActivated

// Begin Delegate FOnTileDeactivated
struct Z_Construct_UDelegateFunction_RoughReality_OnTileDeactivated__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnTileDeactivated_Parms
	{
		ALevelTile* Tile;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Tile;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnTileDeactivated__DelegateSignature_Statics::NewProp_Tile = { "Tile", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnTileDeactivated_Parms, Tile), Z_Construct_UClass_ALevelTile_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnTileDeactivated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnTileDeactivated__DelegateSignature_Statics::NewProp_Tile,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnTileDeactivated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnTileDeactivated__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnTileDeactivated__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnTileDeactivated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnTileDeactivated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnTileDeactivated__DelegateSignature_Statics::_Script_RoughReality_eventOnTileDeactivated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnTileDeactivated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnTileDeactivated__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnTileDeactivated__DelegateSignature_Statics::_Script_RoughReality_eventOnTileDeactivated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnTileDeactivated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnTileDeactivated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnTileDeactivated_DelegateWrapper(const FMulticastScriptDelegate& OnTileDeactivated, ALevelTile* Tile)
{
	struct _Script_RoughReality_eventOnTileDeactivated_Parms
	{
		ALevelTile* Tile;
	};
	_Script_RoughReality_eventOnTileDeactivated_Parms Parms;
	Parms.Tile=Tile;
	OnTileDeactivated.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnTileDeactivated

// Begin Delegate FOnTileCompleted
struct Z_Construct_UDelegateFunction_RoughReality_OnTileCompleted__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnTileCompleted_Parms
	{
		ALevelTile* Tile;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Tile;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnTileCompleted__DelegateSignature_Statics::NewProp_Tile = { "Tile", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnTileCompleted_Parms, Tile), Z_Construct_UClass_ALevelTile_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnTileCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnTileCompleted__DelegateSignature_Statics::NewProp_Tile,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnTileCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnTileCompleted__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnTileCompleted__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnTileCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnTileCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnTileCompleted__DelegateSignature_Statics::_Script_RoughReality_eventOnTileCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnTileCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnTileCompleted__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnTileCompleted__DelegateSignature_Statics::_Script_RoughReality_eventOnTileCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnTileCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnTileCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnTileCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnTileCompleted, ALevelTile* Tile)
{
	struct _Script_RoughReality_eventOnTileCompleted_Parms
	{
		ALevelTile* Tile;
	};
	_Script_RoughReality_eventOnTileCompleted_Parms Parms;
	Parms.Tile=Tile;
	OnTileCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnTileCompleted

// Begin Class ALevelTile Function ActivateTile
struct Z_Construct_UFunction_ALevelTile_ActivateTile_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tile" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Activation */" },
#endif
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Activation" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALevelTile_ActivateTile_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ALevelTile, nullptr, "ActivateTile", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_ActivateTile_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALevelTile_ActivateTile_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ALevelTile_ActivateTile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALevelTile_ActivateTile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALevelTile::execActivateTile)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ActivateTile();
	P_NATIVE_END;
}
// End Class ALevelTile Function ActivateTile

// Begin Class ALevelTile Function ApplyTileLighting
struct Z_Construct_UFunction_ALevelTile_ApplyTileLighting_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Lighting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Lighting */" },
#endif
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lighting" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALevelTile_ApplyTileLighting_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ALevelTile, nullptr, "ApplyTileLighting", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_ApplyTileLighting_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALevelTile_ApplyTileLighting_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ALevelTile_ApplyTileLighting()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALevelTile_ApplyTileLighting_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALevelTile::execApplyTileLighting)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyTileLighting();
	P_NATIVE_END;
}
// End Class ALevelTile Function ApplyTileLighting

// Begin Class ALevelTile Function CheckCompletionConditions
struct Z_Construct_UFunction_ALevelTile_CheckCompletionConditions_Statics
{
	struct LevelTile_eventCheckCompletionConditions_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tile" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ALevelTile_CheckCompletionConditions_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((LevelTile_eventCheckCompletionConditions_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ALevelTile_CheckCompletionConditions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LevelTile_eventCheckCompletionConditions_Parms), &Z_Construct_UFunction_ALevelTile_CheckCompletionConditions_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALevelTile_CheckCompletionConditions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALevelTile_CheckCompletionConditions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_CheckCompletionConditions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALevelTile_CheckCompletionConditions_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ALevelTile, nullptr, "CheckCompletionConditions", nullptr, nullptr, Z_Construct_UFunction_ALevelTile_CheckCompletionConditions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_CheckCompletionConditions_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALevelTile_CheckCompletionConditions_Statics::LevelTile_eventCheckCompletionConditions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_CheckCompletionConditions_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALevelTile_CheckCompletionConditions_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ALevelTile_CheckCompletionConditions_Statics::LevelTile_eventCheckCompletionConditions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALevelTile_CheckCompletionConditions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALevelTile_CheckCompletionConditions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALevelTile::execCheckCompletionConditions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CheckCompletionConditions();
	P_NATIVE_END;
}
// End Class ALevelTile Function CheckCompletionConditions

// Begin Class ALevelTile Function ClearTileContent
struct Z_Construct_UFunction_ALevelTile_ClearTileContent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Content" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALevelTile_ClearTileContent_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ALevelTile, nullptr, "ClearTileContent", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_ClearTileContent_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALevelTile_ClearTileContent_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ALevelTile_ClearTileContent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALevelTile_ClearTileContent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALevelTile::execClearTileContent)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearTileContent();
	P_NATIVE_END;
}
// End Class ALevelTile Function ClearTileContent

// Begin Class ALevelTile Function CompleteTile
struct Z_Construct_UFunction_ALevelTile_CompleteTile_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tile" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Completion */" },
#endif
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Completion" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALevelTile_CompleteTile_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ALevelTile, nullptr, "CompleteTile", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_CompleteTile_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALevelTile_CompleteTile_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ALevelTile_CompleteTile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALevelTile_CompleteTile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALevelTile::execCompleteTile)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CompleteTile();
	P_NATIVE_END;
}
// End Class ALevelTile Function CompleteTile

// Begin Class ALevelTile Function DeactivateTile
struct Z_Construct_UFunction_ALevelTile_DeactivateTile_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tile" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALevelTile_DeactivateTile_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ALevelTile, nullptr, "DeactivateTile", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_DeactivateTile_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALevelTile_DeactivateTile_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ALevelTile_DeactivateTile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALevelTile_DeactivateTile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALevelTile::execDeactivateTile)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DeactivateTile();
	P_NATIVE_END;
}
// End Class ALevelTile Function DeactivateTile

// Begin Class ALevelTile Function GetAliveEnemies
struct Z_Construct_UFunction_ALevelTile_GetAliveEnemies_Statics
{
	struct LevelTile_eventGetAliveEnemies_Parms
	{
		TArray<AActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Content" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ALevelTile_GetAliveEnemies_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ALevelTile_GetAliveEnemies_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelTile_eventGetAliveEnemies_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALevelTile_GetAliveEnemies_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALevelTile_GetAliveEnemies_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALevelTile_GetAliveEnemies_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_GetAliveEnemies_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALevelTile_GetAliveEnemies_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ALevelTile, nullptr, "GetAliveEnemies", nullptr, nullptr, Z_Construct_UFunction_ALevelTile_GetAliveEnemies_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_GetAliveEnemies_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALevelTile_GetAliveEnemies_Statics::LevelTile_eventGetAliveEnemies_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_GetAliveEnemies_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALevelTile_GetAliveEnemies_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ALevelTile_GetAliveEnemies_Statics::LevelTile_eventGetAliveEnemies_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALevelTile_GetAliveEnemies()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALevelTile_GetAliveEnemies_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALevelTile::execGetAliveEnemies)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<AActor*>*)Z_Param__Result=P_THIS->GetAliveEnemies();
	P_NATIVE_END;
}
// End Class ALevelTile Function GetAliveEnemies

// Begin Class ALevelTile Function GetAliveEnemyCount
struct Z_Construct_UFunction_ALevelTile_GetAliveEnemyCount_Statics
{
	struct LevelTile_eventGetAliveEnemyCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Content" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Content Management */" },
#endif
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Content Management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ALevelTile_GetAliveEnemyCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelTile_eventGetAliveEnemyCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALevelTile_GetAliveEnemyCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALevelTile_GetAliveEnemyCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_GetAliveEnemyCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALevelTile_GetAliveEnemyCount_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ALevelTile, nullptr, "GetAliveEnemyCount", nullptr, nullptr, Z_Construct_UFunction_ALevelTile_GetAliveEnemyCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_GetAliveEnemyCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALevelTile_GetAliveEnemyCount_Statics::LevelTile_eventGetAliveEnemyCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_GetAliveEnemyCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALevelTile_GetAliveEnemyCount_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ALevelTile_GetAliveEnemyCount_Statics::LevelTile_eventGetAliveEnemyCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALevelTile_GetAliveEnemyCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALevelTile_GetAliveEnemyCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALevelTile::execGetAliveEnemyCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetAliveEnemyCount();
	P_NATIVE_END;
}
// End Class ALevelTile Function GetAliveEnemyCount

// Begin Class ALevelTile Function GetConnectionPointLocations
struct Z_Construct_UFunction_ALevelTile_GetConnectionPointLocations_Statics
{
	struct LevelTile_eventGetConnectionPointLocations_Parms
	{
		TArray<FVector> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Connections" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Connection Points */" },
#endif
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Connection Points" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALevelTile_GetConnectionPointLocations_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ALevelTile_GetConnectionPointLocations_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelTile_eventGetConnectionPointLocations_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALevelTile_GetConnectionPointLocations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALevelTile_GetConnectionPointLocations_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALevelTile_GetConnectionPointLocations_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_GetConnectionPointLocations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALevelTile_GetConnectionPointLocations_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ALevelTile, nullptr, "GetConnectionPointLocations", nullptr, nullptr, Z_Construct_UFunction_ALevelTile_GetConnectionPointLocations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_GetConnectionPointLocations_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALevelTile_GetConnectionPointLocations_Statics::LevelTile_eventGetConnectionPointLocations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_GetConnectionPointLocations_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALevelTile_GetConnectionPointLocations_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ALevelTile_GetConnectionPointLocations_Statics::LevelTile_eventGetConnectionPointLocations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALevelTile_GetConnectionPointLocations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALevelTile_GetConnectionPointLocations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALevelTile::execGetConnectionPointLocations)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FVector>*)Z_Param__Result=P_THIS->GetConnectionPointLocations();
	P_NATIVE_END;
}
// End Class ALevelTile Function GetConnectionPointLocations

// Begin Class ALevelTile Function GetEntranceLocation
struct Z_Construct_UFunction_ALevelTile_GetEntranceLocation_Statics
{
	struct LevelTile_eventGetEntranceLocation_Parms
	{
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Connections" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALevelTile_GetEntranceLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelTile_eventGetEntranceLocation_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALevelTile_GetEntranceLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALevelTile_GetEntranceLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_GetEntranceLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALevelTile_GetEntranceLocation_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ALevelTile, nullptr, "GetEntranceLocation", nullptr, nullptr, Z_Construct_UFunction_ALevelTile_GetEntranceLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_GetEntranceLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALevelTile_GetEntranceLocation_Statics::LevelTile_eventGetEntranceLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_GetEntranceLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALevelTile_GetEntranceLocation_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ALevelTile_GetEntranceLocation_Statics::LevelTile_eventGetEntranceLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALevelTile_GetEntranceLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALevelTile_GetEntranceLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALevelTile::execGetEntranceLocation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetEntranceLocation();
	P_NATIVE_END;
}
// End Class ALevelTile Function GetEntranceLocation

// Begin Class ALevelTile Function GetExitLocation
struct Z_Construct_UFunction_ALevelTile_GetExitLocation_Statics
{
	struct LevelTile_eventGetExitLocation_Parms
	{
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Connections" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALevelTile_GetExitLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelTile_eventGetExitLocation_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALevelTile_GetExitLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALevelTile_GetExitLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_GetExitLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALevelTile_GetExitLocation_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ALevelTile, nullptr, "GetExitLocation", nullptr, nullptr, Z_Construct_UFunction_ALevelTile_GetExitLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_GetExitLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALevelTile_GetExitLocation_Statics::LevelTile_eventGetExitLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_GetExitLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALevelTile_GetExitLocation_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ALevelTile_GetExitLocation_Statics::LevelTile_eventGetExitLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALevelTile_GetExitLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALevelTile_GetExitLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALevelTile::execGetExitLocation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetExitLocation();
	P_NATIVE_END;
}
// End Class ALevelTile Function GetExitLocation

// Begin Class ALevelTile Function GetTileBounds
struct Z_Construct_UFunction_ALevelTile_GetTileBounds_Statics
{
	struct LevelTile_eventGetTileBounds_Parms
	{
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tile" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALevelTile_GetTileBounds_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelTile_eventGetTileBounds_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALevelTile_GetTileBounds_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALevelTile_GetTileBounds_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_GetTileBounds_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALevelTile_GetTileBounds_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ALevelTile, nullptr, "GetTileBounds", nullptr, nullptr, Z_Construct_UFunction_ALevelTile_GetTileBounds_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_GetTileBounds_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALevelTile_GetTileBounds_Statics::LevelTile_eventGetTileBounds_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_GetTileBounds_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALevelTile_GetTileBounds_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ALevelTile_GetTileBounds_Statics::LevelTile_eventGetTileBounds_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALevelTile_GetTileBounds()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALevelTile_GetTileBounds_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALevelTile::execGetTileBounds)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetTileBounds();
	P_NATIVE_END;
}
// End Class ALevelTile Function GetTileBounds

// Begin Class ALevelTile Function GetTileSize
struct Z_Construct_UFunction_ALevelTile_GetTileSize_Statics
{
	struct LevelTile_eventGetTileSize_Parms
	{
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tile" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ALevelTile_GetTileSize_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelTile_eventGetTileSize_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALevelTile_GetTileSize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALevelTile_GetTileSize_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_GetTileSize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALevelTile_GetTileSize_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ALevelTile, nullptr, "GetTileSize", nullptr, nullptr, Z_Construct_UFunction_ALevelTile_GetTileSize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_GetTileSize_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALevelTile_GetTileSize_Statics::LevelTile_eventGetTileSize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_GetTileSize_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALevelTile_GetTileSize_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ALevelTile_GetTileSize_Statics::LevelTile_eventGetTileSize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALevelTile_GetTileSize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALevelTile_GetTileSize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALevelTile::execGetTileSize)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetTileSize();
	P_NATIVE_END;
}
// End Class ALevelTile Function GetTileSize

// Begin Class ALevelTile Function GetTileType
struct Z_Construct_UFunction_ALevelTile_GetTileType_Statics
{
	struct LevelTile_eventGetTileType_Parms
	{
		ETileType ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tile" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Queries */" },
#endif
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Queries" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ALevelTile_GetTileType_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ALevelTile_GetTileType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelTile_eventGetTileType_Parms, ReturnValue), Z_Construct_UEnum_RoughReality_ETileType, METADATA_PARAMS(0, nullptr) }; // 3250710945
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALevelTile_GetTileType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALevelTile_GetTileType_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALevelTile_GetTileType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_GetTileType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALevelTile_GetTileType_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ALevelTile, nullptr, "GetTileType", nullptr, nullptr, Z_Construct_UFunction_ALevelTile_GetTileType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_GetTileType_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALevelTile_GetTileType_Statics::LevelTile_eventGetTileType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_GetTileType_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALevelTile_GetTileType_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ALevelTile_GetTileType_Statics::LevelTile_eventGetTileType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALevelTile_GetTileType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALevelTile_GetTileType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALevelTile::execGetTileType)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ETileType*)Z_Param__Result=P_THIS->GetTileType();
	P_NATIVE_END;
}
// End Class ALevelTile Function GetTileType

// Begin Class ALevelTile Function InitializeTile
struct Z_Construct_UFunction_ALevelTile_InitializeTile_Statics
{
	struct LevelTile_eventInitializeTile_Parms
	{
		UTileDefinitionDataAsset* InTileDefinition;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tile" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialization */" },
#endif
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialization" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InTileDefinition;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ALevelTile_InitializeTile_Statics::NewProp_InTileDefinition = { "InTileDefinition", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelTile_eventInitializeTile_Parms, InTileDefinition), Z_Construct_UClass_UTileDefinitionDataAsset_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALevelTile_InitializeTile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALevelTile_InitializeTile_Statics::NewProp_InTileDefinition,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_InitializeTile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALevelTile_InitializeTile_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ALevelTile, nullptr, "InitializeTile", nullptr, nullptr, Z_Construct_UFunction_ALevelTile_InitializeTile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_InitializeTile_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALevelTile_InitializeTile_Statics::LevelTile_eventInitializeTile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_InitializeTile_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALevelTile_InitializeTile_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ALevelTile_InitializeTile_Statics::LevelTile_eventInitializeTile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALevelTile_InitializeTile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALevelTile_InitializeTile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALevelTile::execInitializeTile)
{
	P_GET_OBJECT(UTileDefinitionDataAsset,Z_Param_InTileDefinition);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeTile(Z_Param_InTileDefinition);
	P_NATIVE_END;
}
// End Class ALevelTile Function InitializeTile

// Begin Class ALevelTile Function IsActive
struct Z_Construct_UFunction_ALevelTile_IsActive_Statics
{
	struct LevelTile_eventIsActive_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tile" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ALevelTile_IsActive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((LevelTile_eventIsActive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ALevelTile_IsActive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LevelTile_eventIsActive_Parms), &Z_Construct_UFunction_ALevelTile_IsActive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALevelTile_IsActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALevelTile_IsActive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_IsActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALevelTile_IsActive_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ALevelTile, nullptr, "IsActive", nullptr, nullptr, Z_Construct_UFunction_ALevelTile_IsActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_IsActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALevelTile_IsActive_Statics::LevelTile_eventIsActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_IsActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALevelTile_IsActive_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ALevelTile_IsActive_Statics::LevelTile_eventIsActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALevelTile_IsActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALevelTile_IsActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALevelTile::execIsActive)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsActive();
	P_NATIVE_END;
}
// End Class ALevelTile Function IsActive

// Begin Class ALevelTile Function IsCompleted
struct Z_Construct_UFunction_ALevelTile_IsCompleted_Statics
{
	struct LevelTile_eventIsCompleted_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tile" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ALevelTile_IsCompleted_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((LevelTile_eventIsCompleted_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ALevelTile_IsCompleted_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LevelTile_eventIsCompleted_Parms), &Z_Construct_UFunction_ALevelTile_IsCompleted_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALevelTile_IsCompleted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALevelTile_IsCompleted_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_IsCompleted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALevelTile_IsCompleted_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ALevelTile, nullptr, "IsCompleted", nullptr, nullptr, Z_Construct_UFunction_ALevelTile_IsCompleted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_IsCompleted_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALevelTile_IsCompleted_Statics::LevelTile_eventIsCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_IsCompleted_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALevelTile_IsCompleted_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ALevelTile_IsCompleted_Statics::LevelTile_eventIsCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALevelTile_IsCompleted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALevelTile_IsCompleted_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALevelTile::execIsCompleted)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsCompleted();
	P_NATIVE_END;
}
// End Class ALevelTile Function IsCompleted

// Begin Class ALevelTile Function OnEnemyDestroyed
struct Z_Construct_UFunction_ALevelTile_OnEnemyDestroyed_Statics
{
	struct LevelTile_eventOnEnemyDestroyed_Parms
	{
		AActor* DestroyedEnemy;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Content" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DestroyedEnemy;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_ALevelTile_OnEnemyDestroyed_Statics::NewProp_DestroyedEnemy = { "DestroyedEnemy", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelTile_eventOnEnemyDestroyed_Parms, DestroyedEnemy), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ALevelTile_OnEnemyDestroyed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ALevelTile_OnEnemyDestroyed_Statics::NewProp_DestroyedEnemy,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_OnEnemyDestroyed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALevelTile_OnEnemyDestroyed_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ALevelTile, nullptr, "OnEnemyDestroyed", nullptr, nullptr, Z_Construct_UFunction_ALevelTile_OnEnemyDestroyed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_OnEnemyDestroyed_Statics::PropPointers), sizeof(Z_Construct_UFunction_ALevelTile_OnEnemyDestroyed_Statics::LevelTile_eventOnEnemyDestroyed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_OnEnemyDestroyed_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALevelTile_OnEnemyDestroyed_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ALevelTile_OnEnemyDestroyed_Statics::LevelTile_eventOnEnemyDestroyed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ALevelTile_OnEnemyDestroyed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALevelTile_OnEnemyDestroyed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALevelTile::execOnEnemyDestroyed)
{
	P_GET_OBJECT(AActor,Z_Param_DestroyedEnemy);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnEnemyDestroyed(Z_Param_DestroyedEnemy);
	P_NATIVE_END;
}
// End Class ALevelTile Function OnEnemyDestroyed

// Begin Class ALevelTile Function PlayAmbientSound
struct Z_Construct_UFunction_ALevelTile_PlayAmbientSound_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Audio */" },
#endif
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALevelTile_PlayAmbientSound_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ALevelTile, nullptr, "PlayAmbientSound", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_PlayAmbientSound_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALevelTile_PlayAmbientSound_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ALevelTile_PlayAmbientSound()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALevelTile_PlayAmbientSound_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALevelTile::execPlayAmbientSound)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PlayAmbientSound();
	P_NATIVE_END;
}
// End Class ALevelTile Function PlayAmbientSound

// Begin Class ALevelTile Function SpawnEnemies
struct Z_Construct_UFunction_ALevelTile_SpawnEnemies_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Content" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALevelTile_SpawnEnemies_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ALevelTile, nullptr, "SpawnEnemies", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_SpawnEnemies_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALevelTile_SpawnEnemies_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ALevelTile_SpawnEnemies()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALevelTile_SpawnEnemies_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALevelTile::execSpawnEnemies)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SpawnEnemies();
	P_NATIVE_END;
}
// End Class ALevelTile Function SpawnEnemies

// Begin Class ALevelTile Function SpawnInteractables
struct Z_Construct_UFunction_ALevelTile_SpawnInteractables_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Content" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALevelTile_SpawnInteractables_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ALevelTile, nullptr, "SpawnInteractables", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_SpawnInteractables_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALevelTile_SpawnInteractables_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ALevelTile_SpawnInteractables()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALevelTile_SpawnInteractables_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALevelTile::execSpawnInteractables)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SpawnInteractables();
	P_NATIVE_END;
}
// End Class ALevelTile Function SpawnInteractables

// Begin Class ALevelTile Function SpawnItems
struct Z_Construct_UFunction_ALevelTile_SpawnItems_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Content" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALevelTile_SpawnItems_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ALevelTile, nullptr, "SpawnItems", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_SpawnItems_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALevelTile_SpawnItems_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ALevelTile_SpawnItems()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALevelTile_SpawnItems_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALevelTile::execSpawnItems)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SpawnItems();
	P_NATIVE_END;
}
// End Class ALevelTile Function SpawnItems

// Begin Class ALevelTile Function SpawnTileContent
struct Z_Construct_UFunction_ALevelTile_SpawnTileContent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Content" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Content Spawning */" },
#endif
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Content Spawning" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALevelTile_SpawnTileContent_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ALevelTile, nullptr, "SpawnTileContent", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_SpawnTileContent_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALevelTile_SpawnTileContent_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ALevelTile_SpawnTileContent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALevelTile_SpawnTileContent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALevelTile::execSpawnTileContent)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SpawnTileContent();
	P_NATIVE_END;
}
// End Class ALevelTile Function SpawnTileContent

// Begin Class ALevelTile Function StopAmbientSound
struct Z_Construct_UFunction_ALevelTile_StopAmbientSound_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Audio" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ALevelTile_StopAmbientSound_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ALevelTile, nullptr, "StopAmbientSound", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ALevelTile_StopAmbientSound_Statics::Function_MetaDataParams), Z_Construct_UFunction_ALevelTile_StopAmbientSound_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ALevelTile_StopAmbientSound()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ALevelTile_StopAmbientSound_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ALevelTile::execStopAmbientSound)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopAmbientSound();
	P_NATIVE_END;
}
// End Class ALevelTile Function StopAmbientSound

// Begin Class ALevelTile
void ALevelTile::StaticRegisterNativesALevelTile()
{
	UClass* Class = ALevelTile::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ActivateTile", &ALevelTile::execActivateTile },
		{ "ApplyTileLighting", &ALevelTile::execApplyTileLighting },
		{ "CheckCompletionConditions", &ALevelTile::execCheckCompletionConditions },
		{ "ClearTileContent", &ALevelTile::execClearTileContent },
		{ "CompleteTile", &ALevelTile::execCompleteTile },
		{ "DeactivateTile", &ALevelTile::execDeactivateTile },
		{ "GetAliveEnemies", &ALevelTile::execGetAliveEnemies },
		{ "GetAliveEnemyCount", &ALevelTile::execGetAliveEnemyCount },
		{ "GetConnectionPointLocations", &ALevelTile::execGetConnectionPointLocations },
		{ "GetEntranceLocation", &ALevelTile::execGetEntranceLocation },
		{ "GetExitLocation", &ALevelTile::execGetExitLocation },
		{ "GetTileBounds", &ALevelTile::execGetTileBounds },
		{ "GetTileSize", &ALevelTile::execGetTileSize },
		{ "GetTileType", &ALevelTile::execGetTileType },
		{ "InitializeTile", &ALevelTile::execInitializeTile },
		{ "IsActive", &ALevelTile::execIsActive },
		{ "IsCompleted", &ALevelTile::execIsCompleted },
		{ "OnEnemyDestroyed", &ALevelTile::execOnEnemyDestroyed },
		{ "PlayAmbientSound", &ALevelTile::execPlayAmbientSound },
		{ "SpawnEnemies", &ALevelTile::execSpawnEnemies },
		{ "SpawnInteractables", &ALevelTile::execSpawnInteractables },
		{ "SpawnItems", &ALevelTile::execSpawnItems },
		{ "SpawnTileContent", &ALevelTile::execSpawnTileContent },
		{ "StopAmbientSound", &ALevelTile::execStopAmbientSound },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(ALevelTile);
UClass* Z_Construct_UClass_ALevelTile_NoRegister()
{
	return ALevelTile::StaticClass();
}
struct Z_Construct_UClass_ALevelTile_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Level Tile Actor - Represents a single tile in the procedurally generated level\n * Handles spawning of enemies, items, and other content based on tile definition\n */" },
#endif
		{ "IncludePath", "LevelGeneration/LevelTile.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Level Tile Actor - Represents a single tile in the procedurally generated level\nHandles spawning of enemies, items, and other content based on tile definition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RootSceneComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Components */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TileMesh_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AmbientAudio_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TileDefinition_MetaData[] = {
		{ "Category", "Tile" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tile Configuration */" },
#endif
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tile Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TileType_MetaData[] = {
		{ "Category", "Tile" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "Tile" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsCompleted_MetaData[] = {
		{ "Category", "Tile" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnedEnemies_MetaData[] = {
		{ "Category", "Content" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Spawned Content */" },
#endif
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spawned Content" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnedItems_MetaData[] = {
		{ "Category", "Content" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnedInteractables_MetaData[] = {
		{ "Category", "Content" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTileActivated_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Events */" },
#endif
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTileDeactivated_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTileCompleted_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "LevelGeneration/LevelTile.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RootSceneComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TileMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AmbientAudio;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TileDefinition;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TileType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TileType;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static void NewProp_bIsCompleted_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsCompleted;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SpawnedEnemies_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SpawnedEnemies;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SpawnedItems_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SpawnedItems;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SpawnedInteractables_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SpawnedInteractables;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTileActivated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTileDeactivated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTileCompleted;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ALevelTile_ActivateTile, "ActivateTile" }, // 1467170041
		{ &Z_Construct_UFunction_ALevelTile_ApplyTileLighting, "ApplyTileLighting" }, // 858729211
		{ &Z_Construct_UFunction_ALevelTile_CheckCompletionConditions, "CheckCompletionConditions" }, // 3362960493
		{ &Z_Construct_UFunction_ALevelTile_ClearTileContent, "ClearTileContent" }, // 3579779246
		{ &Z_Construct_UFunction_ALevelTile_CompleteTile, "CompleteTile" }, // 1641758160
		{ &Z_Construct_UFunction_ALevelTile_DeactivateTile, "DeactivateTile" }, // 165110852
		{ &Z_Construct_UFunction_ALevelTile_GetAliveEnemies, "GetAliveEnemies" }, // 839462869
		{ &Z_Construct_UFunction_ALevelTile_GetAliveEnemyCount, "GetAliveEnemyCount" }, // 281701396
		{ &Z_Construct_UFunction_ALevelTile_GetConnectionPointLocations, "GetConnectionPointLocations" }, // 1048376209
		{ &Z_Construct_UFunction_ALevelTile_GetEntranceLocation, "GetEntranceLocation" }, // 2667186155
		{ &Z_Construct_UFunction_ALevelTile_GetExitLocation, "GetExitLocation" }, // 1661681229
		{ &Z_Construct_UFunction_ALevelTile_GetTileBounds, "GetTileBounds" }, // 3513762380
		{ &Z_Construct_UFunction_ALevelTile_GetTileSize, "GetTileSize" }, // 1398628202
		{ &Z_Construct_UFunction_ALevelTile_GetTileType, "GetTileType" }, // 3371086159
		{ &Z_Construct_UFunction_ALevelTile_InitializeTile, "InitializeTile" }, // 80880779
		{ &Z_Construct_UFunction_ALevelTile_IsActive, "IsActive" }, // 2056101065
		{ &Z_Construct_UFunction_ALevelTile_IsCompleted, "IsCompleted" }, // 2736327620
		{ &Z_Construct_UFunction_ALevelTile_OnEnemyDestroyed, "OnEnemyDestroyed" }, // 1531522462
		{ &Z_Construct_UFunction_ALevelTile_PlayAmbientSound, "PlayAmbientSound" }, // 3658797005
		{ &Z_Construct_UFunction_ALevelTile_SpawnEnemies, "SpawnEnemies" }, // 2264013184
		{ &Z_Construct_UFunction_ALevelTile_SpawnInteractables, "SpawnInteractables" }, // 2670522193
		{ &Z_Construct_UFunction_ALevelTile_SpawnItems, "SpawnItems" }, // 3398672967
		{ &Z_Construct_UFunction_ALevelTile_SpawnTileContent, "SpawnTileContent" }, // 665902113
		{ &Z_Construct_UFunction_ALevelTile_StopAmbientSound, "StopAmbientSound" }, // 4197025010
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ALevelTile>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ALevelTile_Statics::NewProp_RootSceneComponent = { "RootSceneComponent", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALevelTile, RootSceneComponent), Z_Construct_UClass_USceneComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RootSceneComponent_MetaData), NewProp_RootSceneComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ALevelTile_Statics::NewProp_TileMesh = { "TileMesh", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALevelTile, TileMesh), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TileMesh_MetaData), NewProp_TileMesh_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ALevelTile_Statics::NewProp_AmbientAudio = { "AmbientAudio", nullptr, (EPropertyFlags)0x00200800000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALevelTile, AmbientAudio), Z_Construct_UClass_UAudioComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AmbientAudio_MetaData), NewProp_AmbientAudio_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ALevelTile_Statics::NewProp_TileDefinition = { "TileDefinition", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALevelTile, TileDefinition), Z_Construct_UClass_UTileDefinitionDataAsset_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TileDefinition_MetaData), NewProp_TileDefinition_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_ALevelTile_Statics::NewProp_TileType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_ALevelTile_Statics::NewProp_TileType = { "TileType", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALevelTile, TileType), Z_Construct_UEnum_RoughReality_ETileType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TileType_MetaData), NewProp_TileType_MetaData) }; // 3250710945
void Z_Construct_UClass_ALevelTile_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((ALevelTile*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ALevelTile_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ALevelTile), &Z_Construct_UClass_ALevelTile_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
void Z_Construct_UClass_ALevelTile_Statics::NewProp_bIsCompleted_SetBit(void* Obj)
{
	((ALevelTile*)Obj)->bIsCompleted = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ALevelTile_Statics::NewProp_bIsCompleted = { "bIsCompleted", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ALevelTile), &Z_Construct_UClass_ALevelTile_Statics::NewProp_bIsCompleted_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsCompleted_MetaData), NewProp_bIsCompleted_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ALevelTile_Statics::NewProp_SpawnedEnemies_Inner = { "SpawnedEnemies", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ALevelTile_Statics::NewProp_SpawnedEnemies = { "SpawnedEnemies", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALevelTile, SpawnedEnemies), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnedEnemies_MetaData), NewProp_SpawnedEnemies_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ALevelTile_Statics::NewProp_SpawnedItems_Inner = { "SpawnedItems", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ALevelTile_Statics::NewProp_SpawnedItems = { "SpawnedItems", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALevelTile, SpawnedItems), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnedItems_MetaData), NewProp_SpawnedItems_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ALevelTile_Statics::NewProp_SpawnedInteractables_Inner = { "SpawnedInteractables", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ALevelTile_Statics::NewProp_SpawnedInteractables = { "SpawnedInteractables", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALevelTile, SpawnedInteractables), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnedInteractables_MetaData), NewProp_SpawnedInteractables_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ALevelTile_Statics::NewProp_OnTileActivated = { "OnTileActivated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALevelTile, OnTileActivated), Z_Construct_UDelegateFunction_RoughReality_OnTileActivated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTileActivated_MetaData), NewProp_OnTileActivated_MetaData) }; // 2746467808
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ALevelTile_Statics::NewProp_OnTileDeactivated = { "OnTileDeactivated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALevelTile, OnTileDeactivated), Z_Construct_UDelegateFunction_RoughReality_OnTileDeactivated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTileDeactivated_MetaData), NewProp_OnTileDeactivated_MetaData) }; // 4211829372
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ALevelTile_Statics::NewProp_OnTileCompleted = { "OnTileCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ALevelTile, OnTileCompleted), Z_Construct_UDelegateFunction_RoughReality_OnTileCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTileCompleted_MetaData), NewProp_OnTileCompleted_MetaData) }; // 3755097288
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ALevelTile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALevelTile_Statics::NewProp_RootSceneComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALevelTile_Statics::NewProp_TileMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALevelTile_Statics::NewProp_AmbientAudio,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALevelTile_Statics::NewProp_TileDefinition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALevelTile_Statics::NewProp_TileType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALevelTile_Statics::NewProp_TileType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALevelTile_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALevelTile_Statics::NewProp_bIsCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALevelTile_Statics::NewProp_SpawnedEnemies_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALevelTile_Statics::NewProp_SpawnedEnemies,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALevelTile_Statics::NewProp_SpawnedItems_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALevelTile_Statics::NewProp_SpawnedItems,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALevelTile_Statics::NewProp_SpawnedInteractables_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALevelTile_Statics::NewProp_SpawnedInteractables,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALevelTile_Statics::NewProp_OnTileActivated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALevelTile_Statics::NewProp_OnTileDeactivated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ALevelTile_Statics::NewProp_OnTileCompleted,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ALevelTile_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ALevelTile_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ALevelTile_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ALevelTile_Statics::ClassParams = {
	&ALevelTile::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ALevelTile_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ALevelTile_Statics::PropPointers),
	0,
	0x009000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ALevelTile_Statics::Class_MetaDataParams), Z_Construct_UClass_ALevelTile_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ALevelTile()
{
	if (!Z_Registration_Info_UClass_ALevelTile.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ALevelTile.OuterSingleton, Z_Construct_UClass_ALevelTile_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ALevelTile.OuterSingleton;
}
template<> ROUGHREALITY_API UClass* StaticClass<ALevelTile>()
{
	return ALevelTile::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ALevelTile);
ALevelTile::~ALevelTile() {}
// End Class ALevelTile

// Begin Registration
struct Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_LevelGeneration_LevelTile_h_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ALevelTile, ALevelTile::StaticClass, TEXT("ALevelTile"), &Z_Registration_Info_UClass_ALevelTile, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ALevelTile), 1744378580U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_LevelGeneration_LevelTile_h_282743352(TEXT("/Script/RoughReality"),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_LevelGeneration_LevelTile_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_LevelGeneration_LevelTile_h_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
