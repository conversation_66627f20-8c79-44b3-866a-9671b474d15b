{"Version": "1.2", "Data": {"Source": "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\roughreality\\module.roughreality.1.cpp", "ProvidedModule": "", "PCH": "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\x64\\roughrealityeditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.cpp20.h.pch", "Includes": ["g:\\gamedev\\roughreality\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\roughreality\\definitions.roughreality.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\asyncloadingmanager.gen.cpp", "g:\\gamedev\\roughreality\\source\\roughreality\\core\\asyncloadingmanager.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\engine\\streamablemanager.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\coreuobject\\public\\misc\\packageaccesstracking.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\sourcelocation.h", "c:\\program files\\microsoft visual studio\\2022\\community\\vc\\tools\\msvc\\14.38.33130\\include\\source_location", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\sourcelocationutils.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\engine\\assetmanager.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\engine\\assetmanagertypes.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\assetmanagertypes.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\coreuobject\\public\\misc\\assetregistryinterface.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\assetregistrymodule.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\iassetregistry.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\assetregistry\\uht\\iassetregistry.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformchunkinstall.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\engine\\contentencryptionconfig.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\assetmanager.generated.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\asyncloadingmanager.generated.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\characterstatsdataasset.gen.cpp", "g:\\gamedev\\roughreality\\source\\roughreality\\dataassets\\characterstatsdataasset.h", "g:\\gamedev\\roughreality\\source\\roughreality\\core\\roughrealitydataasset.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\roughrealitydataasset.generated.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\characterstatsdataasset.generated.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\gameanalyticsmanager.gen.cpp", "g:\\gamedev\\roughreality\\source\\roughreality\\analytics\\gameanalyticsmanager.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\gameanalyticsmanager.generated.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\gameeventsystem.gen.cpp", "g:\\gamedev\\roughreality\\source\\roughreality\\core\\gameeventsystem.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\gameeventsystem.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}