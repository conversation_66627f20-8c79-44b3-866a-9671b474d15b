"G:\Gamedev\RoughReality\Source\RoughReality\DataAssets\CharacterStatsDataAsset.cpp"
/FI"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\RoughRealityEditor\Development\UnrealEd\SharedPCH.UnrealEd.Project.ValApi.Cpp20.h"
/FI"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\Definitions.RoughReality.h"
/Yu"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\RoughRealityEditor\Development\UnrealEd\SharedPCH.UnrealEd.Project.ValApi.Cpp20.h"
/Fp"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\RoughRealityEditor\Development\UnrealEd\SharedPCH.UnrealEd.Project.ValApi.Cpp20.h.pch"
/Fo"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\CharacterStatsDataAsset.cpp.obj"
/experimental:log "G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\CharacterStatsDataAsset.cpp.sarif"
/sourceDependencies "G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\CharacterStatsDataAsset.cpp.dep.json"
@"G:\Gamedev\RoughReality\Intermediate\Build\Win64\x64\UnrealEditor\Development\RoughReality\RoughReality.Shared.rsp"
/d2ssa-cfg-question-
/Zc:inline
/nologo
/Oi
/FC
/c
/Gw
/Gy
/utf-8
/wd4819
/DSAL_NO_ATTRIBUTE_DECLARATIONS=1
/permissive-
/Zc:strictStrings-
/Zc:__cplusplus
/D_CRT_STDIO_LEGACY_WIDE_SPECIFIERS=1
/D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS=1
/D_WINDLL
/D_DISABLE_EXTENDED_ALIGNED_STORAGE
/Ob2
/d2ExtendedWarningInfo
/Ox
/Ot
/GF
/errorReport:prompt
/EHsc
/DPLATFORM_EXCEPTIONS_DISABLED=0
/Z7
/MD
/bigobj
/fp:fast
/Zo
/Zp8
/we4456
/we4458
/we4459
/we4668
/wd4244
/wd4838
/TP
/GR-
/W4
/std:c++20
/Zc:preprocessor
/wd5054