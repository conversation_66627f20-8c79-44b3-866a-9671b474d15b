// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RoughReality/Core/ObjectPoolManager.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeObjectPoolManager() {}

// Begin Cross Module References
COREUOBJECT_API UClass* Z_Construct_UClass_UObject_NoRegister();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ROUGHREALITY_API UClass* Z_Construct_UClass_AObjectPoolManager();
ROUGHREALITY_API UClass* Z_Construct_UClass_AObjectPoolManager_NoRegister();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnObjectReturned__DelegateSignature();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnObjectSpawned__DelegateSignature();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FObjectPoolConfiguration();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FPooledObjectInfo();
UPackage* Z_Construct_UPackage__Script_RoughReality();
// End Cross Module References

// Begin Delegate FOnObjectSpawned
struct Z_Construct_UDelegateFunction_RoughReality_OnObjectSpawned__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnObjectSpawned_Parms
	{
		AActor* SpawnedActor;
		FName PoolName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SpawnedActor;
	static const UECodeGen_Private::FNamePropertyParams NewProp_PoolName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnObjectSpawned__DelegateSignature_Statics::NewProp_SpawnedActor = { "SpawnedActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnObjectSpawned_Parms, SpawnedActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UDelegateFunction_RoughReality_OnObjectSpawned__DelegateSignature_Statics::NewProp_PoolName = { "PoolName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnObjectSpawned_Parms, PoolName), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnObjectSpawned__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnObjectSpawned__DelegateSignature_Statics::NewProp_SpawnedActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnObjectSpawned__DelegateSignature_Statics::NewProp_PoolName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnObjectSpawned__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnObjectSpawned__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnObjectSpawned__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnObjectSpawned__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnObjectSpawned__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnObjectSpawned__DelegateSignature_Statics::_Script_RoughReality_eventOnObjectSpawned_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnObjectSpawned__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnObjectSpawned__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnObjectSpawned__DelegateSignature_Statics::_Script_RoughReality_eventOnObjectSpawned_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnObjectSpawned__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnObjectSpawned__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnObjectSpawned_DelegateWrapper(const FMulticastScriptDelegate& OnObjectSpawned, AActor* SpawnedActor, FName PoolName)
{
	struct _Script_RoughReality_eventOnObjectSpawned_Parms
	{
		AActor* SpawnedActor;
		FName PoolName;
	};
	_Script_RoughReality_eventOnObjectSpawned_Parms Parms;
	Parms.SpawnedActor=SpawnedActor;
	Parms.PoolName=PoolName;
	OnObjectSpawned.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnObjectSpawned

// Begin Delegate FOnObjectReturned
struct Z_Construct_UDelegateFunction_RoughReality_OnObjectReturned__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnObjectReturned_Parms
	{
		AActor* ReturnedActor;
		FName PoolName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnedActor;
	static const UECodeGen_Private::FNamePropertyParams NewProp_PoolName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnObjectReturned__DelegateSignature_Statics::NewProp_ReturnedActor = { "ReturnedActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnObjectReturned_Parms, ReturnedActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UDelegateFunction_RoughReality_OnObjectReturned__DelegateSignature_Statics::NewProp_PoolName = { "PoolName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnObjectReturned_Parms, PoolName), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnObjectReturned__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnObjectReturned__DelegateSignature_Statics::NewProp_ReturnedActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnObjectReturned__DelegateSignature_Statics::NewProp_PoolName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnObjectReturned__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnObjectReturned__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnObjectReturned__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnObjectReturned__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnObjectReturned__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnObjectReturned__DelegateSignature_Statics::_Script_RoughReality_eventOnObjectReturned_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnObjectReturned__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnObjectReturned__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnObjectReturned__DelegateSignature_Statics::_Script_RoughReality_eventOnObjectReturned_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnObjectReturned__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnObjectReturned__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnObjectReturned_DelegateWrapper(const FMulticastScriptDelegate& OnObjectReturned, AActor* ReturnedActor, FName PoolName)
{
	struct _Script_RoughReality_eventOnObjectReturned_Parms
	{
		AActor* ReturnedActor;
		FName PoolName;
	};
	_Script_RoughReality_eventOnObjectReturned_Parms Parms;
	Parms.ReturnedActor=ReturnedActor;
	Parms.PoolName=PoolName;
	OnObjectReturned.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnObjectReturned

// Begin ScriptStruct FObjectPoolConfiguration
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_ObjectPoolConfiguration;
class UScriptStruct* FObjectPoolConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_ObjectPoolConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_ObjectPoolConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FObjectPoolConfiguration, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("ObjectPoolConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_ObjectPoolConfiguration.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FObjectPoolConfiguration>()
{
	return FObjectPoolConfiguration::StaticStruct();
}
struct Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PoolName_MetaData[] = {
		{ "Category", "Pool Config" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorClass_MetaData[] = {
		{ "Category", "Pool Config" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InitialPoolSize_MetaData[] = {
		{ "Category", "Pool Config" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxPoolSize_MetaData[] = {
		{ "Category", "Pool Config" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanGrowPool_MetaData[] = {
		{ "Category", "Pool Config" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPrewarmPool_MetaData[] = {
		{ "Category", "Pool Config" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AutoReturnTime_MetaData[] = {
		{ "Category", "Pool Config" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_PoolName;
	static const UECodeGen_Private::FSoftClassPropertyParams NewProp_ActorClass;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InitialPoolSize;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxPoolSize;
	static void NewProp_bCanGrowPool_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanGrowPool;
	static void NewProp_bPrewarmPool_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPrewarmPool;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AutoReturnTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FObjectPoolConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::NewProp_PoolName = { "PoolName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FObjectPoolConfiguration, PoolName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PoolName_MetaData), NewProp_PoolName_MetaData) };
const UECodeGen_Private::FSoftClassPropertyParams Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::NewProp_ActorClass = { "ActorClass", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftClass, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FObjectPoolConfiguration, ActorClass), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorClass_MetaData), NewProp_ActorClass_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::NewProp_InitialPoolSize = { "InitialPoolSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FObjectPoolConfiguration, InitialPoolSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InitialPoolSize_MetaData), NewProp_InitialPoolSize_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::NewProp_MaxPoolSize = { "MaxPoolSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FObjectPoolConfiguration, MaxPoolSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxPoolSize_MetaData), NewProp_MaxPoolSize_MetaData) };
void Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::NewProp_bCanGrowPool_SetBit(void* Obj)
{
	((FObjectPoolConfiguration*)Obj)->bCanGrowPool = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::NewProp_bCanGrowPool = { "bCanGrowPool", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FObjectPoolConfiguration), &Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::NewProp_bCanGrowPool_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanGrowPool_MetaData), NewProp_bCanGrowPool_MetaData) };
void Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::NewProp_bPrewarmPool_SetBit(void* Obj)
{
	((FObjectPoolConfiguration*)Obj)->bPrewarmPool = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::NewProp_bPrewarmPool = { "bPrewarmPool", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FObjectPoolConfiguration), &Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::NewProp_bPrewarmPool_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPrewarmPool_MetaData), NewProp_bPrewarmPool_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::NewProp_AutoReturnTime = { "AutoReturnTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FObjectPoolConfiguration, AutoReturnTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AutoReturnTime_MetaData), NewProp_AutoReturnTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::NewProp_PoolName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::NewProp_ActorClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::NewProp_InitialPoolSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::NewProp_MaxPoolSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::NewProp_bCanGrowPool,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::NewProp_bPrewarmPool,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::NewProp_AutoReturnTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	&NewStructOps,
	"ObjectPoolConfiguration",
	Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::PropPointers),
	sizeof(FObjectPoolConfiguration),
	alignof(FObjectPoolConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FObjectPoolConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_ObjectPoolConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_ObjectPoolConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_ObjectPoolConfiguration.InnerSingleton;
}
// End ScriptStruct FObjectPoolConfiguration

// Begin ScriptStruct FPooledObjectInfo
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_PooledObjectInfo;
class UScriptStruct* FPooledObjectInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_PooledObjectInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_PooledObjectInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPooledObjectInfo, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("PooledObjectInfo"));
	}
	return Z_Registration_Info_UScriptStruct_PooledObjectInfo.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FPooledObjectInfo>()
{
	return FPooledObjectInfo::StaticStruct();
}
struct Z_Construct_UScriptStruct_FPooledObjectInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Actor_MetaData[] = {
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bInUse_MetaData[] = {
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnTime_MetaData[] = {
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PoolName_MetaData[] = {
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static void NewProp_bInUse_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInUse;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpawnTime;
	static const UECodeGen_Private::FNamePropertyParams NewProp_PoolName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPooledObjectInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FPooledObjectInfo_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPooledObjectInfo, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Actor_MetaData), NewProp_Actor_MetaData) };
void Z_Construct_UScriptStruct_FPooledObjectInfo_Statics::NewProp_bInUse_SetBit(void* Obj)
{
	((FPooledObjectInfo*)Obj)->bInUse = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPooledObjectInfo_Statics::NewProp_bInUse = { "bInUse", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPooledObjectInfo), &Z_Construct_UScriptStruct_FPooledObjectInfo_Statics::NewProp_bInUse_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bInUse_MetaData), NewProp_bInUse_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPooledObjectInfo_Statics::NewProp_SpawnTime = { "SpawnTime", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPooledObjectInfo, SpawnTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnTime_MetaData), NewProp_SpawnTime_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FPooledObjectInfo_Statics::NewProp_PoolName = { "PoolName", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPooledObjectInfo, PoolName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PoolName_MetaData), NewProp_PoolName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPooledObjectInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPooledObjectInfo_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPooledObjectInfo_Statics::NewProp_bInUse,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPooledObjectInfo_Statics::NewProp_SpawnTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPooledObjectInfo_Statics::NewProp_PoolName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPooledObjectInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPooledObjectInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	&NewStructOps,
	"PooledObjectInfo",
	Z_Construct_UScriptStruct_FPooledObjectInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPooledObjectInfo_Statics::PropPointers),
	sizeof(FPooledObjectInfo),
	alignof(FPooledObjectInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPooledObjectInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPooledObjectInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPooledObjectInfo()
{
	if (!Z_Registration_Info_UScriptStruct_PooledObjectInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_PooledObjectInfo.InnerSingleton, Z_Construct_UScriptStruct_FPooledObjectInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_PooledObjectInfo.InnerSingleton;
}
// End ScriptStruct FPooledObjectInfo

// Begin Class AObjectPoolManager Function ClearAllPools
struct Z_Construct_UFunction_AObjectPoolManager_ClearAllPools_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AObjectPoolManager_ClearAllPools_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AObjectPoolManager, nullptr, "ClearAllPools", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_ClearAllPools_Statics::Function_MetaDataParams), Z_Construct_UFunction_AObjectPoolManager_ClearAllPools_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AObjectPoolManager_ClearAllPools()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AObjectPoolManager_ClearAllPools_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AObjectPoolManager::execClearAllPools)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearAllPools();
	P_NATIVE_END;
}
// End Class AObjectPoolManager Function ClearAllPools

// Begin Class AObjectPoolManager Function ClearPool
struct Z_Construct_UFunction_AObjectPoolManager_ClearPool_Statics
{
	struct ObjectPoolManager_eventClearPool_Parms
	{
		FName PoolName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_PoolName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AObjectPoolManager_ClearPool_Statics::NewProp_PoolName = { "PoolName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventClearPool_Parms, PoolName), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AObjectPoolManager_ClearPool_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_ClearPool_Statics::NewProp_PoolName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_ClearPool_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AObjectPoolManager_ClearPool_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AObjectPoolManager, nullptr, "ClearPool", nullptr, nullptr, Z_Construct_UFunction_AObjectPoolManager_ClearPool_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_ClearPool_Statics::PropPointers), sizeof(Z_Construct_UFunction_AObjectPoolManager_ClearPool_Statics::ObjectPoolManager_eventClearPool_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_ClearPool_Statics::Function_MetaDataParams), Z_Construct_UFunction_AObjectPoolManager_ClearPool_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AObjectPoolManager_ClearPool_Statics::ObjectPoolManager_eventClearPool_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AObjectPoolManager_ClearPool()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AObjectPoolManager_ClearPool_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AObjectPoolManager::execClearPool)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_PoolName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearPool(Z_Param_PoolName);
	P_NATIVE_END;
}
// End Class AObjectPoolManager Function ClearPool

// Begin Class AObjectPoolManager Function GetAvailableObjects
struct Z_Construct_UFunction_AObjectPoolManager_GetAvailableObjects_Statics
{
	struct ObjectPoolManager_eventGetAvailableObjects_Parms
	{
		FName PoolName;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_PoolName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AObjectPoolManager_GetAvailableObjects_Statics::NewProp_PoolName = { "PoolName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventGetAvailableObjects_Parms, PoolName), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AObjectPoolManager_GetAvailableObjects_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventGetAvailableObjects_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AObjectPoolManager_GetAvailableObjects_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_GetAvailableObjects_Statics::NewProp_PoolName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_GetAvailableObjects_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_GetAvailableObjects_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AObjectPoolManager_GetAvailableObjects_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AObjectPoolManager, nullptr, "GetAvailableObjects", nullptr, nullptr, Z_Construct_UFunction_AObjectPoolManager_GetAvailableObjects_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_GetAvailableObjects_Statics::PropPointers), sizeof(Z_Construct_UFunction_AObjectPoolManager_GetAvailableObjects_Statics::ObjectPoolManager_eventGetAvailableObjects_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_GetAvailableObjects_Statics::Function_MetaDataParams), Z_Construct_UFunction_AObjectPoolManager_GetAvailableObjects_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AObjectPoolManager_GetAvailableObjects_Statics::ObjectPoolManager_eventGetAvailableObjects_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AObjectPoolManager_GetAvailableObjects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AObjectPoolManager_GetAvailableObjects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AObjectPoolManager::execGetAvailableObjects)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_PoolName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetAvailableObjects(Z_Param_PoolName);
	P_NATIVE_END;
}
// End Class AObjectPoolManager Function GetAvailableObjects

// Begin Class AObjectPoolManager Function GetObjectPoolManager
struct Z_Construct_UFunction_AObjectPoolManager_GetObjectPoolManager_Statics
{
	struct ObjectPoolManager_eventGetObjectPoolManager_Parms
	{
		const UObject* WorldContext;
		AObjectPoolManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Static Access */" },
#endif
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Static Access" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldContext_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldContext;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AObjectPoolManager_GetObjectPoolManager_Statics::NewProp_WorldContext = { "WorldContext", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventGetObjectPoolManager_Parms, WorldContext), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldContext_MetaData), NewProp_WorldContext_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AObjectPoolManager_GetObjectPoolManager_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventGetObjectPoolManager_Parms, ReturnValue), Z_Construct_UClass_AObjectPoolManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AObjectPoolManager_GetObjectPoolManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_GetObjectPoolManager_Statics::NewProp_WorldContext,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_GetObjectPoolManager_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_GetObjectPoolManager_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AObjectPoolManager_GetObjectPoolManager_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AObjectPoolManager, nullptr, "GetObjectPoolManager", nullptr, nullptr, Z_Construct_UFunction_AObjectPoolManager_GetObjectPoolManager_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_GetObjectPoolManager_Statics::PropPointers), sizeof(Z_Construct_UFunction_AObjectPoolManager_GetObjectPoolManager_Statics::ObjectPoolManager_eventGetObjectPoolManager_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_GetObjectPoolManager_Statics::Function_MetaDataParams), Z_Construct_UFunction_AObjectPoolManager_GetObjectPoolManager_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AObjectPoolManager_GetObjectPoolManager_Statics::ObjectPoolManager_eventGetObjectPoolManager_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AObjectPoolManager_GetObjectPoolManager()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AObjectPoolManager_GetObjectPoolManager_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AObjectPoolManager::execGetObjectPoolManager)
{
	P_GET_OBJECT(UObject,Z_Param_WorldContext);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(AObjectPoolManager**)Z_Param__Result=AObjectPoolManager::GetObjectPoolManager(Z_Param_WorldContext);
	P_NATIVE_END;
}
// End Class AObjectPoolManager Function GetObjectPoolManager

// Begin Class AObjectPoolManager Function GetObjectsInUse
struct Z_Construct_UFunction_AObjectPoolManager_GetObjectsInUse_Statics
{
	struct ObjectPoolManager_eventGetObjectsInUse_Parms
	{
		FName PoolName;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_PoolName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AObjectPoolManager_GetObjectsInUse_Statics::NewProp_PoolName = { "PoolName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventGetObjectsInUse_Parms, PoolName), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AObjectPoolManager_GetObjectsInUse_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventGetObjectsInUse_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AObjectPoolManager_GetObjectsInUse_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_GetObjectsInUse_Statics::NewProp_PoolName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_GetObjectsInUse_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_GetObjectsInUse_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AObjectPoolManager_GetObjectsInUse_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AObjectPoolManager, nullptr, "GetObjectsInUse", nullptr, nullptr, Z_Construct_UFunction_AObjectPoolManager_GetObjectsInUse_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_GetObjectsInUse_Statics::PropPointers), sizeof(Z_Construct_UFunction_AObjectPoolManager_GetObjectsInUse_Statics::ObjectPoolManager_eventGetObjectsInUse_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_GetObjectsInUse_Statics::Function_MetaDataParams), Z_Construct_UFunction_AObjectPoolManager_GetObjectsInUse_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AObjectPoolManager_GetObjectsInUse_Statics::ObjectPoolManager_eventGetObjectsInUse_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AObjectPoolManager_GetObjectsInUse()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AObjectPoolManager_GetObjectsInUse_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AObjectPoolManager::execGetObjectsInUse)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_PoolName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetObjectsInUse(Z_Param_PoolName);
	P_NATIVE_END;
}
// End Class AObjectPoolManager Function GetObjectsInUse

// Begin Class AObjectPoolManager Function GetPoolHitRate
struct Z_Construct_UFunction_AObjectPoolManager_GetPoolHitRate_Statics
{
	struct ObjectPoolManager_eventGetPoolHitRate_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AObjectPoolManager_GetPoolHitRate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventGetPoolHitRate_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AObjectPoolManager_GetPoolHitRate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_GetPoolHitRate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_GetPoolHitRate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AObjectPoolManager_GetPoolHitRate_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AObjectPoolManager, nullptr, "GetPoolHitRate", nullptr, nullptr, Z_Construct_UFunction_AObjectPoolManager_GetPoolHitRate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_GetPoolHitRate_Statics::PropPointers), sizeof(Z_Construct_UFunction_AObjectPoolManager_GetPoolHitRate_Statics::ObjectPoolManager_eventGetPoolHitRate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_GetPoolHitRate_Statics::Function_MetaDataParams), Z_Construct_UFunction_AObjectPoolManager_GetPoolHitRate_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AObjectPoolManager_GetPoolHitRate_Statics::ObjectPoolManager_eventGetPoolHitRate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AObjectPoolManager_GetPoolHitRate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AObjectPoolManager_GetPoolHitRate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AObjectPoolManager::execGetPoolHitRate)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetPoolHitRate();
	P_NATIVE_END;
}
// End Class AObjectPoolManager Function GetPoolHitRate

// Begin Class AObjectPoolManager Function GetPoolSize
struct Z_Construct_UFunction_AObjectPoolManager_GetPoolSize_Statics
{
	struct ObjectPoolManager_eventGetPoolSize_Parms
	{
		FName PoolName;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pool Queries */" },
#endif
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pool Queries" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_PoolName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AObjectPoolManager_GetPoolSize_Statics::NewProp_PoolName = { "PoolName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventGetPoolSize_Parms, PoolName), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AObjectPoolManager_GetPoolSize_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventGetPoolSize_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AObjectPoolManager_GetPoolSize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_GetPoolSize_Statics::NewProp_PoolName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_GetPoolSize_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_GetPoolSize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AObjectPoolManager_GetPoolSize_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AObjectPoolManager, nullptr, "GetPoolSize", nullptr, nullptr, Z_Construct_UFunction_AObjectPoolManager_GetPoolSize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_GetPoolSize_Statics::PropPointers), sizeof(Z_Construct_UFunction_AObjectPoolManager_GetPoolSize_Statics::ObjectPoolManager_eventGetPoolSize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_GetPoolSize_Statics::Function_MetaDataParams), Z_Construct_UFunction_AObjectPoolManager_GetPoolSize_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AObjectPoolManager_GetPoolSize_Statics::ObjectPoolManager_eventGetPoolSize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AObjectPoolManager_GetPoolSize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AObjectPoolManager_GetPoolSize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AObjectPoolManager::execGetPoolSize)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_PoolName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetPoolSize(Z_Param_PoolName);
	P_NATIVE_END;
}
// End Class AObjectPoolManager Function GetPoolSize

// Begin Class AObjectPoolManager Function InitializeAllPools
struct Z_Construct_UFunction_AObjectPoolManager_InitializeAllPools_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AObjectPoolManager_InitializeAllPools_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AObjectPoolManager, nullptr, "InitializeAllPools", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_InitializeAllPools_Statics::Function_MetaDataParams), Z_Construct_UFunction_AObjectPoolManager_InitializeAllPools_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AObjectPoolManager_InitializeAllPools()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AObjectPoolManager_InitializeAllPools_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AObjectPoolManager::execInitializeAllPools)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeAllPools();
	P_NATIVE_END;
}
// End Class AObjectPoolManager Function InitializeAllPools

// Begin Class AObjectPoolManager Function InitializePool
struct Z_Construct_UFunction_AObjectPoolManager_InitializePool_Statics
{
	struct ObjectPoolManager_eventInitializePool_Parms
	{
		FName PoolName;
		FObjectPoolConfiguration Config;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pool Management */" },
#endif
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pool Management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Config_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_PoolName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Config;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AObjectPoolManager_InitializePool_Statics::NewProp_PoolName = { "PoolName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventInitializePool_Parms, PoolName), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AObjectPoolManager_InitializePool_Statics::NewProp_Config = { "Config", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventInitializePool_Parms, Config), Z_Construct_UScriptStruct_FObjectPoolConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Config_MetaData), NewProp_Config_MetaData) }; // 956482129
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AObjectPoolManager_InitializePool_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_InitializePool_Statics::NewProp_PoolName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_InitializePool_Statics::NewProp_Config,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_InitializePool_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AObjectPoolManager_InitializePool_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AObjectPoolManager, nullptr, "InitializePool", nullptr, nullptr, Z_Construct_UFunction_AObjectPoolManager_InitializePool_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_InitializePool_Statics::PropPointers), sizeof(Z_Construct_UFunction_AObjectPoolManager_InitializePool_Statics::ObjectPoolManager_eventInitializePool_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_InitializePool_Statics::Function_MetaDataParams), Z_Construct_UFunction_AObjectPoolManager_InitializePool_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AObjectPoolManager_InitializePool_Statics::ObjectPoolManager_eventInitializePool_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AObjectPoolManager_InitializePool()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AObjectPoolManager_InitializePool_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AObjectPoolManager::execInitializePool)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_PoolName);
	P_GET_STRUCT_REF(FObjectPoolConfiguration,Z_Param_Out_Config);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializePool(Z_Param_PoolName,Z_Param_Out_Config);
	P_NATIVE_END;
}
// End Class AObjectPoolManager Function InitializePool

// Begin Class AObjectPoolManager Function IsPoolInitialized
struct Z_Construct_UFunction_AObjectPoolManager_IsPoolInitialized_Statics
{
	struct ObjectPoolManager_eventIsPoolInitialized_Parms
	{
		FName PoolName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_PoolName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AObjectPoolManager_IsPoolInitialized_Statics::NewProp_PoolName = { "PoolName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventIsPoolInitialized_Parms, PoolName), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AObjectPoolManager_IsPoolInitialized_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ObjectPoolManager_eventIsPoolInitialized_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AObjectPoolManager_IsPoolInitialized_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ObjectPoolManager_eventIsPoolInitialized_Parms), &Z_Construct_UFunction_AObjectPoolManager_IsPoolInitialized_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AObjectPoolManager_IsPoolInitialized_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_IsPoolInitialized_Statics::NewProp_PoolName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_IsPoolInitialized_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_IsPoolInitialized_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AObjectPoolManager_IsPoolInitialized_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AObjectPoolManager, nullptr, "IsPoolInitialized", nullptr, nullptr, Z_Construct_UFunction_AObjectPoolManager_IsPoolInitialized_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_IsPoolInitialized_Statics::PropPointers), sizeof(Z_Construct_UFunction_AObjectPoolManager_IsPoolInitialized_Statics::ObjectPoolManager_eventIsPoolInitialized_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_IsPoolInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_AObjectPoolManager_IsPoolInitialized_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AObjectPoolManager_IsPoolInitialized_Statics::ObjectPoolManager_eventIsPoolInitialized_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AObjectPoolManager_IsPoolInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AObjectPoolManager_IsPoolInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AObjectPoolManager::execIsPoolInitialized)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_PoolName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPoolInitialized(Z_Param_PoolName);
	P_NATIVE_END;
}
// End Class AObjectPoolManager Function IsPoolInitialized

// Begin Class AObjectPoolManager Function OnDelayedReturn
struct Z_Construct_UFunction_AObjectPoolManager_OnDelayedReturn_Statics
{
	struct ObjectPoolManager_eventOnDelayedReturn_Parms
	{
		AActor* Object;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delayed return callback */" },
#endif
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delayed return callback" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Object;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AObjectPoolManager_OnDelayedReturn_Statics::NewProp_Object = { "Object", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventOnDelayedReturn_Parms, Object), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AObjectPoolManager_OnDelayedReturn_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_OnDelayedReturn_Statics::NewProp_Object,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_OnDelayedReturn_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AObjectPoolManager_OnDelayedReturn_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AObjectPoolManager, nullptr, "OnDelayedReturn", nullptr, nullptr, Z_Construct_UFunction_AObjectPoolManager_OnDelayedReturn_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_OnDelayedReturn_Statics::PropPointers), sizeof(Z_Construct_UFunction_AObjectPoolManager_OnDelayedReturn_Statics::ObjectPoolManager_eventOnDelayedReturn_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_OnDelayedReturn_Statics::Function_MetaDataParams), Z_Construct_UFunction_AObjectPoolManager_OnDelayedReturn_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AObjectPoolManager_OnDelayedReturn_Statics::ObjectPoolManager_eventOnDelayedReturn_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AObjectPoolManager_OnDelayedReturn()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AObjectPoolManager_OnDelayedReturn_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AObjectPoolManager::execOnDelayedReturn)
{
	P_GET_OBJECT(AActor,Z_Param_Object);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnDelayedReturn(Z_Param_Object);
	P_NATIVE_END;
}
// End Class AObjectPoolManager Function OnDelayedReturn

// Begin Class AObjectPoolManager Function OptimizeAllPools
struct Z_Construct_UFunction_AObjectPoolManager_OptimizeAllPools_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AObjectPoolManager_OptimizeAllPools_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AObjectPoolManager, nullptr, "OptimizeAllPools", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_OptimizeAllPools_Statics::Function_MetaDataParams), Z_Construct_UFunction_AObjectPoolManager_OptimizeAllPools_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AObjectPoolManager_OptimizeAllPools()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AObjectPoolManager_OptimizeAllPools_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AObjectPoolManager::execOptimizeAllPools)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeAllPools();
	P_NATIVE_END;
}
// End Class AObjectPoolManager Function OptimizeAllPools

// Begin Class AObjectPoolManager Function PrewarmPool
struct Z_Construct_UFunction_AObjectPoolManager_PrewarmPool_Statics
{
	struct ObjectPoolManager_eventPrewarmPool_Parms
	{
		FName PoolName;
		int32 Count;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Utility Functions */" },
#endif
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility Functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_PoolName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Count;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AObjectPoolManager_PrewarmPool_Statics::NewProp_PoolName = { "PoolName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventPrewarmPool_Parms, PoolName), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AObjectPoolManager_PrewarmPool_Statics::NewProp_Count = { "Count", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventPrewarmPool_Parms, Count), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AObjectPoolManager_PrewarmPool_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_PrewarmPool_Statics::NewProp_PoolName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_PrewarmPool_Statics::NewProp_Count,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_PrewarmPool_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AObjectPoolManager_PrewarmPool_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AObjectPoolManager, nullptr, "PrewarmPool", nullptr, nullptr, Z_Construct_UFunction_AObjectPoolManager_PrewarmPool_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_PrewarmPool_Statics::PropPointers), sizeof(Z_Construct_UFunction_AObjectPoolManager_PrewarmPool_Statics::ObjectPoolManager_eventPrewarmPool_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_PrewarmPool_Statics::Function_MetaDataParams), Z_Construct_UFunction_AObjectPoolManager_PrewarmPool_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AObjectPoolManager_PrewarmPool_Statics::ObjectPoolManager_eventPrewarmPool_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AObjectPoolManager_PrewarmPool()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AObjectPoolManager_PrewarmPool_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AObjectPoolManager::execPrewarmPool)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_PoolName);
	P_GET_PROPERTY(FIntProperty,Z_Param_Count);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PrewarmPool(Z_Param_PoolName,Z_Param_Count);
	P_NATIVE_END;
}
// End Class AObjectPoolManager Function PrewarmPool

// Begin Class AObjectPoolManager Function ReturnPooledObject
struct Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObject_Statics
{
	struct ObjectPoolManager_eventReturnPooledObject_Parms
	{
		AActor* Object;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Object;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObject_Statics::NewProp_Object = { "Object", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventReturnPooledObject_Parms, Object), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObject_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObject_Statics::NewProp_Object,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObject_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObject_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AObjectPoolManager, nullptr, "ReturnPooledObject", nullptr, nullptr, Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObject_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObject_Statics::PropPointers), sizeof(Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObject_Statics::ObjectPoolManager_eventReturnPooledObject_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObject_Statics::Function_MetaDataParams), Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObject_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObject_Statics::ObjectPoolManager_eventReturnPooledObject_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObject()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObject_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AObjectPoolManager::execReturnPooledObject)
{
	P_GET_OBJECT(AActor,Z_Param_Object);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ReturnPooledObject(Z_Param_Object);
	P_NATIVE_END;
}
// End Class AObjectPoolManager Function ReturnPooledObject

// Begin Class AObjectPoolManager Function ReturnPooledObjectDelayed
struct Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObjectDelayed_Statics
{
	struct ObjectPoolManager_eventReturnPooledObjectDelayed_Parms
	{
		AActor* Object;
		float Delay;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Object;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Delay;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObjectDelayed_Statics::NewProp_Object = { "Object", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventReturnPooledObjectDelayed_Parms, Object), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObjectDelayed_Statics::NewProp_Delay = { "Delay", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventReturnPooledObjectDelayed_Parms, Delay), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObjectDelayed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObjectDelayed_Statics::NewProp_Object,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObjectDelayed_Statics::NewProp_Delay,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObjectDelayed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObjectDelayed_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AObjectPoolManager, nullptr, "ReturnPooledObjectDelayed", nullptr, nullptr, Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObjectDelayed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObjectDelayed_Statics::PropPointers), sizeof(Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObjectDelayed_Statics::ObjectPoolManager_eventReturnPooledObjectDelayed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObjectDelayed_Statics::Function_MetaDataParams), Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObjectDelayed_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObjectDelayed_Statics::ObjectPoolManager_eventReturnPooledObjectDelayed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObjectDelayed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObjectDelayed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AObjectPoolManager::execReturnPooledObjectDelayed)
{
	P_GET_OBJECT(AActor,Z_Param_Object);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Delay);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ReturnPooledObjectDelayed(Z_Param_Object,Z_Param_Delay);
	P_NATIVE_END;
}
// End Class AObjectPoolManager Function ReturnPooledObjectDelayed

// Begin Class AObjectPoolManager Function SpawnPooledObject
struct Z_Construct_UFunction_AObjectPoolManager_SpawnPooledObject_Statics
{
	struct ObjectPoolManager_eventSpawnPooledObject_Parms
	{
		FName PoolName;
		FVector Location;
		FRotator Rotation;
		AActor* OwnerActor;
		AActor* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Object Spawning */" },
#endif
		{ "CPP_Default_OwnerActor", "None" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Object Spawning" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_PoolName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OwnerActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AObjectPoolManager_SpawnPooledObject_Statics::NewProp_PoolName = { "PoolName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventSpawnPooledObject_Parms, PoolName), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AObjectPoolManager_SpawnPooledObject_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventSpawnPooledObject_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AObjectPoolManager_SpawnPooledObject_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventSpawnPooledObject_Parms, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AObjectPoolManager_SpawnPooledObject_Statics::NewProp_OwnerActor = { "OwnerActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventSpawnPooledObject_Parms, OwnerActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AObjectPoolManager_SpawnPooledObject_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventSpawnPooledObject_Parms, ReturnValue), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AObjectPoolManager_SpawnPooledObject_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_SpawnPooledObject_Statics::NewProp_PoolName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_SpawnPooledObject_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_SpawnPooledObject_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_SpawnPooledObject_Statics::NewProp_OwnerActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_SpawnPooledObject_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_SpawnPooledObject_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AObjectPoolManager_SpawnPooledObject_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AObjectPoolManager, nullptr, "SpawnPooledObject", nullptr, nullptr, Z_Construct_UFunction_AObjectPoolManager_SpawnPooledObject_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_SpawnPooledObject_Statics::PropPointers), sizeof(Z_Construct_UFunction_AObjectPoolManager_SpawnPooledObject_Statics::ObjectPoolManager_eventSpawnPooledObject_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_SpawnPooledObject_Statics::Function_MetaDataParams), Z_Construct_UFunction_AObjectPoolManager_SpawnPooledObject_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AObjectPoolManager_SpawnPooledObject_Statics::ObjectPoolManager_eventSpawnPooledObject_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AObjectPoolManager_SpawnPooledObject()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AObjectPoolManager_SpawnPooledObject_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AObjectPoolManager::execSpawnPooledObject)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_PoolName);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FRotator,Z_Param_Out_Rotation);
	P_GET_OBJECT(AActor,Z_Param_OwnerActor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(AActor**)Z_Param__Result=P_THIS->SpawnPooledObject(Z_Param_PoolName,Z_Param_Out_Location,Z_Param_Out_Rotation,Z_Param_OwnerActor);
	P_NATIVE_END;
}
// End Class AObjectPoolManager Function SpawnPooledObject

// Begin Class AObjectPoolManager Function TrimPool
struct Z_Construct_UFunction_AObjectPoolManager_TrimPool_Statics
{
	struct ObjectPoolManager_eventTrimPool_Parms
	{
		FName PoolName;
		int32 TargetSize;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_PoolName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TargetSize;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AObjectPoolManager_TrimPool_Statics::NewProp_PoolName = { "PoolName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventTrimPool_Parms, PoolName), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AObjectPoolManager_TrimPool_Statics::NewProp_TargetSize = { "TargetSize", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventTrimPool_Parms, TargetSize), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AObjectPoolManager_TrimPool_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_TrimPool_Statics::NewProp_PoolName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AObjectPoolManager_TrimPool_Statics::NewProp_TargetSize,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_TrimPool_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AObjectPoolManager_TrimPool_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AObjectPoolManager, nullptr, "TrimPool", nullptr, nullptr, Z_Construct_UFunction_AObjectPoolManager_TrimPool_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_TrimPool_Statics::PropPointers), sizeof(Z_Construct_UFunction_AObjectPoolManager_TrimPool_Statics::ObjectPoolManager_eventTrimPool_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AObjectPoolManager_TrimPool_Statics::Function_MetaDataParams), Z_Construct_UFunction_AObjectPoolManager_TrimPool_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AObjectPoolManager_TrimPool_Statics::ObjectPoolManager_eventTrimPool_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AObjectPoolManager_TrimPool()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AObjectPoolManager_TrimPool_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AObjectPoolManager::execTrimPool)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_PoolName);
	P_GET_PROPERTY(FIntProperty,Z_Param_TargetSize);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TrimPool(Z_Param_PoolName,Z_Param_TargetSize);
	P_NATIVE_END;
}
// End Class AObjectPoolManager Function TrimPool

// Begin Class AObjectPoolManager
void AObjectPoolManager::StaticRegisterNativesAObjectPoolManager()
{
	UClass* Class = AObjectPoolManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ClearAllPools", &AObjectPoolManager::execClearAllPools },
		{ "ClearPool", &AObjectPoolManager::execClearPool },
		{ "GetAvailableObjects", &AObjectPoolManager::execGetAvailableObjects },
		{ "GetObjectPoolManager", &AObjectPoolManager::execGetObjectPoolManager },
		{ "GetObjectsInUse", &AObjectPoolManager::execGetObjectsInUse },
		{ "GetPoolHitRate", &AObjectPoolManager::execGetPoolHitRate },
		{ "GetPoolSize", &AObjectPoolManager::execGetPoolSize },
		{ "InitializeAllPools", &AObjectPoolManager::execInitializeAllPools },
		{ "InitializePool", &AObjectPoolManager::execInitializePool },
		{ "IsPoolInitialized", &AObjectPoolManager::execIsPoolInitialized },
		{ "OnDelayedReturn", &AObjectPoolManager::execOnDelayedReturn },
		{ "OptimizeAllPools", &AObjectPoolManager::execOptimizeAllPools },
		{ "PrewarmPool", &AObjectPoolManager::execPrewarmPool },
		{ "ReturnPooledObject", &AObjectPoolManager::execReturnPooledObject },
		{ "ReturnPooledObjectDelayed", &AObjectPoolManager::execReturnPooledObjectDelayed },
		{ "SpawnPooledObject", &AObjectPoolManager::execSpawnPooledObject },
		{ "TrimPool", &AObjectPoolManager::execTrimPool },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(AObjectPoolManager);
UClass* Z_Construct_UClass_AObjectPoolManager_NoRegister()
{
	return AObjectPoolManager::StaticClass();
}
struct Z_Construct_UClass_AObjectPoolManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Object Pool Manager for efficient actor spawning and management\n * Reduces garbage collection overhead and improves performance\n */" },
#endif
		{ "IncludePath", "Core/ObjectPoolManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Object Pool Manager for efficient actor spawning and management\nReduces garbage collection overhead and improves performance" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PoolConfigurations_MetaData[] = {
		{ "Category", "Pool Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Pool Configurations */" },
#endif
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pool Configurations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnObjectSpawned_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Events */" },
#endif
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnObjectReturned_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalObjectsSpawned_MetaData[] = {
		{ "Category", "Statistics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Statistics */" },
#endif
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Statistics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalObjectsReturned_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalPoolHits_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalPoolMisses_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveObjects_MetaData[] = {
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PoolConfigCache_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configuration Cache */" },
#endif
		{ "ModuleRelativePath", "Core/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration Cache" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PoolConfigurations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PoolConfigurations;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnObjectSpawned;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnObjectReturned;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalObjectsSpawned;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalObjectsReturned;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalPoolHits;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalPoolMisses;
	static const UECodeGen_Private::FNamePropertyParams NewProp_ActiveObjects_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ActiveObjects_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActiveObjects;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PoolConfigCache_ValueProp;
	static const UECodeGen_Private::FNamePropertyParams NewProp_PoolConfigCache_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PoolConfigCache;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AObjectPoolManager_ClearAllPools, "ClearAllPools" }, // 273994600
		{ &Z_Construct_UFunction_AObjectPoolManager_ClearPool, "ClearPool" }, // 1924246854
		{ &Z_Construct_UFunction_AObjectPoolManager_GetAvailableObjects, "GetAvailableObjects" }, // 1575679482
		{ &Z_Construct_UFunction_AObjectPoolManager_GetObjectPoolManager, "GetObjectPoolManager" }, // 3745859830
		{ &Z_Construct_UFunction_AObjectPoolManager_GetObjectsInUse, "GetObjectsInUse" }, // 1937418070
		{ &Z_Construct_UFunction_AObjectPoolManager_GetPoolHitRate, "GetPoolHitRate" }, // 3541845387
		{ &Z_Construct_UFunction_AObjectPoolManager_GetPoolSize, "GetPoolSize" }, // 2535742320
		{ &Z_Construct_UFunction_AObjectPoolManager_InitializeAllPools, "InitializeAllPools" }, // 3452389320
		{ &Z_Construct_UFunction_AObjectPoolManager_InitializePool, "InitializePool" }, // 1974057549
		{ &Z_Construct_UFunction_AObjectPoolManager_IsPoolInitialized, "IsPoolInitialized" }, // 838899018
		{ &Z_Construct_UFunction_AObjectPoolManager_OnDelayedReturn, "OnDelayedReturn" }, // 2775958143
		{ &Z_Construct_UFunction_AObjectPoolManager_OptimizeAllPools, "OptimizeAllPools" }, // 1847654710
		{ &Z_Construct_UFunction_AObjectPoolManager_PrewarmPool, "PrewarmPool" }, // 3054129462
		{ &Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObject, "ReturnPooledObject" }, // 2001031549
		{ &Z_Construct_UFunction_AObjectPoolManager_ReturnPooledObjectDelayed, "ReturnPooledObjectDelayed" }, // 3076105226
		{ &Z_Construct_UFunction_AObjectPoolManager_SpawnPooledObject, "SpawnPooledObject" }, // 3675014378
		{ &Z_Construct_UFunction_AObjectPoolManager_TrimPool, "TrimPool" }, // 3046717774
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AObjectPoolManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_PoolConfigurations_Inner = { "PoolConfigurations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FObjectPoolConfiguration, METADATA_PARAMS(0, nullptr) }; // 956482129
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_PoolConfigurations = { "PoolConfigurations", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AObjectPoolManager, PoolConfigurations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PoolConfigurations_MetaData), NewProp_PoolConfigurations_MetaData) }; // 956482129
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_OnObjectSpawned = { "OnObjectSpawned", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AObjectPoolManager, OnObjectSpawned), Z_Construct_UDelegateFunction_RoughReality_OnObjectSpawned__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnObjectSpawned_MetaData), NewProp_OnObjectSpawned_MetaData) }; // 3203295988
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_OnObjectReturned = { "OnObjectReturned", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AObjectPoolManager, OnObjectReturned), Z_Construct_UDelegateFunction_RoughReality_OnObjectReturned__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnObjectReturned_MetaData), NewProp_OnObjectReturned_MetaData) }; // 3369494667
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_TotalObjectsSpawned = { "TotalObjectsSpawned", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AObjectPoolManager, TotalObjectsSpawned), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalObjectsSpawned_MetaData), NewProp_TotalObjectsSpawned_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_TotalObjectsReturned = { "TotalObjectsReturned", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AObjectPoolManager, TotalObjectsReturned), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalObjectsReturned_MetaData), NewProp_TotalObjectsReturned_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_TotalPoolHits = { "TotalPoolHits", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AObjectPoolManager, TotalPoolHits), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalPoolHits_MetaData), NewProp_TotalPoolHits_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_TotalPoolMisses = { "TotalPoolMisses", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AObjectPoolManager, TotalPoolMisses), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalPoolMisses_MetaData), NewProp_TotalPoolMisses_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_ActiveObjects_ValueProp = { "ActiveObjects", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_ActiveObjects_Key_KeyProp = { "ActiveObjects_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_ActiveObjects = { "ActiveObjects", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AObjectPoolManager, ActiveObjects), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveObjects_MetaData), NewProp_ActiveObjects_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_PoolConfigCache_ValueProp = { "PoolConfigCache", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FObjectPoolConfiguration, METADATA_PARAMS(0, nullptr) }; // 956482129
const UECodeGen_Private::FNamePropertyParams Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_PoolConfigCache_Key_KeyProp = { "PoolConfigCache_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_PoolConfigCache = { "PoolConfigCache", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AObjectPoolManager, PoolConfigCache), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PoolConfigCache_MetaData), NewProp_PoolConfigCache_MetaData) }; // 956482129
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AObjectPoolManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_PoolConfigurations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_PoolConfigurations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_OnObjectSpawned,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_OnObjectReturned,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_TotalObjectsSpawned,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_TotalObjectsReturned,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_TotalPoolHits,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_TotalPoolMisses,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_ActiveObjects_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_ActiveObjects_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_ActiveObjects,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_PoolConfigCache_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_PoolConfigCache_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AObjectPoolManager_Statics::NewProp_PoolConfigCache,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AObjectPoolManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AObjectPoolManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AObjectPoolManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AObjectPoolManager_Statics::ClassParams = {
	&AObjectPoolManager::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AObjectPoolManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AObjectPoolManager_Statics::PropPointers),
	0,
	0x009000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AObjectPoolManager_Statics::Class_MetaDataParams), Z_Construct_UClass_AObjectPoolManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AObjectPoolManager()
{
	if (!Z_Registration_Info_UClass_AObjectPoolManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AObjectPoolManager.OuterSingleton, Z_Construct_UClass_AObjectPoolManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AObjectPoolManager.OuterSingleton;
}
template<> ROUGHREALITY_API UClass* StaticClass<AObjectPoolManager>()
{
	return AObjectPoolManager::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AObjectPoolManager);
AObjectPoolManager::~AObjectPoolManager() {}
// End Class AObjectPoolManager

// Begin Registration
struct Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_ObjectPoolManager_h_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FObjectPoolConfiguration::StaticStruct, Z_Construct_UScriptStruct_FObjectPoolConfiguration_Statics::NewStructOps, TEXT("ObjectPoolConfiguration"), &Z_Registration_Info_UScriptStruct_ObjectPoolConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FObjectPoolConfiguration), 956482129U) },
		{ FPooledObjectInfo::StaticStruct, Z_Construct_UScriptStruct_FPooledObjectInfo_Statics::NewStructOps, TEXT("PooledObjectInfo"), &Z_Registration_Info_UScriptStruct_PooledObjectInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPooledObjectInfo), 807759697U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AObjectPoolManager, AObjectPoolManager::StaticClass, TEXT("AObjectPoolManager"), &Z_Registration_Info_UClass_AObjectPoolManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AObjectPoolManager), 3574354320U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_ObjectPoolManager_h_481622704(TEXT("/Script/RoughReality"),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_ObjectPoolManager_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_ObjectPoolManager_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_ObjectPoolManager_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_ObjectPoolManager_h_Statics::ScriptStructInfo),
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
