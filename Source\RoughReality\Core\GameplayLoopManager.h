// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameStateBase.h"
#include "GameplayTagContainer.h"
#include "Engine/StreamableManager.h"
#include "GameplayLoopManager.generated.h"

class ARoughRealityGameModeBase;
class URoughSaveGame;
class URunConfigurationDataAsset;

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnRunStarted, int32, RunNumber);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnRunCompleted, bool, bSuccess, int32, RunNumber);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSectorChanged, int32, NewSector, int32, OldSector);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnDifficultyScaled, float, NewDifficultyMultiplier);

UENUM(BlueprintType)
enum class ERunState : uint8
{
	NotStarted,
	InProgress,
	Completed,
	Failed,
	Abandoned
};

UENUM(BlueprintType)
enum class ESectorType : uint8
{
	DilapidatedCity,
	StationTunnels,
	BelaVegas,
	IndustrialDistrict
};

USTRUCT(BlueprintType)
struct FRunStatistics
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadOnly, Category = "Statistics")
	int32 RunNumber = 0;

	UPROPERTY(BlueprintReadOnly, Category = "Statistics")
	float RunDuration = 0.0f;

	UPROPERTY(BlueprintReadOnly, Category = "Statistics")
	int32 EnemiesKilled = 0;

	UPROPERTY(BlueprintReadOnly, Category = "Statistics")
	int32 TeethCollected = 0;

	UPROPERTY(BlueprintReadOnly, Category = "Statistics")
	int32 SectorsCompleted = 0;

	UPROPERTY(BlueprintReadOnly, Category = "Statistics")
	float AccuracyPercentage = 0.0f;

	UPROPERTY(BlueprintReadOnly, Category = "Statistics")
	int32 BulletTimesUsed = 0;

	UPROPERTY(BlueprintReadOnly, Category = "Statistics")
	int32 RewindsUsed = 0;

	UPROPERTY(BlueprintReadOnly, Category = "Statistics")
	TMap<FString, int32> WeaponUsageStats;

	UPROPERTY(BlueprintReadOnly, Category = "Statistics")
	TMap<ESectorType, float> SectorCompletionTimes;

	FRunStatistics()
	{
		RunNumber = 0;
		RunDuration = 0.0f;
		EnemiesKilled = 0;
		TeethCollected = 0;
		SectorsCompleted = 0;
		AccuracyPercentage = 0.0f;
		BulletTimesUsed = 0;
		RewindsUsed = 0;
	}
};

USTRUCT(BlueprintType)
struct FSectorConfiguration
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sector")
	ESectorType SectorType;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sector")
	FText SectorName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sector")
	int32 MinLevels = 3;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sector")
	int32 MaxLevels = 5;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sector")
	float BaseDifficultyMultiplier = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sector")
	float DifficultyScalingPerRun = 0.1f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sector")
	TArray<FGameplayTag> RequiredUnlocks;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sector")
	int32 TeethReward = 100;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Sector")
	FLinearColor SectorColor = FLinearColor::White;

	FSectorConfiguration()
	{
		SectorType = ESectorType::DilapidatedCity;
		SectorName = FText::FromString("Unknown Sector");
		MinLevels = 3;
		MaxLevels = 5;
		BaseDifficultyMultiplier = 1.0f;
		DifficultyScalingPerRun = 0.1f;
		TeethReward = 100;
		SectorColor = FLinearColor::White;
	}
};

/**
 * Manages the complete gameplay loop for Rough Reality
 * Handles run progression, difficulty scaling, and meta-progression
 */
UCLASS(BlueprintType, Blueprintable)
class ROUGHREALITY_API AGameplayLoopManager : public AGameStateBase
{
	GENERATED_BODY()

public:
	AGameplayLoopManager();

protected:
	virtual void BeginPlay() override;
	virtual void Tick(float DeltaTime) override;

public:
	/** Current Run State */
	UPROPERTY(BlueprintReadOnly, Category = "Run State")
	ERunState CurrentRunState = ERunState::NotStarted;

	UPROPERTY(BlueprintReadOnly, Category = "Run State")
	int32 CurrentRunNumber = 0;

	UPROPERTY(BlueprintReadOnly, Category = "Run State")
	int32 CurrentSectorIndex = 0;

	UPROPERTY(BlueprintReadOnly, Category = "Run State")
	int32 CurrentLevelIndex = 0;

	UPROPERTY(BlueprintReadOnly, Category = "Run State")
	float RunStartTime = 0.0f;

	UPROPERTY(BlueprintReadOnly, Category = "Run State")
	FRunStatistics CurrentRunStats;

	/** Sector Configuration */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Sector Configuration")
	TArray<FSectorConfiguration> SectorConfigurations;

	UPROPERTY(BlueprintReadOnly, Category = "Difficulty")
	float CurrentDifficultyMultiplier = 1.0f;

	/** Events */
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnRunStarted OnRunStarted;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnRunCompleted OnRunCompleted;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnSectorChanged OnSectorChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnDifficultyScaled OnDifficultyScaled;

	/** Streamable Manager for Async Loading */
	UPROPERTY()
	FStreamableManager StreamableManager;

public:
	/** Run Management */
	UFUNCTION(BlueprintCallable, Category = "Run Management")
	void StartNewRun();

	UFUNCTION(BlueprintCallable, Category = "Run Management")
	void CompleteCurrentRun(bool bSuccess);

	UFUNCTION(BlueprintCallable, Category = "Run Management")
	void AbandonCurrentRun();

	/** Progression */
	UFUNCTION(BlueprintCallable, Category = "Progression")
	void AdvanceToNextLevel();

	UFUNCTION(BlueprintCallable, Category = "Progression")
	void AdvanceToNextSector();

	UFUNCTION(BlueprintCallable, Category = "Progression")
	bool CanAdvanceToSector(int32 SectorIndex) const;

	/** Difficulty Scaling */
	UFUNCTION(BlueprintCallable, Category = "Difficulty")
	float CalculateDifficultyMultiplier(int32 SectorIndex, int32 RunNumber) const;

	UFUNCTION(BlueprintCallable, Category = "Difficulty")
	void UpdateDifficultyScaling();

	/** Statistics */
	UFUNCTION(BlueprintCallable, Category = "Statistics")
	void RecordEnemyKill(const FString& EnemyType);

	UFUNCTION(BlueprintCallable, Category = "Statistics")
	void RecordWeaponUsage(const FString& WeaponName);

	UFUNCTION(BlueprintCallable, Category = "Statistics")
	void RecordBulletTimeUsage();

	UFUNCTION(BlueprintCallable, Category = "Statistics")
	void RecordRewindUsage();

	UFUNCTION(BlueprintCallable, Category = "Statistics")
	void RecordTeethCollected(int32 Amount);

	UFUNCTION(BlueprintCallable, Category = "Statistics")
	void UpdateAccuracy(int32 ShotsHit, int32 ShotsFired);

	/** Queries */
	UFUNCTION(BlueprintCallable, Category = "Queries")
	FSectorConfiguration GetCurrentSectorConfiguration() const;

	UFUNCTION(BlueprintCallable, Category = "Queries")
	FSectorConfiguration GetSectorConfiguration(int32 SectorIndex) const;

	UFUNCTION(BlueprintCallable, Category = "Queries")
	int32 GetTotalSectors() const;

	UFUNCTION(BlueprintCallable, Category = "Queries")
	float GetRunProgress() const;

	UFUNCTION(BlueprintCallable, Category = "Queries")
	bool IsRunInProgress() const;

	/** Async Loading */
	UFUNCTION(BlueprintCallable, Category = "Loading")
	void PreloadSectorAssets(int32 SectorIndex);

	UFUNCTION(BlueprintCallable, Category = "Loading")
	void UnloadUnusedAssets();

protected:
	/** Internal Functions */
	void InitializeSectorConfigurations();
	void SaveRunStatistics();
	void ResetRunStatistics();
	void CalculateRunRewards();

	/** Async Loading Callbacks */
	void OnSectorAssetsLoaded();

private:
	/** Tracking variables */
	int32 TotalShotsHit = 0;
	int32 TotalShotsFired = 0;
	float SectorStartTime = 0.0f;

	/** Asset Loading */
	TSharedPtr<FStreamableHandle> CurrentLoadingHandle;
};
