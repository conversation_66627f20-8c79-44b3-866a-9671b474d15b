// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared PCH Definitions for RoughReality
#pragma once
#include "G:/Gamedev/RoughReality/Intermediate/Build/Win64/x64/RoughRealityEditor/Development/UnrealEd/SharedDefinitions.UnrealEd.Project.ValApi.Cpp20.h"
#undef ROUGHREALITY_API
#define UE_IS_ENGINE_MODULE 0
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UE_DEPRECATED_FORENGINE UE_DEPRECATED
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 0
#define UE_PROJECT_NAME RoughReality
#define UE_TARGET_NAME RoughRealityEditor
#define UE_MODULE_NAME "RoughReality"
#define UE_PLUGIN_NAME ""
#define IMPLEMENT_ENCRYPTION_KEY_REGISTRATION() 
#define IMPLEMENT_SIGNING_KEY_REGISTRATION() 
#define ROUGHREALITY_API DLLEXPORT
#define ENHANCEDINPUT_API DLLIMPORT
