// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "VFX/VisualEffectsManager.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class AVisualEffectsManager;
class UObject;
enum class EGameVisualState : uint8;
struct FLinearColor;
struct FVisualEffectSettings;
#ifdef ROUGHREALITY_VisualEffectsManager_generated_h
#error "VisualEffectsManager.generated.h already included, missing '#pragma once' in VisualEffectsManager.h"
#endif
#define ROUGHREALITY_VisualEffectsManager_generated_h

#define FID_RoughReality_Source_RoughReality_VFX_VisualEffectsManager_h_30_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FVisualEffectSettings_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FVisualEffectSettings>();

#define FID_RoughReality_Source_RoughReality_VFX_VisualEffectsManager_h_80_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FBulletTimeEffectSettings_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FBulletTimeEffectSettings>();

#define FID_RoughReality_Source_RoughReality_VFX_VisualEffectsManager_h_115_DELEGATE \
ROUGHREALITY_API void FOnVisualStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnVisualStateChanged, EGameVisualState OldState, EGameVisualState NewState);


#define FID_RoughReality_Source_RoughReality_VFX_VisualEffectsManager_h_124_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetVisualEffectsManager); \
	DECLARE_FUNCTION(execUpdateEffectLOD); \
	DECLARE_FUNCTION(execEnableLODSystem); \
	DECLARE_FUNCTION(execSetEffectQuality); \
	DECLARE_FUNCTION(execCreateBulletTrail); \
	DECLARE_FUNCTION(execTriggerImpactEffect); \
	DECLARE_FUNCTION(execTriggerMuzzleFlashEffect); \
	DECLARE_FUNCTION(execSetSectorFogSettings); \
	DECLARE_FUNCTION(execSetSectorAmbientColor); \
	DECLARE_FUNCTION(execApplySectorVisualTheme); \
	DECLARE_FUNCTION(execBlendVisualEffectSettings); \
	DECLARE_FUNCTION(execApplyVisualEffectSettings); \
	DECLARE_FUNCTION(execUpdateTimeParameters); \
	DECLARE_FUNCTION(execSetGlobalVectorParameter); \
	DECLARE_FUNCTION(execSetGlobalScalarParameter); \
	DECLARE_FUNCTION(execApplyScreenShake); \
	DECLARE_FUNCTION(execApplyExplosionEffect); \
	DECLARE_FUNCTION(execApplyHealEffect); \
	DECLARE_FUNCTION(execApplyDamageEffect); \
	DECLARE_FUNCTION(execUpdateBulletTimeIntensity); \
	DECLARE_FUNCTION(execDisableBulletTimeEffects); \
	DECLARE_FUNCTION(execEnableBulletTimeEffects); \
	DECLARE_FUNCTION(execGetCurrentVisualState); \
	DECLARE_FUNCTION(execSetVisualStateImmediate); \
	DECLARE_FUNCTION(execSetVisualState);


#define FID_RoughReality_Source_RoughReality_VFX_VisualEffectsManager_h_124_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAVisualEffectsManager(); \
	friend struct Z_Construct_UClass_AVisualEffectsManager_Statics; \
public: \
	DECLARE_CLASS(AVisualEffectsManager, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/RoughReality"), NO_API) \
	DECLARE_SERIALIZER(AVisualEffectsManager)


#define FID_RoughReality_Source_RoughReality_VFX_VisualEffectsManager_h_124_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	AVisualEffectsManager(AVisualEffectsManager&&); \
	AVisualEffectsManager(const AVisualEffectsManager&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AVisualEffectsManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AVisualEffectsManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AVisualEffectsManager) \
	NO_API virtual ~AVisualEffectsManager();


#define FID_RoughReality_Source_RoughReality_VFX_VisualEffectsManager_h_121_PROLOG
#define FID_RoughReality_Source_RoughReality_VFX_VisualEffectsManager_h_124_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_RoughReality_Source_RoughReality_VFX_VisualEffectsManager_h_124_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_VFX_VisualEffectsManager_h_124_INCLASS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_VFX_VisualEffectsManager_h_124_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ROUGHREALITY_API UClass* StaticClass<class AVisualEffectsManager>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_RoughReality_Source_RoughReality_VFX_VisualEffectsManager_h


#define FOREACH_ENUM_EGAMEVISUALSTATE(op) \
	op(EGameVisualState::Normal) \
	op(EGameVisualState::BulletTime) \
	op(EGameVisualState::Damaged) \
	op(EGameVisualState::Critical) \
	op(EGameVisualState::Death) \
	op(EGameVisualState::Victory) 

enum class EGameVisualState : uint8;
template<> struct TIsUEnumClass<EGameVisualState> { enum { Value = true }; };
template<> ROUGHREALITY_API UEnum* StaticEnum<EGameVisualState>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
