// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RoughReality/Core/GameplayLoopManager.h"
#include "Runtime/GameplayTags/Classes/GameplayTagContainer.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeGameplayLoopManager() {}

// Begin Cross Module References
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
ENGINE_API UClass* Z_Construct_UClass_AGameStateBase();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTag();
ROUGHREALITY_API UClass* Z_Construct_UClass_AGameplayLoopManager();
ROUGHREALITY_API UClass* Z_Construct_UClass_AGameplayLoopManager_NoRegister();
ROUGHREALITY_API UEnum* Z_Construct_UEnum_RoughReality_ERunState();
ROUGHREALITY_API UEnum* Z_Construct_UEnum_RoughReality_ESectorType();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnDifficultyScaled__DelegateSignature();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnRunStarted__DelegateSignature();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnSectorChanged__DelegateSignature();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FRunStatistics();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FSectorConfiguration();
UPackage* Z_Construct_UPackage__Script_RoughReality();
// End Cross Module References

// Begin Delegate FOnRunStarted
struct Z_Construct_UDelegateFunction_RoughReality_OnRunStarted__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnRunStarted_Parms
	{
		int32 RunNumber;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_RunNumber;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnRunStarted__DelegateSignature_Statics::NewProp_RunNumber = { "RunNumber", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnRunStarted_Parms, RunNumber), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnRunStarted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnRunStarted__DelegateSignature_Statics::NewProp_RunNumber,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnRunStarted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnRunStarted__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnRunStarted__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnRunStarted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnRunStarted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnRunStarted__DelegateSignature_Statics::_Script_RoughReality_eventOnRunStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnRunStarted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnRunStarted__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnRunStarted__DelegateSignature_Statics::_Script_RoughReality_eventOnRunStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnRunStarted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnRunStarted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnRunStarted_DelegateWrapper(const FMulticastScriptDelegate& OnRunStarted, int32 RunNumber)
{
	struct _Script_RoughReality_eventOnRunStarted_Parms
	{
		int32 RunNumber;
	};
	_Script_RoughReality_eventOnRunStarted_Parms Parms;
	Parms.RunNumber=RunNumber;
	OnRunStarted.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnRunStarted

// Begin Delegate FOnRunCompleted
struct Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnRunCompleted_Parms
	{
		bool bSuccess;
		int32 RunNumber;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RunNumber;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((_Script_RoughReality_eventOnRunCompleted_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(_Script_RoughReality_eventOnRunCompleted_Parms), &Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::NewProp_RunNumber = { "RunNumber", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnRunCompleted_Parms, RunNumber), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::NewProp_bSuccess,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::NewProp_RunNumber,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnRunCompleted__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::_Script_RoughReality_eventOnRunCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::_Script_RoughReality_eventOnRunCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnRunCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnRunCompleted, bool bSuccess, int32 RunNumber)
{
	struct _Script_RoughReality_eventOnRunCompleted_Parms
	{
		bool bSuccess;
		int32 RunNumber;
	};
	_Script_RoughReality_eventOnRunCompleted_Parms Parms;
	Parms.bSuccess=bSuccess ? true : false;
	Parms.RunNumber=RunNumber;
	OnRunCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnRunCompleted

// Begin Delegate FOnSectorChanged
struct Z_Construct_UDelegateFunction_RoughReality_OnSectorChanged__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnSectorChanged_Parms
	{
		int32 NewSector;
		int32 OldSector;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewSector;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OldSector;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnSectorChanged__DelegateSignature_Statics::NewProp_NewSector = { "NewSector", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnSectorChanged_Parms, NewSector), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnSectorChanged__DelegateSignature_Statics::NewProp_OldSector = { "OldSector", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnSectorChanged_Parms, OldSector), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnSectorChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnSectorChanged__DelegateSignature_Statics::NewProp_NewSector,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnSectorChanged__DelegateSignature_Statics::NewProp_OldSector,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnSectorChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnSectorChanged__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnSectorChanged__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnSectorChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnSectorChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnSectorChanged__DelegateSignature_Statics::_Script_RoughReality_eventOnSectorChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnSectorChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnSectorChanged__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnSectorChanged__DelegateSignature_Statics::_Script_RoughReality_eventOnSectorChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnSectorChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnSectorChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSectorChanged_DelegateWrapper(const FMulticastScriptDelegate& OnSectorChanged, int32 NewSector, int32 OldSector)
{
	struct _Script_RoughReality_eventOnSectorChanged_Parms
	{
		int32 NewSector;
		int32 OldSector;
	};
	_Script_RoughReality_eventOnSectorChanged_Parms Parms;
	Parms.NewSector=NewSector;
	Parms.OldSector=OldSector;
	OnSectorChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnSectorChanged

// Begin Delegate FOnDifficultyScaled
struct Z_Construct_UDelegateFunction_RoughReality_OnDifficultyScaled__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnDifficultyScaled_Parms
	{
		float NewDifficultyMultiplier;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewDifficultyMultiplier;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnDifficultyScaled__DelegateSignature_Statics::NewProp_NewDifficultyMultiplier = { "NewDifficultyMultiplier", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnDifficultyScaled_Parms, NewDifficultyMultiplier), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnDifficultyScaled__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnDifficultyScaled__DelegateSignature_Statics::NewProp_NewDifficultyMultiplier,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnDifficultyScaled__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnDifficultyScaled__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnDifficultyScaled__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnDifficultyScaled__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnDifficultyScaled__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnDifficultyScaled__DelegateSignature_Statics::_Script_RoughReality_eventOnDifficultyScaled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnDifficultyScaled__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnDifficultyScaled__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnDifficultyScaled__DelegateSignature_Statics::_Script_RoughReality_eventOnDifficultyScaled_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnDifficultyScaled__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnDifficultyScaled__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnDifficultyScaled_DelegateWrapper(const FMulticastScriptDelegate& OnDifficultyScaled, float NewDifficultyMultiplier)
{
	struct _Script_RoughReality_eventOnDifficultyScaled_Parms
	{
		float NewDifficultyMultiplier;
	};
	_Script_RoughReality_eventOnDifficultyScaled_Parms Parms;
	Parms.NewDifficultyMultiplier=NewDifficultyMultiplier;
	OnDifficultyScaled.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnDifficultyScaled

// Begin Enum ERunState
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ERunState;
static UEnum* ERunState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ERunState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ERunState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_RoughReality_ERunState, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("ERunState"));
	}
	return Z_Registration_Info_UEnum_ERunState.OuterSingleton;
}
template<> ROUGHREALITY_API UEnum* StaticEnum<ERunState>()
{
	return ERunState_StaticEnum();
}
struct Z_Construct_UEnum_RoughReality_ERunState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Abandoned.Name", "ERunState::Abandoned" },
		{ "BlueprintType", "true" },
		{ "Completed.Name", "ERunState::Completed" },
		{ "Failed.Name", "ERunState::Failed" },
		{ "InProgress.Name", "ERunState::InProgress" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
		{ "NotStarted.Name", "ERunState::NotStarted" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ERunState::NotStarted", (int64)ERunState::NotStarted },
		{ "ERunState::InProgress", (int64)ERunState::InProgress },
		{ "ERunState::Completed", (int64)ERunState::Completed },
		{ "ERunState::Failed", (int64)ERunState::Failed },
		{ "ERunState::Abandoned", (int64)ERunState::Abandoned },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_RoughReality_ERunState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	"ERunState",
	"ERunState",
	Z_Construct_UEnum_RoughReality_ERunState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_RoughReality_ERunState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_RoughReality_ERunState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_RoughReality_ERunState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_RoughReality_ERunState()
{
	if (!Z_Registration_Info_UEnum_ERunState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ERunState.InnerSingleton, Z_Construct_UEnum_RoughReality_ERunState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ERunState.InnerSingleton;
}
// End Enum ERunState

// Begin Enum ESectorType
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ESectorType;
static UEnum* ESectorType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ESectorType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ESectorType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_RoughReality_ESectorType, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("ESectorType"));
	}
	return Z_Registration_Info_UEnum_ESectorType.OuterSingleton;
}
template<> ROUGHREALITY_API UEnum* StaticEnum<ESectorType>()
{
	return ESectorType_StaticEnum();
}
struct Z_Construct_UEnum_RoughReality_ESectorType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BelaVegas.Name", "ESectorType::BelaVegas" },
		{ "BlueprintType", "true" },
		{ "DilapidatedCity.Name", "ESectorType::DilapidatedCity" },
		{ "IndustrialDistrict.Name", "ESectorType::IndustrialDistrict" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
		{ "StationTunnels.Name", "ESectorType::StationTunnels" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ESectorType::DilapidatedCity", (int64)ESectorType::DilapidatedCity },
		{ "ESectorType::StationTunnels", (int64)ESectorType::StationTunnels },
		{ "ESectorType::BelaVegas", (int64)ESectorType::BelaVegas },
		{ "ESectorType::IndustrialDistrict", (int64)ESectorType::IndustrialDistrict },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_RoughReality_ESectorType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	"ESectorType",
	"ESectorType",
	Z_Construct_UEnum_RoughReality_ESectorType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_RoughReality_ESectorType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_RoughReality_ESectorType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_RoughReality_ESectorType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_RoughReality_ESectorType()
{
	if (!Z_Registration_Info_UEnum_ESectorType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ESectorType.InnerSingleton, Z_Construct_UEnum_RoughReality_ESectorType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ESectorType.InnerSingleton;
}
// End Enum ESectorType

// Begin ScriptStruct FRunStatistics
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_RunStatistics;
class UScriptStruct* FRunStatistics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_RunStatistics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_RunStatistics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FRunStatistics, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("RunStatistics"));
	}
	return Z_Registration_Info_UScriptStruct_RunStatistics.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FRunStatistics>()
{
	return FRunStatistics::StaticStruct();
}
struct Z_Construct_UScriptStruct_FRunStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RunNumber_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RunDuration_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnemiesKilled_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeethCollected_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SectorsCompleted_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccuracyPercentage_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BulletTimesUsed_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewindsUsed_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponUsageStats_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SectorCompletionTimes_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_RunNumber;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RunDuration;
	static const UECodeGen_Private::FIntPropertyParams NewProp_EnemiesKilled;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeethCollected;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SectorsCompleted;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AccuracyPercentage;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BulletTimesUsed;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RewindsUsed;
	static const UECodeGen_Private::FIntPropertyParams NewProp_WeaponUsageStats_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_WeaponUsageStats_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_WeaponUsageStats;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SectorCompletionTimes_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SectorCompletionTimes_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SectorCompletionTimes_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_SectorCompletionTimes;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FRunStatistics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_RunNumber = { "RunNumber", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRunStatistics, RunNumber), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RunNumber_MetaData), NewProp_RunNumber_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_RunDuration = { "RunDuration", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRunStatistics, RunDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RunDuration_MetaData), NewProp_RunDuration_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_EnemiesKilled = { "EnemiesKilled", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRunStatistics, EnemiesKilled), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnemiesKilled_MetaData), NewProp_EnemiesKilled_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_TeethCollected = { "TeethCollected", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRunStatistics, TeethCollected), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeethCollected_MetaData), NewProp_TeethCollected_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_SectorsCompleted = { "SectorsCompleted", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRunStatistics, SectorsCompleted), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SectorsCompleted_MetaData), NewProp_SectorsCompleted_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_AccuracyPercentage = { "AccuracyPercentage", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRunStatistics, AccuracyPercentage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccuracyPercentage_MetaData), NewProp_AccuracyPercentage_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_BulletTimesUsed = { "BulletTimesUsed", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRunStatistics, BulletTimesUsed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BulletTimesUsed_MetaData), NewProp_BulletTimesUsed_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_RewindsUsed = { "RewindsUsed", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRunStatistics, RewindsUsed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewindsUsed_MetaData), NewProp_RewindsUsed_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_WeaponUsageStats_ValueProp = { "WeaponUsageStats", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_WeaponUsageStats_Key_KeyProp = { "WeaponUsageStats_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_WeaponUsageStats = { "WeaponUsageStats", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRunStatistics, WeaponUsageStats), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponUsageStats_MetaData), NewProp_WeaponUsageStats_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_SectorCompletionTimes_ValueProp = { "SectorCompletionTimes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_SectorCompletionTimes_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_SectorCompletionTimes_Key_KeyProp = { "SectorCompletionTimes_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_RoughReality_ESectorType, METADATA_PARAMS(0, nullptr) }; // 3106776128
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_SectorCompletionTimes = { "SectorCompletionTimes", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FRunStatistics, SectorCompletionTimes), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SectorCompletionTimes_MetaData), NewProp_SectorCompletionTimes_MetaData) }; // 3106776128
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FRunStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_RunNumber,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_RunDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_EnemiesKilled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_TeethCollected,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_SectorsCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_AccuracyPercentage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_BulletTimesUsed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_RewindsUsed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_WeaponUsageStats_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_WeaponUsageStats_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_WeaponUsageStats,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_SectorCompletionTimes_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_SectorCompletionTimes_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_SectorCompletionTimes_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FRunStatistics_Statics::NewProp_SectorCompletionTimes,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRunStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FRunStatistics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	&NewStructOps,
	"RunStatistics",
	Z_Construct_UScriptStruct_FRunStatistics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRunStatistics_Statics::PropPointers),
	sizeof(FRunStatistics),
	alignof(FRunStatistics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FRunStatistics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FRunStatistics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FRunStatistics()
{
	if (!Z_Registration_Info_UScriptStruct_RunStatistics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_RunStatistics.InnerSingleton, Z_Construct_UScriptStruct_FRunStatistics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_RunStatistics.InnerSingleton;
}
// End ScriptStruct FRunStatistics

// Begin ScriptStruct FSectorConfiguration
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_SectorConfiguration;
class UScriptStruct* FSectorConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_SectorConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_SectorConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSectorConfiguration, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("SectorConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_SectorConfiguration.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FSectorConfiguration>()
{
	return FSectorConfiguration::StaticStruct();
}
struct Z_Construct_UScriptStruct_FSectorConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SectorType_MetaData[] = {
		{ "Category", "Sector" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SectorName_MetaData[] = {
		{ "Category", "Sector" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinLevels_MetaData[] = {
		{ "Category", "Sector" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxLevels_MetaData[] = {
		{ "Category", "Sector" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseDifficultyMultiplier_MetaData[] = {
		{ "Category", "Sector" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DifficultyScalingPerRun_MetaData[] = {
		{ "Category", "Sector" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredUnlocks_MetaData[] = {
		{ "Category", "Sector" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeethReward_MetaData[] = {
		{ "Category", "Sector" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SectorColor_MetaData[] = {
		{ "Category", "Sector" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SectorType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SectorType;
	static const UECodeGen_Private::FTextPropertyParams NewProp_SectorName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinLevels;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxLevels;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseDifficultyMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DifficultyScalingPerRun;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RequiredUnlocks_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RequiredUnlocks;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeethReward;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SectorColor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSectorConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSectorConfiguration_Statics::NewProp_SectorType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSectorConfiguration_Statics::NewProp_SectorType = { "SectorType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSectorConfiguration, SectorType), Z_Construct_UEnum_RoughReality_ESectorType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SectorType_MetaData), NewProp_SectorType_MetaData) }; // 3106776128
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FSectorConfiguration_Statics::NewProp_SectorName = { "SectorName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSectorConfiguration, SectorName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SectorName_MetaData), NewProp_SectorName_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSectorConfiguration_Statics::NewProp_MinLevels = { "MinLevels", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSectorConfiguration, MinLevels), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinLevels_MetaData), NewProp_MinLevels_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSectorConfiguration_Statics::NewProp_MaxLevels = { "MaxLevels", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSectorConfiguration, MaxLevels), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxLevels_MetaData), NewProp_MaxLevels_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSectorConfiguration_Statics::NewProp_BaseDifficultyMultiplier = { "BaseDifficultyMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSectorConfiguration, BaseDifficultyMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseDifficultyMultiplier_MetaData), NewProp_BaseDifficultyMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSectorConfiguration_Statics::NewProp_DifficultyScalingPerRun = { "DifficultyScalingPerRun", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSectorConfiguration, DifficultyScalingPerRun), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DifficultyScalingPerRun_MetaData), NewProp_DifficultyScalingPerRun_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSectorConfiguration_Statics::NewProp_RequiredUnlocks_Inner = { "RequiredUnlocks", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(0, nullptr) }; // 1298103297
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSectorConfiguration_Statics::NewProp_RequiredUnlocks = { "RequiredUnlocks", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSectorConfiguration, RequiredUnlocks), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredUnlocks_MetaData), NewProp_RequiredUnlocks_MetaData) }; // 1298103297
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSectorConfiguration_Statics::NewProp_TeethReward = { "TeethReward", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSectorConfiguration, TeethReward), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeethReward_MetaData), NewProp_TeethReward_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSectorConfiguration_Statics::NewProp_SectorColor = { "SectorColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSectorConfiguration, SectorColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SectorColor_MetaData), NewProp_SectorColor_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSectorConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSectorConfiguration_Statics::NewProp_SectorType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSectorConfiguration_Statics::NewProp_SectorType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSectorConfiguration_Statics::NewProp_SectorName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSectorConfiguration_Statics::NewProp_MinLevels,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSectorConfiguration_Statics::NewProp_MaxLevels,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSectorConfiguration_Statics::NewProp_BaseDifficultyMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSectorConfiguration_Statics::NewProp_DifficultyScalingPerRun,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSectorConfiguration_Statics::NewProp_RequiredUnlocks_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSectorConfiguration_Statics::NewProp_RequiredUnlocks,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSectorConfiguration_Statics::NewProp_TeethReward,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSectorConfiguration_Statics::NewProp_SectorColor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSectorConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSectorConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	&NewStructOps,
	"SectorConfiguration",
	Z_Construct_UScriptStruct_FSectorConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSectorConfiguration_Statics::PropPointers),
	sizeof(FSectorConfiguration),
	alignof(FSectorConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSectorConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSectorConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSectorConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_SectorConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_SectorConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FSectorConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_SectorConfiguration.InnerSingleton;
}
// End ScriptStruct FSectorConfiguration

// Begin Class AGameplayLoopManager Function AbandonCurrentRun
struct Z_Construct_UFunction_AGameplayLoopManager_AbandonCurrentRun_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Run Management" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameplayLoopManager_AbandonCurrentRun_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameplayLoopManager, nullptr, "AbandonCurrentRun", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_AbandonCurrentRun_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameplayLoopManager_AbandonCurrentRun_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AGameplayLoopManager_AbandonCurrentRun()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameplayLoopManager_AbandonCurrentRun_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameplayLoopManager::execAbandonCurrentRun)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AbandonCurrentRun();
	P_NATIVE_END;
}
// End Class AGameplayLoopManager Function AbandonCurrentRun

// Begin Class AGameplayLoopManager Function AdvanceToNextLevel
struct Z_Construct_UFunction_AGameplayLoopManager_AdvanceToNextLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progression" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Progression */" },
#endif
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Progression" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameplayLoopManager_AdvanceToNextLevel_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameplayLoopManager, nullptr, "AdvanceToNextLevel", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_AdvanceToNextLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameplayLoopManager_AdvanceToNextLevel_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AGameplayLoopManager_AdvanceToNextLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameplayLoopManager_AdvanceToNextLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameplayLoopManager::execAdvanceToNextLevel)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AdvanceToNextLevel();
	P_NATIVE_END;
}
// End Class AGameplayLoopManager Function AdvanceToNextLevel

// Begin Class AGameplayLoopManager Function AdvanceToNextSector
struct Z_Construct_UFunction_AGameplayLoopManager_AdvanceToNextSector_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progression" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameplayLoopManager_AdvanceToNextSector_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameplayLoopManager, nullptr, "AdvanceToNextSector", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_AdvanceToNextSector_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameplayLoopManager_AdvanceToNextSector_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AGameplayLoopManager_AdvanceToNextSector()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameplayLoopManager_AdvanceToNextSector_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameplayLoopManager::execAdvanceToNextSector)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AdvanceToNextSector();
	P_NATIVE_END;
}
// End Class AGameplayLoopManager Function AdvanceToNextSector

// Begin Class AGameplayLoopManager Function CalculateDifficultyMultiplier
struct Z_Construct_UFunction_AGameplayLoopManager_CalculateDifficultyMultiplier_Statics
{
	struct GameplayLoopManager_eventCalculateDifficultyMultiplier_Parms
	{
		int32 SectorIndex;
		int32 RunNumber;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Difficulty" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Difficulty Scaling */" },
#endif
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Difficulty Scaling" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SectorIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RunNumber;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AGameplayLoopManager_CalculateDifficultyMultiplier_Statics::NewProp_SectorIndex = { "SectorIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameplayLoopManager_eventCalculateDifficultyMultiplier_Parms, SectorIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AGameplayLoopManager_CalculateDifficultyMultiplier_Statics::NewProp_RunNumber = { "RunNumber", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameplayLoopManager_eventCalculateDifficultyMultiplier_Parms, RunNumber), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGameplayLoopManager_CalculateDifficultyMultiplier_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameplayLoopManager_eventCalculateDifficultyMultiplier_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameplayLoopManager_CalculateDifficultyMultiplier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameplayLoopManager_CalculateDifficultyMultiplier_Statics::NewProp_SectorIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameplayLoopManager_CalculateDifficultyMultiplier_Statics::NewProp_RunNumber,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameplayLoopManager_CalculateDifficultyMultiplier_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_CalculateDifficultyMultiplier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameplayLoopManager_CalculateDifficultyMultiplier_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameplayLoopManager, nullptr, "CalculateDifficultyMultiplier", nullptr, nullptr, Z_Construct_UFunction_AGameplayLoopManager_CalculateDifficultyMultiplier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_CalculateDifficultyMultiplier_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameplayLoopManager_CalculateDifficultyMultiplier_Statics::GameplayLoopManager_eventCalculateDifficultyMultiplier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_CalculateDifficultyMultiplier_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameplayLoopManager_CalculateDifficultyMultiplier_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameplayLoopManager_CalculateDifficultyMultiplier_Statics::GameplayLoopManager_eventCalculateDifficultyMultiplier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameplayLoopManager_CalculateDifficultyMultiplier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameplayLoopManager_CalculateDifficultyMultiplier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameplayLoopManager::execCalculateDifficultyMultiplier)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SectorIndex);
	P_GET_PROPERTY(FIntProperty,Z_Param_RunNumber);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->CalculateDifficultyMultiplier(Z_Param_SectorIndex,Z_Param_RunNumber);
	P_NATIVE_END;
}
// End Class AGameplayLoopManager Function CalculateDifficultyMultiplier

// Begin Class AGameplayLoopManager Function CanAdvanceToSector
struct Z_Construct_UFunction_AGameplayLoopManager_CanAdvanceToSector_Statics
{
	struct GameplayLoopManager_eventCanAdvanceToSector_Parms
	{
		int32 SectorIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progression" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SectorIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AGameplayLoopManager_CanAdvanceToSector_Statics::NewProp_SectorIndex = { "SectorIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameplayLoopManager_eventCanAdvanceToSector_Parms, SectorIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AGameplayLoopManager_CanAdvanceToSector_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((GameplayLoopManager_eventCanAdvanceToSector_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AGameplayLoopManager_CanAdvanceToSector_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(GameplayLoopManager_eventCanAdvanceToSector_Parms), &Z_Construct_UFunction_AGameplayLoopManager_CanAdvanceToSector_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameplayLoopManager_CanAdvanceToSector_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameplayLoopManager_CanAdvanceToSector_Statics::NewProp_SectorIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameplayLoopManager_CanAdvanceToSector_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_CanAdvanceToSector_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameplayLoopManager_CanAdvanceToSector_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameplayLoopManager, nullptr, "CanAdvanceToSector", nullptr, nullptr, Z_Construct_UFunction_AGameplayLoopManager_CanAdvanceToSector_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_CanAdvanceToSector_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameplayLoopManager_CanAdvanceToSector_Statics::GameplayLoopManager_eventCanAdvanceToSector_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_CanAdvanceToSector_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameplayLoopManager_CanAdvanceToSector_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameplayLoopManager_CanAdvanceToSector_Statics::GameplayLoopManager_eventCanAdvanceToSector_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameplayLoopManager_CanAdvanceToSector()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameplayLoopManager_CanAdvanceToSector_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameplayLoopManager::execCanAdvanceToSector)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SectorIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanAdvanceToSector(Z_Param_SectorIndex);
	P_NATIVE_END;
}
// End Class AGameplayLoopManager Function CanAdvanceToSector

// Begin Class AGameplayLoopManager Function CompleteCurrentRun
struct Z_Construct_UFunction_AGameplayLoopManager_CompleteCurrentRun_Statics
{
	struct GameplayLoopManager_eventCompleteCurrentRun_Parms
	{
		bool bSuccess;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Run Management" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AGameplayLoopManager_CompleteCurrentRun_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((GameplayLoopManager_eventCompleteCurrentRun_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AGameplayLoopManager_CompleteCurrentRun_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(GameplayLoopManager_eventCompleteCurrentRun_Parms), &Z_Construct_UFunction_AGameplayLoopManager_CompleteCurrentRun_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameplayLoopManager_CompleteCurrentRun_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameplayLoopManager_CompleteCurrentRun_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_CompleteCurrentRun_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameplayLoopManager_CompleteCurrentRun_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameplayLoopManager, nullptr, "CompleteCurrentRun", nullptr, nullptr, Z_Construct_UFunction_AGameplayLoopManager_CompleteCurrentRun_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_CompleteCurrentRun_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameplayLoopManager_CompleteCurrentRun_Statics::GameplayLoopManager_eventCompleteCurrentRun_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_CompleteCurrentRun_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameplayLoopManager_CompleteCurrentRun_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameplayLoopManager_CompleteCurrentRun_Statics::GameplayLoopManager_eventCompleteCurrentRun_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameplayLoopManager_CompleteCurrentRun()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameplayLoopManager_CompleteCurrentRun_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameplayLoopManager::execCompleteCurrentRun)
{
	P_GET_UBOOL(Z_Param_bSuccess);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CompleteCurrentRun(Z_Param_bSuccess);
	P_NATIVE_END;
}
// End Class AGameplayLoopManager Function CompleteCurrentRun

// Begin Class AGameplayLoopManager Function GetCurrentSectorConfiguration
struct Z_Construct_UFunction_AGameplayLoopManager_GetCurrentSectorConfiguration_Statics
{
	struct GameplayLoopManager_eventGetCurrentSectorConfiguration_Parms
	{
		FSectorConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Queries" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Queries */" },
#endif
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Queries" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameplayLoopManager_GetCurrentSectorConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameplayLoopManager_eventGetCurrentSectorConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FSectorConfiguration, METADATA_PARAMS(0, nullptr) }; // 2484705251
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameplayLoopManager_GetCurrentSectorConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameplayLoopManager_GetCurrentSectorConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_GetCurrentSectorConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameplayLoopManager_GetCurrentSectorConfiguration_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameplayLoopManager, nullptr, "GetCurrentSectorConfiguration", nullptr, nullptr, Z_Construct_UFunction_AGameplayLoopManager_GetCurrentSectorConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_GetCurrentSectorConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameplayLoopManager_GetCurrentSectorConfiguration_Statics::GameplayLoopManager_eventGetCurrentSectorConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_GetCurrentSectorConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameplayLoopManager_GetCurrentSectorConfiguration_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameplayLoopManager_GetCurrentSectorConfiguration_Statics::GameplayLoopManager_eventGetCurrentSectorConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameplayLoopManager_GetCurrentSectorConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameplayLoopManager_GetCurrentSectorConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameplayLoopManager::execGetCurrentSectorConfiguration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FSectorConfiguration*)Z_Param__Result=P_THIS->GetCurrentSectorConfiguration();
	P_NATIVE_END;
}
// End Class AGameplayLoopManager Function GetCurrentSectorConfiguration

// Begin Class AGameplayLoopManager Function GetRunProgress
struct Z_Construct_UFunction_AGameplayLoopManager_GetRunProgress_Statics
{
	struct GameplayLoopManager_eventGetRunProgress_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Queries" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGameplayLoopManager_GetRunProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameplayLoopManager_eventGetRunProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameplayLoopManager_GetRunProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameplayLoopManager_GetRunProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_GetRunProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameplayLoopManager_GetRunProgress_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameplayLoopManager, nullptr, "GetRunProgress", nullptr, nullptr, Z_Construct_UFunction_AGameplayLoopManager_GetRunProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_GetRunProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameplayLoopManager_GetRunProgress_Statics::GameplayLoopManager_eventGetRunProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_GetRunProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameplayLoopManager_GetRunProgress_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameplayLoopManager_GetRunProgress_Statics::GameplayLoopManager_eventGetRunProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameplayLoopManager_GetRunProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameplayLoopManager_GetRunProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameplayLoopManager::execGetRunProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetRunProgress();
	P_NATIVE_END;
}
// End Class AGameplayLoopManager Function GetRunProgress

// Begin Class AGameplayLoopManager Function GetSectorConfiguration
struct Z_Construct_UFunction_AGameplayLoopManager_GetSectorConfiguration_Statics
{
	struct GameplayLoopManager_eventGetSectorConfiguration_Parms
	{
		int32 SectorIndex;
		FSectorConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Queries" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SectorIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AGameplayLoopManager_GetSectorConfiguration_Statics::NewProp_SectorIndex = { "SectorIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameplayLoopManager_eventGetSectorConfiguration_Parms, SectorIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameplayLoopManager_GetSectorConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameplayLoopManager_eventGetSectorConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FSectorConfiguration, METADATA_PARAMS(0, nullptr) }; // 2484705251
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameplayLoopManager_GetSectorConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameplayLoopManager_GetSectorConfiguration_Statics::NewProp_SectorIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameplayLoopManager_GetSectorConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_GetSectorConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameplayLoopManager_GetSectorConfiguration_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameplayLoopManager, nullptr, "GetSectorConfiguration", nullptr, nullptr, Z_Construct_UFunction_AGameplayLoopManager_GetSectorConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_GetSectorConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameplayLoopManager_GetSectorConfiguration_Statics::GameplayLoopManager_eventGetSectorConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_GetSectorConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameplayLoopManager_GetSectorConfiguration_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameplayLoopManager_GetSectorConfiguration_Statics::GameplayLoopManager_eventGetSectorConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameplayLoopManager_GetSectorConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameplayLoopManager_GetSectorConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameplayLoopManager::execGetSectorConfiguration)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SectorIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FSectorConfiguration*)Z_Param__Result=P_THIS->GetSectorConfiguration(Z_Param_SectorIndex);
	P_NATIVE_END;
}
// End Class AGameplayLoopManager Function GetSectorConfiguration

// Begin Class AGameplayLoopManager Function GetTotalSectors
struct Z_Construct_UFunction_AGameplayLoopManager_GetTotalSectors_Statics
{
	struct GameplayLoopManager_eventGetTotalSectors_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Queries" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AGameplayLoopManager_GetTotalSectors_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameplayLoopManager_eventGetTotalSectors_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameplayLoopManager_GetTotalSectors_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameplayLoopManager_GetTotalSectors_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_GetTotalSectors_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameplayLoopManager_GetTotalSectors_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameplayLoopManager, nullptr, "GetTotalSectors", nullptr, nullptr, Z_Construct_UFunction_AGameplayLoopManager_GetTotalSectors_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_GetTotalSectors_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameplayLoopManager_GetTotalSectors_Statics::GameplayLoopManager_eventGetTotalSectors_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_GetTotalSectors_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameplayLoopManager_GetTotalSectors_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameplayLoopManager_GetTotalSectors_Statics::GameplayLoopManager_eventGetTotalSectors_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameplayLoopManager_GetTotalSectors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameplayLoopManager_GetTotalSectors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameplayLoopManager::execGetTotalSectors)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetTotalSectors();
	P_NATIVE_END;
}
// End Class AGameplayLoopManager Function GetTotalSectors

// Begin Class AGameplayLoopManager Function IsRunInProgress
struct Z_Construct_UFunction_AGameplayLoopManager_IsRunInProgress_Statics
{
	struct GameplayLoopManager_eventIsRunInProgress_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Queries" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AGameplayLoopManager_IsRunInProgress_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((GameplayLoopManager_eventIsRunInProgress_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AGameplayLoopManager_IsRunInProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(GameplayLoopManager_eventIsRunInProgress_Parms), &Z_Construct_UFunction_AGameplayLoopManager_IsRunInProgress_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameplayLoopManager_IsRunInProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameplayLoopManager_IsRunInProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_IsRunInProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameplayLoopManager_IsRunInProgress_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameplayLoopManager, nullptr, "IsRunInProgress", nullptr, nullptr, Z_Construct_UFunction_AGameplayLoopManager_IsRunInProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_IsRunInProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameplayLoopManager_IsRunInProgress_Statics::GameplayLoopManager_eventIsRunInProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_IsRunInProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameplayLoopManager_IsRunInProgress_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameplayLoopManager_IsRunInProgress_Statics::GameplayLoopManager_eventIsRunInProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameplayLoopManager_IsRunInProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameplayLoopManager_IsRunInProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameplayLoopManager::execIsRunInProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsRunInProgress();
	P_NATIVE_END;
}
// End Class AGameplayLoopManager Function IsRunInProgress

// Begin Class AGameplayLoopManager Function PreloadSectorAssets
struct Z_Construct_UFunction_AGameplayLoopManager_PreloadSectorAssets_Statics
{
	struct GameplayLoopManager_eventPreloadSectorAssets_Parms
	{
		int32 SectorIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Loading" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Async Loading */" },
#endif
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Async Loading" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SectorIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AGameplayLoopManager_PreloadSectorAssets_Statics::NewProp_SectorIndex = { "SectorIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameplayLoopManager_eventPreloadSectorAssets_Parms, SectorIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameplayLoopManager_PreloadSectorAssets_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameplayLoopManager_PreloadSectorAssets_Statics::NewProp_SectorIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_PreloadSectorAssets_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameplayLoopManager_PreloadSectorAssets_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameplayLoopManager, nullptr, "PreloadSectorAssets", nullptr, nullptr, Z_Construct_UFunction_AGameplayLoopManager_PreloadSectorAssets_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_PreloadSectorAssets_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameplayLoopManager_PreloadSectorAssets_Statics::GameplayLoopManager_eventPreloadSectorAssets_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_PreloadSectorAssets_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameplayLoopManager_PreloadSectorAssets_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameplayLoopManager_PreloadSectorAssets_Statics::GameplayLoopManager_eventPreloadSectorAssets_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameplayLoopManager_PreloadSectorAssets()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameplayLoopManager_PreloadSectorAssets_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameplayLoopManager::execPreloadSectorAssets)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SectorIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PreloadSectorAssets(Z_Param_SectorIndex);
	P_NATIVE_END;
}
// End Class AGameplayLoopManager Function PreloadSectorAssets

// Begin Class AGameplayLoopManager Function RecordBulletTimeUsage
struct Z_Construct_UFunction_AGameplayLoopManager_RecordBulletTimeUsage_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameplayLoopManager_RecordBulletTimeUsage_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameplayLoopManager, nullptr, "RecordBulletTimeUsage", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_RecordBulletTimeUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameplayLoopManager_RecordBulletTimeUsage_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AGameplayLoopManager_RecordBulletTimeUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameplayLoopManager_RecordBulletTimeUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameplayLoopManager::execRecordBulletTimeUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RecordBulletTimeUsage();
	P_NATIVE_END;
}
// End Class AGameplayLoopManager Function RecordBulletTimeUsage

// Begin Class AGameplayLoopManager Function RecordEnemyKill
struct Z_Construct_UFunction_AGameplayLoopManager_RecordEnemyKill_Statics
{
	struct GameplayLoopManager_eventRecordEnemyKill_Parms
	{
		FString EnemyType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Statistics */" },
#endif
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Statistics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnemyType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EnemyType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGameplayLoopManager_RecordEnemyKill_Statics::NewProp_EnemyType = { "EnemyType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameplayLoopManager_eventRecordEnemyKill_Parms, EnemyType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnemyType_MetaData), NewProp_EnemyType_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameplayLoopManager_RecordEnemyKill_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameplayLoopManager_RecordEnemyKill_Statics::NewProp_EnemyType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_RecordEnemyKill_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameplayLoopManager_RecordEnemyKill_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameplayLoopManager, nullptr, "RecordEnemyKill", nullptr, nullptr, Z_Construct_UFunction_AGameplayLoopManager_RecordEnemyKill_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_RecordEnemyKill_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameplayLoopManager_RecordEnemyKill_Statics::GameplayLoopManager_eventRecordEnemyKill_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_RecordEnemyKill_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameplayLoopManager_RecordEnemyKill_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameplayLoopManager_RecordEnemyKill_Statics::GameplayLoopManager_eventRecordEnemyKill_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameplayLoopManager_RecordEnemyKill()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameplayLoopManager_RecordEnemyKill_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameplayLoopManager::execRecordEnemyKill)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_EnemyType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RecordEnemyKill(Z_Param_EnemyType);
	P_NATIVE_END;
}
// End Class AGameplayLoopManager Function RecordEnemyKill

// Begin Class AGameplayLoopManager Function RecordRewindUsage
struct Z_Construct_UFunction_AGameplayLoopManager_RecordRewindUsage_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameplayLoopManager_RecordRewindUsage_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameplayLoopManager, nullptr, "RecordRewindUsage", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_RecordRewindUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameplayLoopManager_RecordRewindUsage_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AGameplayLoopManager_RecordRewindUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameplayLoopManager_RecordRewindUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameplayLoopManager::execRecordRewindUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RecordRewindUsage();
	P_NATIVE_END;
}
// End Class AGameplayLoopManager Function RecordRewindUsage

// Begin Class AGameplayLoopManager Function RecordTeethCollected
struct Z_Construct_UFunction_AGameplayLoopManager_RecordTeethCollected_Statics
{
	struct GameplayLoopManager_eventRecordTeethCollected_Parms
	{
		int32 Amount;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Amount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AGameplayLoopManager_RecordTeethCollected_Statics::NewProp_Amount = { "Amount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameplayLoopManager_eventRecordTeethCollected_Parms, Amount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameplayLoopManager_RecordTeethCollected_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameplayLoopManager_RecordTeethCollected_Statics::NewProp_Amount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_RecordTeethCollected_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameplayLoopManager_RecordTeethCollected_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameplayLoopManager, nullptr, "RecordTeethCollected", nullptr, nullptr, Z_Construct_UFunction_AGameplayLoopManager_RecordTeethCollected_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_RecordTeethCollected_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameplayLoopManager_RecordTeethCollected_Statics::GameplayLoopManager_eventRecordTeethCollected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_RecordTeethCollected_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameplayLoopManager_RecordTeethCollected_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameplayLoopManager_RecordTeethCollected_Statics::GameplayLoopManager_eventRecordTeethCollected_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameplayLoopManager_RecordTeethCollected()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameplayLoopManager_RecordTeethCollected_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameplayLoopManager::execRecordTeethCollected)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Amount);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RecordTeethCollected(Z_Param_Amount);
	P_NATIVE_END;
}
// End Class AGameplayLoopManager Function RecordTeethCollected

// Begin Class AGameplayLoopManager Function RecordWeaponUsage
struct Z_Construct_UFunction_AGameplayLoopManager_RecordWeaponUsage_Statics
{
	struct GameplayLoopManager_eventRecordWeaponUsage_Parms
	{
		FString WeaponName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_WeaponName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGameplayLoopManager_RecordWeaponUsage_Statics::NewProp_WeaponName = { "WeaponName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameplayLoopManager_eventRecordWeaponUsage_Parms, WeaponName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponName_MetaData), NewProp_WeaponName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameplayLoopManager_RecordWeaponUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameplayLoopManager_RecordWeaponUsage_Statics::NewProp_WeaponName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_RecordWeaponUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameplayLoopManager_RecordWeaponUsage_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameplayLoopManager, nullptr, "RecordWeaponUsage", nullptr, nullptr, Z_Construct_UFunction_AGameplayLoopManager_RecordWeaponUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_RecordWeaponUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameplayLoopManager_RecordWeaponUsage_Statics::GameplayLoopManager_eventRecordWeaponUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_RecordWeaponUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameplayLoopManager_RecordWeaponUsage_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameplayLoopManager_RecordWeaponUsage_Statics::GameplayLoopManager_eventRecordWeaponUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameplayLoopManager_RecordWeaponUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameplayLoopManager_RecordWeaponUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameplayLoopManager::execRecordWeaponUsage)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_WeaponName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RecordWeaponUsage(Z_Param_WeaponName);
	P_NATIVE_END;
}
// End Class AGameplayLoopManager Function RecordWeaponUsage

// Begin Class AGameplayLoopManager Function StartNewRun
struct Z_Construct_UFunction_AGameplayLoopManager_StartNewRun_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Run Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Run Management */" },
#endif
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Run Management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameplayLoopManager_StartNewRun_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameplayLoopManager, nullptr, "StartNewRun", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_StartNewRun_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameplayLoopManager_StartNewRun_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AGameplayLoopManager_StartNewRun()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameplayLoopManager_StartNewRun_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameplayLoopManager::execStartNewRun)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartNewRun();
	P_NATIVE_END;
}
// End Class AGameplayLoopManager Function StartNewRun

// Begin Class AGameplayLoopManager Function UnloadUnusedAssets
struct Z_Construct_UFunction_AGameplayLoopManager_UnloadUnusedAssets_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Loading" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameplayLoopManager_UnloadUnusedAssets_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameplayLoopManager, nullptr, "UnloadUnusedAssets", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_UnloadUnusedAssets_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameplayLoopManager_UnloadUnusedAssets_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AGameplayLoopManager_UnloadUnusedAssets()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameplayLoopManager_UnloadUnusedAssets_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameplayLoopManager::execUnloadUnusedAssets)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnloadUnusedAssets();
	P_NATIVE_END;
}
// End Class AGameplayLoopManager Function UnloadUnusedAssets

// Begin Class AGameplayLoopManager Function UpdateAccuracy
struct Z_Construct_UFunction_AGameplayLoopManager_UpdateAccuracy_Statics
{
	struct GameplayLoopManager_eventUpdateAccuracy_Parms
	{
		int32 ShotsHit;
		int32 ShotsFired;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ShotsHit;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ShotsFired;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AGameplayLoopManager_UpdateAccuracy_Statics::NewProp_ShotsHit = { "ShotsHit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameplayLoopManager_eventUpdateAccuracy_Parms, ShotsHit), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AGameplayLoopManager_UpdateAccuracy_Statics::NewProp_ShotsFired = { "ShotsFired", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameplayLoopManager_eventUpdateAccuracy_Parms, ShotsFired), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameplayLoopManager_UpdateAccuracy_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameplayLoopManager_UpdateAccuracy_Statics::NewProp_ShotsHit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameplayLoopManager_UpdateAccuracy_Statics::NewProp_ShotsFired,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_UpdateAccuracy_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameplayLoopManager_UpdateAccuracy_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameplayLoopManager, nullptr, "UpdateAccuracy", nullptr, nullptr, Z_Construct_UFunction_AGameplayLoopManager_UpdateAccuracy_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_UpdateAccuracy_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameplayLoopManager_UpdateAccuracy_Statics::GameplayLoopManager_eventUpdateAccuracy_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_UpdateAccuracy_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameplayLoopManager_UpdateAccuracy_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameplayLoopManager_UpdateAccuracy_Statics::GameplayLoopManager_eventUpdateAccuracy_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameplayLoopManager_UpdateAccuracy()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameplayLoopManager_UpdateAccuracy_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameplayLoopManager::execUpdateAccuracy)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_ShotsHit);
	P_GET_PROPERTY(FIntProperty,Z_Param_ShotsFired);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateAccuracy(Z_Param_ShotsHit,Z_Param_ShotsFired);
	P_NATIVE_END;
}
// End Class AGameplayLoopManager Function UpdateAccuracy

// Begin Class AGameplayLoopManager Function UpdateDifficultyScaling
struct Z_Construct_UFunction_AGameplayLoopManager_UpdateDifficultyScaling_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Difficulty" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameplayLoopManager_UpdateDifficultyScaling_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameplayLoopManager, nullptr, "UpdateDifficultyScaling", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameplayLoopManager_UpdateDifficultyScaling_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameplayLoopManager_UpdateDifficultyScaling_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AGameplayLoopManager_UpdateDifficultyScaling()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameplayLoopManager_UpdateDifficultyScaling_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameplayLoopManager::execUpdateDifficultyScaling)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateDifficultyScaling();
	P_NATIVE_END;
}
// End Class AGameplayLoopManager Function UpdateDifficultyScaling

// Begin Class AGameplayLoopManager
void AGameplayLoopManager::StaticRegisterNativesAGameplayLoopManager()
{
	UClass* Class = AGameplayLoopManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AbandonCurrentRun", &AGameplayLoopManager::execAbandonCurrentRun },
		{ "AdvanceToNextLevel", &AGameplayLoopManager::execAdvanceToNextLevel },
		{ "AdvanceToNextSector", &AGameplayLoopManager::execAdvanceToNextSector },
		{ "CalculateDifficultyMultiplier", &AGameplayLoopManager::execCalculateDifficultyMultiplier },
		{ "CanAdvanceToSector", &AGameplayLoopManager::execCanAdvanceToSector },
		{ "CompleteCurrentRun", &AGameplayLoopManager::execCompleteCurrentRun },
		{ "GetCurrentSectorConfiguration", &AGameplayLoopManager::execGetCurrentSectorConfiguration },
		{ "GetRunProgress", &AGameplayLoopManager::execGetRunProgress },
		{ "GetSectorConfiguration", &AGameplayLoopManager::execGetSectorConfiguration },
		{ "GetTotalSectors", &AGameplayLoopManager::execGetTotalSectors },
		{ "IsRunInProgress", &AGameplayLoopManager::execIsRunInProgress },
		{ "PreloadSectorAssets", &AGameplayLoopManager::execPreloadSectorAssets },
		{ "RecordBulletTimeUsage", &AGameplayLoopManager::execRecordBulletTimeUsage },
		{ "RecordEnemyKill", &AGameplayLoopManager::execRecordEnemyKill },
		{ "RecordRewindUsage", &AGameplayLoopManager::execRecordRewindUsage },
		{ "RecordTeethCollected", &AGameplayLoopManager::execRecordTeethCollected },
		{ "RecordWeaponUsage", &AGameplayLoopManager::execRecordWeaponUsage },
		{ "StartNewRun", &AGameplayLoopManager::execStartNewRun },
		{ "UnloadUnusedAssets", &AGameplayLoopManager::execUnloadUnusedAssets },
		{ "UpdateAccuracy", &AGameplayLoopManager::execUpdateAccuracy },
		{ "UpdateDifficultyScaling", &AGameplayLoopManager::execUpdateDifficultyScaling },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(AGameplayLoopManager);
UClass* Z_Construct_UClass_AGameplayLoopManager_NoRegister()
{
	return AGameplayLoopManager::StaticClass();
}
struct Z_Construct_UClass_AGameplayLoopManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Manages the complete gameplay loop for Rough Reality\n * Handles run progression, difficulty scaling, and meta-progression\n */" },
#endif
		{ "HideCategories", "Input Movement Collision Rendering HLOD WorldPartition DataLayers Transformation" },
		{ "IncludePath", "Core/GameplayLoopManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
		{ "ShowCategories", "Input|MouseInput Input|TouchInput" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Manages the complete gameplay loop for Rough Reality\nHandles run progression, difficulty scaling, and meta-progression" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentRunState_MetaData[] = {
		{ "Category", "Run State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current Run State */" },
#endif
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current Run State" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentRunNumber_MetaData[] = {
		{ "Category", "Run State" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentSectorIndex_MetaData[] = {
		{ "Category", "Run State" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentLevelIndex_MetaData[] = {
		{ "Category", "Run State" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RunStartTime_MetaData[] = {
		{ "Category", "Run State" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentRunStats_MetaData[] = {
		{ "Category", "Run State" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SectorConfigurations_MetaData[] = {
		{ "Category", "Sector Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sector Configuration */" },
#endif
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sector Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentDifficultyMultiplier_MetaData[] = {
		{ "Category", "Difficulty" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnRunStarted_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Events */" },
#endif
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnRunCompleted_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSectorChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnDifficultyScaled_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/GameplayLoopManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentRunState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentRunState;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentRunNumber;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentSectorIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentLevelIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RunStartTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentRunStats;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SectorConfigurations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SectorConfigurations;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentDifficultyMultiplier;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnRunStarted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnRunCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSectorChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnDifficultyScaled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AGameplayLoopManager_AbandonCurrentRun, "AbandonCurrentRun" }, // 368326575
		{ &Z_Construct_UFunction_AGameplayLoopManager_AdvanceToNextLevel, "AdvanceToNextLevel" }, // 1002222951
		{ &Z_Construct_UFunction_AGameplayLoopManager_AdvanceToNextSector, "AdvanceToNextSector" }, // 1578671154
		{ &Z_Construct_UFunction_AGameplayLoopManager_CalculateDifficultyMultiplier, "CalculateDifficultyMultiplier" }, // 2800395511
		{ &Z_Construct_UFunction_AGameplayLoopManager_CanAdvanceToSector, "CanAdvanceToSector" }, // 616179501
		{ &Z_Construct_UFunction_AGameplayLoopManager_CompleteCurrentRun, "CompleteCurrentRun" }, // 2855304920
		{ &Z_Construct_UFunction_AGameplayLoopManager_GetCurrentSectorConfiguration, "GetCurrentSectorConfiguration" }, // 3403199647
		{ &Z_Construct_UFunction_AGameplayLoopManager_GetRunProgress, "GetRunProgress" }, // 3991888906
		{ &Z_Construct_UFunction_AGameplayLoopManager_GetSectorConfiguration, "GetSectorConfiguration" }, // 1836391737
		{ &Z_Construct_UFunction_AGameplayLoopManager_GetTotalSectors, "GetTotalSectors" }, // 2113383294
		{ &Z_Construct_UFunction_AGameplayLoopManager_IsRunInProgress, "IsRunInProgress" }, // 1630245896
		{ &Z_Construct_UFunction_AGameplayLoopManager_PreloadSectorAssets, "PreloadSectorAssets" }, // 963155011
		{ &Z_Construct_UFunction_AGameplayLoopManager_RecordBulletTimeUsage, "RecordBulletTimeUsage" }, // 3261323675
		{ &Z_Construct_UFunction_AGameplayLoopManager_RecordEnemyKill, "RecordEnemyKill" }, // 2881078585
		{ &Z_Construct_UFunction_AGameplayLoopManager_RecordRewindUsage, "RecordRewindUsage" }, // 2221039028
		{ &Z_Construct_UFunction_AGameplayLoopManager_RecordTeethCollected, "RecordTeethCollected" }, // 2108396847
		{ &Z_Construct_UFunction_AGameplayLoopManager_RecordWeaponUsage, "RecordWeaponUsage" }, // 3596715798
		{ &Z_Construct_UFunction_AGameplayLoopManager_StartNewRun, "StartNewRun" }, // 1063043818
		{ &Z_Construct_UFunction_AGameplayLoopManager_UnloadUnusedAssets, "UnloadUnusedAssets" }, // 2403815816
		{ &Z_Construct_UFunction_AGameplayLoopManager_UpdateAccuracy, "UpdateAccuracy" }, // 1667349845
		{ &Z_Construct_UFunction_AGameplayLoopManager_UpdateDifficultyScaling, "UpdateDifficultyScaling" }, // 3052277049
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AGameplayLoopManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_CurrentRunState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_CurrentRunState = { "CurrentRunState", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameplayLoopManager, CurrentRunState), Z_Construct_UEnum_RoughReality_ERunState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentRunState_MetaData), NewProp_CurrentRunState_MetaData) }; // 828435214
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_CurrentRunNumber = { "CurrentRunNumber", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameplayLoopManager, CurrentRunNumber), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentRunNumber_MetaData), NewProp_CurrentRunNumber_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_CurrentSectorIndex = { "CurrentSectorIndex", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameplayLoopManager, CurrentSectorIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentSectorIndex_MetaData), NewProp_CurrentSectorIndex_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_CurrentLevelIndex = { "CurrentLevelIndex", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameplayLoopManager, CurrentLevelIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentLevelIndex_MetaData), NewProp_CurrentLevelIndex_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_RunStartTime = { "RunStartTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameplayLoopManager, RunStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RunStartTime_MetaData), NewProp_RunStartTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_CurrentRunStats = { "CurrentRunStats", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameplayLoopManager, CurrentRunStats), Z_Construct_UScriptStruct_FRunStatistics, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentRunStats_MetaData), NewProp_CurrentRunStats_MetaData) }; // 3979356677
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_SectorConfigurations_Inner = { "SectorConfigurations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSectorConfiguration, METADATA_PARAMS(0, nullptr) }; // 2484705251
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_SectorConfigurations = { "SectorConfigurations", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameplayLoopManager, SectorConfigurations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SectorConfigurations_MetaData), NewProp_SectorConfigurations_MetaData) }; // 2484705251
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_CurrentDifficultyMultiplier = { "CurrentDifficultyMultiplier", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameplayLoopManager, CurrentDifficultyMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentDifficultyMultiplier_MetaData), NewProp_CurrentDifficultyMultiplier_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_OnRunStarted = { "OnRunStarted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameplayLoopManager, OnRunStarted), Z_Construct_UDelegateFunction_RoughReality_OnRunStarted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnRunStarted_MetaData), NewProp_OnRunStarted_MetaData) }; // 3292716972
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_OnRunCompleted = { "OnRunCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameplayLoopManager, OnRunCompleted), Z_Construct_UDelegateFunction_RoughReality_OnRunCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnRunCompleted_MetaData), NewProp_OnRunCompleted_MetaData) }; // 705961697
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_OnSectorChanged = { "OnSectorChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameplayLoopManager, OnSectorChanged), Z_Construct_UDelegateFunction_RoughReality_OnSectorChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSectorChanged_MetaData), NewProp_OnSectorChanged_MetaData) }; // 3986820282
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_OnDifficultyScaled = { "OnDifficultyScaled", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameplayLoopManager, OnDifficultyScaled), Z_Construct_UDelegateFunction_RoughReality_OnDifficultyScaled__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnDifficultyScaled_MetaData), NewProp_OnDifficultyScaled_MetaData) }; // 3671126848
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AGameplayLoopManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_CurrentRunState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_CurrentRunState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_CurrentRunNumber,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_CurrentSectorIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_CurrentLevelIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_RunStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_CurrentRunStats,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_SectorConfigurations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_SectorConfigurations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_CurrentDifficultyMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_OnRunStarted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_OnRunCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_OnSectorChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameplayLoopManager_Statics::NewProp_OnDifficultyScaled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AGameplayLoopManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AGameplayLoopManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AGameStateBase,
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AGameplayLoopManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AGameplayLoopManager_Statics::ClassParams = {
	&AGameplayLoopManager::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AGameplayLoopManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AGameplayLoopManager_Statics::PropPointers),
	0,
	0x009002A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AGameplayLoopManager_Statics::Class_MetaDataParams), Z_Construct_UClass_AGameplayLoopManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AGameplayLoopManager()
{
	if (!Z_Registration_Info_UClass_AGameplayLoopManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AGameplayLoopManager.OuterSingleton, Z_Construct_UClass_AGameplayLoopManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AGameplayLoopManager.OuterSingleton;
}
template<> ROUGHREALITY_API UClass* StaticClass<AGameplayLoopManager>()
{
	return AGameplayLoopManager::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AGameplayLoopManager);
AGameplayLoopManager::~AGameplayLoopManager() {}
// End Class AGameplayLoopManager

// Begin Registration
struct Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_GameplayLoopManager_h_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ERunState_StaticEnum, TEXT("ERunState"), &Z_Registration_Info_UEnum_ERunState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 828435214U) },
		{ ESectorType_StaticEnum, TEXT("ESectorType"), &Z_Registration_Info_UEnum_ESectorType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3106776128U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FRunStatistics::StaticStruct, Z_Construct_UScriptStruct_FRunStatistics_Statics::NewStructOps, TEXT("RunStatistics"), &Z_Registration_Info_UScriptStruct_RunStatistics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FRunStatistics), 3979356677U) },
		{ FSectorConfiguration::StaticStruct, Z_Construct_UScriptStruct_FSectorConfiguration_Statics::NewStructOps, TEXT("SectorConfiguration"), &Z_Registration_Info_UScriptStruct_SectorConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSectorConfiguration), 2484705251U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AGameplayLoopManager, AGameplayLoopManager::StaticClass, TEXT("AGameplayLoopManager"), &Z_Registration_Info_UClass_AGameplayLoopManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AGameplayLoopManager), 1795661719U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_GameplayLoopManager_h_982921054(TEXT("/Script/RoughReality"),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_GameplayLoopManager_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_GameplayLoopManager_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_GameplayLoopManager_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_GameplayLoopManager_h_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_GameplayLoopManager_h_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_GameplayLoopManager_h_Statics::EnumInfo));
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
