// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Engine/DataAsset.h"
#include "GameplayTagContainer.h"
#include "RoughRealityDataAsset.generated.h"

/**
 * Base class for all data assets in Rough Reality
 * Provides common functionality and tagging system
 */
UCLASS(Abstract, BlueprintType, Blueprintable)
class ROUGHREALITY_API URoughRealityDataAsset : public UDataAsset
{
	GENERATED_BODY()

public:
	URoughRealityDataAsset();

	/** Unique identifier for this data asset */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Core")
	FName AssetID;

	/** Display name for UI */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Core")
	FText DisplayName;

	/** Description for UI */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Core", meta = (MultiLine = true))
	FText Description;

	/** Gameplay tags associated with this asset */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Core")
	FGameplayTagContainer AssetTags;

	/** Icon for UI representation */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Core")
	TSoftObjectPtr<UTexture2D> Icon;

	/** Whether this asset is enabled/available */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Core")
	bool bIsEnabled = true;

	/** Priority for sorting/selection */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Core")
	int32 Priority = 0;

public:
	/** Check if this asset has a specific tag */
	UFUNCTION(BlueprintCallable, Category = "Core")
	bool HasTag(const FGameplayTag& Tag) const;

	/** Check if this asset has any of the specified tags */
	UFUNCTION(BlueprintCallable, Category = "Core")
	bool HasAnyTag(const FGameplayTagContainer& Tags) const;

	/** Check if this asset has all of the specified tags */
	UFUNCTION(BlueprintCallable, Category = "Core")
	bool HasAllTags(const FGameplayTagContainer& Tags) const;

	/** Get the asset ID as string */
	UFUNCTION(BlueprintCallable, Category = "Core")
	FString GetAssetIDString() const;

	/** Validate the data asset (override in derived classes) */
	UFUNCTION(BlueprintCallable, Category = "Core")
	virtual bool ValidateAsset() const;

#if WITH_EDITOR
	/** Called when properties are changed in editor */
	virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;
#endif
};
