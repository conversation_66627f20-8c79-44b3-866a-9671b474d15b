// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/PostProcessComponent.h"
#include "Engine/PostProcessVolume.h"
#include "Materials/MaterialParameterCollection.h"
#include "VisualEffectsManager.generated.h"

class UMaterialParameterCollection;
class UPostProcessComponent;
class UCurveFloat;

UENUM(BlueprintType)
enum class EGameVisualState : uint8
{
	Normal,
	BulletTime,
	Damaged,
	Critical,
	Death,
	Victory
};

USTRUCT(BlueprintType)
struct FVisualEffectSettings
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effect")
	float Saturation = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effect")
	float Contrast = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effect")
	float Brightness = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effect")
	FLinearColor ColorTint = FLinearColor::White;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effect")
	float ChromaticAberration = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effect")
	float Vignette = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effect")
	float MotionBlur = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effect")
	float DepthOfField = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effect")
	float BloomIntensity = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Effect")
	float FilmGrain = 0.0f;

	FVisualEffectSettings()
	{
		Saturation = 1.0f;
		Contrast = 1.0f;
		Brightness = 0.0f;
		ColorTint = FLinearColor::White;
		ChromaticAberration = 0.0f;
		Vignette = 0.0f;
		MotionBlur = 0.0f;
		DepthOfField = 0.0f;
		BloomIntensity = 1.0f;
		FilmGrain = 0.0f;
	}
};

USTRUCT(BlueprintType)
struct FBulletTimeEffectSettings
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bullet Time")
	float DesaturationAmount = 0.7f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bullet Time")
	float ContrastBoost = 1.3f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bullet Time")
	FLinearColor TintColor = FLinearColor(0.8f, 0.9f, 1.0f, 1.0f);

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bullet Time")
	float TrailIntensity = 2.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bullet Time")
	float TimeDistortion = 0.3f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bullet Time")
	float EdgeGlow = 1.5f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bullet Time")
	float TransitionDuration = 0.2f;

	FBulletTimeEffectSettings()
	{
		DesaturationAmount = 0.7f;
		ContrastBoost = 1.3f;
		TintColor = FLinearColor(0.8f, 0.9f, 1.0f, 1.0f);
		TrailIntensity = 2.0f;
		TimeDistortion = 0.3f;
		EdgeGlow = 1.5f;
		TransitionDuration = 0.2f;
	}
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnVisualStateChanged, EGameVisualState, OldState, EGameVisualState, NewState);

/**
 * Visual Effects Manager for Rough Reality
 * Manages post-processing effects, shaders, and visual state transitions
 */
UCLASS(BlueprintType, Blueprintable)
class ROUGHREALITY_API AVisualEffectsManager : public AActor
{
	GENERATED_BODY()

public:
	AVisualEffectsManager();

protected:
	virtual void BeginPlay() override;
	virtual void Tick(float DeltaTime) override;

public:
	/** Components */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UPostProcessComponent* PostProcessComponent;

	/** Visual State */
	UPROPERTY(BlueprintReadOnly, Category = "Visual State")
	EGameVisualState CurrentVisualState = EGameVisualState::Normal;

	UPROPERTY(BlueprintReadOnly, Category = "Visual State")
	bool bIsTransitioning = false;

	/** Material Parameter Collection */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Materials")
	UMaterialParameterCollection* GlobalMaterialParameters;

	/** Visual Effect Presets */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Effect Presets")
	TMap<EGameVisualState, FVisualEffectSettings> VisualStatePresets;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Effect Presets")
	FBulletTimeEffectSettings BulletTimeSettings;

	/** Transition Curves */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Transitions")
	UCurveFloat* TransitionCurve;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Transitions")
	float DefaultTransitionDuration = 0.5f;

	/** Events */
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnVisualStateChanged OnVisualStateChanged;

public:
	/** Visual State Management */
	UFUNCTION(BlueprintCallable, Category = "Visual Effects")
	void SetVisualState(EGameVisualState NewState, float TransitionDuration = -1.0f);

	UFUNCTION(BlueprintCallable, Category = "Visual Effects")
	void SetVisualStateImmediate(EGameVisualState NewState);

	UFUNCTION(BlueprintCallable, Category = "Visual Effects")
	EGameVisualState GetCurrentVisualState() const { return CurrentVisualState; }

	/** Bullet Time Effects */
	UFUNCTION(BlueprintCallable, Category = "Bullet Time")
	void EnableBulletTimeEffects();

	UFUNCTION(BlueprintCallable, Category = "Bullet Time")
	void DisableBulletTimeEffects();

	UFUNCTION(BlueprintCallable, Category = "Bullet Time")
	void UpdateBulletTimeIntensity(float Intensity);

	/** Custom Effects */
	UFUNCTION(BlueprintCallable, Category = "Custom Effects")
	void ApplyDamageEffect(float Intensity, float Duration);

	UFUNCTION(BlueprintCallable, Category = "Custom Effects")
	void ApplyHealEffect(float Duration);

	UFUNCTION(BlueprintCallable, Category = "Custom Effects")
	void ApplyExplosionEffect(const FVector& Location, float Intensity);

	UFUNCTION(BlueprintCallable, Category = "Custom Effects")
	void ApplyScreenShake(float Intensity, float Duration);

	/** Material Parameter Control */
	UFUNCTION(BlueprintCallable, Category = "Material Parameters")
	void SetGlobalScalarParameter(const FName& ParameterName, float Value);

	UFUNCTION(BlueprintCallable, Category = "Material Parameters")
	void SetGlobalVectorParameter(const FName& ParameterName, const FLinearColor& Value);

	UFUNCTION(BlueprintCallable, Category = "Material Parameters")
	void UpdateTimeParameters();

	/** Post Process Control */
	UFUNCTION(BlueprintCallable, Category = "Post Process")
	void ApplyVisualEffectSettings(const FVisualEffectSettings& Settings);

	UFUNCTION(BlueprintCallable, Category = "Post Process")
	void BlendVisualEffectSettings(const FVisualEffectSettings& FromSettings, const FVisualEffectSettings& ToSettings, float Alpha);

	/** Sector-Specific Effects */
	UFUNCTION(BlueprintCallable, Category = "Sector Effects")
	void ApplySectorVisualTheme(int32 SectorIndex);

	UFUNCTION(BlueprintCallable, Category = "Sector Effects")
	void SetSectorAmbientColor(const FLinearColor& Color, float Intensity);

	UFUNCTION(BlueprintCallable, Category = "Sector Effects")
	void SetSectorFogSettings(float Density, const FLinearColor& Color, float StartDistance);

	/** Weapon-Specific Effects */
	UFUNCTION(BlueprintCallable, Category = "Weapon Effects")
	void TriggerMuzzleFlashEffect(const FVector& Location, const FRotator& Rotation, const FString& WeaponType);

	UFUNCTION(BlueprintCallable, Category = "Weapon Effects")
	void TriggerImpactEffect(const FVector& Location, const FVector& Normal, const FString& SurfaceType);

	UFUNCTION(BlueprintCallable, Category = "Weapon Effects")
	void CreateBulletTrail(const FVector& StartLocation, const FVector& EndLocation, const FString& WeaponType);

	/** Performance Optimization */
	UFUNCTION(BlueprintCallable, Category = "Performance")
	void SetEffectQuality(int32 QualityLevel);

	UFUNCTION(BlueprintCallable, Category = "Performance")
	void EnableLODSystem(bool bEnable);

	UFUNCTION(BlueprintCallable, Category = "Performance")
	void UpdateEffectLOD(float DistanceToPlayer);

	/** Static Access */
	UFUNCTION(BlueprintCallable, Category = "Visual Effects", CallInEditor = true)
	static AVisualEffectsManager* GetVisualEffectsManager(const UObject* WorldContext);

protected:
	/** Internal Functions */
	void InitializeVisualStatePresets();
	void UpdateTransition(float DeltaTime);
	void ApplyPostProcessSettings(const FVisualEffectSettings& Settings);
	FVisualEffectSettings LerpVisualEffectSettings(const FVisualEffectSettings& A, const FVisualEffectSettings& B, float Alpha) const;

	/** Transition State */
	FVisualEffectSettings TransitionStartSettings;
	FVisualEffectSettings TransitionTargetSettings;
	float TransitionProgress = 0.0f;
	float TransitionDuration = 0.0f;
	EGameVisualState TargetVisualState = EGameVisualState::Normal;

	/** Effect Timers */
	float DamageEffectTimer = 0.0f;
	float DamageEffectDuration = 0.0f;
	float DamageEffectIntensity = 0.0f;

	float HealEffectTimer = 0.0f;
	float HealEffectDuration = 0.0f;

	/** Performance Settings */
	int32 CurrentEffectQuality = 2; // 0=Low, 1=Medium, 2=High, 3=Ultra
	bool bLODSystemEnabled = true;

private:
	/** Singleton instance */
	static AVisualEffectsManager* Instance;

	/** Material parameter names */
	static const FName PARAM_BULLET_TIME_INTENSITY;
	static const FName PARAM_DAMAGE_EFFECT;
	static const FName PARAM_SECTOR_TINT;
	static const FName PARAM_TIME_SCALE;
	static const FName PARAM_GAME_TIME;
};
