// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameModeBase.h"
#include "GameplayTagContainer.h"
#include "RoughRealityGameModeBase.generated.h"

class ARookie<PERSON>haracter;
class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>uilder;
class URoughSaveGame;

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnGameStateChanged, FGameplayTag, NewState);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPlayerDied, ARookieCharacter*, DeadPlayer);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnRunCompleted, bool, bSuccess);

/**
 * Core game mode for Rough Reality
 * Manages game flow, roguelike mechanics, and level progression
 */
UCLASS(BlueprintType, Blueprintable)
class ROUGHREALITY_API ARoughRealityGameModeBase : public AGameModeBase
{
	GENERATED_BODY()

public:
	ARoughRealityGameModeBase();

protected:
	virtual void BeginPlay() override;
	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

public:
	/** Current game state tag */
	UPROPERTY(BlueprintReadOnly, Category = "Game State")
	FGameplayTag CurrentGameState;

	/** Current run number */
	UPROPERTY(BlueprintReadOnly, Category = "Roguelike")
	int32 CurrentRun = 0;

	/** Current sector index */
	UPROPERTY(BlueprintReadOnly, Category = "Roguelike")
	int32 CurrentSector = 0;

	/** Current level within sector */
	UPROPERTY(BlueprintReadOnly, Category = "Roguelike")
	int32 CurrentLevel = 0;

	/** Total teeth (currency) collected this run */
	UPROPERTY(BlueprintReadOnly, Category = "Roguelike")
	int32 TeethThisRun = 0;

	/** Reference to the procedural level builder */
	UPROPERTY(BlueprintReadOnly, Category = "Level Generation")
	AProceduralLevelBuilder* LevelBuilder;

	/** Reference to the save game */
	UPROPERTY(BlueprintReadOnly, Category = "Save System")
	URoughSaveGame* SaveGame;

public:
	/** Events */
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnGameStateChanged OnGameStateChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnPlayerDied OnPlayerDied;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnRunCompleted OnRunCompleted;

public:
	/** Game state management */
	UFUNCTION(BlueprintCallable, Category = "Game State")
	void SetGameState(const FGameplayTag& NewState);

	UFUNCTION(BlueprintCallable, Category = "Game State")
	bool IsInGameState(const FGameplayTag& State) const;

	/** Roguelike progression */
	UFUNCTION(BlueprintCallable, Category = "Roguelike")
	void StartNewRun();

	UFUNCTION(BlueprintCallable, Category = "Roguelike")
	void EndCurrentRun(bool bSuccess = false);

	UFUNCTION(BlueprintCallable, Category = "Roguelike")
	void AdvanceToNextLevel();

	UFUNCTION(BlueprintCallable, Category = "Roguelike")
	void AdvanceToNextSector();

	/** Player management */
	UFUNCTION(BlueprintCallable, Category = "Player")
	void HandlePlayerDeath(ARookieCharacter* DeadPlayer);

	UFUNCTION(BlueprintCallable, Category = "Player")
	void RespawnPlayer();

	/** Currency management */
	UFUNCTION(BlueprintCallable, Category = "Currency")
	void AddTeeth(int32 Amount);

	UFUNCTION(BlueprintCallable, Category = "Currency")
	bool SpendTeeth(int32 Amount);

	UFUNCTION(BlueprintCallable, Category = "Currency")
	int32 GetTotalTeeth() const;

	/** Save/Load system */
	UFUNCTION(BlueprintCallable, Category = "Save System")
	void SaveGame();

	UFUNCTION(BlueprintCallable, Category = "Save System")
	void LoadGame();

	UFUNCTION(BlueprintCallable, Category = "Save System")
	void ResetSaveData();

protected:
	/** Initialize the game mode */
	virtual void InitializeGameMode();

	/** Setup the level builder */
	virtual void SetupLevelBuilder();

	/** Load or create save game */
	virtual void InitializeSaveGame();

	/** Game state tags */
	UPROPERTY(EditDefaultsOnly, Category = "Game State Tags")
	FGameplayTag MenuState;

	UPROPERTY(EditDefaultsOnly, Category = "Game State Tags")
	FGameplayTag PlayingState;

	UPROPERTY(EditDefaultsOnly, Category = "Game State Tags")
	FGameplayTag PausedState;

	UPROPERTY(EditDefaultsOnly, Category = "Game State Tags")
	FGameplayTag GameOverState;

	UPROPERTY(EditDefaultsOnly, Category = "Game State Tags")
	FGameplayTag VictoryState;
};
