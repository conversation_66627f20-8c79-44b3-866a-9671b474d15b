// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RoughReality/Analytics/GameAnalyticsManager.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeGameAnalyticsManager() {}

// Begin Cross Module References
COREUOBJECT_API UClass* Z_Construct_UClass_UObject_NoRegister();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ROUGHREALITY_API UClass* Z_Construct_UClass_AGameAnalyticsManager();
ROUGHREALITY_API UClass* Z_Construct_UClass_AGameAnalyticsManager_NoRegister();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnAnalyticsEvent__DelegateSignature();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FHeatmapData();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FPerformanceMetrics();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FPlayerActionEvent();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FWeaponAnalytics();
UPackage* Z_Construct_UPackage__Script_RoughReality();
// End Cross Module References

// Begin ScriptStruct FPlayerActionEvent
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_PlayerActionEvent;
class UScriptStruct* FPlayerActionEvent::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_PlayerActionEvent.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_PlayerActionEvent.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPlayerActionEvent, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("PlayerActionEvent"));
	}
	return Z_Registration_Info_UScriptStruct_PlayerActionEvent.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FPlayerActionEvent>()
{
	return FPlayerActionEvent::StaticStruct();
}
struct Z_Construct_UScriptStruct_FPlayerActionEvent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventType_MetaData[] = {
		{ "Category", "Event" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventCategory_MetaData[] = {
		{ "Category", "Event" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "Category", "Event" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Timestamp_MetaData[] = {
		{ "Category", "Event" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "Category", "Event" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EventType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EventCategory;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Timestamp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Parameters;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPlayerActionEvent>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPlayerActionEvent_Statics::NewProp_EventType = { "EventType", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerActionEvent, EventType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventType_MetaData), NewProp_EventType_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPlayerActionEvent_Statics::NewProp_EventCategory = { "EventCategory", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerActionEvent, EventCategory), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventCategory_MetaData), NewProp_EventCategory_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPlayerActionEvent_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerActionEvent, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlayerActionEvent_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerActionEvent, Timestamp), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Timestamp_MetaData), NewProp_Timestamp_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPlayerActionEvent_Statics::NewProp_Parameters_ValueProp = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPlayerActionEvent_Statics::NewProp_Parameters_Key_KeyProp = { "Parameters_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FPlayerActionEvent_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerActionEvent, Parameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPlayerActionEvent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerActionEvent_Statics::NewProp_EventType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerActionEvent_Statics::NewProp_EventCategory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerActionEvent_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerActionEvent_Statics::NewProp_Timestamp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerActionEvent_Statics::NewProp_Parameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerActionEvent_Statics::NewProp_Parameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerActionEvent_Statics::NewProp_Parameters,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerActionEvent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPlayerActionEvent_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	&NewStructOps,
	"PlayerActionEvent",
	Z_Construct_UScriptStruct_FPlayerActionEvent_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerActionEvent_Statics::PropPointers),
	sizeof(FPlayerActionEvent),
	alignof(FPlayerActionEvent),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerActionEvent_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPlayerActionEvent_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPlayerActionEvent()
{
	if (!Z_Registration_Info_UScriptStruct_PlayerActionEvent.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_PlayerActionEvent.InnerSingleton, Z_Construct_UScriptStruct_FPlayerActionEvent_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_PlayerActionEvent.InnerSingleton;
}
// End ScriptStruct FPlayerActionEvent

// Begin ScriptStruct FHeatmapData
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_HeatmapData;
class UScriptStruct* FHeatmapData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_HeatmapData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_HeatmapData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FHeatmapData, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("HeatmapData"));
	}
	return Z_Registration_Info_UScriptStruct_HeatmapData.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FHeatmapData>()
{
	return FHeatmapData::StaticStruct();
}
struct Z_Construct_UScriptStruct_FHeatmapData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "Category", "Heatmap" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventType_MetaData[] = {
		{ "Category", "Heatmap" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Intensity_MetaData[] = {
		{ "Category", "Heatmap" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Timestamp_MetaData[] = {
		{ "Category", "Heatmap" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EventType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Timestamp;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FHeatmapData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FHeatmapData_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHeatmapData, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FHeatmapData_Statics::NewProp_EventType = { "EventType", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHeatmapData, EventType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventType_MetaData), NewProp_EventType_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FHeatmapData_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHeatmapData, Intensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Intensity_MetaData), NewProp_Intensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FHeatmapData_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FHeatmapData, Timestamp), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Timestamp_MetaData), NewProp_Timestamp_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FHeatmapData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHeatmapData_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHeatmapData_Statics::NewProp_EventType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHeatmapData_Statics::NewProp_Intensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FHeatmapData_Statics::NewProp_Timestamp,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHeatmapData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FHeatmapData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	&NewStructOps,
	"HeatmapData",
	Z_Construct_UScriptStruct_FHeatmapData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHeatmapData_Statics::PropPointers),
	sizeof(FHeatmapData),
	alignof(FHeatmapData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FHeatmapData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FHeatmapData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FHeatmapData()
{
	if (!Z_Registration_Info_UScriptStruct_HeatmapData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_HeatmapData.InnerSingleton, Z_Construct_UScriptStruct_FHeatmapData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_HeatmapData.InnerSingleton;
}
// End ScriptStruct FHeatmapData

// Begin ScriptStruct FWeaponAnalytics
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_WeaponAnalytics;
class UScriptStruct* FWeaponAnalytics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_WeaponAnalytics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_WeaponAnalytics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FWeaponAnalytics, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("WeaponAnalytics"));
	}
	return Z_Registration_Info_UScriptStruct_WeaponAnalytics.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FWeaponAnalytics>()
{
	return FWeaponAnalytics::StaticStruct();
}
struct Z_Construct_UScriptStruct_FWeaponAnalytics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponName_MetaData[] = {
		{ "Category", "Weapon Analytics" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShotsFired_MetaData[] = {
		{ "Category", "Weapon Analytics" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShotsHit_MetaData[] = {
		{ "Category", "Weapon Analytics" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Kills_MetaData[] = {
		{ "Category", "Weapon Analytics" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalDamageDealt_MetaData[] = {
		{ "Category", "Weapon Analytics" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeUsed_MetaData[] = {
		{ "Category", "Weapon Analytics" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReloadCount_MetaData[] = {
		{ "Category", "Weapon Analytics" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_WeaponName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ShotsFired;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ShotsHit;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Kills;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalDamageDealt;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeUsed;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReloadCount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FWeaponAnalytics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FWeaponAnalytics_Statics::NewProp_WeaponName = { "WeaponName", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponAnalytics, WeaponName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponName_MetaData), NewProp_WeaponName_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FWeaponAnalytics_Statics::NewProp_ShotsFired = { "ShotsFired", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponAnalytics, ShotsFired), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShotsFired_MetaData), NewProp_ShotsFired_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FWeaponAnalytics_Statics::NewProp_ShotsHit = { "ShotsHit", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponAnalytics, ShotsHit), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShotsHit_MetaData), NewProp_ShotsHit_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FWeaponAnalytics_Statics::NewProp_Kills = { "Kills", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponAnalytics, Kills), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Kills_MetaData), NewProp_Kills_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWeaponAnalytics_Statics::NewProp_TotalDamageDealt = { "TotalDamageDealt", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponAnalytics, TotalDamageDealt), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalDamageDealt_MetaData), NewProp_TotalDamageDealt_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWeaponAnalytics_Statics::NewProp_TimeUsed = { "TimeUsed", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponAnalytics, TimeUsed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeUsed_MetaData), NewProp_TimeUsed_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FWeaponAnalytics_Statics::NewProp_ReloadCount = { "ReloadCount", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponAnalytics, ReloadCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReloadCount_MetaData), NewProp_ReloadCount_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FWeaponAnalytics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponAnalytics_Statics::NewProp_WeaponName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponAnalytics_Statics::NewProp_ShotsFired,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponAnalytics_Statics::NewProp_ShotsHit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponAnalytics_Statics::NewProp_Kills,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponAnalytics_Statics::NewProp_TotalDamageDealt,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponAnalytics_Statics::NewProp_TimeUsed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponAnalytics_Statics::NewProp_ReloadCount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWeaponAnalytics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FWeaponAnalytics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	&NewStructOps,
	"WeaponAnalytics",
	Z_Construct_UScriptStruct_FWeaponAnalytics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWeaponAnalytics_Statics::PropPointers),
	sizeof(FWeaponAnalytics),
	alignof(FWeaponAnalytics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWeaponAnalytics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FWeaponAnalytics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FWeaponAnalytics()
{
	if (!Z_Registration_Info_UScriptStruct_WeaponAnalytics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_WeaponAnalytics.InnerSingleton, Z_Construct_UScriptStruct_FWeaponAnalytics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_WeaponAnalytics.InnerSingleton;
}
// End ScriptStruct FWeaponAnalytics

// Begin ScriptStruct FPerformanceMetrics
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_PerformanceMetrics;
class UScriptStruct* FPerformanceMetrics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_PerformanceMetrics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_PerformanceMetrics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPerformanceMetrics, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("PerformanceMetrics"));
	}
	return Z_Registration_Info_UScriptStruct_PerformanceMetrics.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FPerformanceMetrics>()
{
	return FPerformanceMetrics::StaticStruct();
}
struct Z_Construct_UScriptStruct_FPerformanceMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageFrameRate_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinFrameRate_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxFrameRate_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AverageFrameTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MemoryUsageMB_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DrawCalls_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadingTime_MetaData[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageFrameRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinFrameRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxFrameRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AverageFrameTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MemoryUsageMB;
	static const UECodeGen_Private::FIntPropertyParams NewProp_DrawCalls;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LoadingTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPerformanceMetrics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_AverageFrameRate = { "AverageFrameRate", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPerformanceMetrics, AverageFrameRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageFrameRate_MetaData), NewProp_AverageFrameRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_MinFrameRate = { "MinFrameRate", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPerformanceMetrics, MinFrameRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinFrameRate_MetaData), NewProp_MinFrameRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_MaxFrameRate = { "MaxFrameRate", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPerformanceMetrics, MaxFrameRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxFrameRate_MetaData), NewProp_MaxFrameRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_AverageFrameTime = { "AverageFrameTime", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPerformanceMetrics, AverageFrameTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AverageFrameTime_MetaData), NewProp_AverageFrameTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_MemoryUsageMB = { "MemoryUsageMB", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPerformanceMetrics, MemoryUsageMB), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MemoryUsageMB_MetaData), NewProp_MemoryUsageMB_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_DrawCalls = { "DrawCalls", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPerformanceMetrics, DrawCalls), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DrawCalls_MetaData), NewProp_DrawCalls_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_LoadingTime = { "LoadingTime", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPerformanceMetrics, LoadingTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadingTime_MetaData), NewProp_LoadingTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_AverageFrameRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_MinFrameRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_MaxFrameRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_AverageFrameTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_MemoryUsageMB,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_DrawCalls,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewProp_LoadingTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	&NewStructOps,
	"PerformanceMetrics",
	Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::PropPointers),
	sizeof(FPerformanceMetrics),
	alignof(FPerformanceMetrics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPerformanceMetrics()
{
	if (!Z_Registration_Info_UScriptStruct_PerformanceMetrics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_PerformanceMetrics.InnerSingleton, Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_PerformanceMetrics.InnerSingleton;
}
// End ScriptStruct FPerformanceMetrics

// Begin Delegate FOnAnalyticsEvent
struct Z_Construct_UDelegateFunction_RoughReality_OnAnalyticsEvent__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnAnalyticsEvent_Parms
	{
		FPlayerActionEvent Event;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Event_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Event;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnAnalyticsEvent__DelegateSignature_Statics::NewProp_Event = { "Event", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnAnalyticsEvent_Parms, Event), Z_Construct_UScriptStruct_FPlayerActionEvent, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Event_MetaData), NewProp_Event_MetaData) }; // 1779189699
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnAnalyticsEvent__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnAnalyticsEvent__DelegateSignature_Statics::NewProp_Event,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnAnalyticsEvent__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnAnalyticsEvent__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnAnalyticsEvent__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnAnalyticsEvent__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnAnalyticsEvent__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnAnalyticsEvent__DelegateSignature_Statics::_Script_RoughReality_eventOnAnalyticsEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnAnalyticsEvent__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnAnalyticsEvent__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnAnalyticsEvent__DelegateSignature_Statics::_Script_RoughReality_eventOnAnalyticsEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnAnalyticsEvent__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnAnalyticsEvent__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnAnalyticsEvent_DelegateWrapper(const FMulticastScriptDelegate& OnAnalyticsEvent, FPlayerActionEvent const& Event)
{
	struct _Script_RoughReality_eventOnAnalyticsEvent_Parms
	{
		FPlayerActionEvent Event;
	};
	_Script_RoughReality_eventOnAnalyticsEvent_Parms Parms;
	Parms.Event=Event;
	OnAnalyticsEvent.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnAnalyticsEvent

// Begin Class AGameAnalyticsManager Function ClearHeatmapData
struct Z_Construct_UFunction_AGameAnalyticsManager_ClearHeatmapData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Heatmap" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameAnalyticsManager_ClearHeatmapData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameAnalyticsManager, nullptr, "ClearHeatmapData", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_ClearHeatmapData_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameAnalyticsManager_ClearHeatmapData_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AGameAnalyticsManager_ClearHeatmapData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameAnalyticsManager_ClearHeatmapData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameAnalyticsManager::execClearHeatmapData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearHeatmapData();
	P_NATIVE_END;
}
// End Class AGameAnalyticsManager Function ClearHeatmapData

// Begin Class AGameAnalyticsManager Function EndCurrentSession
struct Z_Construct_UFunction_AGameAnalyticsManager_EndCurrentSession_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Session" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameAnalyticsManager_EndCurrentSession_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameAnalyticsManager, nullptr, "EndCurrentSession", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_EndCurrentSession_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameAnalyticsManager_EndCurrentSession_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AGameAnalyticsManager_EndCurrentSession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameAnalyticsManager_EndCurrentSession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameAnalyticsManager::execEndCurrentSession)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EndCurrentSession();
	P_NATIVE_END;
}
// End Class AGameAnalyticsManager Function EndCurrentSession

// Begin Class AGameAnalyticsManager Function ExportHeatmapToCSV
struct Z_Construct_UFunction_AGameAnalyticsManager_ExportHeatmapToCSV_Statics
{
	struct GameAnalyticsManager_eventExportHeatmapToCSV_Parms
	{
		FString Filename;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Management" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Filename_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Filename;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_ExportHeatmapToCSV_Statics::NewProp_Filename = { "Filename", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventExportHeatmapToCSV_Parms, Filename), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Filename_MetaData), NewProp_Filename_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameAnalyticsManager_ExportHeatmapToCSV_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_ExportHeatmapToCSV_Statics::NewProp_Filename,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_ExportHeatmapToCSV_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameAnalyticsManager_ExportHeatmapToCSV_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameAnalyticsManager, nullptr, "ExportHeatmapToCSV", nullptr, nullptr, Z_Construct_UFunction_AGameAnalyticsManager_ExportHeatmapToCSV_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_ExportHeatmapToCSV_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameAnalyticsManager_ExportHeatmapToCSV_Statics::GameAnalyticsManager_eventExportHeatmapToCSV_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_ExportHeatmapToCSV_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameAnalyticsManager_ExportHeatmapToCSV_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameAnalyticsManager_ExportHeatmapToCSV_Statics::GameAnalyticsManager_eventExportHeatmapToCSV_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameAnalyticsManager_ExportHeatmapToCSV()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameAnalyticsManager_ExportHeatmapToCSV_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameAnalyticsManager::execExportHeatmapToCSV)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Filename);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExportHeatmapToCSV(Z_Param_Filename);
	P_NATIVE_END;
}
// End Class AGameAnalyticsManager Function ExportHeatmapToCSV

// Begin Class AGameAnalyticsManager Function FlushAnalyticsData
struct Z_Construct_UFunction_AGameAnalyticsManager_FlushAnalyticsData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Data Management */" },
#endif
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data Management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameAnalyticsManager_FlushAnalyticsData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameAnalyticsManager, nullptr, "FlushAnalyticsData", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_FlushAnalyticsData_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameAnalyticsManager_FlushAnalyticsData_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AGameAnalyticsManager_FlushAnalyticsData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameAnalyticsManager_FlushAnalyticsData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameAnalyticsManager::execFlushAnalyticsData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->FlushAnalyticsData();
	P_NATIVE_END;
}
// End Class AGameAnalyticsManager Function FlushAnalyticsData

// Begin Class AGameAnalyticsManager Function GetAllWeaponAnalytics
struct Z_Construct_UFunction_AGameAnalyticsManager_GetAllWeaponAnalytics_Statics
{
	struct GameAnalyticsManager_eventGetAllWeaponAnalytics_Parms
	{
		TArray<FWeaponAnalytics> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Analytics" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_GetAllWeaponAnalytics_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FWeaponAnalytics, METADATA_PARAMS(0, nullptr) }; // 3529810086
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_GetAllWeaponAnalytics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventGetAllWeaponAnalytics_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3529810086
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameAnalyticsManager_GetAllWeaponAnalytics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_GetAllWeaponAnalytics_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_GetAllWeaponAnalytics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_GetAllWeaponAnalytics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameAnalyticsManager_GetAllWeaponAnalytics_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameAnalyticsManager, nullptr, "GetAllWeaponAnalytics", nullptr, nullptr, Z_Construct_UFunction_AGameAnalyticsManager_GetAllWeaponAnalytics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_GetAllWeaponAnalytics_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameAnalyticsManager_GetAllWeaponAnalytics_Statics::GameAnalyticsManager_eventGetAllWeaponAnalytics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_GetAllWeaponAnalytics_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameAnalyticsManager_GetAllWeaponAnalytics_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameAnalyticsManager_GetAllWeaponAnalytics_Statics::GameAnalyticsManager_eventGetAllWeaponAnalytics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameAnalyticsManager_GetAllWeaponAnalytics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameAnalyticsManager_GetAllWeaponAnalytics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameAnalyticsManager::execGetAllWeaponAnalytics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FWeaponAnalytics>*)Z_Param__Result=P_THIS->GetAllWeaponAnalytics();
	P_NATIVE_END;
}
// End Class AGameAnalyticsManager Function GetAllWeaponAnalytics

// Begin Class AGameAnalyticsManager Function GetAnalyticsManager
struct Z_Construct_UFunction_AGameAnalyticsManager_GetAnalyticsManager_Statics
{
	struct GameAnalyticsManager_eventGetAnalyticsManager_Parms
	{
		const UObject* WorldContext;
		AGameAnalyticsManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Static Access */" },
#endif
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Static Access" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldContext_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldContext;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_GetAnalyticsManager_Statics::NewProp_WorldContext = { "WorldContext", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventGetAnalyticsManager_Parms, WorldContext), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldContext_MetaData), NewProp_WorldContext_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_GetAnalyticsManager_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventGetAnalyticsManager_Parms, ReturnValue), Z_Construct_UClass_AGameAnalyticsManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameAnalyticsManager_GetAnalyticsManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_GetAnalyticsManager_Statics::NewProp_WorldContext,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_GetAnalyticsManager_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_GetAnalyticsManager_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameAnalyticsManager_GetAnalyticsManager_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameAnalyticsManager, nullptr, "GetAnalyticsManager", nullptr, nullptr, Z_Construct_UFunction_AGameAnalyticsManager_GetAnalyticsManager_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_GetAnalyticsManager_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameAnalyticsManager_GetAnalyticsManager_Statics::GameAnalyticsManager_eventGetAnalyticsManager_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_GetAnalyticsManager_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameAnalyticsManager_GetAnalyticsManager_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameAnalyticsManager_GetAnalyticsManager_Statics::GameAnalyticsManager_eventGetAnalyticsManager_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameAnalyticsManager_GetAnalyticsManager()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameAnalyticsManager_GetAnalyticsManager_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameAnalyticsManager::execGetAnalyticsManager)
{
	P_GET_OBJECT(UObject,Z_Param_WorldContext);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(AGameAnalyticsManager**)Z_Param__Result=AGameAnalyticsManager::GetAnalyticsManager(Z_Param_WorldContext);
	P_NATIVE_END;
}
// End Class AGameAnalyticsManager Function GetAnalyticsManager

// Begin Class AGameAnalyticsManager Function GetCurrentPerformanceMetrics
struct Z_Construct_UFunction_AGameAnalyticsManager_GetCurrentPerformanceMetrics_Statics
{
	struct GameAnalyticsManager_eventGetCurrentPerformanceMetrics_Parms
	{
		FPerformanceMetrics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_GetCurrentPerformanceMetrics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventGetCurrentPerformanceMetrics_Parms, ReturnValue), Z_Construct_UScriptStruct_FPerformanceMetrics, METADATA_PARAMS(0, nullptr) }; // 3286400143
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameAnalyticsManager_GetCurrentPerformanceMetrics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_GetCurrentPerformanceMetrics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_GetCurrentPerformanceMetrics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameAnalyticsManager_GetCurrentPerformanceMetrics_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameAnalyticsManager, nullptr, "GetCurrentPerformanceMetrics", nullptr, nullptr, Z_Construct_UFunction_AGameAnalyticsManager_GetCurrentPerformanceMetrics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_GetCurrentPerformanceMetrics_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameAnalyticsManager_GetCurrentPerformanceMetrics_Statics::GameAnalyticsManager_eventGetCurrentPerformanceMetrics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_GetCurrentPerformanceMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameAnalyticsManager_GetCurrentPerformanceMetrics_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameAnalyticsManager_GetCurrentPerformanceMetrics_Statics::GameAnalyticsManager_eventGetCurrentPerformanceMetrics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameAnalyticsManager_GetCurrentPerformanceMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameAnalyticsManager_GetCurrentPerformanceMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameAnalyticsManager::execGetCurrentPerformanceMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPerformanceMetrics*)Z_Param__Result=P_THIS->GetCurrentPerformanceMetrics();
	P_NATIVE_END;
}
// End Class AGameAnalyticsManager Function GetCurrentPerformanceMetrics

// Begin Class AGameAnalyticsManager Function GetHeatmapData
struct Z_Construct_UFunction_AGameAnalyticsManager_GetHeatmapData_Statics
{
	struct GameAnalyticsManager_eventGetHeatmapData_Parms
	{
		FString EventType;
		TArray<FHeatmapData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Heatmap" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EventType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_GetHeatmapData_Statics::NewProp_EventType = { "EventType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventGetHeatmapData_Parms, EventType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventType_MetaData), NewProp_EventType_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_GetHeatmapData_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FHeatmapData, METADATA_PARAMS(0, nullptr) }; // 348473466
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_GetHeatmapData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventGetHeatmapData_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 348473466
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameAnalyticsManager_GetHeatmapData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_GetHeatmapData_Statics::NewProp_EventType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_GetHeatmapData_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_GetHeatmapData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_GetHeatmapData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameAnalyticsManager_GetHeatmapData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameAnalyticsManager, nullptr, "GetHeatmapData", nullptr, nullptr, Z_Construct_UFunction_AGameAnalyticsManager_GetHeatmapData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_GetHeatmapData_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameAnalyticsManager_GetHeatmapData_Statics::GameAnalyticsManager_eventGetHeatmapData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_GetHeatmapData_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameAnalyticsManager_GetHeatmapData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameAnalyticsManager_GetHeatmapData_Statics::GameAnalyticsManager_eventGetHeatmapData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameAnalyticsManager_GetHeatmapData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameAnalyticsManager_GetHeatmapData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameAnalyticsManager::execGetHeatmapData)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_EventType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FHeatmapData>*)Z_Param__Result=P_THIS->GetHeatmapData(Z_Param_EventType);
	P_NATIVE_END;
}
// End Class AGameAnalyticsManager Function GetHeatmapData

// Begin Class AGameAnalyticsManager Function GetSessionDuration
struct Z_Construct_UFunction_AGameAnalyticsManager_GetSessionDuration_Statics
{
	struct GameAnalyticsManager_eventGetSessionDuration_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Session" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_GetSessionDuration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventGetSessionDuration_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameAnalyticsManager_GetSessionDuration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_GetSessionDuration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_GetSessionDuration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameAnalyticsManager_GetSessionDuration_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameAnalyticsManager, nullptr, "GetSessionDuration", nullptr, nullptr, Z_Construct_UFunction_AGameAnalyticsManager_GetSessionDuration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_GetSessionDuration_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameAnalyticsManager_GetSessionDuration_Statics::GameAnalyticsManager_eventGetSessionDuration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_GetSessionDuration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameAnalyticsManager_GetSessionDuration_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameAnalyticsManager_GetSessionDuration_Statics::GameAnalyticsManager_eventGetSessionDuration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameAnalyticsManager_GetSessionDuration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameAnalyticsManager_GetSessionDuration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameAnalyticsManager::execGetSessionDuration)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetSessionDuration();
	P_NATIVE_END;
}
// End Class AGameAnalyticsManager Function GetSessionDuration

// Begin Class AGameAnalyticsManager Function GetWeaponAnalytics
struct Z_Construct_UFunction_AGameAnalyticsManager_GetWeaponAnalytics_Statics
{
	struct GameAnalyticsManager_eventGetWeaponAnalytics_Parms
	{
		FString WeaponName;
		FWeaponAnalytics ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Analytics" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_WeaponName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_GetWeaponAnalytics_Statics::NewProp_WeaponName = { "WeaponName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventGetWeaponAnalytics_Parms, WeaponName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponName_MetaData), NewProp_WeaponName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_GetWeaponAnalytics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventGetWeaponAnalytics_Parms, ReturnValue), Z_Construct_UScriptStruct_FWeaponAnalytics, METADATA_PARAMS(0, nullptr) }; // 3529810086
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameAnalyticsManager_GetWeaponAnalytics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_GetWeaponAnalytics_Statics::NewProp_WeaponName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_GetWeaponAnalytics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_GetWeaponAnalytics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameAnalyticsManager_GetWeaponAnalytics_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameAnalyticsManager, nullptr, "GetWeaponAnalytics", nullptr, nullptr, Z_Construct_UFunction_AGameAnalyticsManager_GetWeaponAnalytics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_GetWeaponAnalytics_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameAnalyticsManager_GetWeaponAnalytics_Statics::GameAnalyticsManager_eventGetWeaponAnalytics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_GetWeaponAnalytics_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameAnalyticsManager_GetWeaponAnalytics_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameAnalyticsManager_GetWeaponAnalytics_Statics::GameAnalyticsManager_eventGetWeaponAnalytics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameAnalyticsManager_GetWeaponAnalytics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameAnalyticsManager_GetWeaponAnalytics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameAnalyticsManager::execGetWeaponAnalytics)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_WeaponName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FWeaponAnalytics*)Z_Param__Result=P_THIS->GetWeaponAnalytics(Z_Param_WeaponName);
	P_NATIVE_END;
}
// End Class AGameAnalyticsManager Function GetWeaponAnalytics

// Begin Class AGameAnalyticsManager Function LoadAnalyticsFromFile
struct Z_Construct_UFunction_AGameAnalyticsManager_LoadAnalyticsFromFile_Statics
{
	struct GameAnalyticsManager_eventLoadAnalyticsFromFile_Parms
	{
		FString Filename;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Management" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Filename_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Filename;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_LoadAnalyticsFromFile_Statics::NewProp_Filename = { "Filename", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventLoadAnalyticsFromFile_Parms, Filename), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Filename_MetaData), NewProp_Filename_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameAnalyticsManager_LoadAnalyticsFromFile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_LoadAnalyticsFromFile_Statics::NewProp_Filename,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LoadAnalyticsFromFile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameAnalyticsManager_LoadAnalyticsFromFile_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameAnalyticsManager, nullptr, "LoadAnalyticsFromFile", nullptr, nullptr, Z_Construct_UFunction_AGameAnalyticsManager_LoadAnalyticsFromFile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LoadAnalyticsFromFile_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameAnalyticsManager_LoadAnalyticsFromFile_Statics::GameAnalyticsManager_eventLoadAnalyticsFromFile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LoadAnalyticsFromFile_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameAnalyticsManager_LoadAnalyticsFromFile_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameAnalyticsManager_LoadAnalyticsFromFile_Statics::GameAnalyticsManager_eventLoadAnalyticsFromFile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameAnalyticsManager_LoadAnalyticsFromFile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameAnalyticsManager_LoadAnalyticsFromFile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameAnalyticsManager::execLoadAnalyticsFromFile)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Filename);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LoadAnalyticsFromFile(Z_Param_Filename);
	P_NATIVE_END;
}
// End Class AGameAnalyticsManager Function LoadAnalyticsFromFile

// Begin Class AGameAnalyticsManager Function LogBulletTimeUsage
struct Z_Construct_UFunction_AGameAnalyticsManager_LogBulletTimeUsage_Statics
{
	struct GameAnalyticsManager_eventLogBulletTimeUsage_Parms
	{
		FVector Location;
		float Duration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Analytics" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_LogBulletTimeUsage_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventLogBulletTimeUsage_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_LogBulletTimeUsage_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventLogBulletTimeUsage_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameAnalyticsManager_LogBulletTimeUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_LogBulletTimeUsage_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_LogBulletTimeUsage_Statics::NewProp_Duration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LogBulletTimeUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameAnalyticsManager_LogBulletTimeUsage_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameAnalyticsManager, nullptr, "LogBulletTimeUsage", nullptr, nullptr, Z_Construct_UFunction_AGameAnalyticsManager_LogBulletTimeUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LogBulletTimeUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameAnalyticsManager_LogBulletTimeUsage_Statics::GameAnalyticsManager_eventLogBulletTimeUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LogBulletTimeUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameAnalyticsManager_LogBulletTimeUsage_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameAnalyticsManager_LogBulletTimeUsage_Statics::GameAnalyticsManager_eventLogBulletTimeUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameAnalyticsManager_LogBulletTimeUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameAnalyticsManager_LogBulletTimeUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameAnalyticsManager::execLogBulletTimeUsage)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogBulletTimeUsage(Z_Param_Out_Location,Z_Param_Duration);
	P_NATIVE_END;
}
// End Class AGameAnalyticsManager Function LogBulletTimeUsage

// Begin Class AGameAnalyticsManager Function LogEnemyKill
struct Z_Construct_UFunction_AGameAnalyticsManager_LogEnemyKill_Statics
{
	struct GameAnalyticsManager_eventLogEnemyKill_Parms
	{
		FString EnemyType;
		FString WeaponUsed;
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Analytics" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnemyType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponUsed_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EnemyType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_WeaponUsed;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_LogEnemyKill_Statics::NewProp_EnemyType = { "EnemyType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventLogEnemyKill_Parms, EnemyType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnemyType_MetaData), NewProp_EnemyType_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_LogEnemyKill_Statics::NewProp_WeaponUsed = { "WeaponUsed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventLogEnemyKill_Parms, WeaponUsed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponUsed_MetaData), NewProp_WeaponUsed_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_LogEnemyKill_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventLogEnemyKill_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameAnalyticsManager_LogEnemyKill_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_LogEnemyKill_Statics::NewProp_EnemyType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_LogEnemyKill_Statics::NewProp_WeaponUsed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_LogEnemyKill_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LogEnemyKill_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameAnalyticsManager_LogEnemyKill_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameAnalyticsManager, nullptr, "LogEnemyKill", nullptr, nullptr, Z_Construct_UFunction_AGameAnalyticsManager_LogEnemyKill_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LogEnemyKill_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameAnalyticsManager_LogEnemyKill_Statics::GameAnalyticsManager_eventLogEnemyKill_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LogEnemyKill_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameAnalyticsManager_LogEnemyKill_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameAnalyticsManager_LogEnemyKill_Statics::GameAnalyticsManager_eventLogEnemyKill_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameAnalyticsManager_LogEnemyKill()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameAnalyticsManager_LogEnemyKill_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameAnalyticsManager::execLogEnemyKill)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_EnemyType);
	P_GET_PROPERTY(FStrProperty,Z_Param_WeaponUsed);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogEnemyKill(Z_Param_EnemyType,Z_Param_WeaponUsed,Z_Param_Out_Location);
	P_NATIVE_END;
}
// End Class AGameAnalyticsManager Function LogEnemyKill

// Begin Class AGameAnalyticsManager Function LogLevelComplete
struct Z_Construct_UFunction_AGameAnalyticsManager_LogLevelComplete_Statics
{
	struct GameAnalyticsManager_eventLogLevelComplete_Parms
	{
		int32 SectorIndex;
		int32 LevelIndex;
		float CompletionTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Analytics" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SectorIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LevelIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CompletionTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_LogLevelComplete_Statics::NewProp_SectorIndex = { "SectorIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventLogLevelComplete_Parms, SectorIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_LogLevelComplete_Statics::NewProp_LevelIndex = { "LevelIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventLogLevelComplete_Parms, LevelIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_LogLevelComplete_Statics::NewProp_CompletionTime = { "CompletionTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventLogLevelComplete_Parms, CompletionTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameAnalyticsManager_LogLevelComplete_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_LogLevelComplete_Statics::NewProp_SectorIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_LogLevelComplete_Statics::NewProp_LevelIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_LogLevelComplete_Statics::NewProp_CompletionTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LogLevelComplete_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameAnalyticsManager_LogLevelComplete_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameAnalyticsManager, nullptr, "LogLevelComplete", nullptr, nullptr, Z_Construct_UFunction_AGameAnalyticsManager_LogLevelComplete_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LogLevelComplete_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameAnalyticsManager_LogLevelComplete_Statics::GameAnalyticsManager_eventLogLevelComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LogLevelComplete_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameAnalyticsManager_LogLevelComplete_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameAnalyticsManager_LogLevelComplete_Statics::GameAnalyticsManager_eventLogLevelComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameAnalyticsManager_LogLevelComplete()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameAnalyticsManager_LogLevelComplete_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameAnalyticsManager::execLogLevelComplete)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SectorIndex);
	P_GET_PROPERTY(FIntProperty,Z_Param_LevelIndex);
	P_GET_PROPERTY(FFloatProperty,Z_Param_CompletionTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogLevelComplete(Z_Param_SectorIndex,Z_Param_LevelIndex,Z_Param_CompletionTime);
	P_NATIVE_END;
}
// End Class AGameAnalyticsManager Function LogLevelComplete

// Begin Class AGameAnalyticsManager Function LogPlayerAction
struct Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerAction_Statics
{
	struct GameAnalyticsManager_eventLogPlayerAction_Parms
	{
		FString ActionType;
		FString Category;
		FVector Location;
		TMap<FString,FString> Parameters;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Event Logging */" },
#endif
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Event Logging" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActionType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Category;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Parameters;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerAction_Statics::NewProp_ActionType = { "ActionType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventLogPlayerAction_Parms, ActionType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionType_MetaData), NewProp_ActionType_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerAction_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventLogPlayerAction_Parms, Category), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerAction_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventLogPlayerAction_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerAction_Statics::NewProp_Parameters_ValueProp = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerAction_Statics::NewProp_Parameters_Key_KeyProp = { "Parameters_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerAction_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventLogPlayerAction_Parms, Parameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerAction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerAction_Statics::NewProp_ActionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerAction_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerAction_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerAction_Statics::NewProp_Parameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerAction_Statics::NewProp_Parameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerAction_Statics::NewProp_Parameters,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerAction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerAction_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameAnalyticsManager, nullptr, "LogPlayerAction", nullptr, nullptr, Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerAction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerAction_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerAction_Statics::GameAnalyticsManager_eventLogPlayerAction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerAction_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerAction_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerAction_Statics::GameAnalyticsManager_eventLogPlayerAction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerAction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerAction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameAnalyticsManager::execLogPlayerAction)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ActionType);
	P_GET_PROPERTY(FStrProperty,Z_Param_Category);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_Parameters);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogPlayerAction(Z_Param_ActionType,Z_Param_Category,Z_Param_Out_Location,Z_Param_Out_Parameters);
	P_NATIVE_END;
}
// End Class AGameAnalyticsManager Function LogPlayerAction

// Begin Class AGameAnalyticsManager Function LogPlayerDeath
struct Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerDeath_Statics
{
	struct GameAnalyticsManager_eventLogPlayerDeath_Parms
	{
		FString CauseOfDeath;
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Analytics" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CauseOfDeath_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CauseOfDeath;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerDeath_Statics::NewProp_CauseOfDeath = { "CauseOfDeath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventLogPlayerDeath_Parms, CauseOfDeath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CauseOfDeath_MetaData), NewProp_CauseOfDeath_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerDeath_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventLogPlayerDeath_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerDeath_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerDeath_Statics::NewProp_CauseOfDeath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerDeath_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerDeath_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerDeath_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameAnalyticsManager, nullptr, "LogPlayerDeath", nullptr, nullptr, Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerDeath_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerDeath_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerDeath_Statics::GameAnalyticsManager_eventLogPlayerDeath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerDeath_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerDeath_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerDeath_Statics::GameAnalyticsManager_eventLogPlayerDeath_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerDeath()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerDeath_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameAnalyticsManager::execLogPlayerDeath)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_CauseOfDeath);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogPlayerDeath(Z_Param_CauseOfDeath,Z_Param_Out_Location);
	P_NATIVE_END;
}
// End Class AGameAnalyticsManager Function LogPlayerDeath

// Begin Class AGameAnalyticsManager Function LogTimeRewind
struct Z_Construct_UFunction_AGameAnalyticsManager_LogTimeRewind_Statics
{
	struct GameAnalyticsManager_eventLogTimeRewind_Parms
	{
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Analytics" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_LogTimeRewind_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventLogTimeRewind_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameAnalyticsManager_LogTimeRewind_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_LogTimeRewind_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LogTimeRewind_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameAnalyticsManager_LogTimeRewind_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameAnalyticsManager, nullptr, "LogTimeRewind", nullptr, nullptr, Z_Construct_UFunction_AGameAnalyticsManager_LogTimeRewind_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LogTimeRewind_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameAnalyticsManager_LogTimeRewind_Statics::GameAnalyticsManager_eventLogTimeRewind_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LogTimeRewind_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameAnalyticsManager_LogTimeRewind_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameAnalyticsManager_LogTimeRewind_Statics::GameAnalyticsManager_eventLogTimeRewind_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameAnalyticsManager_LogTimeRewind()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameAnalyticsManager_LogTimeRewind_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameAnalyticsManager::execLogTimeRewind)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogTimeRewind(Z_Param_Out_Location);
	P_NATIVE_END;
}
// End Class AGameAnalyticsManager Function LogTimeRewind

// Begin Class AGameAnalyticsManager Function LogWeaponFired
struct Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponFired_Statics
{
	struct GameAnalyticsManager_eventLogWeaponFired_Parms
	{
		FString WeaponName;
		FVector Location;
		bool bHit;
		float Damage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Analytics" },
		{ "CPP_Default_Damage", "0.000000" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_WeaponName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static void NewProp_bHit_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHit;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Damage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponFired_Statics::NewProp_WeaponName = { "WeaponName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventLogWeaponFired_Parms, WeaponName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponName_MetaData), NewProp_WeaponName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponFired_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventLogWeaponFired_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
void Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponFired_Statics::NewProp_bHit_SetBit(void* Obj)
{
	((GameAnalyticsManager_eventLogWeaponFired_Parms*)Obj)->bHit = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponFired_Statics::NewProp_bHit = { "bHit", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(GameAnalyticsManager_eventLogWeaponFired_Parms), &Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponFired_Statics::NewProp_bHit_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponFired_Statics::NewProp_Damage = { "Damage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventLogWeaponFired_Parms, Damage), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponFired_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponFired_Statics::NewProp_WeaponName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponFired_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponFired_Statics::NewProp_bHit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponFired_Statics::NewProp_Damage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponFired_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponFired_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameAnalyticsManager, nullptr, "LogWeaponFired", nullptr, nullptr, Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponFired_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponFired_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponFired_Statics::GameAnalyticsManager_eventLogWeaponFired_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponFired_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponFired_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponFired_Statics::GameAnalyticsManager_eventLogWeaponFired_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponFired()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponFired_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameAnalyticsManager::execLogWeaponFired)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_WeaponName);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_UBOOL(Z_Param_bHit);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Damage);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogWeaponFired(Z_Param_WeaponName,Z_Param_Out_Location,Z_Param_bHit,Z_Param_Damage);
	P_NATIVE_END;
}
// End Class AGameAnalyticsManager Function LogWeaponFired

// Begin Class AGameAnalyticsManager Function LogWeaponReload
struct Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponReload_Statics
{
	struct GameAnalyticsManager_eventLogWeaponReload_Parms
	{
		FString WeaponName;
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Analytics" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_WeaponName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponReload_Statics::NewProp_WeaponName = { "WeaponName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventLogWeaponReload_Parms, WeaponName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponName_MetaData), NewProp_WeaponName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponReload_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventLogWeaponReload_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponReload_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponReload_Statics::NewProp_WeaponName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponReload_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponReload_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponReload_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameAnalyticsManager, nullptr, "LogWeaponReload", nullptr, nullptr, Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponReload_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponReload_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponReload_Statics::GameAnalyticsManager_eventLogWeaponReload_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponReload_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponReload_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponReload_Statics::GameAnalyticsManager_eventLogWeaponReload_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponReload()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponReload_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameAnalyticsManager::execLogWeaponReload)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_WeaponName);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogWeaponReload(Z_Param_WeaponName,Z_Param_Out_Location);
	P_NATIVE_END;
}
// End Class AGameAnalyticsManager Function LogWeaponReload

// Begin Class AGameAnalyticsManager Function RecordHeatmapEvent
struct Z_Construct_UFunction_AGameAnalyticsManager_RecordHeatmapEvent_Statics
{
	struct GameAnalyticsManager_eventRecordHeatmapEvent_Parms
	{
		FString EventType;
		FVector Location;
		int32 Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Heatmap" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Heatmap Data */" },
#endif
		{ "CPP_Default_Intensity", "1" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Heatmap Data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventType_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EventType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_RecordHeatmapEvent_Statics::NewProp_EventType = { "EventType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventRecordHeatmapEvent_Parms, EventType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventType_MetaData), NewProp_EventType_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_RecordHeatmapEvent_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventRecordHeatmapEvent_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_RecordHeatmapEvent_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventRecordHeatmapEvent_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameAnalyticsManager_RecordHeatmapEvent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_RecordHeatmapEvent_Statics::NewProp_EventType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_RecordHeatmapEvent_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_RecordHeatmapEvent_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_RecordHeatmapEvent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameAnalyticsManager_RecordHeatmapEvent_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameAnalyticsManager, nullptr, "RecordHeatmapEvent", nullptr, nullptr, Z_Construct_UFunction_AGameAnalyticsManager_RecordHeatmapEvent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_RecordHeatmapEvent_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameAnalyticsManager_RecordHeatmapEvent_Statics::GameAnalyticsManager_eventRecordHeatmapEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_RecordHeatmapEvent_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameAnalyticsManager_RecordHeatmapEvent_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameAnalyticsManager_RecordHeatmapEvent_Statics::GameAnalyticsManager_eventRecordHeatmapEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameAnalyticsManager_RecordHeatmapEvent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameAnalyticsManager_RecordHeatmapEvent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameAnalyticsManager::execRecordHeatmapEvent)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_EventType);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FIntProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RecordHeatmapEvent(Z_Param_EventType,Z_Param_Out_Location,Z_Param_Intensity);
	P_NATIVE_END;
}
// End Class AGameAnalyticsManager Function RecordHeatmapEvent

// Begin Class AGameAnalyticsManager Function SaveAnalyticsToFile
struct Z_Construct_UFunction_AGameAnalyticsManager_SaveAnalyticsToFile_Statics
{
	struct GameAnalyticsManager_eventSaveAnalyticsToFile_Parms
	{
		FString Filename;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Data Management" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Filename_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Filename;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_SaveAnalyticsToFile_Statics::NewProp_Filename = { "Filename", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventSaveAnalyticsToFile_Parms, Filename), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Filename_MetaData), NewProp_Filename_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameAnalyticsManager_SaveAnalyticsToFile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_SaveAnalyticsToFile_Statics::NewProp_Filename,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_SaveAnalyticsToFile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameAnalyticsManager_SaveAnalyticsToFile_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameAnalyticsManager, nullptr, "SaveAnalyticsToFile", nullptr, nullptr, Z_Construct_UFunction_AGameAnalyticsManager_SaveAnalyticsToFile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_SaveAnalyticsToFile_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameAnalyticsManager_SaveAnalyticsToFile_Statics::GameAnalyticsManager_eventSaveAnalyticsToFile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_SaveAnalyticsToFile_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameAnalyticsManager_SaveAnalyticsToFile_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameAnalyticsManager_SaveAnalyticsToFile_Statics::GameAnalyticsManager_eventSaveAnalyticsToFile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameAnalyticsManager_SaveAnalyticsToFile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameAnalyticsManager_SaveAnalyticsToFile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameAnalyticsManager::execSaveAnalyticsToFile)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Filename);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SaveAnalyticsToFile(Z_Param_Filename);
	P_NATIVE_END;
}
// End Class AGameAnalyticsManager Function SaveAnalyticsToFile

// Begin Class AGameAnalyticsManager Function StartNewSession
struct Z_Construct_UFunction_AGameAnalyticsManager_StartNewSession_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Session" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Session Management */" },
#endif
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Session Management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameAnalyticsManager_StartNewSession_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameAnalyticsManager, nullptr, "StartNewSession", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_StartNewSession_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameAnalyticsManager_StartNewSession_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AGameAnalyticsManager_StartNewSession()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameAnalyticsManager_StartNewSession_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameAnalyticsManager::execStartNewSession)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartNewSession();
	P_NATIVE_END;
}
// End Class AGameAnalyticsManager Function StartNewSession

// Begin Class AGameAnalyticsManager Function UpdatePerformanceMetrics
struct Z_Construct_UFunction_AGameAnalyticsManager_UpdatePerformanceMetrics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Performance Tracking */" },
#endif
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance Tracking" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameAnalyticsManager_UpdatePerformanceMetrics_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameAnalyticsManager, nullptr, "UpdatePerformanceMetrics", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_UpdatePerformanceMetrics_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameAnalyticsManager_UpdatePerformanceMetrics_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AGameAnalyticsManager_UpdatePerformanceMetrics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameAnalyticsManager_UpdatePerformanceMetrics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameAnalyticsManager::execUpdatePerformanceMetrics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdatePerformanceMetrics();
	P_NATIVE_END;
}
// End Class AGameAnalyticsManager Function UpdatePerformanceMetrics

// Begin Class AGameAnalyticsManager Function UpdateWeaponUsageTime
struct Z_Construct_UFunction_AGameAnalyticsManager_UpdateWeaponUsageTime_Statics
{
	struct GameAnalyticsManager_eventUpdateWeaponUsageTime_Parms
	{
		FString WeaponName;
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Weapon Analytics */" },
#endif
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weapon Analytics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_WeaponName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_UpdateWeaponUsageTime_Statics::NewProp_WeaponName = { "WeaponName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventUpdateWeaponUsageTime_Parms, WeaponName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponName_MetaData), NewProp_WeaponName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGameAnalyticsManager_UpdateWeaponUsageTime_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameAnalyticsManager_eventUpdateWeaponUsageTime_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameAnalyticsManager_UpdateWeaponUsageTime_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_UpdateWeaponUsageTime_Statics::NewProp_WeaponName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameAnalyticsManager_UpdateWeaponUsageTime_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_UpdateWeaponUsageTime_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameAnalyticsManager_UpdateWeaponUsageTime_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameAnalyticsManager, nullptr, "UpdateWeaponUsageTime", nullptr, nullptr, Z_Construct_UFunction_AGameAnalyticsManager_UpdateWeaponUsageTime_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_UpdateWeaponUsageTime_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameAnalyticsManager_UpdateWeaponUsageTime_Statics::GameAnalyticsManager_eventUpdateWeaponUsageTime_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameAnalyticsManager_UpdateWeaponUsageTime_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameAnalyticsManager_UpdateWeaponUsageTime_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameAnalyticsManager_UpdateWeaponUsageTime_Statics::GameAnalyticsManager_eventUpdateWeaponUsageTime_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameAnalyticsManager_UpdateWeaponUsageTime()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameAnalyticsManager_UpdateWeaponUsageTime_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameAnalyticsManager::execUpdateWeaponUsageTime)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_WeaponName);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateWeaponUsageTime(Z_Param_WeaponName,Z_Param_DeltaTime);
	P_NATIVE_END;
}
// End Class AGameAnalyticsManager Function UpdateWeaponUsageTime

// Begin Class AGameAnalyticsManager
void AGameAnalyticsManager::StaticRegisterNativesAGameAnalyticsManager()
{
	UClass* Class = AGameAnalyticsManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ClearHeatmapData", &AGameAnalyticsManager::execClearHeatmapData },
		{ "EndCurrentSession", &AGameAnalyticsManager::execEndCurrentSession },
		{ "ExportHeatmapToCSV", &AGameAnalyticsManager::execExportHeatmapToCSV },
		{ "FlushAnalyticsData", &AGameAnalyticsManager::execFlushAnalyticsData },
		{ "GetAllWeaponAnalytics", &AGameAnalyticsManager::execGetAllWeaponAnalytics },
		{ "GetAnalyticsManager", &AGameAnalyticsManager::execGetAnalyticsManager },
		{ "GetCurrentPerformanceMetrics", &AGameAnalyticsManager::execGetCurrentPerformanceMetrics },
		{ "GetHeatmapData", &AGameAnalyticsManager::execGetHeatmapData },
		{ "GetSessionDuration", &AGameAnalyticsManager::execGetSessionDuration },
		{ "GetWeaponAnalytics", &AGameAnalyticsManager::execGetWeaponAnalytics },
		{ "LoadAnalyticsFromFile", &AGameAnalyticsManager::execLoadAnalyticsFromFile },
		{ "LogBulletTimeUsage", &AGameAnalyticsManager::execLogBulletTimeUsage },
		{ "LogEnemyKill", &AGameAnalyticsManager::execLogEnemyKill },
		{ "LogLevelComplete", &AGameAnalyticsManager::execLogLevelComplete },
		{ "LogPlayerAction", &AGameAnalyticsManager::execLogPlayerAction },
		{ "LogPlayerDeath", &AGameAnalyticsManager::execLogPlayerDeath },
		{ "LogTimeRewind", &AGameAnalyticsManager::execLogTimeRewind },
		{ "LogWeaponFired", &AGameAnalyticsManager::execLogWeaponFired },
		{ "LogWeaponReload", &AGameAnalyticsManager::execLogWeaponReload },
		{ "RecordHeatmapEvent", &AGameAnalyticsManager::execRecordHeatmapEvent },
		{ "SaveAnalyticsToFile", &AGameAnalyticsManager::execSaveAnalyticsToFile },
		{ "StartNewSession", &AGameAnalyticsManager::execStartNewSession },
		{ "UpdatePerformanceMetrics", &AGameAnalyticsManager::execUpdatePerformanceMetrics },
		{ "UpdateWeaponUsageTime", &AGameAnalyticsManager::execUpdateWeaponUsageTime },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(AGameAnalyticsManager);
UClass* Z_Construct_UClass_AGameAnalyticsManager_NoRegister()
{
	return AGameAnalyticsManager::StaticClass();
}
struct Z_Construct_UClass_AGameAnalyticsManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Game Analytics Manager for comprehensive data collection and analysis\n * Tracks player behavior, performance metrics, and gameplay patterns\n */" },
#endif
		{ "IncludePath", "Analytics/GameAnalyticsManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Game Analytics Manager for comprehensive data collection and analysis\nTracks player behavior, performance metrics, and gameplay patterns" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAnalytics_MetaData[] = {
		{ "Category", "Analytics Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Analytics Configuration */" },
#endif
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Analytics Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableHeatmaps_MetaData[] = {
		{ "Category", "Analytics Config" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePerformanceTracking_MetaData[] = {
		{ "Category", "Analytics Config" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventBatchSize_MetaData[] = {
		{ "Category", "Analytics Config" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FlushInterval_MetaData[] = {
		{ "Category", "Analytics Config" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnalyticsEndpoint_MetaData[] = {
		{ "Category", "Analytics Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Seconds\n" },
#endif
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Seconds" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAnalyticsEvent_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Events */" },
#endif
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionID_MetaData[] = {
		{ "Category", "Session Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current Session Data */" },
#endif
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current Session Data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SessionStartTime_MetaData[] = {
		{ "Category", "Session Data" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PendingEvents_MetaData[] = {
		{ "Category", "Session Data" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HeatmapData_MetaData[] = {
		{ "Category", "Session Data" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponAnalytics_MetaData[] = {
		{ "Category", "Session Data" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentPerformanceMetrics_MetaData[] = {
		{ "Category", "Session Data" },
		{ "ModuleRelativePath", "Analytics/GameAnalyticsManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnableAnalytics_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAnalytics;
	static void NewProp_bEnableHeatmaps_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableHeatmaps;
	static void NewProp_bEnablePerformanceTracking_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePerformanceTracking;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EventBatchSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FlushInterval;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AnalyticsEndpoint;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAnalyticsEvent;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SessionID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SessionStartTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PendingEvents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PendingEvents;
	static const UECodeGen_Private::FStructPropertyParams NewProp_HeatmapData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_HeatmapData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WeaponAnalytics_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_WeaponAnalytics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_WeaponAnalytics;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentPerformanceMetrics;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AGameAnalyticsManager_ClearHeatmapData, "ClearHeatmapData" }, // 154734488
		{ &Z_Construct_UFunction_AGameAnalyticsManager_EndCurrentSession, "EndCurrentSession" }, // 2629028748
		{ &Z_Construct_UFunction_AGameAnalyticsManager_ExportHeatmapToCSV, "ExportHeatmapToCSV" }, // 2284546543
		{ &Z_Construct_UFunction_AGameAnalyticsManager_FlushAnalyticsData, "FlushAnalyticsData" }, // 822253542
		{ &Z_Construct_UFunction_AGameAnalyticsManager_GetAllWeaponAnalytics, "GetAllWeaponAnalytics" }, // 3535650944
		{ &Z_Construct_UFunction_AGameAnalyticsManager_GetAnalyticsManager, "GetAnalyticsManager" }, // 677471318
		{ &Z_Construct_UFunction_AGameAnalyticsManager_GetCurrentPerformanceMetrics, "GetCurrentPerformanceMetrics" }, // 401558613
		{ &Z_Construct_UFunction_AGameAnalyticsManager_GetHeatmapData, "GetHeatmapData" }, // 4005782521
		{ &Z_Construct_UFunction_AGameAnalyticsManager_GetSessionDuration, "GetSessionDuration" }, // 506450609
		{ &Z_Construct_UFunction_AGameAnalyticsManager_GetWeaponAnalytics, "GetWeaponAnalytics" }, // 923144285
		{ &Z_Construct_UFunction_AGameAnalyticsManager_LoadAnalyticsFromFile, "LoadAnalyticsFromFile" }, // 2632598793
		{ &Z_Construct_UFunction_AGameAnalyticsManager_LogBulletTimeUsage, "LogBulletTimeUsage" }, // 2096734446
		{ &Z_Construct_UFunction_AGameAnalyticsManager_LogEnemyKill, "LogEnemyKill" }, // 2044069565
		{ &Z_Construct_UFunction_AGameAnalyticsManager_LogLevelComplete, "LogLevelComplete" }, // 3617193533
		{ &Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerAction, "LogPlayerAction" }, // 1469733182
		{ &Z_Construct_UFunction_AGameAnalyticsManager_LogPlayerDeath, "LogPlayerDeath" }, // 2302352875
		{ &Z_Construct_UFunction_AGameAnalyticsManager_LogTimeRewind, "LogTimeRewind" }, // 2482541335
		{ &Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponFired, "LogWeaponFired" }, // 594150260
		{ &Z_Construct_UFunction_AGameAnalyticsManager_LogWeaponReload, "LogWeaponReload" }, // 3930955640
		{ &Z_Construct_UFunction_AGameAnalyticsManager_RecordHeatmapEvent, "RecordHeatmapEvent" }, // 3600329256
		{ &Z_Construct_UFunction_AGameAnalyticsManager_SaveAnalyticsToFile, "SaveAnalyticsToFile" }, // 3491807352
		{ &Z_Construct_UFunction_AGameAnalyticsManager_StartNewSession, "StartNewSession" }, // 2147284526
		{ &Z_Construct_UFunction_AGameAnalyticsManager_UpdatePerformanceMetrics, "UpdatePerformanceMetrics" }, // 1345623180
		{ &Z_Construct_UFunction_AGameAnalyticsManager_UpdateWeaponUsageTime, "UpdateWeaponUsageTime" }, // 2547021197
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AGameAnalyticsManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
void Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_bEnableAnalytics_SetBit(void* Obj)
{
	((AGameAnalyticsManager*)Obj)->bEnableAnalytics = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_bEnableAnalytics = { "bEnableAnalytics", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AGameAnalyticsManager), &Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_bEnableAnalytics_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAnalytics_MetaData), NewProp_bEnableAnalytics_MetaData) };
void Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_bEnableHeatmaps_SetBit(void* Obj)
{
	((AGameAnalyticsManager*)Obj)->bEnableHeatmaps = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_bEnableHeatmaps = { "bEnableHeatmaps", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AGameAnalyticsManager), &Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_bEnableHeatmaps_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableHeatmaps_MetaData), NewProp_bEnableHeatmaps_MetaData) };
void Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_bEnablePerformanceTracking_SetBit(void* Obj)
{
	((AGameAnalyticsManager*)Obj)->bEnablePerformanceTracking = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_bEnablePerformanceTracking = { "bEnablePerformanceTracking", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AGameAnalyticsManager), &Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_bEnablePerformanceTracking_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePerformanceTracking_MetaData), NewProp_bEnablePerformanceTracking_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_EventBatchSize = { "EventBatchSize", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameAnalyticsManager, EventBatchSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventBatchSize_MetaData), NewProp_EventBatchSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_FlushInterval = { "FlushInterval", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameAnalyticsManager, FlushInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FlushInterval_MetaData), NewProp_FlushInterval_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_AnalyticsEndpoint = { "AnalyticsEndpoint", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameAnalyticsManager, AnalyticsEndpoint), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnalyticsEndpoint_MetaData), NewProp_AnalyticsEndpoint_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_OnAnalyticsEvent = { "OnAnalyticsEvent", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameAnalyticsManager, OnAnalyticsEvent), Z_Construct_UDelegateFunction_RoughReality_OnAnalyticsEvent__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAnalyticsEvent_MetaData), NewProp_OnAnalyticsEvent_MetaData) }; // 1690262825
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_SessionID = { "SessionID", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameAnalyticsManager, SessionID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionID_MetaData), NewProp_SessionID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_SessionStartTime = { "SessionStartTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameAnalyticsManager, SessionStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SessionStartTime_MetaData), NewProp_SessionStartTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_PendingEvents_Inner = { "PendingEvents", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPlayerActionEvent, METADATA_PARAMS(0, nullptr) }; // 1779189699
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_PendingEvents = { "PendingEvents", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameAnalyticsManager, PendingEvents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PendingEvents_MetaData), NewProp_PendingEvents_MetaData) }; // 1779189699
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_HeatmapData_Inner = { "HeatmapData", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FHeatmapData, METADATA_PARAMS(0, nullptr) }; // 348473466
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_HeatmapData = { "HeatmapData", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameAnalyticsManager, HeatmapData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HeatmapData_MetaData), NewProp_HeatmapData_MetaData) }; // 348473466
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_WeaponAnalytics_ValueProp = { "WeaponAnalytics", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FWeaponAnalytics, METADATA_PARAMS(0, nullptr) }; // 3529810086
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_WeaponAnalytics_Key_KeyProp = { "WeaponAnalytics_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_WeaponAnalytics = { "WeaponAnalytics", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameAnalyticsManager, WeaponAnalytics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponAnalytics_MetaData), NewProp_WeaponAnalytics_MetaData) }; // 3529810086
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_CurrentPerformanceMetrics = { "CurrentPerformanceMetrics", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameAnalyticsManager, CurrentPerformanceMetrics), Z_Construct_UScriptStruct_FPerformanceMetrics, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentPerformanceMetrics_MetaData), NewProp_CurrentPerformanceMetrics_MetaData) }; // 3286400143
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AGameAnalyticsManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_bEnableAnalytics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_bEnableHeatmaps,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_bEnablePerformanceTracking,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_EventBatchSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_FlushInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_AnalyticsEndpoint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_OnAnalyticsEvent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_SessionID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_SessionStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_PendingEvents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_PendingEvents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_HeatmapData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_HeatmapData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_WeaponAnalytics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_WeaponAnalytics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_WeaponAnalytics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameAnalyticsManager_Statics::NewProp_CurrentPerformanceMetrics,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AGameAnalyticsManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AGameAnalyticsManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AGameAnalyticsManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AGameAnalyticsManager_Statics::ClassParams = {
	&AGameAnalyticsManager::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AGameAnalyticsManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AGameAnalyticsManager_Statics::PropPointers),
	0,
	0x009000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AGameAnalyticsManager_Statics::Class_MetaDataParams), Z_Construct_UClass_AGameAnalyticsManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AGameAnalyticsManager()
{
	if (!Z_Registration_Info_UClass_AGameAnalyticsManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AGameAnalyticsManager.OuterSingleton, Z_Construct_UClass_AGameAnalyticsManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AGameAnalyticsManager.OuterSingleton;
}
template<> ROUGHREALITY_API UClass* StaticClass<AGameAnalyticsManager>()
{
	return AGameAnalyticsManager::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AGameAnalyticsManager);
AGameAnalyticsManager::~AGameAnalyticsManager() {}
// End Class AGameAnalyticsManager

// Begin Registration
struct Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Analytics_GameAnalyticsManager_h_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPlayerActionEvent::StaticStruct, Z_Construct_UScriptStruct_FPlayerActionEvent_Statics::NewStructOps, TEXT("PlayerActionEvent"), &Z_Registration_Info_UScriptStruct_PlayerActionEvent, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPlayerActionEvent), 1779189699U) },
		{ FHeatmapData::StaticStruct, Z_Construct_UScriptStruct_FHeatmapData_Statics::NewStructOps, TEXT("HeatmapData"), &Z_Registration_Info_UScriptStruct_HeatmapData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FHeatmapData), 348473466U) },
		{ FWeaponAnalytics::StaticStruct, Z_Construct_UScriptStruct_FWeaponAnalytics_Statics::NewStructOps, TEXT("WeaponAnalytics"), &Z_Registration_Info_UScriptStruct_WeaponAnalytics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FWeaponAnalytics), 3529810086U) },
		{ FPerformanceMetrics::StaticStruct, Z_Construct_UScriptStruct_FPerformanceMetrics_Statics::NewStructOps, TEXT("PerformanceMetrics"), &Z_Registration_Info_UScriptStruct_PerformanceMetrics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPerformanceMetrics), 3286400143U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AGameAnalyticsManager, AGameAnalyticsManager::StaticClass, TEXT("AGameAnalyticsManager"), &Z_Registration_Info_UClass_AGameAnalyticsManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AGameAnalyticsManager), 848385286U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Analytics_GameAnalyticsManager_h_945037169(TEXT("/Script/RoughReality"),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Analytics_GameAnalyticsManager_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Analytics_GameAnalyticsManager_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Analytics_GameAnalyticsManager_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Analytics_GameAnalyticsManager_h_Statics::ScriptStructInfo),
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
