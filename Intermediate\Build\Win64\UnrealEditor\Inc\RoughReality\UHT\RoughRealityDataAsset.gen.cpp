// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RoughReality/Core/RoughRealityDataAsset.h"
#include "Runtime/GameplayTags/Classes/GameplayTagContainer.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeRoughRealityDataAsset() {}

// Begin Cross Module References
ENGINE_API UClass* Z_Construct_UClass_UDataAsset();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTag();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
ROUGHREALITY_API UClass* Z_Construct_UClass_URoughRealityDataAsset();
ROUGHREALITY_API UClass* Z_Construct_UClass_URoughRealityDataAsset_NoRegister();
UPackage* Z_Construct_UPackage__Script_RoughReality();
// End Cross Module References

// Begin Class URoughRealityDataAsset Function GetAssetIDString
struct Z_Construct_UFunction_URoughRealityDataAsset_GetAssetIDString_Statics
{
	struct RoughRealityDataAsset_eventGetAssetIDString_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get the asset ID as string */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get the asset ID as string" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URoughRealityDataAsset_GetAssetIDString_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughRealityDataAsset_eventGetAssetIDString_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URoughRealityDataAsset_GetAssetIDString_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughRealityDataAsset_GetAssetIDString_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughRealityDataAsset_GetAssetIDString_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughRealityDataAsset_GetAssetIDString_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughRealityDataAsset, nullptr, "GetAssetIDString", nullptr, nullptr, Z_Construct_UFunction_URoughRealityDataAsset_GetAssetIDString_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URoughRealityDataAsset_GetAssetIDString_Statics::PropPointers), sizeof(Z_Construct_UFunction_URoughRealityDataAsset_GetAssetIDString_Statics::RoughRealityDataAsset_eventGetAssetIDString_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughRealityDataAsset_GetAssetIDString_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughRealityDataAsset_GetAssetIDString_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_URoughRealityDataAsset_GetAssetIDString_Statics::RoughRealityDataAsset_eventGetAssetIDString_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URoughRealityDataAsset_GetAssetIDString()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughRealityDataAsset_GetAssetIDString_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughRealityDataAsset::execGetAssetIDString)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetAssetIDString();
	P_NATIVE_END;
}
// End Class URoughRealityDataAsset Function GetAssetIDString

// Begin Class URoughRealityDataAsset Function HasAllTags
struct Z_Construct_UFunction_URoughRealityDataAsset_HasAllTags_Statics
{
	struct RoughRealityDataAsset_eventHasAllTags_Parms
	{
		FGameplayTagContainer Tags;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Check if this asset has all of the specified tags */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if this asset has all of the specified tags" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tags_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Tags;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URoughRealityDataAsset_HasAllTags_Statics::NewProp_Tags = { "Tags", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughRealityDataAsset_eventHasAllTags_Parms, Tags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tags_MetaData), NewProp_Tags_MetaData) }; // 3352185621
void Z_Construct_UFunction_URoughRealityDataAsset_HasAllTags_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RoughRealityDataAsset_eventHasAllTags_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URoughRealityDataAsset_HasAllTags_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RoughRealityDataAsset_eventHasAllTags_Parms), &Z_Construct_UFunction_URoughRealityDataAsset_HasAllTags_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URoughRealityDataAsset_HasAllTags_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughRealityDataAsset_HasAllTags_Statics::NewProp_Tags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughRealityDataAsset_HasAllTags_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughRealityDataAsset_HasAllTags_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughRealityDataAsset_HasAllTags_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughRealityDataAsset, nullptr, "HasAllTags", nullptr, nullptr, Z_Construct_UFunction_URoughRealityDataAsset_HasAllTags_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URoughRealityDataAsset_HasAllTags_Statics::PropPointers), sizeof(Z_Construct_UFunction_URoughRealityDataAsset_HasAllTags_Statics::RoughRealityDataAsset_eventHasAllTags_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughRealityDataAsset_HasAllTags_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughRealityDataAsset_HasAllTags_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_URoughRealityDataAsset_HasAllTags_Statics::RoughRealityDataAsset_eventHasAllTags_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URoughRealityDataAsset_HasAllTags()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughRealityDataAsset_HasAllTags_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughRealityDataAsset::execHasAllTags)
{
	P_GET_STRUCT_REF(FGameplayTagContainer,Z_Param_Out_Tags);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasAllTags(Z_Param_Out_Tags);
	P_NATIVE_END;
}
// End Class URoughRealityDataAsset Function HasAllTags

// Begin Class URoughRealityDataAsset Function HasAnyTag
struct Z_Construct_UFunction_URoughRealityDataAsset_HasAnyTag_Statics
{
	struct RoughRealityDataAsset_eventHasAnyTag_Parms
	{
		FGameplayTagContainer Tags;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Check if this asset has any of the specified tags */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if this asset has any of the specified tags" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tags_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Tags;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URoughRealityDataAsset_HasAnyTag_Statics::NewProp_Tags = { "Tags", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughRealityDataAsset_eventHasAnyTag_Parms, Tags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tags_MetaData), NewProp_Tags_MetaData) }; // 3352185621
void Z_Construct_UFunction_URoughRealityDataAsset_HasAnyTag_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RoughRealityDataAsset_eventHasAnyTag_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URoughRealityDataAsset_HasAnyTag_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RoughRealityDataAsset_eventHasAnyTag_Parms), &Z_Construct_UFunction_URoughRealityDataAsset_HasAnyTag_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URoughRealityDataAsset_HasAnyTag_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughRealityDataAsset_HasAnyTag_Statics::NewProp_Tags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughRealityDataAsset_HasAnyTag_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughRealityDataAsset_HasAnyTag_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughRealityDataAsset_HasAnyTag_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughRealityDataAsset, nullptr, "HasAnyTag", nullptr, nullptr, Z_Construct_UFunction_URoughRealityDataAsset_HasAnyTag_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URoughRealityDataAsset_HasAnyTag_Statics::PropPointers), sizeof(Z_Construct_UFunction_URoughRealityDataAsset_HasAnyTag_Statics::RoughRealityDataAsset_eventHasAnyTag_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughRealityDataAsset_HasAnyTag_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughRealityDataAsset_HasAnyTag_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_URoughRealityDataAsset_HasAnyTag_Statics::RoughRealityDataAsset_eventHasAnyTag_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URoughRealityDataAsset_HasAnyTag()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughRealityDataAsset_HasAnyTag_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughRealityDataAsset::execHasAnyTag)
{
	P_GET_STRUCT_REF(FGameplayTagContainer,Z_Param_Out_Tags);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasAnyTag(Z_Param_Out_Tags);
	P_NATIVE_END;
}
// End Class URoughRealityDataAsset Function HasAnyTag

// Begin Class URoughRealityDataAsset Function HasTag
struct Z_Construct_UFunction_URoughRealityDataAsset_HasTag_Statics
{
	struct RoughRealityDataAsset_eventHasTag_Parms
	{
		FGameplayTag Tag;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Check if this asset has a specific tag */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if this asset has a specific tag" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Tag;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URoughRealityDataAsset_HasTag_Statics::NewProp_Tag = { "Tag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughRealityDataAsset_eventHasTag_Parms, Tag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tag_MetaData), NewProp_Tag_MetaData) }; // 1298103297
void Z_Construct_UFunction_URoughRealityDataAsset_HasTag_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RoughRealityDataAsset_eventHasTag_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URoughRealityDataAsset_HasTag_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RoughRealityDataAsset_eventHasTag_Parms), &Z_Construct_UFunction_URoughRealityDataAsset_HasTag_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URoughRealityDataAsset_HasTag_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughRealityDataAsset_HasTag_Statics::NewProp_Tag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughRealityDataAsset_HasTag_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughRealityDataAsset_HasTag_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughRealityDataAsset_HasTag_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughRealityDataAsset, nullptr, "HasTag", nullptr, nullptr, Z_Construct_UFunction_URoughRealityDataAsset_HasTag_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URoughRealityDataAsset_HasTag_Statics::PropPointers), sizeof(Z_Construct_UFunction_URoughRealityDataAsset_HasTag_Statics::RoughRealityDataAsset_eventHasTag_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughRealityDataAsset_HasTag_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughRealityDataAsset_HasTag_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_URoughRealityDataAsset_HasTag_Statics::RoughRealityDataAsset_eventHasTag_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URoughRealityDataAsset_HasTag()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughRealityDataAsset_HasTag_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughRealityDataAsset::execHasTag)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_Tag);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasTag(Z_Param_Out_Tag);
	P_NATIVE_END;
}
// End Class URoughRealityDataAsset Function HasTag

// Begin Class URoughRealityDataAsset Function ValidateAsset
struct Z_Construct_UFunction_URoughRealityDataAsset_ValidateAsset_Statics
{
	struct RoughRealityDataAsset_eventValidateAsset_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Validate the data asset (override in derived classes) */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validate the data asset (override in derived classes)" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URoughRealityDataAsset_ValidateAsset_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RoughRealityDataAsset_eventValidateAsset_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URoughRealityDataAsset_ValidateAsset_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RoughRealityDataAsset_eventValidateAsset_Parms), &Z_Construct_UFunction_URoughRealityDataAsset_ValidateAsset_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URoughRealityDataAsset_ValidateAsset_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughRealityDataAsset_ValidateAsset_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughRealityDataAsset_ValidateAsset_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughRealityDataAsset_ValidateAsset_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughRealityDataAsset, nullptr, "ValidateAsset", nullptr, nullptr, Z_Construct_UFunction_URoughRealityDataAsset_ValidateAsset_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URoughRealityDataAsset_ValidateAsset_Statics::PropPointers), sizeof(Z_Construct_UFunction_URoughRealityDataAsset_ValidateAsset_Statics::RoughRealityDataAsset_eventValidateAsset_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughRealityDataAsset_ValidateAsset_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughRealityDataAsset_ValidateAsset_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_URoughRealityDataAsset_ValidateAsset_Statics::RoughRealityDataAsset_eventValidateAsset_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URoughRealityDataAsset_ValidateAsset()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughRealityDataAsset_ValidateAsset_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughRealityDataAsset::execValidateAsset)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateAsset();
	P_NATIVE_END;
}
// End Class URoughRealityDataAsset Function ValidateAsset

// Begin Class URoughRealityDataAsset
void URoughRealityDataAsset::StaticRegisterNativesURoughRealityDataAsset()
{
	UClass* Class = URoughRealityDataAsset::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "GetAssetIDString", &URoughRealityDataAsset::execGetAssetIDString },
		{ "HasAllTags", &URoughRealityDataAsset::execHasAllTags },
		{ "HasAnyTag", &URoughRealityDataAsset::execHasAnyTag },
		{ "HasTag", &URoughRealityDataAsset::execHasTag },
		{ "ValidateAsset", &URoughRealityDataAsset::execValidateAsset },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(URoughRealityDataAsset);
UClass* Z_Construct_UClass_URoughRealityDataAsset_NoRegister()
{
	return URoughRealityDataAsset::StaticClass();
}
struct Z_Construct_UClass_URoughRealityDataAsset_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Base class for all data assets in Rough Reality\n * Provides common functionality and tagging system\n */" },
#endif
		{ "IncludePath", "Core/RoughRealityDataAsset.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Core/RoughRealityDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Base class for all data assets in Rough Reality\nProvides common functionality and tagging system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetID_MetaData[] = {
		{ "Category", "Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Unique identifier for this data asset */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Unique identifier for this data asset" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DisplayName_MetaData[] = {
		{ "Category", "Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Display name for UI */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Display name for UI" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "Category", "Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Description for UI */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityDataAsset.h" },
		{ "MultiLine", "TRUE" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Description for UI" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetTags_MetaData[] = {
		{ "Category", "Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Gameplay tags associated with this asset */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gameplay tags associated with this asset" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Icon_MetaData[] = {
		{ "Category", "Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Icon for UI representation */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Icon for UI representation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsEnabled_MetaData[] = {
		{ "Category", "Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Whether this asset is enabled/available */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether this asset is enabled/available" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Priority_MetaData[] = {
		{ "Category", "Core" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Priority for sorting/selection */" },
#endif
		{ "ModuleRelativePath", "Core/RoughRealityDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Priority for sorting/selection" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_AssetID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_DisplayName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_Description;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AssetTags;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_Icon;
	static void NewProp_bIsEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEnabled;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_URoughRealityDataAsset_GetAssetIDString, "GetAssetIDString" }, // 2941854848
		{ &Z_Construct_UFunction_URoughRealityDataAsset_HasAllTags, "HasAllTags" }, // 3901440102
		{ &Z_Construct_UFunction_URoughRealityDataAsset_HasAnyTag, "HasAnyTag" }, // 601648355
		{ &Z_Construct_UFunction_URoughRealityDataAsset_HasTag, "HasTag" }, // 1695544108
		{ &Z_Construct_UFunction_URoughRealityDataAsset_ValidateAsset, "ValidateAsset" }, // 1426858554
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URoughRealityDataAsset>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UClass_URoughRealityDataAsset_Statics::NewProp_AssetID = { "AssetID", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URoughRealityDataAsset, AssetID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetID_MetaData), NewProp_AssetID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UClass_URoughRealityDataAsset_Statics::NewProp_DisplayName = { "DisplayName", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URoughRealityDataAsset, DisplayName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DisplayName_MetaData), NewProp_DisplayName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UClass_URoughRealityDataAsset_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URoughRealityDataAsset, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URoughRealityDataAsset_Statics::NewProp_AssetTags = { "AssetTags", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URoughRealityDataAsset, AssetTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetTags_MetaData), NewProp_AssetTags_MetaData) }; // 3352185621
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_URoughRealityDataAsset_Statics::NewProp_Icon = { "Icon", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URoughRealityDataAsset, Icon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Icon_MetaData), NewProp_Icon_MetaData) };
void Z_Construct_UClass_URoughRealityDataAsset_Statics::NewProp_bIsEnabled_SetBit(void* Obj)
{
	((URoughRealityDataAsset*)Obj)->bIsEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URoughRealityDataAsset_Statics::NewProp_bIsEnabled = { "bIsEnabled", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URoughRealityDataAsset), &Z_Construct_UClass_URoughRealityDataAsset_Statics::NewProp_bIsEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsEnabled_MetaData), NewProp_bIsEnabled_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_URoughRealityDataAsset_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URoughRealityDataAsset, Priority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Priority_MetaData), NewProp_Priority_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URoughRealityDataAsset_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughRealityDataAsset_Statics::NewProp_AssetID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughRealityDataAsset_Statics::NewProp_DisplayName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughRealityDataAsset_Statics::NewProp_Description,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughRealityDataAsset_Statics::NewProp_AssetTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughRealityDataAsset_Statics::NewProp_Icon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughRealityDataAsset_Statics::NewProp_bIsEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughRealityDataAsset_Statics::NewProp_Priority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URoughRealityDataAsset_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URoughRealityDataAsset_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UDataAsset,
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URoughRealityDataAsset_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URoughRealityDataAsset_Statics::ClassParams = {
	&URoughRealityDataAsset::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_URoughRealityDataAsset_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_URoughRealityDataAsset_Statics::PropPointers),
	0,
	0x001000A1u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URoughRealityDataAsset_Statics::Class_MetaDataParams), Z_Construct_UClass_URoughRealityDataAsset_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URoughRealityDataAsset()
{
	if (!Z_Registration_Info_UClass_URoughRealityDataAsset.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URoughRealityDataAsset.OuterSingleton, Z_Construct_UClass_URoughRealityDataAsset_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URoughRealityDataAsset.OuterSingleton;
}
template<> ROUGHREALITY_API UClass* StaticClass<URoughRealityDataAsset>()
{
	return URoughRealityDataAsset::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URoughRealityDataAsset);
URoughRealityDataAsset::~URoughRealityDataAsset() {}
// End Class URoughRealityDataAsset

// Begin Registration
struct Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_RoughRealityDataAsset_h_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_URoughRealityDataAsset, URoughRealityDataAsset::StaticClass, TEXT("URoughRealityDataAsset"), &Z_Registration_Info_UClass_URoughRealityDataAsset, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URoughRealityDataAsset), 1870184989U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_RoughRealityDataAsset_h_3850987418(TEXT("/Script/RoughReality"),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_RoughRealityDataAsset_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_RoughRealityDataAsset_h_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
