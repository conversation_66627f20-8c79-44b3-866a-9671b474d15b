// Copyright Epic Games, Inc. All Rights Reserved.

#include "GameplayLoopManager.h"
#include "RoughRealityGameModeBase.h"
#include "../SaveSystem/RoughSaveGame.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/World.h"

AGameplayLoopManager::AGameplayLoopManager()
{
	PrimaryActorTick.bCanEverTick = true;
	PrimaryActorTick.TickInterval = 1.0f; // Tick once per second for statistics

	CurrentRunState = ERunState::NotStarted;
	CurrentRunNumber = 0;
	CurrentSectorIndex = 0;
	CurrentLevelIndex = 0;
	RunStartTime = 0.0f;
	CurrentDifficultyMultiplier = 1.0f;
	TotalShotsHit = 0;
	TotalShotsFired = 0;
	SectorStartTime = 0.0f;
}

void AGameplayLoopManager::BeginPlay()
{
	Super::BeginPlay();
	InitializeSectorConfigurations();
}

void AGameplayLoopManager::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	// Update run duration if run is in progress
	if (CurrentRunState == ERunState::InProgress)
	{
		CurrentRunStats.RunDuration = GetWorld()->GetTimeSeconds() - RunStartTime;
	}
}

void AGameplayLoopManager::StartNewRun()
{
	if (CurrentRunState == ERunState::InProgress)
	{
		UE_LOG(LogTemp, Warning, TEXT("Cannot start new run: Run already in progress"));
		return;
	}

	// Initialize new run
	CurrentRunNumber++;
	CurrentSectorIndex = 0;
	CurrentLevelIndex = 0;
	CurrentRunState = ERunState::InProgress;
	RunStartTime = GetWorld()->GetTimeSeconds();
	SectorStartTime = RunStartTime;

	// Reset statistics
	ResetRunStatistics();
	CurrentRunStats.RunNumber = CurrentRunNumber;

	// Update difficulty scaling
	UpdateDifficultyScaling();

	// Preload first sector assets
	PreloadSectorAssets(0);

	OnRunStarted.Broadcast(CurrentRunNumber);

	UE_LOG(LogTemp, Log, TEXT("Started new run #%d with difficulty multiplier %.2f"), 
		CurrentRunNumber, CurrentDifficultyMultiplier);
}

void AGameplayLoopManager::CompleteCurrentRun(bool bSuccess)
{
	if (CurrentRunState != ERunState::InProgress)
	{
		UE_LOG(LogTemp, Warning, TEXT("Cannot complete run: No run in progress"));
		return;
	}

	CurrentRunState = bSuccess ? ERunState::Completed : ERunState::Failed;

	// Finalize statistics
	CurrentRunStats.RunDuration = GetWorld()->GetTimeSeconds() - RunStartTime;
	if (TotalShotsFired > 0)
	{
		CurrentRunStats.AccuracyPercentage = (float)TotalShotsHit / TotalShotsFired * 100.0f;
	}

	// Calculate and apply rewards
	CalculateRunRewards();

	// Save statistics
	SaveRunStatistics();

	OnRunCompleted.Broadcast(bSuccess, CurrentRunNumber);

	UE_LOG(LogTemp, Log, TEXT("Run #%d completed. Success: %s, Duration: %.2f seconds, Teeth: %d"), 
		CurrentRunNumber, bSuccess ? TEXT("Yes") : TEXT("No"), 
		CurrentRunStats.RunDuration, CurrentRunStats.TeethCollected);
}

void AGameplayLoopManager::AbandonCurrentRun()
{
	if (CurrentRunState != ERunState::InProgress)
	{
		return;
	}

	CurrentRunState = ERunState::Abandoned;
	SaveRunStatistics();
	OnRunCompleted.Broadcast(false, CurrentRunNumber);

	UE_LOG(LogTemp, Log, TEXT("Run #%d abandoned"), CurrentRunNumber);
}

void AGameplayLoopManager::AdvanceToNextLevel()
{
	if (CurrentRunState != ERunState::InProgress)
	{
		return;
	}

	CurrentLevelIndex++;

	// Check if we need to advance to next sector
	FSectorConfiguration CurrentSector = GetCurrentSectorConfiguration();
	int32 LevelsInSector = FMath::RandRange(CurrentSector.MinLevels, CurrentSector.MaxLevels);

	if (CurrentLevelIndex >= LevelsInSector)
	{
		AdvanceToNextSector();
	}

	UE_LOG(LogTemp, Log, TEXT("Advanced to level %d in sector %d"), CurrentLevelIndex, CurrentSectorIndex);
}

void AGameplayLoopManager::AdvanceToNextSector()
{
	if (CurrentRunState != ERunState::InProgress)
	{
		return;
	}

	// Record sector completion time
	float SectorDuration = GetWorld()->GetTimeSeconds() - SectorStartTime;
	FSectorConfiguration CurrentSector = GetCurrentSectorConfiguration();
	CurrentRunStats.SectorCompletionTimes.Add(CurrentSector.SectorType, SectorDuration);

	int32 OldSector = CurrentSectorIndex;
	CurrentSectorIndex++;
	CurrentLevelIndex = 0;
	CurrentRunStats.SectorsCompleted++;
	SectorStartTime = GetWorld()->GetTimeSeconds();

	// Check if run is complete
	if (CurrentSectorIndex >= SectorConfigurations.Num())
	{
		CompleteCurrentRun(true);
		return;
	}

	// Update difficulty for new sector
	UpdateDifficultyScaling();

	// Preload next sector assets
	PreloadSectorAssets(CurrentSectorIndex);

	OnSectorChanged.Broadcast(CurrentSectorIndex, OldSector);

	UE_LOG(LogTemp, Log, TEXT("Advanced to sector %d"), CurrentSectorIndex);
}

bool AGameplayLoopManager::CanAdvanceToSector(int32 SectorIndex) const
{
	if (SectorIndex < 0 || SectorIndex >= SectorConfigurations.Num())
	{
		return false;
	}

	// Check unlock requirements
	FSectorConfiguration SectorConfig = SectorConfigurations[SectorIndex];
	
	// TODO: Check against player's unlocked tags
	// For now, allow progression to any sector during a run
	return true;
}

float AGameplayLoopManager::CalculateDifficultyMultiplier(int32 SectorIndex, int32 RunNumber) const
{
	if (SectorIndex < 0 || SectorIndex >= SectorConfigurations.Num())
	{
		return 1.0f;
	}

	FSectorConfiguration SectorConfig = SectorConfigurations[SectorIndex];
	float BaseMultiplier = SectorConfig.BaseDifficultyMultiplier;
	float RunScaling = SectorConfig.DifficultyScalingPerRun * (RunNumber - 1);
	float SectorScaling = SectorIndex * 0.2f; // Each sector is 20% harder

	return BaseMultiplier + RunScaling + SectorScaling;
}

void AGameplayLoopManager::UpdateDifficultyScaling()
{
	float NewDifficulty = CalculateDifficultyMultiplier(CurrentSectorIndex, CurrentRunNumber);
	
	if (FMath::Abs(NewDifficulty - CurrentDifficultyMultiplier) > 0.01f)
	{
		CurrentDifficultyMultiplier = NewDifficulty;
		OnDifficultyScaled.Broadcast(CurrentDifficultyMultiplier);
	}
}

void AGameplayLoopManager::RecordEnemyKill(const FString& EnemyType)
{
	CurrentRunStats.EnemiesKilled++;
	UE_LOG(LogTemp, VeryVerbose, TEXT("Enemy killed: %s (Total: %d)"), *EnemyType, CurrentRunStats.EnemiesKilled);
}

void AGameplayLoopManager::RecordWeaponUsage(const FString& WeaponName)
{
	int32* CurrentCount = CurrentRunStats.WeaponUsageStats.Find(WeaponName);
	if (CurrentCount)
	{
		(*CurrentCount)++;
	}
	else
	{
		CurrentRunStats.WeaponUsageStats.Add(WeaponName, 1);
	}
}

void AGameplayLoopManager::RecordBulletTimeUsage()
{
	CurrentRunStats.BulletTimesUsed++;
}

void AGameplayLoopManager::RecordRewindUsage()
{
	CurrentRunStats.RewindsUsed++;
}

void AGameplayLoopManager::RecordTeethCollected(int32 Amount)
{
	CurrentRunStats.TeethCollected += Amount;
}

void AGameplayLoopManager::UpdateAccuracy(int32 ShotsHit, int32 ShotsFired)
{
	TotalShotsHit += ShotsHit;
	TotalShotsFired += ShotsFired;
}

FSectorConfiguration AGameplayLoopManager::GetCurrentSectorConfiguration() const
{
	return GetSectorConfiguration(CurrentSectorIndex);
}

FSectorConfiguration AGameplayLoopManager::GetSectorConfiguration(int32 SectorIndex) const
{
	if (SectorIndex >= 0 && SectorIndex < SectorConfigurations.Num())
	{
		return SectorConfigurations[SectorIndex];
	}
	return FSectorConfiguration();
}

int32 AGameplayLoopManager::GetTotalSectors() const
{
	return SectorConfigurations.Num();
}

float AGameplayLoopManager::GetRunProgress() const
{
	if (SectorConfigurations.Num() == 0)
	{
		return 0.0f;
	}

	float SectorProgress = (float)CurrentSectorIndex / SectorConfigurations.Num();
	
	// Add partial progress within current sector
	FSectorConfiguration CurrentSector = GetCurrentSectorConfiguration();
	int32 LevelsInSector = FMath::RandRange(CurrentSector.MinLevels, CurrentSector.MaxLevels);
	if (LevelsInSector > 0)
	{
		float LevelProgress = (float)CurrentLevelIndex / LevelsInSector;
		SectorProgress += LevelProgress / SectorConfigurations.Num();
	}

	return FMath::Clamp(SectorProgress, 0.0f, 1.0f);
}

bool AGameplayLoopManager::IsRunInProgress() const
{
	return CurrentRunState == ERunState::InProgress;
}

void AGameplayLoopManager::PreloadSectorAssets(int32 SectorIndex)
{
	// TODO: Implement asset preloading based on sector configuration
	UE_LOG(LogTemp, Log, TEXT("Preloading assets for sector %d"), SectorIndex);
}

void AGameplayLoopManager::UnloadUnusedAssets()
{
	// TODO: Implement asset unloading for memory optimization
	UE_LOG(LogTemp, Log, TEXT("Unloading unused assets"));
}

void AGameplayLoopManager::InitializeSectorConfigurations()
{
	SectorConfigurations.Empty();

	// Dilapidated City
	FSectorConfiguration DilapidatedCity;
	DilapidatedCity.SectorType = ESectorType::DilapidatedCity;
	DilapidatedCity.SectorName = FText::FromString("Dilapidated City");
	DilapidatedCity.MinLevels = 3;
	DilapidatedCity.MaxLevels = 5;
	DilapidatedCity.BaseDifficultyMultiplier = 1.0f;
	DilapidatedCity.DifficultyScalingPerRun = 0.1f;
	DilapidatedCity.TeethReward = 100;
	DilapidatedCity.SectorColor = FLinearColor(0.8f, 0.6f, 0.4f);
	SectorConfigurations.Add(DilapidatedCity);

	// Station Tunnels
	FSectorConfiguration StationTunnels;
	StationTunnels.SectorType = ESectorType::StationTunnels;
	StationTunnels.SectorName = FText::FromString("Station Tunnels");
	StationTunnels.MinLevels = 4;
	StationTunnels.MaxLevels = 6;
	StationTunnels.BaseDifficultyMultiplier = 1.2f;
	StationTunnels.DifficultyScalingPerRun = 0.12f;
	StationTunnels.TeethReward = 150;
	StationTunnels.SectorColor = FLinearColor(0.4f, 0.4f, 0.6f);
	SectorConfigurations.Add(StationTunnels);

	// Bela Vegas District
	FSectorConfiguration BelaVegas;
	BelaVegas.SectorType = ESectorType::BelaVegas;
	BelaVegas.SectorName = FText::FromString("Bela Vegas District");
	BelaVegas.MinLevels = 5;
	BelaVegas.MaxLevels = 7;
	BelaVegas.BaseDifficultyMultiplier = 1.4f;
	BelaVegas.DifficultyScalingPerRun = 0.15f;
	BelaVegas.TeethReward = 200;
	BelaVegas.SectorColor = FLinearColor(1.0f, 0.2f, 0.8f);
	SectorConfigurations.Add(BelaVegas);

	// Industrial District
	FSectorConfiguration Industrial;
	Industrial.SectorType = ESectorType::IndustrialDistrict;
	Industrial.SectorName = FText::FromString("Abandoned Industrial District");
	Industrial.MinLevels = 6;
	Industrial.MaxLevels = 8;
	Industrial.BaseDifficultyMultiplier = 1.6f;
	Industrial.DifficultyScalingPerRun = 0.18f;
	Industrial.TeethReward = 300;
	Industrial.SectorColor = FLinearColor(0.6f, 0.3f, 0.1f);
	SectorConfigurations.Add(Industrial);

	UE_LOG(LogTemp, Log, TEXT("Initialized %d sector configurations"), SectorConfigurations.Num());
}

void AGameplayLoopManager::SaveRunStatistics()
{
	// TODO: Save to persistent storage and analytics
	UE_LOG(LogTemp, Log, TEXT("Saving run statistics for run #%d"), CurrentRunStats.RunNumber);
}

void AGameplayLoopManager::ResetRunStatistics()
{
	CurrentRunStats = FRunStatistics();
	TotalShotsHit = 0;
	TotalShotsFired = 0;
}

void AGameplayLoopManager::CalculateRunRewards()
{
	// Calculate teeth rewards based on performance
	int32 BaseReward = 0;
	for (int32 i = 0; i <= CurrentRunStats.SectorsCompleted && i < SectorConfigurations.Num(); i++)
	{
		BaseReward += SectorConfigurations[i].TeethReward;
	}

	// Apply performance bonuses
	float AccuracyBonus = CurrentRunStats.AccuracyPercentage > 75.0f ? 1.2f : 1.0f;
	float SpeedBonus = CurrentRunStats.RunDuration < 600.0f ? 1.1f : 1.0f; // 10 minute bonus

	int32 FinalReward = FMath::RoundToInt(BaseReward * AccuracyBonus * SpeedBonus);
	CurrentRunStats.TeethCollected += FinalReward;

	UE_LOG(LogTemp, Log, TEXT("Calculated run rewards: %d teeth (Base: %d, Accuracy: %.1fx, Speed: %.1fx)"), 
		FinalReward, BaseReward, AccuracyBonus, SpeedBonus);
}

void AGameplayLoopManager::OnSectorAssetsLoaded()
{
	UE_LOG(LogTemp, Log, TEXT("Sector assets loaded successfully"));
}
