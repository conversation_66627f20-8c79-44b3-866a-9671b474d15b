// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Progression/ProgressionManager.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
 
class AProgressionManager;
class UObject;
struct FGameplayTag;
struct FPrestigeLevel;
struct FUnlockCondition;
struct FUpgradeNode;
#ifdef ROUGHREALITY_ProgressionManager_generated_h
#error "ProgressionManager.generated.h already included, missing '#pragma once' in ProgressionManager.h"
#endif
#define ROUGHREALITY_ProgressionManager_generated_h

#define FID_RoughReality_Source_RoughReality_Progression_ProgressionManager_h_16_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FUpgradeNode_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct(); \
	typedef FTableRowBase Super;


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FUpgradeNode>();

#define FID_RoughReality_Source_RoughReality_Progression_ProgressionManager_h_67_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FUnlockCondition_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FUnlockCondition>();

#define FID_RoughReality_Source_RoughReality_Progression_ProgressionManager_h_113_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPrestigeLevel_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FPrestigeLevel>();

#define FID_RoughReality_Source_RoughReality_Progression_ProgressionManager_h_150_DELEGATE \
ROUGHREALITY_API void FOnUpgradePurchased_DelegateWrapper(const FMulticastScriptDelegate& OnUpgradePurchased, FName UpgradeID, int32 NewLevel);


#define FID_RoughReality_Source_RoughReality_Progression_ProgressionManager_h_151_DELEGATE \
ROUGHREALITY_API void FOnUnlockAchieved_DelegateWrapper(const FMulticastScriptDelegate& OnUnlockAchieved, FName UnlockID);


#define FID_RoughReality_Source_RoughReality_Progression_ProgressionManager_h_152_DELEGATE \
ROUGHREALITY_API void FOnPrestigeLevelUp_DelegateWrapper(const FMulticastScriptDelegate& OnPrestigeLevelUp, int32 NewPrestigeLevel);


#define FID_RoughReality_Source_RoughReality_Progression_ProgressionManager_h_161_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetProgressionManager); \
	DECLARE_FUNCTION(execAddTeeth); \
	DECLARE_FUNCTION(execSpendTeeth); \
	DECLARE_FUNCTION(execGetAvailableTeeth); \
	DECLARE_FUNCTION(execGetUnlockedAchievements); \
	DECLARE_FUNCTION(execHasAchievement); \
	DECLARE_FUNCTION(execUnlockAchievement); \
	DECLARE_FUNCTION(execGetAllStatModifiers); \
	DECLARE_FUNCTION(execGetWeaponStatModifier); \
	DECLARE_FUNCTION(execGetTotalStatModifier); \
	DECLARE_FUNCTION(execGetPrestigeProgress); \
	DECLARE_FUNCTION(execGetNextPrestigeData); \
	DECLARE_FUNCTION(execGetCurrentPrestigeData); \
	DECLARE_FUNCTION(execGetPrestigeLevel); \
	DECLARE_FUNCTION(execPerformPrestige); \
	DECLARE_FUNCTION(execCanPrestige); \
	DECLARE_FUNCTION(execGetUnlockProgress); \
	DECLARE_FUNCTION(execGetPendingUnlocks); \
	DECLARE_FUNCTION(execForceUnlock); \
	DECLARE_FUNCTION(execIsUnlocked); \
	DECLARE_FUNCTION(execCheckUnlockConditions); \
	DECLARE_FUNCTION(execGetUpgradeData); \
	DECLARE_FUNCTION(execGetPurchasedUpgrades); \
	DECLARE_FUNCTION(execGetAvailableUpgrades); \
	DECLARE_FUNCTION(execGetUpgradeCost); \
	DECLARE_FUNCTION(execGetUpgradeLevel); \
	DECLARE_FUNCTION(execPurchaseUpgrade); \
	DECLARE_FUNCTION(execCanPurchaseUpgrade);


#define FID_RoughReality_Source_RoughReality_Progression_ProgressionManager_h_161_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAProgressionManager(); \
	friend struct Z_Construct_UClass_AProgressionManager_Statics; \
public: \
	DECLARE_CLASS(AProgressionManager, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/RoughReality"), NO_API) \
	DECLARE_SERIALIZER(AProgressionManager)


#define FID_RoughReality_Source_RoughReality_Progression_ProgressionManager_h_161_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	AProgressionManager(AProgressionManager&&); \
	AProgressionManager(const AProgressionManager&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AProgressionManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AProgressionManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AProgressionManager) \
	NO_API virtual ~AProgressionManager();


#define FID_RoughReality_Source_RoughReality_Progression_ProgressionManager_h_158_PROLOG
#define FID_RoughReality_Source_RoughReality_Progression_ProgressionManager_h_161_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_RoughReality_Source_RoughReality_Progression_ProgressionManager_h_161_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_Progression_ProgressionManager_h_161_INCLASS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_Progression_ProgressionManager_h_161_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ROUGHREALITY_API UClass* StaticClass<class AProgressionManager>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_RoughReality_Source_RoughReality_Progression_ProgressionManager_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
