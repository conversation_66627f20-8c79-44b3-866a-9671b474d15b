// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "GameplayTagContainer.h"
#include "WeaponBase.generated.h"

class ARook<PERSON><PERSON>haracter;
class UWeaponDataAsset;
class USkeletalMeshComponent;
class UStaticMeshComponent;
class USceneComponent;
class USoundBase;
class UParticleSystem;
class UAnimMontage;

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAmmoChanged, int32, NewAmmoCount);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnWeaponFired, AWeaponBase*, Weapon);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnWeaponReloaded, AWeaponBase*, Weapon);

UENUM(BlueprintType)
enum class EWeaponState : uint8
{
	Idle,
	Firing,
	Reloading,
	Equipping,
	Unequipping
};

UENUM(BlueprintType)
enum class EWeaponType : uint8
{
	<PERSON><PERSON><PERSON>,
	AssaultR<PERSON><PERSON>,
	Shotgun,
	RocketLauncher,
	Chaingun,
	Sniper
};

USTRUCT(BlueprintType)
struct FWeaponStats
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
	float Damage = 25.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
	float FireRate = 600.0f; // Rounds per minute

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
	float Range = 5000.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
	float Accuracy = 0.95f; // 0.0 to 1.0

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
	int32 MagazineSize = 15;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
	int32 MaxAmmo = 150;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
	float ReloadTime = 2.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
	int32 ProjectileCount = 1; // For shotguns

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
	float SpreadAngle = 1.0f; // Degrees

	FWeaponStats()
	{
		Damage = 25.0f;
		FireRate = 600.0f;
		Range = 5000.0f;
		Accuracy = 0.95f;
		MagazineSize = 15;
		MaxAmmo = 150;
		ReloadTime = 2.0f;
		ProjectileCount = 1;
		SpreadAngle = 1.0f;
	}
};

/**
 * Base class for all weapons in Rough Reality
 * Provides core weapon functionality including firing, reloading, and ammo management
 */
UCLASS(BlueprintType, Blueprintable)
class ROUGHREALITY_API AWeaponBase : public AActor
{
	GENERATED_BODY()

public:
	AWeaponBase();

protected:
	virtual void BeginPlay() override;
	virtual void Tick(float DeltaTime) override;

	/** Components */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	USceneComponent* RootSceneComponent;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	USkeletalMeshComponent* WeaponMesh;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	USceneComponent* MuzzleLocation;

public:
	/** Weapon Configuration */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Weapon Config")
	UWeaponDataAsset* WeaponData;

	UPROPERTY(BlueprintReadOnly, Category = "Weapon State")
	EWeaponState CurrentState = EWeaponState::Idle;

	UPROPERTY(BlueprintReadOnly, Category = "Weapon State")
	EWeaponType WeaponType = EWeaponType::Pistol;

	UPROPERTY(BlueprintReadOnly, Category = "Weapon State")
	ARookieCharacter* OwnerCharacter;

	/** Ammo System */
	UPROPERTY(BlueprintReadOnly, Category = "Ammo")
	int32 CurrentAmmo = 0;

	UPROPERTY(BlueprintReadOnly, Category = "Ammo")
	int32 CurrentMagazine = 0;

	UPROPERTY(BlueprintReadOnly, Category = "Ammo")
	FWeaponStats WeaponStats;

	/** Events */
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnAmmoChanged OnAmmoChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnWeaponFired OnWeaponFired;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnWeaponReloaded OnWeaponReloaded;

public:
	/** Core Weapon Functions */
	UFUNCTION(BlueprintCallable, Category = "Weapon")
	virtual void StartFire();

	UFUNCTION(BlueprintCallable, Category = "Weapon")
	virtual void StopFire();

	UFUNCTION(BlueprintCallable, Category = "Weapon")
	virtual void Reload();

	UFUNCTION(BlueprintCallable, Category = "Weapon")
	virtual void OnEquipped(ARookieCharacter* NewOwner);

	UFUNCTION(BlueprintCallable, Category = "Weapon")
	virtual void OnUnequipped();

	/** State Queries */
	UFUNCTION(BlueprintCallable, Category = "Weapon")
	bool CanFire() const;

	UFUNCTION(BlueprintCallable, Category = "Weapon")
	bool CanReload() const;

	UFUNCTION(BlueprintCallable, Category = "Weapon")
	bool IsEmpty() const;

	UFUNCTION(BlueprintCallable, Category = "Weapon")
	bool NeedsReload() const;

	UFUNCTION(BlueprintCallable, Category = "Weapon")
	float GetAmmoPercent() const;

	UFUNCTION(BlueprintCallable, Category = "Weapon")
	float GetMagazinePercent() const;

	/** Weapon Info */
	UFUNCTION(BlueprintCallable, Category = "Weapon")
	FString GetWeaponName() const;

	UFUNCTION(BlueprintCallable, Category = "Weapon")
	FGameplayTagContainer GetWeaponTags() const;

protected:
	/** Internal Functions */
	virtual void FireWeapon();
	virtual void PerformReload();
	virtual void UpdateWeaponState(float DeltaTime);

	/** Line Trace for Hit Detection */
	virtual bool PerformLineTrace(FVector Start, FVector End, FHitResult& OutHit);

	/** Apply Damage to Target */
	virtual void ApplyDamage(AActor* Target, float DamageAmount, const FVector& HitLocation);

	/** Visual and Audio Effects */
	virtual void PlayFireEffects();
	virtual void PlayReloadEffects();
	virtual void PlayMuzzleFlash();
	virtual void PlayFireSound();
	virtual void PlayReloadSound();

	/** Timers */
	FTimerHandle FireRateTimer;
	FTimerHandle ReloadTimer;

	/** Fire Rate Control */
	bool bCanFireAgain = true;
	bool bIsFiring = false;
	bool bIsReloading = false;

	/** Automatic Fire */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Weapon Config")
	bool bIsAutomatic = false;

	/** Muzzle Flash Effect */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Effects")
	UParticleSystem* MuzzleFlashEffect;

	/** Fire Sound */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Audio")
	USoundBase* FireSound;

	/** Reload Sound */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Audio")
	USoundBase* ReloadSound;

	/** Fire Animation */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Animation")
	UAnimMontage* FireAnimation;

	/** Reload Animation */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Animation")
	UAnimMontage* ReloadAnimation;

private:
	/** Reset fire rate timer */
	void ResetFireRate();

	/** Complete reload */
	void CompleteReload();
};
