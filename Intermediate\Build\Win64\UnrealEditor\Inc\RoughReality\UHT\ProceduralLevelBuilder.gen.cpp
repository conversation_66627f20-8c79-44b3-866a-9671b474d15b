// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RoughReality/LevelGeneration/ProceduralLevelBuilder.h"
#include "Runtime/GameplayTags/Classes/GameplayTagContainer.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeProceduralLevelBuilder() {}

// Begin Cross Module References
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
ROUGHREALITY_API UClass* Z_Construct_UClass_ALevelTile_NoRegister();
ROUGHREALITY_API UClass* Z_Construct_UClass_AProceduralLevelBuilder();
ROUGHREALITY_API UClass* Z_Construct_UClass_AProceduralLevelBuilder_NoRegister();
ROUGHREALITY_API UClass* Z_Construct_UClass_UTileDefinitionDataAsset_NoRegister();
ROUGHREALITY_API UEnum* Z_Construct_UEnum_RoughReality_ETileType();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerated__DelegateSignature();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerationFailed__DelegateSignature();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FLevelSectorConfiguration();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FTileSpawnInfo();
UPackage* Z_Construct_UPackage__Script_RoughReality();
// End Cross Module References

// Begin Delegate FOnLevelGenerated
struct Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerated__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnLevelGenerated_Parms
	{
		int32 SectorIndex;
		int32 LevelIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SectorIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LevelIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerated__DelegateSignature_Statics::NewProp_SectorIndex = { "SectorIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnLevelGenerated_Parms, SectorIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerated__DelegateSignature_Statics::NewProp_LevelIndex = { "LevelIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnLevelGenerated_Parms, LevelIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerated__DelegateSignature_Statics::NewProp_SectorIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerated__DelegateSignature_Statics::NewProp_LevelIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerated__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnLevelGenerated__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerated__DelegateSignature_Statics::_Script_RoughReality_eventOnLevelGenerated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerated__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerated__DelegateSignature_Statics::_Script_RoughReality_eventOnLevelGenerated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnLevelGenerated_DelegateWrapper(const FMulticastScriptDelegate& OnLevelGenerated, int32 SectorIndex, int32 LevelIndex)
{
	struct _Script_RoughReality_eventOnLevelGenerated_Parms
	{
		int32 SectorIndex;
		int32 LevelIndex;
	};
	_Script_RoughReality_eventOnLevelGenerated_Parms Parms;
	Parms.SectorIndex=SectorIndex;
	Parms.LevelIndex=LevelIndex;
	OnLevelGenerated.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnLevelGenerated

// Begin Delegate FOnLevelGenerationFailed
struct Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerationFailed__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnLevelGenerationFailed_Parms
	{
		FString ErrorMessage;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerationFailed__DelegateSignature_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnLevelGenerationFailed_Parms, ErrorMessage), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerationFailed__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerationFailed__DelegateSignature_Statics::NewProp_ErrorMessage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerationFailed__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerationFailed__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnLevelGenerationFailed__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerationFailed__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerationFailed__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerationFailed__DelegateSignature_Statics::_Script_RoughReality_eventOnLevelGenerationFailed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerationFailed__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerationFailed__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerationFailed__DelegateSignature_Statics::_Script_RoughReality_eventOnLevelGenerationFailed_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerationFailed__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerationFailed__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnLevelGenerationFailed_DelegateWrapper(const FMulticastScriptDelegate& OnLevelGenerationFailed, const FString& ErrorMessage)
{
	struct _Script_RoughReality_eventOnLevelGenerationFailed_Parms
	{
		FString ErrorMessage;
	};
	_Script_RoughReality_eventOnLevelGenerationFailed_Parms Parms;
	Parms.ErrorMessage=ErrorMessage;
	OnLevelGenerationFailed.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnLevelGenerationFailed

// Begin Enum ETileType
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ETileType;
static UEnum* ETileType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ETileType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ETileType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_RoughReality_ETileType, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("ETileType"));
	}
	return Z_Registration_Info_UEnum_ETileType.OuterSingleton;
}
template<> ROUGHREALITY_API UEnum* StaticEnum<ETileType>()
{
	return ETileType_StaticEnum();
}
struct Z_Construct_UEnum_RoughReality_ETileType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Boss.Name", "ETileType::Boss" },
		{ "Combat.Name", "ETileType::Combat" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
		{ "Shop.Name", "ETileType::Shop" },
		{ "Special.Name", "ETileType::Special" },
		{ "Start.Name", "ETileType::Start" },
		{ "Transition.Name", "ETileType::Transition" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ETileType::Start", (int64)ETileType::Start },
		{ "ETileType::Combat", (int64)ETileType::Combat },
		{ "ETileType::Shop", (int64)ETileType::Shop },
		{ "ETileType::Boss", (int64)ETileType::Boss },
		{ "ETileType::Transition", (int64)ETileType::Transition },
		{ "ETileType::Special", (int64)ETileType::Special },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_RoughReality_ETileType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	"ETileType",
	"ETileType",
	Z_Construct_UEnum_RoughReality_ETileType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_RoughReality_ETileType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_RoughReality_ETileType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_RoughReality_ETileType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_RoughReality_ETileType()
{
	if (!Z_Registration_Info_UEnum_ETileType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ETileType.InnerSingleton, Z_Construct_UEnum_RoughReality_ETileType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ETileType.InnerSingleton;
}
// End Enum ETileType

// Begin ScriptStruct FTileSpawnInfo
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_TileSpawnInfo;
class UScriptStruct* FTileSpawnInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_TileSpawnInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_TileSpawnInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FTileSpawnInfo, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("TileSpawnInfo"));
	}
	return Z_Registration_Info_UScriptStruct_TileSpawnInfo.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FTileSpawnInfo>()
{
	return FTileSpawnInfo::StaticStruct();
}
struct Z_Construct_UScriptStruct_FTileSpawnInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TileDefinition_MetaData[] = {
		{ "Category", "Tile" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "Category", "Tile" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "Category", "Tile" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TileType_MetaData[] = {
		{ "Category", "Tile" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsFixed_MetaData[] = {
		{ "Category", "Tile" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TileDefinition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FBytePropertyParams NewProp_TileType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TileType;
	static void NewProp_bIsFixed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsFixed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FTileSpawnInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FTileSpawnInfo_Statics::NewProp_TileDefinition = { "TileDefinition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTileSpawnInfo, TileDefinition), Z_Construct_UClass_UTileDefinitionDataAsset_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TileDefinition_MetaData), NewProp_TileDefinition_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTileSpawnInfo_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTileSpawnInfo, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTileSpawnInfo_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTileSpawnInfo, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FTileSpawnInfo_Statics::NewProp_TileType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FTileSpawnInfo_Statics::NewProp_TileType = { "TileType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTileSpawnInfo, TileType), Z_Construct_UEnum_RoughReality_ETileType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TileType_MetaData), NewProp_TileType_MetaData) }; // 3250710945
void Z_Construct_UScriptStruct_FTileSpawnInfo_Statics::NewProp_bIsFixed_SetBit(void* Obj)
{
	((FTileSpawnInfo*)Obj)->bIsFixed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FTileSpawnInfo_Statics::NewProp_bIsFixed = { "bIsFixed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FTileSpawnInfo), &Z_Construct_UScriptStruct_FTileSpawnInfo_Statics::NewProp_bIsFixed_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsFixed_MetaData), NewProp_bIsFixed_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FTileSpawnInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTileSpawnInfo_Statics::NewProp_TileDefinition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTileSpawnInfo_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTileSpawnInfo_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTileSpawnInfo_Statics::NewProp_TileType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTileSpawnInfo_Statics::NewProp_TileType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTileSpawnInfo_Statics::NewProp_bIsFixed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTileSpawnInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FTileSpawnInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	&NewStructOps,
	"TileSpawnInfo",
	Z_Construct_UScriptStruct_FTileSpawnInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTileSpawnInfo_Statics::PropPointers),
	sizeof(FTileSpawnInfo),
	alignof(FTileSpawnInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTileSpawnInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FTileSpawnInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FTileSpawnInfo()
{
	if (!Z_Registration_Info_UScriptStruct_TileSpawnInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_TileSpawnInfo.InnerSingleton, Z_Construct_UScriptStruct_FTileSpawnInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_TileSpawnInfo.InnerSingleton;
}
// End ScriptStruct FTileSpawnInfo

// Begin ScriptStruct FLevelSectorConfiguration
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_LevelSectorConfiguration;
class UScriptStruct* FLevelSectorConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_LevelSectorConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_LevelSectorConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FLevelSectorConfiguration, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("LevelSectorConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_LevelSectorConfiguration.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FLevelSectorConfiguration>()
{
	return FLevelSectorConfiguration::StaticStruct();
}
struct Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SectorName_MetaData[] = {
		{ "Category", "Sector" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SectorTags_MetaData[] = {
		{ "Category", "Sector" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinTiles_MetaData[] = {
		{ "Category", "Sector" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxTiles_MetaData[] = {
		{ "Category", "Sector" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AvailableTiles_MetaData[] = {
		{ "Category", "Sector" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FixedTiles_MetaData[] = {
		{ "Category", "Sector" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TileSpacing_MetaData[] = {
		{ "Category", "Sector" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_SectorName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SectorTags;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinTiles;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxTiles;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AvailableTiles_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AvailableTiles;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FixedTiles_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_FixedTiles;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TileSpacing;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FLevelSectorConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::NewProp_SectorName = { "SectorName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLevelSectorConfiguration, SectorName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SectorName_MetaData), NewProp_SectorName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::NewProp_SectorTags = { "SectorTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLevelSectorConfiguration, SectorTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SectorTags_MetaData), NewProp_SectorTags_MetaData) }; // 3352185621
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::NewProp_MinTiles = { "MinTiles", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLevelSectorConfiguration, MinTiles), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinTiles_MetaData), NewProp_MinTiles_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::NewProp_MaxTiles = { "MaxTiles", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLevelSectorConfiguration, MaxTiles), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxTiles_MetaData), NewProp_MaxTiles_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::NewProp_AvailableTiles_Inner = { "AvailableTiles", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UTileDefinitionDataAsset_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::NewProp_AvailableTiles = { "AvailableTiles", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLevelSectorConfiguration, AvailableTiles), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AvailableTiles_MetaData), NewProp_AvailableTiles_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::NewProp_FixedTiles_Inner = { "FixedTiles", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTileSpawnInfo, METADATA_PARAMS(0, nullptr) }; // 232049283
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::NewProp_FixedTiles = { "FixedTiles", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLevelSectorConfiguration, FixedTiles), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FixedTiles_MetaData), NewProp_FixedTiles_MetaData) }; // 232049283
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::NewProp_TileSpacing = { "TileSpacing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLevelSectorConfiguration, TileSpacing), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TileSpacing_MetaData), NewProp_TileSpacing_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::NewProp_SectorName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::NewProp_SectorTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::NewProp_MinTiles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::NewProp_MaxTiles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::NewProp_AvailableTiles_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::NewProp_AvailableTiles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::NewProp_FixedTiles_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::NewProp_FixedTiles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::NewProp_TileSpacing,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	&NewStructOps,
	"LevelSectorConfiguration",
	Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::PropPointers),
	sizeof(FLevelSectorConfiguration),
	alignof(FLevelSectorConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FLevelSectorConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_LevelSectorConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_LevelSectorConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_LevelSectorConfiguration.InnerSingleton;
}
// End ScriptStruct FLevelSectorConfiguration

// Begin Class AProceduralLevelBuilder Function CalculateTilePosition
struct Z_Construct_UFunction_AProceduralLevelBuilder_CalculateTilePosition_Statics
{
	struct ProceduralLevelBuilder_eventCalculateTilePosition_Parms
	{
		int32 TileIndex;
		float Spacing;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Utility Functions */" },
#endif
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility Functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TileIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Spacing;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AProceduralLevelBuilder_CalculateTilePosition_Statics::NewProp_TileIndex = { "TileIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralLevelBuilder_eventCalculateTilePosition_Parms, TileIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AProceduralLevelBuilder_CalculateTilePosition_Statics::NewProp_Spacing = { "Spacing", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralLevelBuilder_eventCalculateTilePosition_Parms, Spacing), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AProceduralLevelBuilder_CalculateTilePosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralLevelBuilder_eventCalculateTilePosition_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralLevelBuilder_CalculateTilePosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralLevelBuilder_CalculateTilePosition_Statics::NewProp_TileIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralLevelBuilder_CalculateTilePosition_Statics::NewProp_Spacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralLevelBuilder_CalculateTilePosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_CalculateTilePosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralLevelBuilder_CalculateTilePosition_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProceduralLevelBuilder, nullptr, "CalculateTilePosition", nullptr, nullptr, Z_Construct_UFunction_AProceduralLevelBuilder_CalculateTilePosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_CalculateTilePosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralLevelBuilder_CalculateTilePosition_Statics::ProceduralLevelBuilder_eventCalculateTilePosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_CalculateTilePosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralLevelBuilder_CalculateTilePosition_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProceduralLevelBuilder_CalculateTilePosition_Statics::ProceduralLevelBuilder_eventCalculateTilePosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralLevelBuilder_CalculateTilePosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralLevelBuilder_CalculateTilePosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralLevelBuilder::execCalculateTilePosition)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_TileIndex);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Spacing);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->CalculateTilePosition(Z_Param_TileIndex,Z_Param_Spacing);
	P_NATIVE_END;
}
// End Class AProceduralLevelBuilder Function CalculateTilePosition

// Begin Class AProceduralLevelBuilder Function ClearCurrentLevel
struct Z_Construct_UFunction_AProceduralLevelBuilder_ClearCurrentLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Level Generation" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralLevelBuilder_ClearCurrentLevel_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProceduralLevelBuilder, nullptr, "ClearCurrentLevel", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_ClearCurrentLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralLevelBuilder_ClearCurrentLevel_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AProceduralLevelBuilder_ClearCurrentLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralLevelBuilder_ClearCurrentLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralLevelBuilder::execClearCurrentLevel)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearCurrentLevel();
	P_NATIVE_END;
}
// End Class AProceduralLevelBuilder Function ClearCurrentLevel

// Begin Class AProceduralLevelBuilder Function DestroyTile
struct Z_Construct_UFunction_AProceduralLevelBuilder_DestroyTile_Statics
{
	struct ProceduralLevelBuilder_eventDestroyTile_Parms
	{
		ALevelTile* Tile;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tile Management" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Tile;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AProceduralLevelBuilder_DestroyTile_Statics::NewProp_Tile = { "Tile", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralLevelBuilder_eventDestroyTile_Parms, Tile), Z_Construct_UClass_ALevelTile_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralLevelBuilder_DestroyTile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralLevelBuilder_DestroyTile_Statics::NewProp_Tile,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_DestroyTile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralLevelBuilder_DestroyTile_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProceduralLevelBuilder, nullptr, "DestroyTile", nullptr, nullptr, Z_Construct_UFunction_AProceduralLevelBuilder_DestroyTile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_DestroyTile_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralLevelBuilder_DestroyTile_Statics::ProceduralLevelBuilder_eventDestroyTile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_DestroyTile_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralLevelBuilder_DestroyTile_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProceduralLevelBuilder_DestroyTile_Statics::ProceduralLevelBuilder_eventDestroyTile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralLevelBuilder_DestroyTile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralLevelBuilder_DestroyTile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralLevelBuilder::execDestroyTile)
{
	P_GET_OBJECT(ALevelTile,Z_Param_Tile);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DestroyTile(Z_Param_Tile);
	P_NATIVE_END;
}
// End Class AProceduralLevelBuilder Function DestroyTile

// Begin Class AProceduralLevelBuilder Function GenerateLevel
struct Z_Construct_UFunction_AProceduralLevelBuilder_GenerateLevel_Statics
{
	struct ProceduralLevelBuilder_eventGenerateLevel_Parms
	{
		int32 SectorIndex;
		int32 LevelIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Level Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Main Generation Functions */" },
#endif
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Main Generation Functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SectorIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LevelIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AProceduralLevelBuilder_GenerateLevel_Statics::NewProp_SectorIndex = { "SectorIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralLevelBuilder_eventGenerateLevel_Parms, SectorIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AProceduralLevelBuilder_GenerateLevel_Statics::NewProp_LevelIndex = { "LevelIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralLevelBuilder_eventGenerateLevel_Parms, LevelIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AProceduralLevelBuilder_GenerateLevel_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ProceduralLevelBuilder_eventGenerateLevel_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AProceduralLevelBuilder_GenerateLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ProceduralLevelBuilder_eventGenerateLevel_Parms), &Z_Construct_UFunction_AProceduralLevelBuilder_GenerateLevel_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralLevelBuilder_GenerateLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralLevelBuilder_GenerateLevel_Statics::NewProp_SectorIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralLevelBuilder_GenerateLevel_Statics::NewProp_LevelIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralLevelBuilder_GenerateLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_GenerateLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralLevelBuilder_GenerateLevel_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProceduralLevelBuilder, nullptr, "GenerateLevel", nullptr, nullptr, Z_Construct_UFunction_AProceduralLevelBuilder_GenerateLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_GenerateLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralLevelBuilder_GenerateLevel_Statics::ProceduralLevelBuilder_eventGenerateLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_GenerateLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralLevelBuilder_GenerateLevel_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProceduralLevelBuilder_GenerateLevel_Statics::ProceduralLevelBuilder_eventGenerateLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralLevelBuilder_GenerateLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralLevelBuilder_GenerateLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralLevelBuilder::execGenerateLevel)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SectorIndex);
	P_GET_PROPERTY(FIntProperty,Z_Param_LevelIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GenerateLevel(Z_Param_SectorIndex,Z_Param_LevelIndex);
	P_NATIVE_END;
}
// End Class AProceduralLevelBuilder Function GenerateLevel

// Begin Class AProceduralLevelBuilder Function GetCurrentRandomSeed
struct Z_Construct_UFunction_AProceduralLevelBuilder_GetCurrentRandomSeed_Statics
{
	struct ProceduralLevelBuilder_eventGetCurrentRandomSeed_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AProceduralLevelBuilder_GetCurrentRandomSeed_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralLevelBuilder_eventGetCurrentRandomSeed_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralLevelBuilder_GetCurrentRandomSeed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralLevelBuilder_GetCurrentRandomSeed_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_GetCurrentRandomSeed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralLevelBuilder_GetCurrentRandomSeed_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProceduralLevelBuilder, nullptr, "GetCurrentRandomSeed", nullptr, nullptr, Z_Construct_UFunction_AProceduralLevelBuilder_GetCurrentRandomSeed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_GetCurrentRandomSeed_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralLevelBuilder_GetCurrentRandomSeed_Statics::ProceduralLevelBuilder_eventGetCurrentRandomSeed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_GetCurrentRandomSeed_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralLevelBuilder_GetCurrentRandomSeed_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProceduralLevelBuilder_GetCurrentRandomSeed_Statics::ProceduralLevelBuilder_eventGetCurrentRandomSeed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralLevelBuilder_GetCurrentRandomSeed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralLevelBuilder_GetCurrentRandomSeed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralLevelBuilder::execGetCurrentRandomSeed)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetCurrentRandomSeed();
	P_NATIVE_END;
}
// End Class AProceduralLevelBuilder Function GetCurrentRandomSeed

// Begin Class AProceduralLevelBuilder Function GetSectorConfiguration
struct Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorConfiguration_Statics
{
	struct ProceduralLevelBuilder_eventGetSectorConfiguration_Parms
	{
		int32 SectorIndex;
		FLevelSectorConfiguration ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sector Management" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SectorIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorConfiguration_Statics::NewProp_SectorIndex = { "SectorIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralLevelBuilder_eventGetSectorConfiguration_Parms, SectorIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralLevelBuilder_eventGetSectorConfiguration_Parms, ReturnValue), Z_Construct_UScriptStruct_FLevelSectorConfiguration, METADATA_PARAMS(0, nullptr) }; // 4217237295
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorConfiguration_Statics::NewProp_SectorIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorConfiguration_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProceduralLevelBuilder, nullptr, "GetSectorConfiguration", nullptr, nullptr, Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorConfiguration_Statics::ProceduralLevelBuilder_eventGetSectorConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorConfiguration_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorConfiguration_Statics::ProceduralLevelBuilder_eventGetSectorConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralLevelBuilder::execGetSectorConfiguration)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SectorIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FLevelSectorConfiguration*)Z_Param__Result=P_THIS->GetSectorConfiguration(Z_Param_SectorIndex);
	P_NATIVE_END;
}
// End Class AProceduralLevelBuilder Function GetSectorConfiguration

// Begin Class AProceduralLevelBuilder Function GetSectorCount
struct Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorCount_Statics
{
	struct ProceduralLevelBuilder_eventGetSectorCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sector Management" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralLevelBuilder_eventGetSectorCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorCount_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProceduralLevelBuilder, nullptr, "GetSectorCount", nullptr, nullptr, Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorCount_Statics::ProceduralLevelBuilder_eventGetSectorCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorCount_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorCount_Statics::ProceduralLevelBuilder_eventGetSectorCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralLevelBuilder::execGetSectorCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetSectorCount();
	P_NATIVE_END;
}
// End Class AProceduralLevelBuilder Function GetSectorCount

// Begin Class AProceduralLevelBuilder Function GetTilesByType
struct Z_Construct_UFunction_AProceduralLevelBuilder_GetTilesByType_Statics
{
	struct ProceduralLevelBuilder_eventGetTilesByType_Parms
	{
		ETileType TileType;
		TArray<ALevelTile*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tile Management" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TileType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TileType;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AProceduralLevelBuilder_GetTilesByType_Statics::NewProp_TileType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AProceduralLevelBuilder_GetTilesByType_Statics::NewProp_TileType = { "TileType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralLevelBuilder_eventGetTilesByType_Parms, TileType), Z_Construct_UEnum_RoughReality_ETileType, METADATA_PARAMS(0, nullptr) }; // 3250710945
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AProceduralLevelBuilder_GetTilesByType_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ALevelTile_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AProceduralLevelBuilder_GetTilesByType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralLevelBuilder_eventGetTilesByType_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralLevelBuilder_GetTilesByType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralLevelBuilder_GetTilesByType_Statics::NewProp_TileType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralLevelBuilder_GetTilesByType_Statics::NewProp_TileType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralLevelBuilder_GetTilesByType_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralLevelBuilder_GetTilesByType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_GetTilesByType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralLevelBuilder_GetTilesByType_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProceduralLevelBuilder, nullptr, "GetTilesByType", nullptr, nullptr, Z_Construct_UFunction_AProceduralLevelBuilder_GetTilesByType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_GetTilesByType_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralLevelBuilder_GetTilesByType_Statics::ProceduralLevelBuilder_eventGetTilesByType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_GetTilesByType_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralLevelBuilder_GetTilesByType_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProceduralLevelBuilder_GetTilesByType_Statics::ProceduralLevelBuilder_eventGetTilesByType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralLevelBuilder_GetTilesByType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralLevelBuilder_GetTilesByType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralLevelBuilder::execGetTilesByType)
{
	P_GET_ENUM(ETileType,Z_Param_TileType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<ALevelTile*>*)Z_Param__Result=P_THIS->GetTilesByType(ETileType(Z_Param_TileType));
	P_NATIVE_END;
}
// End Class AProceduralLevelBuilder Function GetTilesByType

// Begin Class AProceduralLevelBuilder Function IsSectorValid
struct Z_Construct_UFunction_AProceduralLevelBuilder_IsSectorValid_Statics
{
	struct ProceduralLevelBuilder_eventIsSectorValid_Parms
	{
		int32 SectorIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sector Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sector Management */" },
#endif
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sector Management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SectorIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AProceduralLevelBuilder_IsSectorValid_Statics::NewProp_SectorIndex = { "SectorIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralLevelBuilder_eventIsSectorValid_Parms, SectorIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AProceduralLevelBuilder_IsSectorValid_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ProceduralLevelBuilder_eventIsSectorValid_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AProceduralLevelBuilder_IsSectorValid_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ProceduralLevelBuilder_eventIsSectorValid_Parms), &Z_Construct_UFunction_AProceduralLevelBuilder_IsSectorValid_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralLevelBuilder_IsSectorValid_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralLevelBuilder_IsSectorValid_Statics::NewProp_SectorIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralLevelBuilder_IsSectorValid_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_IsSectorValid_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralLevelBuilder_IsSectorValid_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProceduralLevelBuilder, nullptr, "IsSectorValid", nullptr, nullptr, Z_Construct_UFunction_AProceduralLevelBuilder_IsSectorValid_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_IsSectorValid_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralLevelBuilder_IsSectorValid_Statics::ProceduralLevelBuilder_eventIsSectorValid_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_IsSectorValid_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralLevelBuilder_IsSectorValid_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProceduralLevelBuilder_IsSectorValid_Statics::ProceduralLevelBuilder_eventIsSectorValid_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralLevelBuilder_IsSectorValid()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralLevelBuilder_IsSectorValid_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralLevelBuilder::execIsSectorValid)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SectorIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsSectorValid(Z_Param_SectorIndex);
	P_NATIVE_END;
}
// End Class AProceduralLevelBuilder Function IsSectorValid

// Begin Class AProceduralLevelBuilder Function RegenerateCurrentLevel
struct Z_Construct_UFunction_AProceduralLevelBuilder_RegenerateCurrentLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Level Generation" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralLevelBuilder_RegenerateCurrentLevel_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProceduralLevelBuilder, nullptr, "RegenerateCurrentLevel", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_RegenerateCurrentLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralLevelBuilder_RegenerateCurrentLevel_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AProceduralLevelBuilder_RegenerateCurrentLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralLevelBuilder_RegenerateCurrentLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralLevelBuilder::execRegenerateCurrentLevel)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegenerateCurrentLevel();
	P_NATIVE_END;
}
// End Class AProceduralLevelBuilder Function RegenerateCurrentLevel

// Begin Class AProceduralLevelBuilder Function SetRandomSeed
struct Z_Construct_UFunction_AProceduralLevelBuilder_SetRandomSeed_Statics
{
	struct ProceduralLevelBuilder_eventSetRandomSeed_Parms
	{
		int32 NewSeed;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewSeed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AProceduralLevelBuilder_SetRandomSeed_Statics::NewProp_NewSeed = { "NewSeed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralLevelBuilder_eventSetRandomSeed_Parms, NewSeed), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralLevelBuilder_SetRandomSeed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralLevelBuilder_SetRandomSeed_Statics::NewProp_NewSeed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_SetRandomSeed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralLevelBuilder_SetRandomSeed_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProceduralLevelBuilder, nullptr, "SetRandomSeed", nullptr, nullptr, Z_Construct_UFunction_AProceduralLevelBuilder_SetRandomSeed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_SetRandomSeed_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralLevelBuilder_SetRandomSeed_Statics::ProceduralLevelBuilder_eventSetRandomSeed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_SetRandomSeed_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralLevelBuilder_SetRandomSeed_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProceduralLevelBuilder_SetRandomSeed_Statics::ProceduralLevelBuilder_eventSetRandomSeed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralLevelBuilder_SetRandomSeed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralLevelBuilder_SetRandomSeed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralLevelBuilder::execSetRandomSeed)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_NewSeed);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetRandomSeed(Z_Param_NewSeed);
	P_NATIVE_END;
}
// End Class AProceduralLevelBuilder Function SetRandomSeed

// Begin Class AProceduralLevelBuilder Function SpawnTile
struct Z_Construct_UFunction_AProceduralLevelBuilder_SpawnTile_Statics
{
	struct ProceduralLevelBuilder_eventSpawnTile_Parms
	{
		UTileDefinitionDataAsset* TileDefinition;
		FVector Location;
		FRotator Rotation;
		ALevelTile* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tile Management" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tile Management */" },
#endif
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tile Management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TileDefinition;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AProceduralLevelBuilder_SpawnTile_Statics::NewProp_TileDefinition = { "TileDefinition", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralLevelBuilder_eventSpawnTile_Parms, TileDefinition), Z_Construct_UClass_UTileDefinitionDataAsset_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AProceduralLevelBuilder_SpawnTile_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralLevelBuilder_eventSpawnTile_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AProceduralLevelBuilder_SpawnTile_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralLevelBuilder_eventSpawnTile_Parms, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AProceduralLevelBuilder_SpawnTile_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralLevelBuilder_eventSpawnTile_Parms, ReturnValue), Z_Construct_UClass_ALevelTile_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralLevelBuilder_SpawnTile_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralLevelBuilder_SpawnTile_Statics::NewProp_TileDefinition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralLevelBuilder_SpawnTile_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralLevelBuilder_SpawnTile_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralLevelBuilder_SpawnTile_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_SpawnTile_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralLevelBuilder_SpawnTile_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProceduralLevelBuilder, nullptr, "SpawnTile", nullptr, nullptr, Z_Construct_UFunction_AProceduralLevelBuilder_SpawnTile_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_SpawnTile_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralLevelBuilder_SpawnTile_Statics::ProceduralLevelBuilder_eventSpawnTile_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_SpawnTile_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralLevelBuilder_SpawnTile_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProceduralLevelBuilder_SpawnTile_Statics::ProceduralLevelBuilder_eventSpawnTile_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralLevelBuilder_SpawnTile()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralLevelBuilder_SpawnTile_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralLevelBuilder::execSpawnTile)
{
	P_GET_OBJECT(UTileDefinitionDataAsset,Z_Param_TileDefinition);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FRotator,Z_Param_Out_Rotation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ALevelTile**)Z_Param__Result=P_THIS->SpawnTile(Z_Param_TileDefinition,Z_Param_Out_Location,Z_Param_Out_Rotation);
	P_NATIVE_END;
}
// End Class AProceduralLevelBuilder Function SpawnTile

// Begin Class AProceduralLevelBuilder Function ValidateLevel
struct Z_Construct_UFunction_AProceduralLevelBuilder_ValidateLevel_Statics
{
	struct ProceduralLevelBuilder_eventValidateLevel_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Validation */" },
#endif
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validation" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AProceduralLevelBuilder_ValidateLevel_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ProceduralLevelBuilder_eventValidateLevel_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AProceduralLevelBuilder_ValidateLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ProceduralLevelBuilder_eventValidateLevel_Parms), &Z_Construct_UFunction_AProceduralLevelBuilder_ValidateLevel_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralLevelBuilder_ValidateLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralLevelBuilder_ValidateLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_ValidateLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralLevelBuilder_ValidateLevel_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProceduralLevelBuilder, nullptr, "ValidateLevel", nullptr, nullptr, Z_Construct_UFunction_AProceduralLevelBuilder_ValidateLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_ValidateLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralLevelBuilder_ValidateLevel_Statics::ProceduralLevelBuilder_eventValidateLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_ValidateLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralLevelBuilder_ValidateLevel_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProceduralLevelBuilder_ValidateLevel_Statics::ProceduralLevelBuilder_eventValidateLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralLevelBuilder_ValidateLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralLevelBuilder_ValidateLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralLevelBuilder::execValidateLevel)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateLevel();
	P_NATIVE_END;
}
// End Class AProceduralLevelBuilder Function ValidateLevel

// Begin Class AProceduralLevelBuilder Function ValidateSectorConfiguration
struct Z_Construct_UFunction_AProceduralLevelBuilder_ValidateSectorConfiguration_Statics
{
	struct ProceduralLevelBuilder_eventValidateSectorConfiguration_Parms
	{
		FLevelSectorConfiguration SectorConfig;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SectorConfig_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SectorConfig;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AProceduralLevelBuilder_ValidateSectorConfiguration_Statics::NewProp_SectorConfig = { "SectorConfig", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProceduralLevelBuilder_eventValidateSectorConfiguration_Parms, SectorConfig), Z_Construct_UScriptStruct_FLevelSectorConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SectorConfig_MetaData), NewProp_SectorConfig_MetaData) }; // 4217237295
void Z_Construct_UFunction_AProceduralLevelBuilder_ValidateSectorConfiguration_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ProceduralLevelBuilder_eventValidateSectorConfiguration_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AProceduralLevelBuilder_ValidateSectorConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ProceduralLevelBuilder_eventValidateSectorConfiguration_Parms), &Z_Construct_UFunction_AProceduralLevelBuilder_ValidateSectorConfiguration_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProceduralLevelBuilder_ValidateSectorConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralLevelBuilder_ValidateSectorConfiguration_Statics::NewProp_SectorConfig,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProceduralLevelBuilder_ValidateSectorConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_ValidateSectorConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProceduralLevelBuilder_ValidateSectorConfiguration_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProceduralLevelBuilder, nullptr, "ValidateSectorConfiguration", nullptr, nullptr, Z_Construct_UFunction_AProceduralLevelBuilder_ValidateSectorConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_ValidateSectorConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProceduralLevelBuilder_ValidateSectorConfiguration_Statics::ProceduralLevelBuilder_eventValidateSectorConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProceduralLevelBuilder_ValidateSectorConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProceduralLevelBuilder_ValidateSectorConfiguration_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProceduralLevelBuilder_ValidateSectorConfiguration_Statics::ProceduralLevelBuilder_eventValidateSectorConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProceduralLevelBuilder_ValidateSectorConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProceduralLevelBuilder_ValidateSectorConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProceduralLevelBuilder::execValidateSectorConfiguration)
{
	P_GET_STRUCT_REF(FLevelSectorConfiguration,Z_Param_Out_SectorConfig);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateSectorConfiguration(Z_Param_Out_SectorConfig);
	P_NATIVE_END;
}
// End Class AProceduralLevelBuilder Function ValidateSectorConfiguration

// Begin Class AProceduralLevelBuilder
void AProceduralLevelBuilder::StaticRegisterNativesAProceduralLevelBuilder()
{
	UClass* Class = AProceduralLevelBuilder::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CalculateTilePosition", &AProceduralLevelBuilder::execCalculateTilePosition },
		{ "ClearCurrentLevel", &AProceduralLevelBuilder::execClearCurrentLevel },
		{ "DestroyTile", &AProceduralLevelBuilder::execDestroyTile },
		{ "GenerateLevel", &AProceduralLevelBuilder::execGenerateLevel },
		{ "GetCurrentRandomSeed", &AProceduralLevelBuilder::execGetCurrentRandomSeed },
		{ "GetSectorConfiguration", &AProceduralLevelBuilder::execGetSectorConfiguration },
		{ "GetSectorCount", &AProceduralLevelBuilder::execGetSectorCount },
		{ "GetTilesByType", &AProceduralLevelBuilder::execGetTilesByType },
		{ "IsSectorValid", &AProceduralLevelBuilder::execIsSectorValid },
		{ "RegenerateCurrentLevel", &AProceduralLevelBuilder::execRegenerateCurrentLevel },
		{ "SetRandomSeed", &AProceduralLevelBuilder::execSetRandomSeed },
		{ "SpawnTile", &AProceduralLevelBuilder::execSpawnTile },
		{ "ValidateLevel", &AProceduralLevelBuilder::execValidateLevel },
		{ "ValidateSectorConfiguration", &AProceduralLevelBuilder::execValidateSectorConfiguration },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(AProceduralLevelBuilder);
UClass* Z_Construct_UClass_AProceduralLevelBuilder_NoRegister()
{
	return AProceduralLevelBuilder::StaticClass();
}
struct Z_Construct_UClass_AProceduralLevelBuilder_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Procedural Level Builder for Rough Reality\n * Generates levels using modular tiles with fixed landmarks\n */" },
#endif
		{ "IncludePath", "LevelGeneration/ProceduralLevelBuilder.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Procedural Level Builder for Rough Reality\nGenerates levels using modular tiles with fixed landmarks" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SectorConfigurations_MetaData[] = {
		{ "Category", "Level Generation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sector Configurations */" },
#endif
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sector Configurations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentSector_MetaData[] = {
		{ "Category", "Level State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Current Level State */" },
#endif
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current Level State" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentLevel_MetaData[] = {
		{ "Category", "Level State" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnedTiles_MetaData[] = {
		{ "Category", "Level State" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RandomSeed_MetaData[] = {
		{ "Category", "Generation Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Generation Settings */" },
#endif
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Generation Settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseRandomSeed_MetaData[] = {
		{ "Category", "Generation Settings" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultTileSpacing_MetaData[] = {
		{ "Category", "Generation Settings" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LevelOrigin_MetaData[] = {
		{ "Category", "Generation Settings" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLevelGenerated_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Events */" },
#endif
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLevelGenerationFailed_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "LevelGeneration/ProceduralLevelBuilder.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SectorConfigurations_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SectorConfigurations;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentSector;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentLevel;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SpawnedTiles_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SpawnedTiles;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RandomSeed;
	static void NewProp_bUseRandomSeed_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseRandomSeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultTileSpacing;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LevelOrigin;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLevelGenerated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLevelGenerationFailed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AProceduralLevelBuilder_CalculateTilePosition, "CalculateTilePosition" }, // 2027704529
		{ &Z_Construct_UFunction_AProceduralLevelBuilder_ClearCurrentLevel, "ClearCurrentLevel" }, // 1744829851
		{ &Z_Construct_UFunction_AProceduralLevelBuilder_DestroyTile, "DestroyTile" }, // 3752550125
		{ &Z_Construct_UFunction_AProceduralLevelBuilder_GenerateLevel, "GenerateLevel" }, // 1503925709
		{ &Z_Construct_UFunction_AProceduralLevelBuilder_GetCurrentRandomSeed, "GetCurrentRandomSeed" }, // 74198834
		{ &Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorConfiguration, "GetSectorConfiguration" }, // 2340853736
		{ &Z_Construct_UFunction_AProceduralLevelBuilder_GetSectorCount, "GetSectorCount" }, // 138041419
		{ &Z_Construct_UFunction_AProceduralLevelBuilder_GetTilesByType, "GetTilesByType" }, // 2579958828
		{ &Z_Construct_UFunction_AProceduralLevelBuilder_IsSectorValid, "IsSectorValid" }, // 3812220888
		{ &Z_Construct_UFunction_AProceduralLevelBuilder_RegenerateCurrentLevel, "RegenerateCurrentLevel" }, // 2694173654
		{ &Z_Construct_UFunction_AProceduralLevelBuilder_SetRandomSeed, "SetRandomSeed" }, // 1936970484
		{ &Z_Construct_UFunction_AProceduralLevelBuilder_SpawnTile, "SpawnTile" }, // 2849241811
		{ &Z_Construct_UFunction_AProceduralLevelBuilder_ValidateLevel, "ValidateLevel" }, // 3429328352
		{ &Z_Construct_UFunction_AProceduralLevelBuilder_ValidateSectorConfiguration, "ValidateSectorConfiguration" }, // 1774367477
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AProceduralLevelBuilder>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_SectorConfigurations_Inner = { "SectorConfigurations", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FLevelSectorConfiguration, METADATA_PARAMS(0, nullptr) }; // 4217237295
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_SectorConfigurations = { "SectorConfigurations", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralLevelBuilder, SectorConfigurations), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SectorConfigurations_MetaData), NewProp_SectorConfigurations_MetaData) }; // 4217237295
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_CurrentSector = { "CurrentSector", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralLevelBuilder, CurrentSector), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentSector_MetaData), NewProp_CurrentSector_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_CurrentLevel = { "CurrentLevel", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralLevelBuilder, CurrentLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentLevel_MetaData), NewProp_CurrentLevel_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_SpawnedTiles_Inner = { "SpawnedTiles", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_ALevelTile_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_SpawnedTiles = { "SpawnedTiles", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralLevelBuilder, SpawnedTiles), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnedTiles_MetaData), NewProp_SpawnedTiles_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_RandomSeed = { "RandomSeed", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralLevelBuilder, RandomSeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RandomSeed_MetaData), NewProp_RandomSeed_MetaData) };
void Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_bUseRandomSeed_SetBit(void* Obj)
{
	((AProceduralLevelBuilder*)Obj)->bUseRandomSeed = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_bUseRandomSeed = { "bUseRandomSeed", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AProceduralLevelBuilder), &Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_bUseRandomSeed_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseRandomSeed_MetaData), NewProp_bUseRandomSeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_DefaultTileSpacing = { "DefaultTileSpacing", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralLevelBuilder, DefaultTileSpacing), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultTileSpacing_MetaData), NewProp_DefaultTileSpacing_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_LevelOrigin = { "LevelOrigin", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralLevelBuilder, LevelOrigin), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LevelOrigin_MetaData), NewProp_LevelOrigin_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_OnLevelGenerated = { "OnLevelGenerated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralLevelBuilder, OnLevelGenerated), Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLevelGenerated_MetaData), NewProp_OnLevelGenerated_MetaData) }; // 3324027332
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_OnLevelGenerationFailed = { "OnLevelGenerationFailed", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProceduralLevelBuilder, OnLevelGenerationFailed), Z_Construct_UDelegateFunction_RoughReality_OnLevelGenerationFailed__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLevelGenerationFailed_MetaData), NewProp_OnLevelGenerationFailed_MetaData) }; // 41989766
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AProceduralLevelBuilder_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_SectorConfigurations_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_SectorConfigurations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_CurrentSector,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_CurrentLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_SpawnedTiles_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_SpawnedTiles,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_RandomSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_bUseRandomSeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_DefaultTileSpacing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_LevelOrigin,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_OnLevelGenerated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProceduralLevelBuilder_Statics::NewProp_OnLevelGenerationFailed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AProceduralLevelBuilder_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AProceduralLevelBuilder_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AProceduralLevelBuilder_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AProceduralLevelBuilder_Statics::ClassParams = {
	&AProceduralLevelBuilder::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AProceduralLevelBuilder_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AProceduralLevelBuilder_Statics::PropPointers),
	0,
	0x009000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AProceduralLevelBuilder_Statics::Class_MetaDataParams), Z_Construct_UClass_AProceduralLevelBuilder_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AProceduralLevelBuilder()
{
	if (!Z_Registration_Info_UClass_AProceduralLevelBuilder.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AProceduralLevelBuilder.OuterSingleton, Z_Construct_UClass_AProceduralLevelBuilder_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AProceduralLevelBuilder.OuterSingleton;
}
template<> ROUGHREALITY_API UClass* StaticClass<AProceduralLevelBuilder>()
{
	return AProceduralLevelBuilder::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AProceduralLevelBuilder);
AProceduralLevelBuilder::~AProceduralLevelBuilder() {}
// End Class AProceduralLevelBuilder

// Begin Registration
struct Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_LevelGeneration_ProceduralLevelBuilder_h_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ETileType_StaticEnum, TEXT("ETileType"), &Z_Registration_Info_UEnum_ETileType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3250710945U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FTileSpawnInfo::StaticStruct, Z_Construct_UScriptStruct_FTileSpawnInfo_Statics::NewStructOps, TEXT("TileSpawnInfo"), &Z_Registration_Info_UScriptStruct_TileSpawnInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FTileSpawnInfo), 232049283U) },
		{ FLevelSectorConfiguration::StaticStruct, Z_Construct_UScriptStruct_FLevelSectorConfiguration_Statics::NewStructOps, TEXT("LevelSectorConfiguration"), &Z_Registration_Info_UScriptStruct_LevelSectorConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FLevelSectorConfiguration), 4217237295U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AProceduralLevelBuilder, AProceduralLevelBuilder::StaticClass, TEXT("AProceduralLevelBuilder"), &Z_Registration_Info_UClass_AProceduralLevelBuilder, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AProceduralLevelBuilder), 571823188U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_LevelGeneration_ProceduralLevelBuilder_h_635865415(TEXT("/Script/RoughReality"),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_LevelGeneration_ProceduralLevelBuilder_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_LevelGeneration_ProceduralLevelBuilder_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_LevelGeneration_ProceduralLevelBuilder_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_LevelGeneration_ProceduralLevelBuilder_h_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_LevelGeneration_ProceduralLevelBuilder_h_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_LevelGeneration_ProceduralLevelBuilder_h_Statics::EnumInfo));
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
