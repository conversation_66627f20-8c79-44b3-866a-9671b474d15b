// Copyright Epic Games, Inc. All Rights Reserved.

#include "VisualEffectsManager.h"
#include "Engine/World.h"
#include "Components/PostProcessComponent.h"
#include "Materials/MaterialParameterCollection.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetMaterialLibrary.h"
#include "EngineUtils.h"

// Static member definitions
AVisualEffectsManager* AVisualEffectsManager::Instance = nullptr;

const FName AVisualEffectsManager::PARAM_BULLET_TIME_INTENSITY = FName("BulletTimeIntensity");
const FName AVisualEffectsManager::PARAM_DAMAGE_EFFECT = FName("DamageEffect");
const FName AVisualEffectsManager::PARAM_SECTOR_TINT = FName("SectorTint");
const FName AVisualEffectsManager::PARAM_TIME_SCALE = FName("TimeScale");
const FName AVisualEffectsManager::PARAM_GAME_TIME = FName("GameTime");

AVisualEffectsManager::AVisualEffectsManager()
{
	PrimaryActorTick.bCanEverTick = true;
	PrimaryActorTick.TickInterval = 0.016f; // 60 FPS

	// Create post process component
	PostProcessComponent = CreateDefaultSubobject<UPostProcessComponent>(TEXT("PostProcessComponent"));
	RootComponent = PostProcessComponent;

	// Initialize state
	CurrentVisualState = EGameVisualState::Normal;
	bIsTransitioning = false;
	DefaultTransitionDuration = 0.5f;
	CurrentEffectQuality = 2;
	bLODSystemEnabled = true;

	// Initialize transition state
	TransitionProgress = 0.0f;
	TransitionDuration = 0.0f;
	TargetVisualState = EGameVisualState::Normal;

	// Initialize effect timers
	DamageEffectTimer = 0.0f;
	DamageEffectDuration = 0.0f;
	DamageEffectIntensity = 0.0f;
	HealEffectTimer = 0.0f;
	HealEffectDuration = 0.0f;
}

void AVisualEffectsManager::BeginPlay()
{
	Super::BeginPlay();
	
	Instance = this;
	InitializeVisualStatePresets();
	
	UE_LOG(LogTemp, Log, TEXT("Visual Effects Manager initialized"));
}

void AVisualEffectsManager::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	// Update transitions
	if (bIsTransitioning)
	{
		UpdateTransition(DeltaTime);
	}

	// Update material parameters
	UpdateTimeParameters();

	// Update temporary effects
	if (DamageEffectTimer > 0.0f)
	{
		DamageEffectTimer -= DeltaTime;
		if (DamageEffectTimer <= 0.0f)
		{
			DamageEffectTimer = 0.0f;
		}
	}

	if (HealEffectTimer > 0.0f)
	{
		HealEffectTimer -= DeltaTime;
		if (HealEffectTimer <= 0.0f)
		{
			HealEffectTimer = 0.0f;
		}
	}
}

void AVisualEffectsManager::SetVisualState(EGameVisualState NewState, float InTransitionDuration)
{
	if (NewState == CurrentVisualState && !bIsTransitioning)
	{
		return;
	}

	EGameVisualState OldState = CurrentVisualState;
	TargetVisualState = NewState;

	if (InTransitionDuration < 0.0f)
	{
		InTransitionDuration = DefaultTransitionDuration;
	}

	if (InTransitionDuration <= 0.0f)
	{
		SetVisualStateImmediate(NewState);
		return;
	}

	// Start transition
	bIsTransitioning = true;
	this->TransitionDuration = InTransitionDuration;
	TransitionProgress = 0.0f;

	// Get current settings as start point
	if (VisualStatePresets.Contains(CurrentVisualState))
	{
		TransitionStartSettings = VisualStatePresets[CurrentVisualState];
	}

	// Get target settings
	if (VisualStatePresets.Contains(NewState))
	{
		TransitionTargetSettings = VisualStatePresets[NewState];
	}

	OnVisualStateChanged.Broadcast(OldState, NewState);
}

void AVisualEffectsManager::SetVisualStateImmediate(EGameVisualState NewState)
{
	EGameVisualState OldState = CurrentVisualState;
	CurrentVisualState = NewState;
	bIsTransitioning = false;
	TransitionProgress = 1.0f;

	if (VisualStatePresets.Contains(NewState))
	{
		ApplyPostProcessSettings(VisualStatePresets[NewState]);
	}

	OnVisualStateChanged.Broadcast(OldState, NewState);
}

void AVisualEffectsManager::EnableBulletTimeEffects()
{
	SetVisualState(EGameVisualState::BulletTime, this->BulletTimeSettings.TransitionDuration);
	SetGlobalScalarParameter(PARAM_BULLET_TIME_INTENSITY, 1.0f);
}

void AVisualEffectsManager::DisableBulletTimeEffects()
{
	SetVisualState(EGameVisualState::Normal, this->BulletTimeSettings.TransitionDuration);
	SetGlobalScalarParameter(PARAM_BULLET_TIME_INTENSITY, 0.0f);
}

void AVisualEffectsManager::UpdateBulletTimeIntensity(float Intensity)
{
	SetGlobalScalarParameter(PARAM_BULLET_TIME_INTENSITY, FMath::Clamp(Intensity, 0.0f, 1.0f));
}

void AVisualEffectsManager::ApplyDamageEffect(float Intensity, float Duration)
{
	DamageEffectIntensity = FMath::Clamp(Intensity, 0.0f, 1.0f);
	DamageEffectDuration = Duration;
	DamageEffectTimer = Duration;

	SetGlobalScalarParameter(PARAM_DAMAGE_EFFECT, DamageEffectIntensity);
}

void AVisualEffectsManager::ApplyHealEffect(float Duration)
{
	HealEffectDuration = Duration;
	HealEffectTimer = Duration;
}

void AVisualEffectsManager::ApplyExplosionEffect(const FVector& Location, float Intensity)
{
	// Apply screen shake and flash effect
	ApplyScreenShake(Intensity, 0.5f);
	ApplyDamageEffect(Intensity * 0.5f, 0.3f);
}

void AVisualEffectsManager::ApplyScreenShake(float Intensity, float Duration)
{
	// TODO: Implement screen shake using camera shake
	UE_LOG(LogTemp, Log, TEXT("Screen shake applied: Intensity=%.2f, Duration=%.2f"), Intensity, Duration);
}

void AVisualEffectsManager::SetGlobalScalarParameter(const FName& ParameterName, float Value)
{
	if (GlobalMaterialParameters)
	{
		UKismetMaterialLibrary::SetScalarParameterValue(GetWorld(), GlobalMaterialParameters, ParameterName, Value);
	}
}

void AVisualEffectsManager::SetGlobalVectorParameter(const FName& ParameterName, const FLinearColor& Value)
{
	if (GlobalMaterialParameters)
	{
		UKismetMaterialLibrary::SetVectorParameterValue(GetWorld(), GlobalMaterialParameters, ParameterName, Value);
	}
}

void AVisualEffectsManager::UpdateTimeParameters()
{
	if (GlobalMaterialParameters)
	{
		float GameTime = GetWorld()->GetTimeSeconds();
		SetGlobalScalarParameter(PARAM_GAME_TIME, GameTime);
	}
}

void AVisualEffectsManager::ApplySectorVisualTheme(int32 SectorIndex)
{
	// Apply sector-specific visual theme
	FLinearColor SectorColor = FLinearColor::White;
	
	switch (SectorIndex)
	{
	case 0: // Dilapidated City
		SectorColor = FLinearColor(0.8f, 0.6f, 0.4f, 1.0f);
		break;
	case 1: // Station Tunnels
		SectorColor = FLinearColor(0.4f, 0.4f, 0.6f, 1.0f);
		break;
	case 2: // Bela Vegas
		SectorColor = FLinearColor(1.0f, 0.2f, 0.8f, 1.0f);
		break;
	case 3: // Industrial
		SectorColor = FLinearColor(0.6f, 0.3f, 0.1f, 1.0f);
		break;
	default:
		SectorColor = FLinearColor::White;
		break;
	}

	SetSectorAmbientColor(SectorColor, 1.0f);
}

void AVisualEffectsManager::SetSectorAmbientColor(const FLinearColor& Color, float Intensity)
{
	SetGlobalVectorParameter(PARAM_SECTOR_TINT, Color * Intensity);
}

void AVisualEffectsManager::SetSectorFogSettings(float Density, const FLinearColor& Color, float StartDistance)
{
	// TODO: Implement fog settings
	UE_LOG(LogTemp, Log, TEXT("Fog settings applied: Density=%.2f, StartDistance=%.2f"), Density, StartDistance);
}

void AVisualEffectsManager::TriggerMuzzleFlashEffect(const FVector& Location, const FRotator& Rotation, const FString& WeaponType)
{
	// TODO: Implement muzzle flash effect
	UE_LOG(LogTemp, Log, TEXT("Muzzle flash at %s for weapon %s"), *Location.ToString(), *WeaponType);
}

void AVisualEffectsManager::TriggerImpactEffect(const FVector& Location, const FVector& Normal, const FString& SurfaceType)
{
	// TODO: Implement impact effect
	UE_LOG(LogTemp, Log, TEXT("Impact effect at %s on surface %s"), *Location.ToString(), *SurfaceType);
}

void AVisualEffectsManager::CreateBulletTrail(const FVector& StartLocation, const FVector& EndLocation, const FString& WeaponType)
{
	// TODO: Implement bullet trail
	UE_LOG(LogTemp, Log, TEXT("Bullet trail from %s to %s"), *StartLocation.ToString(), *EndLocation.ToString());
}

void AVisualEffectsManager::SetEffectQuality(int32 QualityLevel)
{
	CurrentEffectQuality = FMath::Clamp(QualityLevel, 0, 3);
	UE_LOG(LogTemp, Log, TEXT("Effect quality set to %d"), CurrentEffectQuality);
}

void AVisualEffectsManager::EnableLODSystem(bool bEnable)
{
	bLODSystemEnabled = bEnable;
}

void AVisualEffectsManager::UpdateEffectLOD(float DistanceToPlayer)
{
	if (!bLODSystemEnabled) return;

	int32 NewLODLevel = 0;
	if (DistanceToPlayer > 5000.0f)
	{
		NewLODLevel = 3; // Very low detail
	}
	else if (DistanceToPlayer > 2500.0f)
	{
		NewLODLevel = 2; // Low detail
	}
	else if (DistanceToPlayer > 1000.0f)
	{
		NewLODLevel = 1; // Medium detail
	}
	// else NewLODLevel = 0 (High detail)
}

AVisualEffectsManager* AVisualEffectsManager::GetVisualEffectsManager(const UObject* WorldContext)
{
	if (Instance && IsValid(Instance))
	{
		return Instance;
	}

	// Try to find existing instance
	UWorld* World = GEngine->GetWorldFromContextObject(WorldContext, EGetWorldErrorMode::LogAndReturnNull);
	if (World)
	{
		for (TActorIterator<AVisualEffectsManager> ActorItr(World); ActorItr; ++ActorItr)
		{
			Instance = *ActorItr;
			return Instance;
		}

		// Create new instance if none found
		Instance = World->SpawnActor<AVisualEffectsManager>();
	}

	return Instance;
}

void AVisualEffectsManager::InitializeVisualStatePresets()
{
	// Initialize default presets
	FVisualEffectSettings NormalSettings;
	VisualStatePresets.Add(EGameVisualState::Normal, NormalSettings);

	FVisualEffectSettings BulletTimePreset;
	BulletTimePreset.Saturation = 0.3f;
	BulletTimePreset.Contrast = 1.3f;
	BulletTimePreset.ColorTint = FLinearColor(0.8f, 0.9f, 1.0f, 1.0f);
	VisualStatePresets.Add(EGameVisualState::BulletTime, BulletTimePreset);

	FVisualEffectSettings DamageSettings;
	DamageSettings.ColorTint = FLinearColor(1.0f, 0.3f, 0.3f, 1.0f);
	DamageSettings.Vignette = 0.5f;
	VisualStatePresets.Add(EGameVisualState::Damaged, DamageSettings);

	FVisualEffectSettings CriticalSettings;
	CriticalSettings.ColorTint = FLinearColor(1.0f, 0.1f, 0.1f, 1.0f);
	CriticalSettings.Vignette = 0.8f;
	CriticalSettings.ChromaticAberration = 0.3f;
	VisualStatePresets.Add(EGameVisualState::Critical, CriticalSettings);

	FVisualEffectSettings DeathSettings;
	DeathSettings.Saturation = 0.0f;
	DeathSettings.Brightness = -0.5f;
	DeathSettings.Vignette = 1.0f;
	VisualStatePresets.Add(EGameVisualState::Death, DeathSettings);

	FVisualEffectSettings VictorySettings;
	VictorySettings.Saturation = 1.2f;
	VictorySettings.Contrast = 1.1f;
	VictorySettings.BloomIntensity = 1.5f;
	VisualStatePresets.Add(EGameVisualState::Victory, VictorySettings);
}

void AVisualEffectsManager::UpdateTransition(float DeltaTime)
{
	if (!bIsTransitioning) return;

	TransitionProgress += DeltaTime / TransitionDuration;
	
	if (TransitionProgress >= 1.0f)
	{
		TransitionProgress = 1.0f;
		bIsTransitioning = false;
		CurrentVisualState = TargetVisualState;
	}

	// Apply interpolated settings
	float Alpha = TransitionProgress;
	if (TransitionCurve)
	{
		Alpha = TransitionCurve->GetFloatValue(TransitionProgress);
	}

	FVisualEffectSettings InterpolatedSettings = LerpVisualEffectSettings(TransitionStartSettings, TransitionTargetSettings, Alpha);
	ApplyPostProcessSettings(InterpolatedSettings);
}

void AVisualEffectsManager::ApplyPostProcessSettings(const FVisualEffectSettings& Settings)
{
	if (!PostProcessComponent) return;

	// Apply settings to post process component
	// Note: This is a simplified implementation
	// In a real project, you would set the actual post process settings
	UE_LOG(LogTemp, VeryVerbose, TEXT("Applying post process settings: Saturation=%.2f, Contrast=%.2f"), 
		Settings.Saturation, Settings.Contrast);
}

FVisualEffectSettings AVisualEffectsManager::LerpVisualEffectSettings(const FVisualEffectSettings& A, const FVisualEffectSettings& B, float Alpha) const
{
	FVisualEffectSettings Result;
	
	Result.Saturation = FMath::Lerp(A.Saturation, B.Saturation, Alpha);
	Result.Contrast = FMath::Lerp(A.Contrast, B.Contrast, Alpha);
	Result.Brightness = FMath::Lerp(A.Brightness, B.Brightness, Alpha);
	Result.ColorTint = FMath::Lerp(A.ColorTint, B.ColorTint, Alpha);
	Result.ChromaticAberration = FMath::Lerp(A.ChromaticAberration, B.ChromaticAberration, Alpha);
	Result.Vignette = FMath::Lerp(A.Vignette, B.Vignette, Alpha);
	Result.MotionBlur = FMath::Lerp(A.MotionBlur, B.MotionBlur, Alpha);
	Result.DepthOfField = FMath::Lerp(A.DepthOfField, B.DepthOfField, Alpha);
	Result.BloomIntensity = FMath::Lerp(A.BloomIntensity, B.BloomIntensity, Alpha);
	Result.FilmGrain = FMath::Lerp(A.FilmGrain, B.FilmGrain, Alpha);
	
	return Result;
}

void AVisualEffectsManager::ApplyVisualEffectSettings(const FVisualEffectSettings& Settings)
{
	ApplyPostProcessSettings(Settings);
}

void AVisualEffectsManager::BlendVisualEffectSettings(const FVisualEffectSettings& SettingsA, const FVisualEffectSettings& SettingsB, float Alpha)
{
	FVisualEffectSettings BlendedSettings = LerpVisualEffectSettings(SettingsA, SettingsB, Alpha);
	ApplyVisualEffectSettings(BlendedSettings);
}
