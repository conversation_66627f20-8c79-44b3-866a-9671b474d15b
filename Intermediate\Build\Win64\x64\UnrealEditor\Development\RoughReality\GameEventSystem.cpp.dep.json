{"Version": "1.2", "Data": {"Source": "g:\\gamedev\\roughreality\\source\\roughreality\\core\\gameeventsystem.cpp", "ProvidedModule": "", "PCH": "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\x64\\roughrealityeditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.cpp20.h.pch", "Includes": ["g:\\gamedev\\roughreality\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\roughreality\\definitions.roughreality.h", "g:\\gamedev\\roughreality\\source\\roughreality\\core\\gameeventsystem.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\gameeventsystem.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystatics.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetsystemlibrary.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\kismetsystemlibrary.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\sound\\dialoguetypes.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\dialoguetypes.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystaticstypes.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystaticstypes.generated.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystatics.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\public\\engineutils.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}