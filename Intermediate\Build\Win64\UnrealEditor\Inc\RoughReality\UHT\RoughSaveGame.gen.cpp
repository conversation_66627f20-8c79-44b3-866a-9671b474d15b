// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RoughReality/SaveSystem/RoughSaveGame.h"
#include "Runtime/GameplayTags/Classes/GameplayTagContainer.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeRoughSaveGame() {}

// Begin Cross Module References
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
ENGINE_API UClass* Z_Construct_UClass_USaveGame();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTag();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
ROUGHREALITY_API UClass* Z_Construct_UClass_URoughSaveGame();
ROUGHREALITY_API UClass* Z_Construct_UClass_URoughSaveGame_NoRegister();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FGameStatistics();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FUnlockedUpgrade();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FUnlockedWeapon();
UPackage* Z_Construct_UPackage__Script_RoughReality();
// End Cross Module References

// Begin ScriptStruct FUnlockedWeapon
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_UnlockedWeapon;
class UScriptStruct* FUnlockedWeapon::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_UnlockedWeapon.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_UnlockedWeapon.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FUnlockedWeapon, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("UnlockedWeapon"));
	}
	return Z_Registration_Info_UScriptStruct_UnlockedWeapon.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FUnlockedWeapon>()
{
	return FUnlockedWeapon::StaticStruct();
}
struct Z_Construct_UScriptStruct_FUnlockedWeapon_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponID_MetaData[] = {
		{ "Category", "Weapon" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsUnlocked_MetaData[] = {
		{ "Category", "Weapon" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpgradeLevel_MetaData[] = {
		{ "Category", "Weapon" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static void NewProp_bIsUnlocked_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsUnlocked;
	static const UECodeGen_Private::FIntPropertyParams NewProp_UpgradeLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FUnlockedWeapon>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FUnlockedWeapon_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUnlockedWeapon, WeaponID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponID_MetaData), NewProp_WeaponID_MetaData) };
void Z_Construct_UScriptStruct_FUnlockedWeapon_Statics::NewProp_bIsUnlocked_SetBit(void* Obj)
{
	((FUnlockedWeapon*)Obj)->bIsUnlocked = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FUnlockedWeapon_Statics::NewProp_bIsUnlocked = { "bIsUnlocked", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FUnlockedWeapon), &Z_Construct_UScriptStruct_FUnlockedWeapon_Statics::NewProp_bIsUnlocked_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsUnlocked_MetaData), NewProp_bIsUnlocked_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FUnlockedWeapon_Statics::NewProp_UpgradeLevel = { "UpgradeLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUnlockedWeapon, UpgradeLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpgradeLevel_MetaData), NewProp_UpgradeLevel_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FUnlockedWeapon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUnlockedWeapon_Statics::NewProp_WeaponID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUnlockedWeapon_Statics::NewProp_bIsUnlocked,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUnlockedWeapon_Statics::NewProp_UpgradeLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FUnlockedWeapon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FUnlockedWeapon_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	&NewStructOps,
	"UnlockedWeapon",
	Z_Construct_UScriptStruct_FUnlockedWeapon_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FUnlockedWeapon_Statics::PropPointers),
	sizeof(FUnlockedWeapon),
	alignof(FUnlockedWeapon),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FUnlockedWeapon_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FUnlockedWeapon_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FUnlockedWeapon()
{
	if (!Z_Registration_Info_UScriptStruct_UnlockedWeapon.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_UnlockedWeapon.InnerSingleton, Z_Construct_UScriptStruct_FUnlockedWeapon_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_UnlockedWeapon.InnerSingleton;
}
// End ScriptStruct FUnlockedWeapon

// Begin ScriptStruct FUnlockedUpgrade
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_UnlockedUpgrade;
class UScriptStruct* FUnlockedUpgrade::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_UnlockedUpgrade.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_UnlockedUpgrade.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FUnlockedUpgrade, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("UnlockedUpgrade"));
	}
	return Z_Registration_Info_UScriptStruct_UnlockedUpgrade.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FUnlockedUpgrade>()
{
	return FUnlockedUpgrade::StaticStruct();
}
struct Z_Construct_UScriptStruct_FUnlockedUpgrade_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpgradeID_MetaData[] = {
		{ "Category", "Upgrade" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsUnlocked_MetaData[] = {
		{ "Category", "Upgrade" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Level_MetaData[] = {
		{ "Category", "Upgrade" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_UpgradeID;
	static void NewProp_bIsUnlocked_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsUnlocked;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Level;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FUnlockedUpgrade>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FUnlockedUpgrade_Statics::NewProp_UpgradeID = { "UpgradeID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUnlockedUpgrade, UpgradeID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpgradeID_MetaData), NewProp_UpgradeID_MetaData) };
void Z_Construct_UScriptStruct_FUnlockedUpgrade_Statics::NewProp_bIsUnlocked_SetBit(void* Obj)
{
	((FUnlockedUpgrade*)Obj)->bIsUnlocked = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FUnlockedUpgrade_Statics::NewProp_bIsUnlocked = { "bIsUnlocked", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FUnlockedUpgrade), &Z_Construct_UScriptStruct_FUnlockedUpgrade_Statics::NewProp_bIsUnlocked_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsUnlocked_MetaData), NewProp_bIsUnlocked_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FUnlockedUpgrade_Statics::NewProp_Level = { "Level", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUnlockedUpgrade, Level), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Level_MetaData), NewProp_Level_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FUnlockedUpgrade_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUnlockedUpgrade_Statics::NewProp_UpgradeID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUnlockedUpgrade_Statics::NewProp_bIsUnlocked,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUnlockedUpgrade_Statics::NewProp_Level,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FUnlockedUpgrade_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FUnlockedUpgrade_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	&NewStructOps,
	"UnlockedUpgrade",
	Z_Construct_UScriptStruct_FUnlockedUpgrade_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FUnlockedUpgrade_Statics::PropPointers),
	sizeof(FUnlockedUpgrade),
	alignof(FUnlockedUpgrade),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FUnlockedUpgrade_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FUnlockedUpgrade_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FUnlockedUpgrade()
{
	if (!Z_Registration_Info_UScriptStruct_UnlockedUpgrade.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_UnlockedUpgrade.InnerSingleton, Z_Construct_UScriptStruct_FUnlockedUpgrade_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_UnlockedUpgrade.InnerSingleton;
}
// End ScriptStruct FUnlockedUpgrade

// Begin ScriptStruct FGameStatistics
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_GameStatistics;
class UScriptStruct* FGameStatistics::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_GameStatistics.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_GameStatistics.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FGameStatistics, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("GameStatistics"));
	}
	return Z_Registration_Info_UScriptStruct_GameStatistics.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FGameStatistics>()
{
	return FGameStatistics::StaticStruct();
}
struct Z_Construct_UScriptStruct_FGameStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalRuns_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SuccessfulRuns_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalKills_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalDeaths_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalPlayTime_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HighestSectorReached_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MostTeethInSingleRun_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalRuns;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SuccessfulRuns;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalKills;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalDeaths;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalPlayTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_HighestSectorReached;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MostTeethInSingleRun;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FGameStatistics>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FGameStatistics_Statics::NewProp_TotalRuns = { "TotalRuns", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGameStatistics, TotalRuns), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalRuns_MetaData), NewProp_TotalRuns_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FGameStatistics_Statics::NewProp_SuccessfulRuns = { "SuccessfulRuns", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGameStatistics, SuccessfulRuns), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SuccessfulRuns_MetaData), NewProp_SuccessfulRuns_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FGameStatistics_Statics::NewProp_TotalKills = { "TotalKills", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGameStatistics, TotalKills), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalKills_MetaData), NewProp_TotalKills_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FGameStatistics_Statics::NewProp_TotalDeaths = { "TotalDeaths", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGameStatistics, TotalDeaths), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalDeaths_MetaData), NewProp_TotalDeaths_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FGameStatistics_Statics::NewProp_TotalPlayTime = { "TotalPlayTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGameStatistics, TotalPlayTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalPlayTime_MetaData), NewProp_TotalPlayTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FGameStatistics_Statics::NewProp_HighestSectorReached = { "HighestSectorReached", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGameStatistics, HighestSectorReached), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HighestSectorReached_MetaData), NewProp_HighestSectorReached_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FGameStatistics_Statics::NewProp_MostTeethInSingleRun = { "MostTeethInSingleRun", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGameStatistics, MostTeethInSingleRun), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MostTeethInSingleRun_MetaData), NewProp_MostTeethInSingleRun_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FGameStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGameStatistics_Statics::NewProp_TotalRuns,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGameStatistics_Statics::NewProp_SuccessfulRuns,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGameStatistics_Statics::NewProp_TotalKills,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGameStatistics_Statics::NewProp_TotalDeaths,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGameStatistics_Statics::NewProp_TotalPlayTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGameStatistics_Statics::NewProp_HighestSectorReached,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGameStatistics_Statics::NewProp_MostTeethInSingleRun,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FGameStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FGameStatistics_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	&NewStructOps,
	"GameStatistics",
	Z_Construct_UScriptStruct_FGameStatistics_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FGameStatistics_Statics::PropPointers),
	sizeof(FGameStatistics),
	alignof(FGameStatistics),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FGameStatistics_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FGameStatistics_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FGameStatistics()
{
	if (!Z_Registration_Info_UScriptStruct_GameStatistics.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_GameStatistics.InnerSingleton, Z_Construct_UScriptStruct_FGameStatistics_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_GameStatistics.InnerSingleton;
}
// End ScriptStruct FGameStatistics

// Begin Class URoughSaveGame Function AddKills
struct Z_Construct_UFunction_URoughSaveGame_AddKills_Statics
{
	struct RoughSaveGame_eventAddKills_Parms
	{
		int32 KillCount;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_KillCount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URoughSaveGame_AddKills_Statics::NewProp_KillCount = { "KillCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughSaveGame_eventAddKills_Parms, KillCount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URoughSaveGame_AddKills_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughSaveGame_AddKills_Statics::NewProp_KillCount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_AddKills_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughSaveGame_AddKills_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughSaveGame, nullptr, "AddKills", nullptr, nullptr, Z_Construct_UFunction_URoughSaveGame_AddKills_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_AddKills_Statics::PropPointers), sizeof(Z_Construct_UFunction_URoughSaveGame_AddKills_Statics::RoughSaveGame_eventAddKills_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_AddKills_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughSaveGame_AddKills_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_URoughSaveGame_AddKills_Statics::RoughSaveGame_eventAddKills_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URoughSaveGame_AddKills()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughSaveGame_AddKills_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughSaveGame::execAddKills)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_KillCount);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddKills(Z_Param_KillCount);
	P_NATIVE_END;
}
// End Class URoughSaveGame Function AddKills

// Begin Class URoughSaveGame Function AddPlayTime
struct Z_Construct_UFunction_URoughSaveGame_AddPlayTime_Statics
{
	struct RoughSaveGame_eventAddPlayTime_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_URoughSaveGame_AddPlayTime_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughSaveGame_eventAddPlayTime_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URoughSaveGame_AddPlayTime_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughSaveGame_AddPlayTime_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_AddPlayTime_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughSaveGame_AddPlayTime_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughSaveGame, nullptr, "AddPlayTime", nullptr, nullptr, Z_Construct_UFunction_URoughSaveGame_AddPlayTime_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_AddPlayTime_Statics::PropPointers), sizeof(Z_Construct_UFunction_URoughSaveGame_AddPlayTime_Statics::RoughSaveGame_eventAddPlayTime_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_AddPlayTime_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughSaveGame_AddPlayTime_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_URoughSaveGame_AddPlayTime_Statics::RoughSaveGame_eventAddPlayTime_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URoughSaveGame_AddPlayTime()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughSaveGame_AddPlayTime_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughSaveGame::execAddPlayTime)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddPlayTime(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// End Class URoughSaveGame Function AddPlayTime

// Begin Class URoughSaveGame Function CreateBackup
struct Z_Construct_UFunction_URoughSaveGame_CreateBackup_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughSaveGame_CreateBackup_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughSaveGame, nullptr, "CreateBackup", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_CreateBackup_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughSaveGame_CreateBackup_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_URoughSaveGame_CreateBackup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughSaveGame_CreateBackup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughSaveGame::execCreateBackup)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateBackup();
	P_NATIVE_END;
}
// End Class URoughSaveGame Function CreateBackup

// Begin Class URoughSaveGame Function GetSaveDataChecksum
struct Z_Construct_UFunction_URoughSaveGame_GetSaveDataChecksum_Statics
{
	struct RoughSaveGame_eventGetSaveDataChecksum_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URoughSaveGame_GetSaveDataChecksum_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughSaveGame_eventGetSaveDataChecksum_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URoughSaveGame_GetSaveDataChecksum_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughSaveGame_GetSaveDataChecksum_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_GetSaveDataChecksum_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughSaveGame_GetSaveDataChecksum_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughSaveGame, nullptr, "GetSaveDataChecksum", nullptr, nullptr, Z_Construct_UFunction_URoughSaveGame_GetSaveDataChecksum_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_GetSaveDataChecksum_Statics::PropPointers), sizeof(Z_Construct_UFunction_URoughSaveGame_GetSaveDataChecksum_Statics::RoughSaveGame_eventGetSaveDataChecksum_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_GetSaveDataChecksum_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughSaveGame_GetSaveDataChecksum_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_URoughSaveGame_GetSaveDataChecksum_Statics::RoughSaveGame_eventGetSaveDataChecksum_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URoughSaveGame_GetSaveDataChecksum()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughSaveGame_GetSaveDataChecksum_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughSaveGame::execGetSaveDataChecksum)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetSaveDataChecksum();
	P_NATIVE_END;
}
// End Class URoughSaveGame Function GetSaveDataChecksum

// Begin Class URoughSaveGame Function GetSaveMetadata
struct Z_Construct_UFunction_URoughSaveGame_GetSaveMetadata_Statics
{
	struct RoughSaveGame_eventGetSaveMetadata_Parms
	{
		TMap<FString,FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URoughSaveGame_GetSaveMetadata_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_URoughSaveGame_GetSaveMetadata_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_URoughSaveGame_GetSaveMetadata_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughSaveGame_eventGetSaveMetadata_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URoughSaveGame_GetSaveMetadata_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughSaveGame_GetSaveMetadata_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughSaveGame_GetSaveMetadata_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughSaveGame_GetSaveMetadata_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_GetSaveMetadata_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughSaveGame_GetSaveMetadata_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughSaveGame, nullptr, "GetSaveMetadata", nullptr, nullptr, Z_Construct_UFunction_URoughSaveGame_GetSaveMetadata_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_GetSaveMetadata_Statics::PropPointers), sizeof(Z_Construct_UFunction_URoughSaveGame_GetSaveMetadata_Statics::RoughSaveGame_eventGetSaveMetadata_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_GetSaveMetadata_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughSaveGame_GetSaveMetadata_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_URoughSaveGame_GetSaveMetadata_Statics::RoughSaveGame_eventGetSaveMetadata_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URoughSaveGame_GetSaveMetadata()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughSaveGame_GetSaveMetadata_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughSaveGame::execGetSaveMetadata)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,FString>*)Z_Param__Result=P_THIS->GetSaveMetadata();
	P_NATIVE_END;
}
// End Class URoughSaveGame Function GetSaveMetadata

// Begin Class URoughSaveGame Function GetUpgradeLevel
struct Z_Construct_UFunction_URoughSaveGame_GetUpgradeLevel_Statics
{
	struct RoughSaveGame_eventGetUpgradeLevel_Parms
	{
		FName UpgradeID;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Upgrades" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_UpgradeID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_URoughSaveGame_GetUpgradeLevel_Statics::NewProp_UpgradeID = { "UpgradeID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughSaveGame_eventGetUpgradeLevel_Parms, UpgradeID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URoughSaveGame_GetUpgradeLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughSaveGame_eventGetUpgradeLevel_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URoughSaveGame_GetUpgradeLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughSaveGame_GetUpgradeLevel_Statics::NewProp_UpgradeID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughSaveGame_GetUpgradeLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_GetUpgradeLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughSaveGame_GetUpgradeLevel_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughSaveGame, nullptr, "GetUpgradeLevel", nullptr, nullptr, Z_Construct_UFunction_URoughSaveGame_GetUpgradeLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_GetUpgradeLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_URoughSaveGame_GetUpgradeLevel_Statics::RoughSaveGame_eventGetUpgradeLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_GetUpgradeLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughSaveGame_GetUpgradeLevel_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_URoughSaveGame_GetUpgradeLevel_Statics::RoughSaveGame_eventGetUpgradeLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URoughSaveGame_GetUpgradeLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughSaveGame_GetUpgradeLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughSaveGame::execGetUpgradeLevel)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_UpgradeID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetUpgradeLevel(Z_Param_UpgradeID);
	P_NATIVE_END;
}
// End Class URoughSaveGame Function GetUpgradeLevel

// Begin Class URoughSaveGame Function GetWeaponUpgradeLevel
struct Z_Construct_UFunction_URoughSaveGame_GetWeaponUpgradeLevel_Statics
{
	struct RoughSaveGame_eventGetWeaponUpgradeLevel_Parms
	{
		FName WeaponID;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapons" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_URoughSaveGame_GetWeaponUpgradeLevel_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughSaveGame_eventGetWeaponUpgradeLevel_Parms, WeaponID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URoughSaveGame_GetWeaponUpgradeLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughSaveGame_eventGetWeaponUpgradeLevel_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URoughSaveGame_GetWeaponUpgradeLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughSaveGame_GetWeaponUpgradeLevel_Statics::NewProp_WeaponID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughSaveGame_GetWeaponUpgradeLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_GetWeaponUpgradeLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughSaveGame_GetWeaponUpgradeLevel_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughSaveGame, nullptr, "GetWeaponUpgradeLevel", nullptr, nullptr, Z_Construct_UFunction_URoughSaveGame_GetWeaponUpgradeLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_GetWeaponUpgradeLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_URoughSaveGame_GetWeaponUpgradeLevel_Statics::RoughSaveGame_eventGetWeaponUpgradeLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_GetWeaponUpgradeLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughSaveGame_GetWeaponUpgradeLevel_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_URoughSaveGame_GetWeaponUpgradeLevel_Statics::RoughSaveGame_eventGetWeaponUpgradeLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URoughSaveGame_GetWeaponUpgradeLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughSaveGame_GetWeaponUpgradeLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughSaveGame::execGetWeaponUpgradeLevel)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_WeaponID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetWeaponUpgradeLevel(Z_Param_WeaponID);
	P_NATIVE_END;
}
// End Class URoughSaveGame Function GetWeaponUpgradeLevel

// Begin Class URoughSaveGame Function HasUnlockedTag
struct Z_Construct_UFunction_URoughSaveGame_HasUnlockedTag_Statics
{
	struct RoughSaveGame_eventHasUnlockedTag_Parms
	{
		FGameplayTag Tag;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tags" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tag management */" },
#endif
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tag management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Tag;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URoughSaveGame_HasUnlockedTag_Statics::NewProp_Tag = { "Tag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughSaveGame_eventHasUnlockedTag_Parms, Tag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tag_MetaData), NewProp_Tag_MetaData) }; // 1298103297
void Z_Construct_UFunction_URoughSaveGame_HasUnlockedTag_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RoughSaveGame_eventHasUnlockedTag_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URoughSaveGame_HasUnlockedTag_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RoughSaveGame_eventHasUnlockedTag_Parms), &Z_Construct_UFunction_URoughSaveGame_HasUnlockedTag_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URoughSaveGame_HasUnlockedTag_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughSaveGame_HasUnlockedTag_Statics::NewProp_Tag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughSaveGame_HasUnlockedTag_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_HasUnlockedTag_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughSaveGame_HasUnlockedTag_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughSaveGame, nullptr, "HasUnlockedTag", nullptr, nullptr, Z_Construct_UFunction_URoughSaveGame_HasUnlockedTag_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_HasUnlockedTag_Statics::PropPointers), sizeof(Z_Construct_UFunction_URoughSaveGame_HasUnlockedTag_Statics::RoughSaveGame_eventHasUnlockedTag_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_HasUnlockedTag_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughSaveGame_HasUnlockedTag_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_URoughSaveGame_HasUnlockedTag_Statics::RoughSaveGame_eventHasUnlockedTag_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URoughSaveGame_HasUnlockedTag()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughSaveGame_HasUnlockedTag_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughSaveGame::execHasUnlockedTag)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_Tag);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasUnlockedTag(Z_Param_Out_Tag);
	P_NATIVE_END;
}
// End Class URoughSaveGame Function HasUnlockedTag

// Begin Class URoughSaveGame Function IncrementDeaths
struct Z_Construct_UFunction_URoughSaveGame_IncrementDeaths_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughSaveGame_IncrementDeaths_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughSaveGame, nullptr, "IncrementDeaths", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_IncrementDeaths_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughSaveGame_IncrementDeaths_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_URoughSaveGame_IncrementDeaths()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughSaveGame_IncrementDeaths_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughSaveGame::execIncrementDeaths)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IncrementDeaths();
	P_NATIVE_END;
}
// End Class URoughSaveGame Function IncrementDeaths

// Begin Class URoughSaveGame Function IncrementRuns
struct Z_Construct_UFunction_URoughSaveGame_IncrementRuns_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Statistics */" },
#endif
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Statistics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughSaveGame_IncrementRuns_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughSaveGame, nullptr, "IncrementRuns", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_IncrementRuns_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughSaveGame_IncrementRuns_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_URoughSaveGame_IncrementRuns()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughSaveGame_IncrementRuns_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughSaveGame::execIncrementRuns)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IncrementRuns();
	P_NATIVE_END;
}
// End Class URoughSaveGame Function IncrementRuns

// Begin Class URoughSaveGame Function IncrementSuccessfulRuns
struct Z_Construct_UFunction_URoughSaveGame_IncrementSuccessfulRuns_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughSaveGame_IncrementSuccessfulRuns_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughSaveGame, nullptr, "IncrementSuccessfulRuns", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_IncrementSuccessfulRuns_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughSaveGame_IncrementSuccessfulRuns_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_URoughSaveGame_IncrementSuccessfulRuns()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughSaveGame_IncrementSuccessfulRuns_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughSaveGame::execIncrementSuccessfulRuns)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->IncrementSuccessfulRuns();
	P_NATIVE_END;
}
// End Class URoughSaveGame Function IncrementSuccessfulRuns

// Begin Class URoughSaveGame Function InitializeDefaults
struct Z_Construct_UFunction_URoughSaveGame_InitializeDefaults_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save Game" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Initialize default values */" },
#endif
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initialize default values" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughSaveGame_InitializeDefaults_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughSaveGame, nullptr, "InitializeDefaults", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_InitializeDefaults_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughSaveGame_InitializeDefaults_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_URoughSaveGame_InitializeDefaults()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughSaveGame_InitializeDefaults_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughSaveGame::execInitializeDefaults)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InitializeDefaults();
	P_NATIVE_END;
}
// End Class URoughSaveGame Function InitializeDefaults

// Begin Class URoughSaveGame Function IsUpgradeUnlocked
struct Z_Construct_UFunction_URoughSaveGame_IsUpgradeUnlocked_Statics
{
	struct RoughSaveGame_eventIsUpgradeUnlocked_Parms
	{
		FName UpgradeID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Upgrades" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Upgrade management */" },
#endif
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Upgrade management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_UpgradeID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_URoughSaveGame_IsUpgradeUnlocked_Statics::NewProp_UpgradeID = { "UpgradeID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughSaveGame_eventIsUpgradeUnlocked_Parms, UpgradeID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URoughSaveGame_IsUpgradeUnlocked_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RoughSaveGame_eventIsUpgradeUnlocked_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URoughSaveGame_IsUpgradeUnlocked_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RoughSaveGame_eventIsUpgradeUnlocked_Parms), &Z_Construct_UFunction_URoughSaveGame_IsUpgradeUnlocked_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URoughSaveGame_IsUpgradeUnlocked_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughSaveGame_IsUpgradeUnlocked_Statics::NewProp_UpgradeID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughSaveGame_IsUpgradeUnlocked_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_IsUpgradeUnlocked_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughSaveGame_IsUpgradeUnlocked_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughSaveGame, nullptr, "IsUpgradeUnlocked", nullptr, nullptr, Z_Construct_UFunction_URoughSaveGame_IsUpgradeUnlocked_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_IsUpgradeUnlocked_Statics::PropPointers), sizeof(Z_Construct_UFunction_URoughSaveGame_IsUpgradeUnlocked_Statics::RoughSaveGame_eventIsUpgradeUnlocked_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_IsUpgradeUnlocked_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughSaveGame_IsUpgradeUnlocked_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_URoughSaveGame_IsUpgradeUnlocked_Statics::RoughSaveGame_eventIsUpgradeUnlocked_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URoughSaveGame_IsUpgradeUnlocked()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughSaveGame_IsUpgradeUnlocked_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughSaveGame::execIsUpgradeUnlocked)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_UpgradeID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsUpgradeUnlocked(Z_Param_UpgradeID);
	P_NATIVE_END;
}
// End Class URoughSaveGame Function IsUpgradeUnlocked

// Begin Class URoughSaveGame Function IsWeaponUnlocked
struct Z_Construct_UFunction_URoughSaveGame_IsWeaponUnlocked_Statics
{
	struct RoughSaveGame_eventIsWeaponUnlocked_Parms
	{
		FName WeaponID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapons" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Weapon management */" },
#endif
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weapon management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_URoughSaveGame_IsWeaponUnlocked_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughSaveGame_eventIsWeaponUnlocked_Parms, WeaponID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_URoughSaveGame_IsWeaponUnlocked_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RoughSaveGame_eventIsWeaponUnlocked_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URoughSaveGame_IsWeaponUnlocked_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RoughSaveGame_eventIsWeaponUnlocked_Parms), &Z_Construct_UFunction_URoughSaveGame_IsWeaponUnlocked_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URoughSaveGame_IsWeaponUnlocked_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughSaveGame_IsWeaponUnlocked_Statics::NewProp_WeaponID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughSaveGame_IsWeaponUnlocked_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_IsWeaponUnlocked_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughSaveGame_IsWeaponUnlocked_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughSaveGame, nullptr, "IsWeaponUnlocked", nullptr, nullptr, Z_Construct_UFunction_URoughSaveGame_IsWeaponUnlocked_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_IsWeaponUnlocked_Statics::PropPointers), sizeof(Z_Construct_UFunction_URoughSaveGame_IsWeaponUnlocked_Statics::RoughSaveGame_eventIsWeaponUnlocked_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_IsWeaponUnlocked_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughSaveGame_IsWeaponUnlocked_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_URoughSaveGame_IsWeaponUnlocked_Statics::RoughSaveGame_eventIsWeaponUnlocked_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URoughSaveGame_IsWeaponUnlocked()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughSaveGame_IsWeaponUnlocked_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughSaveGame::execIsWeaponUnlocked)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_WeaponID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsWeaponUnlocked(Z_Param_WeaponID);
	P_NATIVE_END;
}
// End Class URoughSaveGame Function IsWeaponUnlocked

// Begin Class URoughSaveGame Function RestoreFromBackup
struct Z_Construct_UFunction_URoughSaveGame_RestoreFromBackup_Statics
{
	struct RoughSaveGame_eventRestoreFromBackup_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URoughSaveGame_RestoreFromBackup_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RoughSaveGame_eventRestoreFromBackup_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URoughSaveGame_RestoreFromBackup_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RoughSaveGame_eventRestoreFromBackup_Parms), &Z_Construct_UFunction_URoughSaveGame_RestoreFromBackup_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URoughSaveGame_RestoreFromBackup_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughSaveGame_RestoreFromBackup_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_RestoreFromBackup_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughSaveGame_RestoreFromBackup_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughSaveGame, nullptr, "RestoreFromBackup", nullptr, nullptr, Z_Construct_UFunction_URoughSaveGame_RestoreFromBackup_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_RestoreFromBackup_Statics::PropPointers), sizeof(Z_Construct_UFunction_URoughSaveGame_RestoreFromBackup_Statics::RoughSaveGame_eventRestoreFromBackup_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_RestoreFromBackup_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughSaveGame_RestoreFromBackup_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_URoughSaveGame_RestoreFromBackup_Statics::RoughSaveGame_eventRestoreFromBackup_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URoughSaveGame_RestoreFromBackup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughSaveGame_RestoreFromBackup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughSaveGame::execRestoreFromBackup)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RestoreFromBackup();
	P_NATIVE_END;
}
// End Class URoughSaveGame Function RestoreFromBackup

// Begin Class URoughSaveGame Function UnlockTag
struct Z_Construct_UFunction_URoughSaveGame_UnlockTag_Statics
{
	struct RoughSaveGame_eventUnlockTag_Parms
	{
		FGameplayTag Tag;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tags" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Tag;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_URoughSaveGame_UnlockTag_Statics::NewProp_Tag = { "Tag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughSaveGame_eventUnlockTag_Parms, Tag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tag_MetaData), NewProp_Tag_MetaData) }; // 1298103297
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URoughSaveGame_UnlockTag_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughSaveGame_UnlockTag_Statics::NewProp_Tag,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_UnlockTag_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughSaveGame_UnlockTag_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughSaveGame, nullptr, "UnlockTag", nullptr, nullptr, Z_Construct_UFunction_URoughSaveGame_UnlockTag_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_UnlockTag_Statics::PropPointers), sizeof(Z_Construct_UFunction_URoughSaveGame_UnlockTag_Statics::RoughSaveGame_eventUnlockTag_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_UnlockTag_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughSaveGame_UnlockTag_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_URoughSaveGame_UnlockTag_Statics::RoughSaveGame_eventUnlockTag_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URoughSaveGame_UnlockTag()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughSaveGame_UnlockTag_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughSaveGame::execUnlockTag)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_Tag);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnlockTag(Z_Param_Out_Tag);
	P_NATIVE_END;
}
// End Class URoughSaveGame Function UnlockTag

// Begin Class URoughSaveGame Function UnlockUpgrade
struct Z_Construct_UFunction_URoughSaveGame_UnlockUpgrade_Statics
{
	struct RoughSaveGame_eventUnlockUpgrade_Parms
	{
		FName UpgradeID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Upgrades" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_UpgradeID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_URoughSaveGame_UnlockUpgrade_Statics::NewProp_UpgradeID = { "UpgradeID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughSaveGame_eventUnlockUpgrade_Parms, UpgradeID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URoughSaveGame_UnlockUpgrade_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughSaveGame_UnlockUpgrade_Statics::NewProp_UpgradeID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_UnlockUpgrade_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughSaveGame_UnlockUpgrade_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughSaveGame, nullptr, "UnlockUpgrade", nullptr, nullptr, Z_Construct_UFunction_URoughSaveGame_UnlockUpgrade_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_UnlockUpgrade_Statics::PropPointers), sizeof(Z_Construct_UFunction_URoughSaveGame_UnlockUpgrade_Statics::RoughSaveGame_eventUnlockUpgrade_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_UnlockUpgrade_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughSaveGame_UnlockUpgrade_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_URoughSaveGame_UnlockUpgrade_Statics::RoughSaveGame_eventUnlockUpgrade_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URoughSaveGame_UnlockUpgrade()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughSaveGame_UnlockUpgrade_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughSaveGame::execUnlockUpgrade)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_UpgradeID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnlockUpgrade(Z_Param_UpgradeID);
	P_NATIVE_END;
}
// End Class URoughSaveGame Function UnlockUpgrade

// Begin Class URoughSaveGame Function UnlockWeapon
struct Z_Construct_UFunction_URoughSaveGame_UnlockWeapon_Statics
{
	struct RoughSaveGame_eventUnlockWeapon_Parms
	{
		FName WeaponID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapons" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_URoughSaveGame_UnlockWeapon_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughSaveGame_eventUnlockWeapon_Parms, WeaponID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URoughSaveGame_UnlockWeapon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughSaveGame_UnlockWeapon_Statics::NewProp_WeaponID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_UnlockWeapon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughSaveGame_UnlockWeapon_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughSaveGame, nullptr, "UnlockWeapon", nullptr, nullptr, Z_Construct_UFunction_URoughSaveGame_UnlockWeapon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_UnlockWeapon_Statics::PropPointers), sizeof(Z_Construct_UFunction_URoughSaveGame_UnlockWeapon_Statics::RoughSaveGame_eventUnlockWeapon_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_UnlockWeapon_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughSaveGame_UnlockWeapon_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_URoughSaveGame_UnlockWeapon_Statics::RoughSaveGame_eventUnlockWeapon_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URoughSaveGame_UnlockWeapon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughSaveGame_UnlockWeapon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughSaveGame::execUnlockWeapon)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_WeaponID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnlockWeapon(Z_Param_WeaponID);
	P_NATIVE_END;
}
// End Class URoughSaveGame Function UnlockWeapon

// Begin Class URoughSaveGame Function UpdateHighestSector
struct Z_Construct_UFunction_URoughSaveGame_UpdateHighestSector_Statics
{
	struct RoughSaveGame_eventUpdateHighestSector_Parms
	{
		int32 SectorIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SectorIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URoughSaveGame_UpdateHighestSector_Statics::NewProp_SectorIndex = { "SectorIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughSaveGame_eventUpdateHighestSector_Parms, SectorIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URoughSaveGame_UpdateHighestSector_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughSaveGame_UpdateHighestSector_Statics::NewProp_SectorIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_UpdateHighestSector_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughSaveGame_UpdateHighestSector_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughSaveGame, nullptr, "UpdateHighestSector", nullptr, nullptr, Z_Construct_UFunction_URoughSaveGame_UpdateHighestSector_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_UpdateHighestSector_Statics::PropPointers), sizeof(Z_Construct_UFunction_URoughSaveGame_UpdateHighestSector_Statics::RoughSaveGame_eventUpdateHighestSector_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_UpdateHighestSector_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughSaveGame_UpdateHighestSector_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_URoughSaveGame_UpdateHighestSector_Statics::RoughSaveGame_eventUpdateHighestSector_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URoughSaveGame_UpdateHighestSector()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughSaveGame_UpdateHighestSector_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughSaveGame::execUpdateHighestSector)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SectorIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateHighestSector(Z_Param_SectorIndex);
	P_NATIVE_END;
}
// End Class URoughSaveGame Function UpdateHighestSector

// Begin Class URoughSaveGame Function UpdateMostTeethInRun
struct Z_Construct_UFunction_URoughSaveGame_UpdateMostTeethInRun_Statics
{
	struct RoughSaveGame_eventUpdateMostTeethInRun_Parms
	{
		int32 TeethAmount;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeethAmount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_URoughSaveGame_UpdateMostTeethInRun_Statics::NewProp_TeethAmount = { "TeethAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughSaveGame_eventUpdateMostTeethInRun_Parms, TeethAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URoughSaveGame_UpdateMostTeethInRun_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughSaveGame_UpdateMostTeethInRun_Statics::NewProp_TeethAmount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_UpdateMostTeethInRun_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughSaveGame_UpdateMostTeethInRun_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughSaveGame, nullptr, "UpdateMostTeethInRun", nullptr, nullptr, Z_Construct_UFunction_URoughSaveGame_UpdateMostTeethInRun_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_UpdateMostTeethInRun_Statics::PropPointers), sizeof(Z_Construct_UFunction_URoughSaveGame_UpdateMostTeethInRun_Statics::RoughSaveGame_eventUpdateMostTeethInRun_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_UpdateMostTeethInRun_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughSaveGame_UpdateMostTeethInRun_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_URoughSaveGame_UpdateMostTeethInRun_Statics::RoughSaveGame_eventUpdateMostTeethInRun_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URoughSaveGame_UpdateMostTeethInRun()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughSaveGame_UpdateMostTeethInRun_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughSaveGame::execUpdateMostTeethInRun)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_TeethAmount);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateMostTeethInRun(Z_Param_TeethAmount);
	P_NATIVE_END;
}
// End Class URoughSaveGame Function UpdateMostTeethInRun

// Begin Class URoughSaveGame Function UpgradeUpgrade
struct Z_Construct_UFunction_URoughSaveGame_UpgradeUpgrade_Statics
{
	struct RoughSaveGame_eventUpgradeUpgrade_Parms
	{
		FName UpgradeID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Upgrades" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_UpgradeID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_URoughSaveGame_UpgradeUpgrade_Statics::NewProp_UpgradeID = { "UpgradeID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughSaveGame_eventUpgradeUpgrade_Parms, UpgradeID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URoughSaveGame_UpgradeUpgrade_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughSaveGame_UpgradeUpgrade_Statics::NewProp_UpgradeID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_UpgradeUpgrade_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughSaveGame_UpgradeUpgrade_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughSaveGame, nullptr, "UpgradeUpgrade", nullptr, nullptr, Z_Construct_UFunction_URoughSaveGame_UpgradeUpgrade_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_UpgradeUpgrade_Statics::PropPointers), sizeof(Z_Construct_UFunction_URoughSaveGame_UpgradeUpgrade_Statics::RoughSaveGame_eventUpgradeUpgrade_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_UpgradeUpgrade_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughSaveGame_UpgradeUpgrade_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_URoughSaveGame_UpgradeUpgrade_Statics::RoughSaveGame_eventUpgradeUpgrade_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URoughSaveGame_UpgradeUpgrade()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughSaveGame_UpgradeUpgrade_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughSaveGame::execUpgradeUpgrade)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_UpgradeID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpgradeUpgrade(Z_Param_UpgradeID);
	P_NATIVE_END;
}
// End Class URoughSaveGame Function UpgradeUpgrade

// Begin Class URoughSaveGame Function UpgradeWeapon
struct Z_Construct_UFunction_URoughSaveGame_UpgradeWeapon_Statics
{
	struct RoughSaveGame_eventUpgradeWeapon_Parms
	{
		FName WeaponID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapons" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_URoughSaveGame_UpgradeWeapon_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(RoughSaveGame_eventUpgradeWeapon_Parms, WeaponID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URoughSaveGame_UpgradeWeapon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughSaveGame_UpgradeWeapon_Statics::NewProp_WeaponID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_UpgradeWeapon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughSaveGame_UpgradeWeapon_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughSaveGame, nullptr, "UpgradeWeapon", nullptr, nullptr, Z_Construct_UFunction_URoughSaveGame_UpgradeWeapon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_UpgradeWeapon_Statics::PropPointers), sizeof(Z_Construct_UFunction_URoughSaveGame_UpgradeWeapon_Statics::RoughSaveGame_eventUpgradeWeapon_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_UpgradeWeapon_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughSaveGame_UpgradeWeapon_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_URoughSaveGame_UpgradeWeapon_Statics::RoughSaveGame_eventUpgradeWeapon_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URoughSaveGame_UpgradeWeapon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughSaveGame_UpgradeWeapon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughSaveGame::execUpgradeWeapon)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_WeaponID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpgradeWeapon(Z_Param_WeaponID);
	P_NATIVE_END;
}
// End Class URoughSaveGame Function UpgradeWeapon

// Begin Class URoughSaveGame Function ValidateSaveData
struct Z_Construct_UFunction_URoughSaveGame_ValidateSaveData_Statics
{
	struct RoughSaveGame_eventValidateSaveData_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Enhanced Save System */" },
#endif
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enhanced Save System" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_URoughSaveGame_ValidateSaveData_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((RoughSaveGame_eventValidateSaveData_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_URoughSaveGame_ValidateSaveData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(RoughSaveGame_eventValidateSaveData_Parms), &Z_Construct_UFunction_URoughSaveGame_ValidateSaveData_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_URoughSaveGame_ValidateSaveData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_URoughSaveGame_ValidateSaveData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_ValidateSaveData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_URoughSaveGame_ValidateSaveData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_URoughSaveGame, nullptr, "ValidateSaveData", nullptr, nullptr, Z_Construct_UFunction_URoughSaveGame_ValidateSaveData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_ValidateSaveData_Statics::PropPointers), sizeof(Z_Construct_UFunction_URoughSaveGame_ValidateSaveData_Statics::RoughSaveGame_eventValidateSaveData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_URoughSaveGame_ValidateSaveData_Statics::Function_MetaDataParams), Z_Construct_UFunction_URoughSaveGame_ValidateSaveData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_URoughSaveGame_ValidateSaveData_Statics::RoughSaveGame_eventValidateSaveData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_URoughSaveGame_ValidateSaveData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_URoughSaveGame_ValidateSaveData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(URoughSaveGame::execValidateSaveData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateSaveData();
	P_NATIVE_END;
}
// End Class URoughSaveGame Function ValidateSaveData

// Begin Class URoughSaveGame
void URoughSaveGame::StaticRegisterNativesURoughSaveGame()
{
	UClass* Class = URoughSaveGame::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddKills", &URoughSaveGame::execAddKills },
		{ "AddPlayTime", &URoughSaveGame::execAddPlayTime },
		{ "CreateBackup", &URoughSaveGame::execCreateBackup },
		{ "GetSaveDataChecksum", &URoughSaveGame::execGetSaveDataChecksum },
		{ "GetSaveMetadata", &URoughSaveGame::execGetSaveMetadata },
		{ "GetUpgradeLevel", &URoughSaveGame::execGetUpgradeLevel },
		{ "GetWeaponUpgradeLevel", &URoughSaveGame::execGetWeaponUpgradeLevel },
		{ "HasUnlockedTag", &URoughSaveGame::execHasUnlockedTag },
		{ "IncrementDeaths", &URoughSaveGame::execIncrementDeaths },
		{ "IncrementRuns", &URoughSaveGame::execIncrementRuns },
		{ "IncrementSuccessfulRuns", &URoughSaveGame::execIncrementSuccessfulRuns },
		{ "InitializeDefaults", &URoughSaveGame::execInitializeDefaults },
		{ "IsUpgradeUnlocked", &URoughSaveGame::execIsUpgradeUnlocked },
		{ "IsWeaponUnlocked", &URoughSaveGame::execIsWeaponUnlocked },
		{ "RestoreFromBackup", &URoughSaveGame::execRestoreFromBackup },
		{ "UnlockTag", &URoughSaveGame::execUnlockTag },
		{ "UnlockUpgrade", &URoughSaveGame::execUnlockUpgrade },
		{ "UnlockWeapon", &URoughSaveGame::execUnlockWeapon },
		{ "UpdateHighestSector", &URoughSaveGame::execUpdateHighestSector },
		{ "UpdateMostTeethInRun", &URoughSaveGame::execUpdateMostTeethInRun },
		{ "UpgradeUpgrade", &URoughSaveGame::execUpgradeUpgrade },
		{ "UpgradeWeapon", &URoughSaveGame::execUpgradeWeapon },
		{ "ValidateSaveData", &URoughSaveGame::execValidateSaveData },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(URoughSaveGame);
UClass* Z_Construct_UClass_URoughSaveGame_NoRegister()
{
	return URoughSaveGame::StaticClass();
}
struct Z_Construct_UClass_URoughSaveGame_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Save game class for Rough Reality\n * Stores persistent progression data between runs\n */" },
#endif
		{ "IncludePath", "SaveSystem/RoughSaveGame.h" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Save game class for Rough Reality\nStores persistent progression data between runs" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalTeeth_MetaData[] = {
		{ "Category", "Currency" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Total teeth (currency) accumulated across all runs */" },
#endif
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Total teeth (currency) accumulated across all runs" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeethCount_MetaData[] = {
		{ "Category", "Currency" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Alternative name for compatibility */" },
#endif
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Alternative name for compatibility" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrestigeLevel_MetaData[] = {
		{ "Category", "Progression" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Prestige system */" },
#endif
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prestige system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpgradeLevels_MetaData[] = {
		{ "Category", "Progression" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Upgrade levels */" },
#endif
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Upgrade levels" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnlockedFeatures_MetaData[] = {
		{ "Category", "Progression" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Unlocked features */" },
#endif
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Unlocked features" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalRuns_MetaData[] = {
		{ "Category", "Statistics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Statistics for progression */" },
#endif
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Statistics for progression" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalKills_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnlockedAchievements_MetaData[] = {
		{ "Category", "Achievements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Achievements */" },
#endif
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Achievements" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnlockedWeapons_MetaData[] = {
		{ "Category", "Progression" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Unlocked weapons */" },
#endif
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Unlocked weapons" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnlockedUpgrades_MetaData[] = {
		{ "Category", "Progression" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Unlocked upgrades */" },
#endif
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Unlocked upgrades" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnlockedTags_MetaData[] = {
		{ "Category", "Progression" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Unlocked gameplay tags (for various unlocks) */" },
#endif
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Unlocked gameplay tags (for various unlocks)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Statistics_MetaData[] = {
		{ "Category", "Statistics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Game statistics */" },
#endif
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Game statistics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MasterVolume_MetaData[] = {
		{ "Category", "Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Game settings */" },
#endif
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Game settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SFXVolume_MetaData[] = {
		{ "Category", "Settings" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MusicVolume_MetaData[] = {
		{ "Category", "Settings" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GraphicsQuality_MetaData[] = {
		{ "Category", "Settings" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CloudSaveID_MetaData[] = {
		{ "Category", "Cloud Save" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cloud Save Support */" },
#endif
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cloud Save Support" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastCloudSync_MetaData[] = {
		{ "Category", "Cloud Save" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCloudSaveEnabled_MetaData[] = {
		{ "Category", "Cloud Save" },
		{ "ModuleRelativePath", "SaveSystem/RoughSaveGame.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalTeeth;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeethCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PrestigeLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_UpgradeLevels_ValueProp;
	static const UECodeGen_Private::FNamePropertyParams NewProp_UpgradeLevels_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_UpgradeLevels;
	static const UECodeGen_Private::FNamePropertyParams NewProp_UnlockedFeatures_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_UnlockedFeatures;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalRuns;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalKills;
	static const UECodeGen_Private::FStructPropertyParams NewProp_UnlockedAchievements;
	static const UECodeGen_Private::FStructPropertyParams NewProp_UnlockedWeapons_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_UnlockedWeapons;
	static const UECodeGen_Private::FStructPropertyParams NewProp_UnlockedUpgrades_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_UnlockedUpgrades;
	static const UECodeGen_Private::FStructPropertyParams NewProp_UnlockedTags;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Statistics;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MasterVolume;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SFXVolume;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MusicVolume;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GraphicsQuality;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CloudSaveID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastCloudSync;
	static void NewProp_bCloudSaveEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCloudSaveEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_URoughSaveGame_AddKills, "AddKills" }, // 961763895
		{ &Z_Construct_UFunction_URoughSaveGame_AddPlayTime, "AddPlayTime" }, // 410263888
		{ &Z_Construct_UFunction_URoughSaveGame_CreateBackup, "CreateBackup" }, // 3957284378
		{ &Z_Construct_UFunction_URoughSaveGame_GetSaveDataChecksum, "GetSaveDataChecksum" }, // 1151248621
		{ &Z_Construct_UFunction_URoughSaveGame_GetSaveMetadata, "GetSaveMetadata" }, // 54674833
		{ &Z_Construct_UFunction_URoughSaveGame_GetUpgradeLevel, "GetUpgradeLevel" }, // 3597497074
		{ &Z_Construct_UFunction_URoughSaveGame_GetWeaponUpgradeLevel, "GetWeaponUpgradeLevel" }, // 2224268280
		{ &Z_Construct_UFunction_URoughSaveGame_HasUnlockedTag, "HasUnlockedTag" }, // 998794225
		{ &Z_Construct_UFunction_URoughSaveGame_IncrementDeaths, "IncrementDeaths" }, // 4165318908
		{ &Z_Construct_UFunction_URoughSaveGame_IncrementRuns, "IncrementRuns" }, // 3005217177
		{ &Z_Construct_UFunction_URoughSaveGame_IncrementSuccessfulRuns, "IncrementSuccessfulRuns" }, // 4283720522
		{ &Z_Construct_UFunction_URoughSaveGame_InitializeDefaults, "InitializeDefaults" }, // 4228574664
		{ &Z_Construct_UFunction_URoughSaveGame_IsUpgradeUnlocked, "IsUpgradeUnlocked" }, // 4140364318
		{ &Z_Construct_UFunction_URoughSaveGame_IsWeaponUnlocked, "IsWeaponUnlocked" }, // 3872836070
		{ &Z_Construct_UFunction_URoughSaveGame_RestoreFromBackup, "RestoreFromBackup" }, // 2367334057
		{ &Z_Construct_UFunction_URoughSaveGame_UnlockTag, "UnlockTag" }, // 3913354179
		{ &Z_Construct_UFunction_URoughSaveGame_UnlockUpgrade, "UnlockUpgrade" }, // 2963350704
		{ &Z_Construct_UFunction_URoughSaveGame_UnlockWeapon, "UnlockWeapon" }, // 2464021030
		{ &Z_Construct_UFunction_URoughSaveGame_UpdateHighestSector, "UpdateHighestSector" }, // 3840904963
		{ &Z_Construct_UFunction_URoughSaveGame_UpdateMostTeethInRun, "UpdateMostTeethInRun" }, // 2866310549
		{ &Z_Construct_UFunction_URoughSaveGame_UpgradeUpgrade, "UpgradeUpgrade" }, // 2324938010
		{ &Z_Construct_UFunction_URoughSaveGame_UpgradeWeapon, "UpgradeWeapon" }, // 2246575241
		{ &Z_Construct_UFunction_URoughSaveGame_ValidateSaveData, "ValidateSaveData" }, // 2613307766
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<URoughSaveGame>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_URoughSaveGame_Statics::NewProp_TotalTeeth = { "TotalTeeth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URoughSaveGame, TotalTeeth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalTeeth_MetaData), NewProp_TotalTeeth_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_URoughSaveGame_Statics::NewProp_TeethCount = { "TeethCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URoughSaveGame, TeethCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeethCount_MetaData), NewProp_TeethCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_URoughSaveGame_Statics::NewProp_PrestigeLevel = { "PrestigeLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URoughSaveGame, PrestigeLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrestigeLevel_MetaData), NewProp_PrestigeLevel_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_URoughSaveGame_Statics::NewProp_UpgradeLevels_ValueProp = { "UpgradeLevels", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UClass_URoughSaveGame_Statics::NewProp_UpgradeLevels_Key_KeyProp = { "UpgradeLevels_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_URoughSaveGame_Statics::NewProp_UpgradeLevels = { "UpgradeLevels", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URoughSaveGame, UpgradeLevels), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpgradeLevels_MetaData), NewProp_UpgradeLevels_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UClass_URoughSaveGame_Statics::NewProp_UnlockedFeatures_Inner = { "UnlockedFeatures", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_URoughSaveGame_Statics::NewProp_UnlockedFeatures = { "UnlockedFeatures", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URoughSaveGame, UnlockedFeatures), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnlockedFeatures_MetaData), NewProp_UnlockedFeatures_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_URoughSaveGame_Statics::NewProp_TotalRuns = { "TotalRuns", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URoughSaveGame, TotalRuns), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalRuns_MetaData), NewProp_TotalRuns_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_URoughSaveGame_Statics::NewProp_TotalKills = { "TotalKills", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URoughSaveGame, TotalKills), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalKills_MetaData), NewProp_TotalKills_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URoughSaveGame_Statics::NewProp_UnlockedAchievements = { "UnlockedAchievements", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URoughSaveGame, UnlockedAchievements), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnlockedAchievements_MetaData), NewProp_UnlockedAchievements_MetaData) }; // 3352185621
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URoughSaveGame_Statics::NewProp_UnlockedWeapons_Inner = { "UnlockedWeapons", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FUnlockedWeapon, METADATA_PARAMS(0, nullptr) }; // 3836544197
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_URoughSaveGame_Statics::NewProp_UnlockedWeapons = { "UnlockedWeapons", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URoughSaveGame, UnlockedWeapons), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnlockedWeapons_MetaData), NewProp_UnlockedWeapons_MetaData) }; // 3836544197
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URoughSaveGame_Statics::NewProp_UnlockedUpgrades_Inner = { "UnlockedUpgrades", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FUnlockedUpgrade, METADATA_PARAMS(0, nullptr) }; // 3348054647
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_URoughSaveGame_Statics::NewProp_UnlockedUpgrades = { "UnlockedUpgrades", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URoughSaveGame, UnlockedUpgrades), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnlockedUpgrades_MetaData), NewProp_UnlockedUpgrades_MetaData) }; // 3348054647
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URoughSaveGame_Statics::NewProp_UnlockedTags = { "UnlockedTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URoughSaveGame, UnlockedTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnlockedTags_MetaData), NewProp_UnlockedTags_MetaData) }; // 3352185621
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URoughSaveGame_Statics::NewProp_Statistics = { "Statistics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URoughSaveGame, Statistics), Z_Construct_UScriptStruct_FGameStatistics, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Statistics_MetaData), NewProp_Statistics_MetaData) }; // 1444449558
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URoughSaveGame_Statics::NewProp_MasterVolume = { "MasterVolume", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URoughSaveGame, MasterVolume), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MasterVolume_MetaData), NewProp_MasterVolume_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URoughSaveGame_Statics::NewProp_SFXVolume = { "SFXVolume", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URoughSaveGame, SFXVolume), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SFXVolume_MetaData), NewProp_SFXVolume_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_URoughSaveGame_Statics::NewProp_MusicVolume = { "MusicVolume", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URoughSaveGame, MusicVolume), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MusicVolume_MetaData), NewProp_MusicVolume_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_URoughSaveGame_Statics::NewProp_GraphicsQuality = { "GraphicsQuality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URoughSaveGame, GraphicsQuality), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GraphicsQuality_MetaData), NewProp_GraphicsQuality_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_URoughSaveGame_Statics::NewProp_CloudSaveID = { "CloudSaveID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URoughSaveGame, CloudSaveID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CloudSaveID_MetaData), NewProp_CloudSaveID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_URoughSaveGame_Statics::NewProp_LastCloudSync = { "LastCloudSync", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(URoughSaveGame, LastCloudSync), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastCloudSync_MetaData), NewProp_LastCloudSync_MetaData) };
void Z_Construct_UClass_URoughSaveGame_Statics::NewProp_bCloudSaveEnabled_SetBit(void* Obj)
{
	((URoughSaveGame*)Obj)->bCloudSaveEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_URoughSaveGame_Statics::NewProp_bCloudSaveEnabled = { "bCloudSaveEnabled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(URoughSaveGame), &Z_Construct_UClass_URoughSaveGame_Statics::NewProp_bCloudSaveEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCloudSaveEnabled_MetaData), NewProp_bCloudSaveEnabled_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_URoughSaveGame_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughSaveGame_Statics::NewProp_TotalTeeth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughSaveGame_Statics::NewProp_TeethCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughSaveGame_Statics::NewProp_PrestigeLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughSaveGame_Statics::NewProp_UpgradeLevels_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughSaveGame_Statics::NewProp_UpgradeLevels_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughSaveGame_Statics::NewProp_UpgradeLevels,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughSaveGame_Statics::NewProp_UnlockedFeatures_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughSaveGame_Statics::NewProp_UnlockedFeatures,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughSaveGame_Statics::NewProp_TotalRuns,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughSaveGame_Statics::NewProp_TotalKills,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughSaveGame_Statics::NewProp_UnlockedAchievements,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughSaveGame_Statics::NewProp_UnlockedWeapons_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughSaveGame_Statics::NewProp_UnlockedWeapons,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughSaveGame_Statics::NewProp_UnlockedUpgrades_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughSaveGame_Statics::NewProp_UnlockedUpgrades,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughSaveGame_Statics::NewProp_UnlockedTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughSaveGame_Statics::NewProp_Statistics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughSaveGame_Statics::NewProp_MasterVolume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughSaveGame_Statics::NewProp_SFXVolume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughSaveGame_Statics::NewProp_MusicVolume,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughSaveGame_Statics::NewProp_GraphicsQuality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughSaveGame_Statics::NewProp_CloudSaveID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughSaveGame_Statics::NewProp_LastCloudSync,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_URoughSaveGame_Statics::NewProp_bCloudSaveEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URoughSaveGame_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_URoughSaveGame_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_USaveGame,
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_URoughSaveGame_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_URoughSaveGame_Statics::ClassParams = {
	&URoughSaveGame::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_URoughSaveGame_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_URoughSaveGame_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_URoughSaveGame_Statics::Class_MetaDataParams), Z_Construct_UClass_URoughSaveGame_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_URoughSaveGame()
{
	if (!Z_Registration_Info_UClass_URoughSaveGame.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_URoughSaveGame.OuterSingleton, Z_Construct_UClass_URoughSaveGame_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_URoughSaveGame.OuterSingleton;
}
template<> ROUGHREALITY_API UClass* StaticClass<URoughSaveGame>()
{
	return URoughSaveGame::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(URoughSaveGame);
URoughSaveGame::~URoughSaveGame() {}
// End Class URoughSaveGame

// Begin Registration
struct Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_SaveSystem_RoughSaveGame_h_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FUnlockedWeapon::StaticStruct, Z_Construct_UScriptStruct_FUnlockedWeapon_Statics::NewStructOps, TEXT("UnlockedWeapon"), &Z_Registration_Info_UScriptStruct_UnlockedWeapon, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FUnlockedWeapon), 3836544197U) },
		{ FUnlockedUpgrade::StaticStruct, Z_Construct_UScriptStruct_FUnlockedUpgrade_Statics::NewStructOps, TEXT("UnlockedUpgrade"), &Z_Registration_Info_UScriptStruct_UnlockedUpgrade, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FUnlockedUpgrade), 3348054647U) },
		{ FGameStatistics::StaticStruct, Z_Construct_UScriptStruct_FGameStatistics_Statics::NewStructOps, TEXT("GameStatistics"), &Z_Registration_Info_UScriptStruct_GameStatistics, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FGameStatistics), 1444449558U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_URoughSaveGame, URoughSaveGame::StaticClass, TEXT("URoughSaveGame"), &Z_Registration_Info_UClass_URoughSaveGame, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(URoughSaveGame), 3211274137U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_SaveSystem_RoughSaveGame_h_1245948034(TEXT("/Script/RoughReality"),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_SaveSystem_RoughSaveGame_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_SaveSystem_RoughSaveGame_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_SaveSystem_RoughSaveGame_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_SaveSystem_RoughSaveGame_h_Statics::ScriptStructInfo),
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
