// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Core/GameEventSystem.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
 
class AActor;
class AGameEventSystem;
class UObject;
struct FGameEventData;
struct FGameplayTag;
#ifdef ROUGHREALITY_GameEventSystem_generated_h
#error "GameEventSystem.generated.h already included, missing '#pragma once' in GameEventSystem.h"
#endif
#define ROUGHREALITY_GameEventSystem_generated_h

#define FID_RoughReality_Source_RoughReality_Core_GameEventSystem_h_13_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FGameEventData_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FGameEventData>();

#define FID_RoughReality_Source_RoughReality_Core_GameEventSystem_h_57_DELEGATE \
ROUGHREALITY_API void FGameEventDelegate_DelegateWrapper(const FScriptDelegate& GameEventDelegate, FGameEventData const& EventData);


#define FID_RoughReality_Source_RoughReality_Core_GameEventSystem_h_58_DELEGATE \
ROUGHREALITY_API void FGameEventMulticastDelegate_DelegateWrapper(const FMulticastScriptDelegate& GameEventMulticastDelegate, FGameEventData const& EventData);


#define FID_RoughReality_Source_RoughReality_Core_GameEventSystem_h_63_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FEventListener_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FEventListener>();

#define FID_RoughReality_Source_RoughReality_Core_GameEventSystem_h_105_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnDelayedEventTimer); \
	DECLARE_FUNCTION(execBroadcastTimeRewind); \
	DECLARE_FUNCTION(execBroadcastBulletTimeActivated); \
	DECLARE_FUNCTION(execBroadcastLevelCompleted); \
	DECLARE_FUNCTION(execBroadcastWeaponFired); \
	DECLARE_FUNCTION(execBroadcastEnemyKilled); \
	DECLARE_FUNCTION(execBroadcastPlayerDamaged); \
	DECLARE_FUNCTION(execGetGameEventSystem); \
	DECLARE_FUNCTION(execRegisterValidEventTag); \
	DECLARE_FUNCTION(execIsValidEventTag); \
	DECLARE_FUNCTION(execCancelDelayedEvent); \
	DECLARE_FUNCTION(execBroadcastEventDelayed); \
	DECLARE_FUNCTION(execGetActiveEventTags); \
	DECLARE_FUNCTION(execLogEventStatistics); \
	DECLARE_FUNCTION(execEnableEventLogging); \
	DECLARE_FUNCTION(execGetEventCount); \
	DECLARE_FUNCTION(execClearEventHistory); \
	DECLARE_FUNCTION(execGetEventHistory); \
	DECLARE_FUNCTION(execIsEventFiltered); \
	DECLARE_FUNCTION(execSetEventFilter); \
	DECLARE_FUNCTION(execUnregisterAllListeners); \
	DECLARE_FUNCTION(execUnregisterEventListener); \
	DECLARE_FUNCTION(execRegisterEventListener); \
	DECLARE_FUNCTION(execBroadcastEventWithParameters); \
	DECLARE_FUNCTION(execBroadcastEventByTag); \
	DECLARE_FUNCTION(execBroadcastEvent);


#define FID_RoughReality_Source_RoughReality_Core_GameEventSystem_h_105_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAGameEventSystem(); \
	friend struct Z_Construct_UClass_AGameEventSystem_Statics; \
public: \
	DECLARE_CLASS(AGameEventSystem, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/RoughReality"), NO_API) \
	DECLARE_SERIALIZER(AGameEventSystem)


#define FID_RoughReality_Source_RoughReality_Core_GameEventSystem_h_105_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	AGameEventSystem(AGameEventSystem&&); \
	AGameEventSystem(const AGameEventSystem&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AGameEventSystem); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AGameEventSystem); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AGameEventSystem) \
	NO_API virtual ~AGameEventSystem();


#define FID_RoughReality_Source_RoughReality_Core_GameEventSystem_h_102_PROLOG
#define FID_RoughReality_Source_RoughReality_Core_GameEventSystem_h_105_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_RoughReality_Source_RoughReality_Core_GameEventSystem_h_105_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_Core_GameEventSystem_h_105_INCLASS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_Core_GameEventSystem_h_105_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ROUGHREALITY_API UClass* StaticClass<class AGameEventSystem>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_RoughReality_Source_RoughReality_Core_GameEventSystem_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
