// Copyright Epic Games, Inc. All Rights Reserved.

#include "WeaponDataAsset.h"

UWeaponDataAsset::UWeaponDataAsset()
{
	// Set default values
	AssetID = FName("DefaultWeapon");
	DisplayName = FText::FromString("Default Weapon");
	Description = FText::FromString("A basic weapon configuration");

	WeaponType = EWeaponType::Pistol;
	bIsAutomatic = false;
	bCanBurstFire = false;
	BurstCount = 3;

	// Default weapon stats
	WeaponStats.Damage = 25.0f;
	WeaponStats.FireRate = 600.0f;
	WeaponStats.Range = 5000.0f;
	WeaponStats.Accuracy = 0.95f;
	WeaponStats.MagazineSize = 15;
	WeaponStats.MaxAmmo = 150;
	WeaponStats.ReloadTime = 2.0f;
	WeaponStats.ProjectileCount = 1;
	WeaponStats.SpreadAngle = 1.0f;

	// Upgrade system defaults
	MaxUpgradeLevel = 5;
	UpgradeCosts = { 50, 100, 200, 400, 800 }; // Costs for levels 1-5
	DamageUpgradeMultiplier = 0.2f;
	FireRateUpgradeMultiplier = 0.15f;
	AccuracyUpgradeMultiplier = 0.1f;
	MagazineSizeUpgradeAmount = 5;

	// Unlock requirements
	UnlockCost = 0;
	RequiredSectorProgress = 0;

	// Classification
	WeaponTier = 1;
	bIsStartingWeapon = false;
	bIsUnlockable = true;

	// Special properties
	bCanPenetrate = false;
	PenetrationCount = 1;
	bHasExplosiveDamage = false;
	ExplosionRadius = 0.0f;
	ExplosionDamage = 0.0f;
}

FWeaponStats UWeaponDataAsset::GetUpgradedStats(int32 UpgradeLevel) const
{
	FWeaponStats UpgradedStats = WeaponStats;

	if (UpgradeLevel > 0)
	{
		// Apply damage upgrade
		UpgradedStats.Damage *= (1.0f + (DamageUpgradeMultiplier * UpgradeLevel));

		// Apply fire rate upgrade
		UpgradedStats.FireRate *= (1.0f + (FireRateUpgradeMultiplier * UpgradeLevel));

		// Apply accuracy upgrade (reduce spread)
		UpgradedStats.Accuracy = FMath::Clamp(
			UpgradedStats.Accuracy + (AccuracyUpgradeMultiplier * UpgradeLevel),
			0.0f,
			1.0f
		);

		// Apply magazine size upgrade
		UpgradedStats.MagazineSize += (MagazineSizeUpgradeAmount * UpgradeLevel);

		// Reduce spread angle with accuracy improvements
		UpgradedStats.SpreadAngle = FMath::Max(
			UpgradedStats.SpreadAngle * (1.0f - (AccuracyUpgradeMultiplier * UpgradeLevel * 0.5f)),
			0.1f
		);
	}

	return UpgradedStats;
}

int32 UWeaponDataAsset::GetUpgradeCost(int32 UpgradeLevel) const
{
	if (UpgradeLevel > 0 && UpgradeLevel <= UpgradeCosts.Num())
	{
		return UpgradeCosts[UpgradeLevel - 1];
	}
	return 0;
}

bool UWeaponDataAsset::CanUpgrade(int32 CurrentLevel) const
{
	return CurrentLevel < MaxUpgradeLevel;
}

bool UWeaponDataAsset::ValidateAsset() const
{
	bool bIsValid = Super::ValidateAsset();

	// Validate weapon stats
	if (WeaponStats.Damage <= 0.0f)
	{
		UE_LOG(LogTemp, Error, TEXT("WeaponDataAsset %s: Damage must be greater than 0"), *GetName());
		bIsValid = false;
	}

	if (WeaponStats.FireRate <= 0.0f)
	{
		UE_LOG(LogTemp, Error, TEXT("WeaponDataAsset %s: FireRate must be greater than 0"), *GetName());
		bIsValid = false;
	}

	if (WeaponStats.Range <= 0.0f)
	{
		UE_LOG(LogTemp, Error, TEXT("WeaponDataAsset %s: Range must be greater than 0"), *GetName());
		bIsValid = false;
	}

	if (WeaponStats.Accuracy < 0.0f || WeaponStats.Accuracy > 1.0f)
	{
		UE_LOG(LogTemp, Error, TEXT("WeaponDataAsset %s: Accuracy must be between 0 and 1"), *GetName());
		bIsValid = false;
	}

	if (WeaponStats.MagazineSize <= 0)
	{
		UE_LOG(LogTemp, Error, TEXT("WeaponDataAsset %s: MagazineSize must be greater than 0"), *GetName());
		bIsValid = false;
	}

	if (WeaponStats.ReloadTime <= 0.0f)
	{
		UE_LOG(LogTemp, Error, TEXT("WeaponDataAsset %s: ReloadTime must be greater than 0"), *GetName());
		bIsValid = false;
	}

	if (WeaponStats.ProjectileCount <= 0)
	{
		UE_LOG(LogTemp, Error, TEXT("WeaponDataAsset %s: ProjectileCount must be greater than 0"), *GetName());
		bIsValid = false;
	}

	// Validate upgrade costs array
	if (UpgradeCosts.Num() != MaxUpgradeLevel)
	{
		UE_LOG(LogTemp, Warning, TEXT("WeaponDataAsset %s: UpgradeCosts array size (%d) doesn't match MaxUpgradeLevel (%d)"), 
			*GetName(), UpgradeCosts.Num(), MaxUpgradeLevel);
	}

	// Validate tier
	if (WeaponTier < 1 || WeaponTier > 5)
	{
		UE_LOG(LogTemp, Warning, TEXT("WeaponDataAsset %s: WeaponTier should be between 1 and 5"), *GetName());
	}

	// Validate explosive properties
	if (bHasExplosiveDamage)
	{
		if (ExplosionRadius <= 0.0f)
		{
			UE_LOG(LogTemp, Error, TEXT("WeaponDataAsset %s: ExplosionRadius must be greater than 0 for explosive weapons"), *GetName());
			bIsValid = false;
		}

		if (ExplosionDamage <= 0.0f)
		{
			UE_LOG(LogTemp, Error, TEXT("WeaponDataAsset %s: ExplosionDamage must be greater than 0 for explosive weapons"), *GetName());
			bIsValid = false;
		}
	}

	return bIsValid;
}

FString UWeaponDataAsset::GetWeaponTypeString() const
{
	switch (WeaponType)
	{
	case EWeaponType::Pistol:
		return TEXT("Pistol");
	case EWeaponType::AssaultRifle:
		return TEXT("Assault Rifle");
	case EWeaponType::Shotgun:
		return TEXT("Shotgun");
	case EWeaponType::RocketLauncher:
		return TEXT("Rocket Launcher");
	case EWeaponType::Chaingun:
		return TEXT("Chaingun");
	case EWeaponType::Sniper:
		return TEXT("Sniper Rifle");
	default:
		return TEXT("Unknown");
	}
}

FLinearColor UWeaponDataAsset::GetWeaponTierColor() const
{
	return GetTierColor(WeaponTier);
}

bool UWeaponDataAsset::MeetsUnlockRequirements(const FGameplayTagContainer& PlayerTags, int32 PlayerSectorProgress) const
{
	// Check sector progress requirement
	if (PlayerSectorProgress < RequiredSectorProgress)
	{
		return false;
	}

	// Check required tags
	if (RequiredTags.Num() > 0 && !PlayerTags.HasAll(RequiredTags))
	{
		return false;
	}

	return true;
}

FLinearColor UWeaponDataAsset::GetTierColor(int32 Tier) const
{
	switch (Tier)
	{
	case 1:
		return FLinearColor::Gray;      // Common - Gray
	case 2:
		return FLinearColor::Green;     // Uncommon - Green
	case 3:
		return FLinearColor::Blue;      // Rare - Blue
	case 4:
		return FLinearColor(1.0f, 0.5f, 1.0f); // Epic - Purple
	case 5:
		return FLinearColor(1.0f, 0.8f, 0.0f); // Legendary - Gold
	default:
		return FLinearColor::White;     // Default - White
	}
}
