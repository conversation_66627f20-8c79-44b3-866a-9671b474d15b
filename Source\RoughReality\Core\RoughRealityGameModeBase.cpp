// Copyright Epic Games, Inc. All Rights Reserved.

#include "RoughRealityGameModeBase.h"
#include "../Characters/RookieCharacter.h"
#include "../LevelGeneration/ProceduralLevelBuilder.h"
#include "../SaveSystem/RoughSaveGame.h"
#include "GameplayTagsManager.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/World.h"

ARoughRealityGameModeBase::ARoughRealityGameModeBase()
{
	// Set default pawn class to Rookie
	DefaultPawnClass = ARookieCharacter::StaticClass();

	// Initialize game state tags
	MenuState = FGameplayTag::RequestGameplayTag(FName("GameState.Menu"));
	PlayingState = FGameplayTag::RequestGameplayTag(FName("GameState.Playing"));
	PausedState = FGameplayTag::RequestGameplayTag(FName("GameState.Paused"));
	GameOverState = FGameplayTag::RequestGameplayTag(FName("GameState.GameOver"));
	VictoryState = FGameplayTag::RequestGameplayTag(FName("GameState.Victory"));

	CurrentGameState = MenuState;
	CurrentRun = 0;
	CurrentSector = 0;
	CurrentLevel = 0;
	TeethThisRun = 0;
}

void ARoughRealityGameModeBase::BeginPlay()
{
	Super::BeginPlay();
	InitializeGameMode();
}

void ARoughRealityGameModeBase::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	SaveGameData();
	Super::EndPlay(EndPlayReason);
}

void ARoughRealityGameModeBase::InitializeGameMode()
{
	SetupLevelBuilder();
	InitializeSaveGame();
	SetGameState(MenuState);
}

void ARoughRealityGameModeBase::SetupLevelBuilder()
{
	// Find or spawn the level builder
	LevelBuilder = GetWorld()->SpawnActor<AProceduralLevelBuilder>();
	if (LevelBuilder)
	{
		UE_LOG(LogTemp, Log, TEXT("Level Builder initialized"));
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to initialize Level Builder"));
	}
}

void ARoughRealityGameModeBase::InitializeSaveGame()
{
	// Try to load existing save game
	if (UGameplayStatics::DoesSaveGameExist(TEXT("RoughRealitySave"), 0))
	{
		SaveGame = Cast<URoughSaveGame>(UGameplayStatics::LoadGameFromSlot(TEXT("RoughRealitySave"), 0));
	}

	// Create new save game if loading failed
	if (!SaveGame)
	{
		SaveGame = Cast<URoughSaveGame>(UGameplayStatics::CreateSaveGameObject(URoughSaveGame::StaticClass()));
		if (SaveGame)
		{
			SaveGame->InitializeDefaults();
		}
	}

	if (SaveGame)
	{
		UE_LOG(LogTemp, Log, TEXT("Save Game initialized"));
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to initialize Save Game"));
	}
}

void ARoughRealityGameModeBase::SetGameState(const FGameplayTag& NewState)
{
	if (CurrentGameState != NewState)
	{
		FGameplayTag PreviousState = CurrentGameState;
		CurrentGameState = NewState;
		
		UE_LOG(LogTemp, Log, TEXT("Game State changed from %s to %s"), 
			*PreviousState.ToString(), *CurrentGameState.ToString());
		
		OnGameStateChanged.Broadcast(CurrentGameState);
	}
}

bool ARoughRealityGameModeBase::IsInGameState(const FGameplayTag& State) const
{
	return CurrentGameState == State;
}

void ARoughRealityGameModeBase::StartNewRun()
{
	CurrentRun++;
	CurrentSector = 0;
	CurrentLevel = 0;
	TeethThisRun = 0;

	SetGameState(PlayingState);

	// Generate first level
	if (LevelBuilder)
	{
		LevelBuilder->GenerateLevel(CurrentSector, CurrentLevel);
	}

	UE_LOG(LogTemp, Log, TEXT("Started new run #%d"), CurrentRun);
}

void ARoughRealityGameModeBase::EndCurrentRun(bool bSuccess)
{
	if (bSuccess)
	{
		SetGameState(VictoryState);
		// Add teeth to persistent save
		if (SaveGame)
		{
			SaveGame->TotalTeeth += TeethThisRun;
		}
	}
	else
	{
		SetGameState(GameOverState);
	}

	OnRunCompleted.Broadcast(bSuccess);
	SaveGameData();

	UE_LOG(LogTemp, Log, TEXT("Run #%d ended. Success: %s, Teeth collected: %d"), 
		CurrentRun, bSuccess ? TEXT("Yes") : TEXT("No"), TeethThisRun);
}

void ARoughRealityGameModeBase::AdvanceToNextLevel()
{
	CurrentLevel++;
	
	if (LevelBuilder)
	{
		LevelBuilder->GenerateLevel(CurrentSector, CurrentLevel);
	}

	UE_LOG(LogTemp, Log, TEXT("Advanced to Level %d in Sector %d"), CurrentLevel, CurrentSector);
}

void ARoughRealityGameModeBase::AdvanceToNextSector()
{
	CurrentSector++;
	CurrentLevel = 0;
	
	if (LevelBuilder)
	{
		LevelBuilder->GenerateLevel(CurrentSector, CurrentLevel);
	}

	UE_LOG(LogTemp, Log, TEXT("Advanced to Sector %d"), CurrentSector);
}

void ARoughRealityGameModeBase::HandlePlayerDeath(ARookieCharacter* DeadPlayer)
{
	OnPlayerDied.Broadcast(DeadPlayer);
	
	// In roguelike mode, death ends the run
	EndCurrentRun(false);
}

void ARoughRealityGameModeBase::RespawnPlayer()
{
	// Restart the level or run based on game mode
	RestartPlayer(GetWorld()->GetFirstPlayerController());
}

void ARoughRealityGameModeBase::AddTeeth(int32 Amount)
{
	TeethThisRun += Amount;
	UE_LOG(LogTemp, Log, TEXT("Added %d teeth. Total this run: %d"), Amount, TeethThisRun);
}

bool ARoughRealityGameModeBase::SpendTeeth(int32 Amount)
{
	if (GetTotalTeeth() >= Amount)
	{
		if (SaveGame)
		{
			SaveGame->TotalTeeth -= Amount;
			return true;
		}
	}
	return false;
}

int32 ARoughRealityGameModeBase::GetTotalTeeth() const
{
	return SaveGame ? SaveGame->TotalTeeth : 0;
}

void ARoughRealityGameModeBase::SaveGameData()
{
	if (SaveGame)
	{
		UGameplayStatics::SaveGameToSlot(SaveGame, TEXT("RoughRealitySave"), 0);
		UE_LOG(LogTemp, Log, TEXT("Game saved"));
	}
}

void ARoughRealityGameModeBase::LoadGameData()
{
	InitializeSaveGame();
}

void ARoughRealityGameModeBase::ResetSaveData()
{
	if (SaveGame)
	{
		SaveGame->InitializeDefaults();
		SaveGameData();
		UE_LOG(LogTemp, Log, TEXT("Save data reset"));
	}
}
