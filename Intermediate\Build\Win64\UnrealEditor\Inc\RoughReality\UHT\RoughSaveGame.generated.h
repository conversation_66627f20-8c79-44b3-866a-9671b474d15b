// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "SaveSystem/RoughSaveGame.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
 
struct FGameplayTag;
#ifdef ROUGHREALITY_RoughSaveGame_generated_h
#error "RoughSaveGame.generated.h already included, missing '#pragma once' in RoughSaveGame.h"
#endif
#define ROUGHREALITY_RoughSaveGame_generated_h

#define FID_RoughReality_Source_RoughReality_SaveSystem_RoughSaveGame_h_13_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FUnlockedWeapon_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FUnlockedWeapon>();

#define FID_RoughReality_Source_RoughReality_SaveSystem_RoughSaveGame_h_40_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FUnlockedUpgrade_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FUnlockedUpgrade>();

#define FID_RoughReality_Source_RoughReality_SaveSystem_RoughSaveGame_h_67_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FGameStatistics_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FGameStatistics>();

#define FID_RoughReality_Source_RoughReality_SaveSystem_RoughSaveGame_h_109_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetSaveMetadata); \
	DECLARE_FUNCTION(execRestoreFromBackup); \
	DECLARE_FUNCTION(execCreateBackup); \
	DECLARE_FUNCTION(execGetSaveDataChecksum); \
	DECLARE_FUNCTION(execValidateSaveData); \
	DECLARE_FUNCTION(execUpdateMostTeethInRun); \
	DECLARE_FUNCTION(execUpdateHighestSector); \
	DECLARE_FUNCTION(execAddPlayTime); \
	DECLARE_FUNCTION(execIncrementDeaths); \
	DECLARE_FUNCTION(execAddKills); \
	DECLARE_FUNCTION(execIncrementSuccessfulRuns); \
	DECLARE_FUNCTION(execIncrementRuns); \
	DECLARE_FUNCTION(execUnlockTag); \
	DECLARE_FUNCTION(execHasUnlockedTag); \
	DECLARE_FUNCTION(execUpgradeUpgrade); \
	DECLARE_FUNCTION(execGetUpgradeLevel); \
	DECLARE_FUNCTION(execUnlockUpgrade); \
	DECLARE_FUNCTION(execIsUpgradeUnlocked); \
	DECLARE_FUNCTION(execUpgradeWeapon); \
	DECLARE_FUNCTION(execGetWeaponUpgradeLevel); \
	DECLARE_FUNCTION(execUnlockWeapon); \
	DECLARE_FUNCTION(execIsWeaponUnlocked); \
	DECLARE_FUNCTION(execInitializeDefaults);


#define FID_RoughReality_Source_RoughReality_SaveSystem_RoughSaveGame_h_109_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesURoughSaveGame(); \
	friend struct Z_Construct_UClass_URoughSaveGame_Statics; \
public: \
	DECLARE_CLASS(URoughSaveGame, USaveGame, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/RoughReality"), NO_API) \
	DECLARE_SERIALIZER(URoughSaveGame)


#define FID_RoughReality_Source_RoughReality_SaveSystem_RoughSaveGame_h_109_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	URoughSaveGame(URoughSaveGame&&); \
	URoughSaveGame(const URoughSaveGame&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, URoughSaveGame); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(URoughSaveGame); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(URoughSaveGame) \
	NO_API virtual ~URoughSaveGame();


#define FID_RoughReality_Source_RoughReality_SaveSystem_RoughSaveGame_h_106_PROLOG
#define FID_RoughReality_Source_RoughReality_SaveSystem_RoughSaveGame_h_109_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_RoughReality_Source_RoughReality_SaveSystem_RoughSaveGame_h_109_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_SaveSystem_RoughSaveGame_h_109_INCLASS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_SaveSystem_RoughSaveGame_h_109_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ROUGHREALITY_API UClass* StaticClass<class URoughSaveGame>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_RoughReality_Source_RoughReality_SaveSystem_RoughSaveGame_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
