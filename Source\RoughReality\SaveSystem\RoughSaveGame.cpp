// Copyright Epic Games, Inc. All Rights Reserved.

#include "RoughSaveGame.h"
#include "GameplayTagsManager.h"

URoughSaveGame::URoughSaveGame()
{
	TotalTeeth = 0;
	MasterVolume = 1.0f;
	SFXVolume = 1.0f;
	MusicVolume = 1.0f;
	GraphicsQuality = 2;
}

void URoughSaveGame::InitializeDefaults()
{
	TotalTeeth = 0;
	UnlockedWeapons.Empty();
	UnlockedUpgrades.Empty();
	UnlockedTags.Reset();
	Statistics = FGameStatistics();

	// Unlock starting weapon (Venom .50 Pistol)
	UnlockWeapon(FName("Venom50"));

	// Set default settings
	MasterVolume = 1.0f;
	SFXVolume = 1.0f;
	MusicVolume = 1.0f;
	GraphicsQuality = 2;

	UE_LOG(LogTemp, Log, TEXT("Save game initialized with defaults"));
}

bool URoughSaveGame::IsWeaponUnlocked(FName WeaponID) const
{
	const FUnlockedWeapon* WeaponData = FindWeaponData(WeaponID);
	return WeaponData ? WeaponData->bIsUnlocked : false;
}

void URoughSaveGame::UnlockWeapon(FName WeaponID)
{
	FUnlockedWeapon* WeaponData = FindWeaponData(WeaponID);
	if (WeaponData)
	{
		WeaponData->bIsUnlocked = true;
	}
	else
	{
		UnlockedWeapons.Add(FUnlockedWeapon(WeaponID, true, 0));
	}
	
	UE_LOG(LogTemp, Log, TEXT("Weapon unlocked: %s"), *WeaponID.ToString());
}

int32 URoughSaveGame::GetWeaponUpgradeLevel(FName WeaponID) const
{
	const FUnlockedWeapon* WeaponData = FindWeaponData(WeaponID);
	return WeaponData ? WeaponData->UpgradeLevel : 0;
}

void URoughSaveGame::UpgradeWeapon(FName WeaponID)
{
	FUnlockedWeapon* WeaponData = FindWeaponData(WeaponID);
	if (WeaponData)
	{
		WeaponData->UpgradeLevel++;
		UE_LOG(LogTemp, Log, TEXT("Weapon upgraded: %s to level %d"), 
			*WeaponID.ToString(), WeaponData->UpgradeLevel);
	}
}

bool URoughSaveGame::IsUpgradeUnlocked(FName UpgradeID) const
{
	const FUnlockedUpgrade* UpgradeData = FindUpgradeData(UpgradeID);
	return UpgradeData ? UpgradeData->bIsUnlocked : false;
}

void URoughSaveGame::UnlockUpgrade(FName UpgradeID)
{
	FUnlockedUpgrade* UpgradeData = FindUpgradeData(UpgradeID);
	if (UpgradeData)
	{
		UpgradeData->bIsUnlocked = true;
	}
	else
	{
		UnlockedUpgrades.Add(FUnlockedUpgrade(UpgradeID, true, 0));
	}
	
	UE_LOG(LogTemp, Log, TEXT("Upgrade unlocked: %s"), *UpgradeID.ToString());
}

int32 URoughSaveGame::GetUpgradeLevel(FName UpgradeID) const
{
	const FUnlockedUpgrade* UpgradeData = FindUpgradeData(UpgradeID);
	return UpgradeData ? UpgradeData->Level : 0;
}

void URoughSaveGame::UpgradeUpgrade(FName UpgradeID)
{
	FUnlockedUpgrade* UpgradeData = FindUpgradeData(UpgradeID);
	if (UpgradeData)
	{
		UpgradeData->Level++;
		UE_LOG(LogTemp, Log, TEXT("Upgrade upgraded: %s to level %d"), 
			*UpgradeID.ToString(), UpgradeData->Level);
	}
}

bool URoughSaveGame::HasUnlockedTag(const FGameplayTag& Tag) const
{
	return UnlockedTags.HasTag(Tag);
}

void URoughSaveGame::UnlockTag(const FGameplayTag& Tag)
{
	UnlockedTags.AddTag(Tag);
	UE_LOG(LogTemp, Log, TEXT("Tag unlocked: %s"), *Tag.ToString());
}

void URoughSaveGame::IncrementRuns()
{
	Statistics.TotalRuns++;
}

void URoughSaveGame::IncrementSuccessfulRuns()
{
	Statistics.SuccessfulRuns++;
}

void URoughSaveGame::AddKills(int32 KillCount)
{
	Statistics.TotalKills += KillCount;
}

void URoughSaveGame::IncrementDeaths()
{
	Statistics.TotalDeaths++;
}

void URoughSaveGame::AddPlayTime(float DeltaTime)
{
	Statistics.TotalPlayTime += DeltaTime;
}

void URoughSaveGame::UpdateHighestSector(int32 SectorIndex)
{
	if (SectorIndex > Statistics.HighestSectorReached)
	{
		Statistics.HighestSectorReached = SectorIndex;
	}
}

void URoughSaveGame::UpdateMostTeethInRun(int32 TeethCount)
{
	if (TeethCount > Statistics.MostTeethInSingleRun)
	{
		Statistics.MostTeethInSingleRun = TeethCount;
	}
}

FUnlockedWeapon* URoughSaveGame::FindWeaponData(FName WeaponID)
{
	return UnlockedWeapons.FindByPredicate([WeaponID](const FUnlockedWeapon& Weapon)
	{
		return Weapon.WeaponID == WeaponID;
	});
}

const FUnlockedWeapon* URoughSaveGame::FindWeaponData(FName WeaponID) const
{
	return UnlockedWeapons.FindByPredicate([WeaponID](const FUnlockedWeapon& Weapon)
	{
		return Weapon.WeaponID == WeaponID;
	});
}

FUnlockedUpgrade* URoughSaveGame::FindUpgradeData(FName UpgradeID)
{
	return UnlockedUpgrades.FindByPredicate([UpgradeID](const FUnlockedUpgrade& Upgrade)
	{
		return Upgrade.UpgradeID == UpgradeID;
	});
}

const FUnlockedUpgrade* URoughSaveGame::FindUpgradeData(FName UpgradeID) const
{
	return UnlockedUpgrades.FindByPredicate([UpgradeID](const FUnlockedUpgrade& Upgrade)
	{
		return Upgrade.UpgradeID == UpgradeID;
	});
}
