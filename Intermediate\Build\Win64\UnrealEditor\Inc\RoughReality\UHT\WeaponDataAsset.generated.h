// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "DataAssets/WeaponDataAsset.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
struct FGameplayTagContainer;
struct FLinearColor;
struct FWeaponStats;
#ifdef ROUGHREALITY_WeaponDataAsset_generated_h
#error "WeaponDataAsset.generated.h already included, missing '#pragma once' in WeaponDataAsset.h"
#endif
#define ROUGHREALITY_WeaponDataAsset_generated_h

#define FID_RoughReality_Source_RoughReality_DataAssets_WeaponDataAsset_h_24_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execMeetsUnlockRequirements); \
	DECLARE_FUNCTION(execGetWeaponTierColor); \
	DECLARE_FUNCTION(execGetWeaponTypeString); \
	DECLARE_FUNCTION(execCanUpgrade); \
	DECLARE_FUNCTION(execGetUpgradeCost); \
	DECLARE_FUNCTION(execGetUpgradedStats);


#define FID_RoughReality_Source_RoughReality_DataAssets_WeaponDataAsset_h_24_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUWeaponDataAsset(); \
	friend struct Z_Construct_UClass_UWeaponDataAsset_Statics; \
public: \
	DECLARE_CLASS(UWeaponDataAsset, URoughRealityDataAsset, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/RoughReality"), NO_API) \
	DECLARE_SERIALIZER(UWeaponDataAsset)


#define FID_RoughReality_Source_RoughReality_DataAssets_WeaponDataAsset_h_24_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UWeaponDataAsset(UWeaponDataAsset&&); \
	UWeaponDataAsset(const UWeaponDataAsset&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UWeaponDataAsset); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UWeaponDataAsset); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UWeaponDataAsset) \
	NO_API virtual ~UWeaponDataAsset();


#define FID_RoughReality_Source_RoughReality_DataAssets_WeaponDataAsset_h_21_PROLOG
#define FID_RoughReality_Source_RoughReality_DataAssets_WeaponDataAsset_h_24_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_RoughReality_Source_RoughReality_DataAssets_WeaponDataAsset_h_24_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_DataAssets_WeaponDataAsset_h_24_INCLASS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_DataAssets_WeaponDataAsset_h_24_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ROUGHREALITY_API UClass* StaticClass<class UWeaponDataAsset>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_RoughReality_Source_RoughReality_DataAssets_WeaponDataAsset_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
