// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RoughReality/Progression/ProgressionManager.h"
#include "Runtime/GameplayTags/Classes/GameplayTagContainer.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeProgressionManager() {}

// Begin Cross Module References
COREUOBJECT_API UClass* Z_Construct_UClass_UObject_NoRegister();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_UDataTable_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FTableRowBase();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTag();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
ROUGHREALITY_API UClass* Z_Construct_UClass_AProgressionManager();
ROUGHREALITY_API UClass* Z_Construct_UClass_AProgressionManager_NoRegister();
ROUGHREALITY_API UClass* Z_Construct_UClass_URoughSaveGame_NoRegister();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnPrestigeLevelUp__DelegateSignature();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnUnlockAchieved__DelegateSignature();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnUpgradePurchased__DelegateSignature();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FPrestigeLevel();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FUnlockCondition();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FUpgradeNode();
UPackage* Z_Construct_UPackage__Script_RoughReality();
// End Cross Module References

// Begin ScriptStruct FUpgradeNode
static_assert(std::is_polymorphic<FUpgradeNode>() == std::is_polymorphic<FTableRowBase>(), "USTRUCT FUpgradeNode cannot be polymorphic unless super FTableRowBase is polymorphic");
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_UpgradeNode;
class UScriptStruct* FUpgradeNode::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_UpgradeNode.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_UpgradeNode.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FUpgradeNode, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("UpgradeNode"));
	}
	return Z_Registration_Info_UScriptStruct_UpgradeNode.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FUpgradeNode>()
{
	return FUpgradeNode::StaticStruct();
}
struct Z_Construct_UScriptStruct_FUpgradeNode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpgradeID_MetaData[] = {
		{ "Category", "Upgrade" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DisplayName_MetaData[] = {
		{ "Category", "Upgrade" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "Category", "Upgrade" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Icon_MetaData[] = {
		{ "Category", "Upgrade" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Cost_MetaData[] = {
		{ "Category", "Upgrade" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxLevel_MetaData[] = {
		{ "Category", "Upgrade" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Prerequisites_MetaData[] = {
		{ "Category", "Upgrade" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredTags_MetaData[] = {
		{ "Category", "Upgrade" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GrantedTags_MetaData[] = {
		{ "Category", "Upgrade" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StatModifiers_MetaData[] = {
		{ "Category", "Upgrade" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsWeaponUpgrade_MetaData[] = {
		{ "Category", "Upgrade" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetWeapon_MetaData[] = {
		{ "Category", "Upgrade" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_UpgradeID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_DisplayName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_Description;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_Icon;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Cost;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxLevel;
	static const UECodeGen_Private::FNamePropertyParams NewProp_Prerequisites_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Prerequisites;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RequiredTags;
	static const UECodeGen_Private::FStructPropertyParams NewProp_GrantedTags;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StatModifiers_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_StatModifiers_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_StatModifiers;
	static void NewProp_bIsWeaponUpgrade_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsWeaponUpgrade;
	static const UECodeGen_Private::FNamePropertyParams NewProp_TargetWeapon;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FUpgradeNode>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_UpgradeID = { "UpgradeID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUpgradeNode, UpgradeID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpgradeID_MetaData), NewProp_UpgradeID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_DisplayName = { "DisplayName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUpgradeNode, DisplayName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DisplayName_MetaData), NewProp_DisplayName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUpgradeNode, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_Icon = { "Icon", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUpgradeNode, Icon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Icon_MetaData), NewProp_Icon_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_Cost = { "Cost", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUpgradeNode, Cost), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Cost_MetaData), NewProp_Cost_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_MaxLevel = { "MaxLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUpgradeNode, MaxLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxLevel_MetaData), NewProp_MaxLevel_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_Prerequisites_Inner = { "Prerequisites", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_Prerequisites = { "Prerequisites", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUpgradeNode, Prerequisites), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Prerequisites_MetaData), NewProp_Prerequisites_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_RequiredTags = { "RequiredTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUpgradeNode, RequiredTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredTags_MetaData), NewProp_RequiredTags_MetaData) }; // 3352185621
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_GrantedTags = { "GrantedTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUpgradeNode, GrantedTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GrantedTags_MetaData), NewProp_GrantedTags_MetaData) }; // 3352185621
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_StatModifiers_ValueProp = { "StatModifiers", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_StatModifiers_Key_KeyProp = { "StatModifiers_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_StatModifiers = { "StatModifiers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUpgradeNode, StatModifiers), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StatModifiers_MetaData), NewProp_StatModifiers_MetaData) };
void Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_bIsWeaponUpgrade_SetBit(void* Obj)
{
	((FUpgradeNode*)Obj)->bIsWeaponUpgrade = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_bIsWeaponUpgrade = { "bIsWeaponUpgrade", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FUpgradeNode), &Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_bIsWeaponUpgrade_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsWeaponUpgrade_MetaData), NewProp_bIsWeaponUpgrade_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_TargetWeapon = { "TargetWeapon", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUpgradeNode, TargetWeapon), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetWeapon_MetaData), NewProp_TargetWeapon_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FUpgradeNode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_UpgradeID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_DisplayName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_Description,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_Icon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_Cost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_MaxLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_Prerequisites_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_Prerequisites,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_RequiredTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_GrantedTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_StatModifiers_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_StatModifiers_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_StatModifiers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_bIsWeaponUpgrade,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewProp_TargetWeapon,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FUpgradeNode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FUpgradeNode_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	Z_Construct_UScriptStruct_FTableRowBase,
	&NewStructOps,
	"UpgradeNode",
	Z_Construct_UScriptStruct_FUpgradeNode_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FUpgradeNode_Statics::PropPointers),
	sizeof(FUpgradeNode),
	alignof(FUpgradeNode),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FUpgradeNode_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FUpgradeNode_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FUpgradeNode()
{
	if (!Z_Registration_Info_UScriptStruct_UpgradeNode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_UpgradeNode.InnerSingleton, Z_Construct_UScriptStruct_FUpgradeNode_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_UpgradeNode.InnerSingleton;
}
// End ScriptStruct FUpgradeNode

// Begin ScriptStruct FUnlockCondition
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_UnlockCondition;
class UScriptStruct* FUnlockCondition::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_UnlockCondition.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_UnlockCondition.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FUnlockCondition, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("UnlockCondition"));
	}
	return Z_Registration_Info_UScriptStruct_UnlockCondition.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FUnlockCondition>()
{
	return FUnlockCondition::StaticStruct();
}
struct Z_Construct_UScriptStruct_FUnlockCondition_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnlockID_MetaData[] = {
		{ "Category", "Unlock" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DisplayName_MetaData[] = {
		{ "Category", "Unlock" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "Category", "Unlock" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredRuns_MetaData[] = {
		{ "Category", "Unlock" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredKills_MetaData[] = {
		{ "Category", "Unlock" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredSectorCompletions_MetaData[] = {
		{ "Category", "Unlock" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredAccuracy_MetaData[] = {
		{ "Category", "Unlock" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredCompletionTime_MetaData[] = {
		{ "Category", "Unlock" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredWeaponKills_MetaData[] = {
		{ "Category", "Unlock" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredAchievements_MetaData[] = {
		{ "Category", "Unlock" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_UnlockID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_DisplayName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_Description;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RequiredRuns;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RequiredKills;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RequiredSectorCompletions;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RequiredAccuracy;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RequiredCompletionTime;
	static const UECodeGen_Private::FNamePropertyParams NewProp_RequiredWeaponKills_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RequiredWeaponKills;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RequiredAchievements;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FUnlockCondition>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FUnlockCondition_Statics::NewProp_UnlockID = { "UnlockID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUnlockCondition, UnlockID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnlockID_MetaData), NewProp_UnlockID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FUnlockCondition_Statics::NewProp_DisplayName = { "DisplayName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUnlockCondition, DisplayName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DisplayName_MetaData), NewProp_DisplayName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FUnlockCondition_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUnlockCondition, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FUnlockCondition_Statics::NewProp_RequiredRuns = { "RequiredRuns", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUnlockCondition, RequiredRuns), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredRuns_MetaData), NewProp_RequiredRuns_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FUnlockCondition_Statics::NewProp_RequiredKills = { "RequiredKills", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUnlockCondition, RequiredKills), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredKills_MetaData), NewProp_RequiredKills_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FUnlockCondition_Statics::NewProp_RequiredSectorCompletions = { "RequiredSectorCompletions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUnlockCondition, RequiredSectorCompletions), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredSectorCompletions_MetaData), NewProp_RequiredSectorCompletions_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FUnlockCondition_Statics::NewProp_RequiredAccuracy = { "RequiredAccuracy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUnlockCondition, RequiredAccuracy), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredAccuracy_MetaData), NewProp_RequiredAccuracy_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FUnlockCondition_Statics::NewProp_RequiredCompletionTime = { "RequiredCompletionTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUnlockCondition, RequiredCompletionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredCompletionTime_MetaData), NewProp_RequiredCompletionTime_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FUnlockCondition_Statics::NewProp_RequiredWeaponKills_Inner = { "RequiredWeaponKills", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FUnlockCondition_Statics::NewProp_RequiredWeaponKills = { "RequiredWeaponKills", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUnlockCondition, RequiredWeaponKills), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredWeaponKills_MetaData), NewProp_RequiredWeaponKills_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FUnlockCondition_Statics::NewProp_RequiredAchievements = { "RequiredAchievements", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FUnlockCondition, RequiredAchievements), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredAchievements_MetaData), NewProp_RequiredAchievements_MetaData) }; // 3352185621
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FUnlockCondition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUnlockCondition_Statics::NewProp_UnlockID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUnlockCondition_Statics::NewProp_DisplayName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUnlockCondition_Statics::NewProp_Description,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUnlockCondition_Statics::NewProp_RequiredRuns,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUnlockCondition_Statics::NewProp_RequiredKills,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUnlockCondition_Statics::NewProp_RequiredSectorCompletions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUnlockCondition_Statics::NewProp_RequiredAccuracy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUnlockCondition_Statics::NewProp_RequiredCompletionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUnlockCondition_Statics::NewProp_RequiredWeaponKills_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUnlockCondition_Statics::NewProp_RequiredWeaponKills,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FUnlockCondition_Statics::NewProp_RequiredAchievements,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FUnlockCondition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FUnlockCondition_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	&NewStructOps,
	"UnlockCondition",
	Z_Construct_UScriptStruct_FUnlockCondition_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FUnlockCondition_Statics::PropPointers),
	sizeof(FUnlockCondition),
	alignof(FUnlockCondition),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FUnlockCondition_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FUnlockCondition_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FUnlockCondition()
{
	if (!Z_Registration_Info_UScriptStruct_UnlockCondition.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_UnlockCondition.InnerSingleton, Z_Construct_UScriptStruct_FUnlockCondition_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_UnlockCondition.InnerSingleton;
}
// End ScriptStruct FUnlockCondition

// Begin ScriptStruct FPrestigeLevel
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_PrestigeLevel;
class UScriptStruct* FPrestigeLevel::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_PrestigeLevel.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_PrestigeLevel.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPrestigeLevel, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("PrestigeLevel"));
	}
	return Z_Registration_Info_UScriptStruct_PrestigeLevel.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FPrestigeLevel>()
{
	return FPrestigeLevel::StaticStruct();
}
struct Z_Construct_UScriptStruct_FPrestigeLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Level_MetaData[] = {
		{ "Category", "Prestige" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredTeeth_MetaData[] = {
		{ "Category", "Prestige" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LevelName_MetaData[] = {
		{ "Category", "Prestige" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LevelColor_MetaData[] = {
		{ "Category", "Prestige" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnlockedFeatures_MetaData[] = {
		{ "Category", "Prestige" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalDamageBonus_MetaData[] = {
		{ "Category", "Prestige" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalHealthBonus_MetaData[] = {
		{ "Category", "Prestige" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ExtraRewindCharges_MetaData[] = {
		{ "Category", "Prestige" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Level;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RequiredTeeth;
	static const UECodeGen_Private::FTextPropertyParams NewProp_LevelName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LevelColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_UnlockedFeatures_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_UnlockedFeatures;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GlobalDamageBonus;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GlobalHealthBonus;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ExtraRewindCharges;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPrestigeLevel>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPrestigeLevel_Statics::NewProp_Level = { "Level", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrestigeLevel, Level), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Level_MetaData), NewProp_Level_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPrestigeLevel_Statics::NewProp_RequiredTeeth = { "RequiredTeeth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrestigeLevel, RequiredTeeth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredTeeth_MetaData), NewProp_RequiredTeeth_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FPrestigeLevel_Statics::NewProp_LevelName = { "LevelName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrestigeLevel, LevelName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LevelName_MetaData), NewProp_LevelName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPrestigeLevel_Statics::NewProp_LevelColor = { "LevelColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrestigeLevel, LevelColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LevelColor_MetaData), NewProp_LevelColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPrestigeLevel_Statics::NewProp_UnlockedFeatures_Inner = { "UnlockedFeatures", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(0, nullptr) }; // 1298103297
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPrestigeLevel_Statics::NewProp_UnlockedFeatures = { "UnlockedFeatures", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrestigeLevel, UnlockedFeatures), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnlockedFeatures_MetaData), NewProp_UnlockedFeatures_MetaData) }; // 1298103297
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPrestigeLevel_Statics::NewProp_GlobalDamageBonus = { "GlobalDamageBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrestigeLevel, GlobalDamageBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalDamageBonus_MetaData), NewProp_GlobalDamageBonus_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPrestigeLevel_Statics::NewProp_GlobalHealthBonus = { "GlobalHealthBonus", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrestigeLevel, GlobalHealthBonus), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalHealthBonus_MetaData), NewProp_GlobalHealthBonus_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPrestigeLevel_Statics::NewProp_ExtraRewindCharges = { "ExtraRewindCharges", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPrestigeLevel, ExtraRewindCharges), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ExtraRewindCharges_MetaData), NewProp_ExtraRewindCharges_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPrestigeLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrestigeLevel_Statics::NewProp_Level,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrestigeLevel_Statics::NewProp_RequiredTeeth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrestigeLevel_Statics::NewProp_LevelName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrestigeLevel_Statics::NewProp_LevelColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrestigeLevel_Statics::NewProp_UnlockedFeatures_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrestigeLevel_Statics::NewProp_UnlockedFeatures,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrestigeLevel_Statics::NewProp_GlobalDamageBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrestigeLevel_Statics::NewProp_GlobalHealthBonus,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPrestigeLevel_Statics::NewProp_ExtraRewindCharges,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPrestigeLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPrestigeLevel_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	&NewStructOps,
	"PrestigeLevel",
	Z_Construct_UScriptStruct_FPrestigeLevel_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPrestigeLevel_Statics::PropPointers),
	sizeof(FPrestigeLevel),
	alignof(FPrestigeLevel),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPrestigeLevel_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPrestigeLevel_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPrestigeLevel()
{
	if (!Z_Registration_Info_UScriptStruct_PrestigeLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_PrestigeLevel.InnerSingleton, Z_Construct_UScriptStruct_FPrestigeLevel_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_PrestigeLevel.InnerSingleton;
}
// End ScriptStruct FPrestigeLevel

// Begin Delegate FOnUpgradePurchased
struct Z_Construct_UDelegateFunction_RoughReality_OnUpgradePurchased__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnUpgradePurchased_Parms
	{
		FName UpgradeID;
		int32 NewLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_UpgradeID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UDelegateFunction_RoughReality_OnUpgradePurchased__DelegateSignature_Statics::NewProp_UpgradeID = { "UpgradeID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnUpgradePurchased_Parms, UpgradeID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnUpgradePurchased__DelegateSignature_Statics::NewProp_NewLevel = { "NewLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnUpgradePurchased_Parms, NewLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnUpgradePurchased__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnUpgradePurchased__DelegateSignature_Statics::NewProp_UpgradeID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnUpgradePurchased__DelegateSignature_Statics::NewProp_NewLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnUpgradePurchased__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnUpgradePurchased__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnUpgradePurchased__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnUpgradePurchased__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnUpgradePurchased__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnUpgradePurchased__DelegateSignature_Statics::_Script_RoughReality_eventOnUpgradePurchased_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnUpgradePurchased__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnUpgradePurchased__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnUpgradePurchased__DelegateSignature_Statics::_Script_RoughReality_eventOnUpgradePurchased_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnUpgradePurchased__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnUpgradePurchased__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnUpgradePurchased_DelegateWrapper(const FMulticastScriptDelegate& OnUpgradePurchased, FName UpgradeID, int32 NewLevel)
{
	struct _Script_RoughReality_eventOnUpgradePurchased_Parms
	{
		FName UpgradeID;
		int32 NewLevel;
	};
	_Script_RoughReality_eventOnUpgradePurchased_Parms Parms;
	Parms.UpgradeID=UpgradeID;
	Parms.NewLevel=NewLevel;
	OnUpgradePurchased.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnUpgradePurchased

// Begin Delegate FOnUnlockAchieved
struct Z_Construct_UDelegateFunction_RoughReality_OnUnlockAchieved__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnUnlockAchieved_Parms
	{
		FName UnlockID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_UnlockID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UDelegateFunction_RoughReality_OnUnlockAchieved__DelegateSignature_Statics::NewProp_UnlockID = { "UnlockID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnUnlockAchieved_Parms, UnlockID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnUnlockAchieved__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnUnlockAchieved__DelegateSignature_Statics::NewProp_UnlockID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnUnlockAchieved__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnUnlockAchieved__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnUnlockAchieved__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnUnlockAchieved__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnUnlockAchieved__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnUnlockAchieved__DelegateSignature_Statics::_Script_RoughReality_eventOnUnlockAchieved_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnUnlockAchieved__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnUnlockAchieved__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnUnlockAchieved__DelegateSignature_Statics::_Script_RoughReality_eventOnUnlockAchieved_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnUnlockAchieved__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnUnlockAchieved__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnUnlockAchieved_DelegateWrapper(const FMulticastScriptDelegate& OnUnlockAchieved, FName UnlockID)
{
	struct _Script_RoughReality_eventOnUnlockAchieved_Parms
	{
		FName UnlockID;
	};
	_Script_RoughReality_eventOnUnlockAchieved_Parms Parms;
	Parms.UnlockID=UnlockID;
	OnUnlockAchieved.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnUnlockAchieved

// Begin Delegate FOnPrestigeLevelUp
struct Z_Construct_UDelegateFunction_RoughReality_OnPrestigeLevelUp__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnPrestigeLevelUp_Parms
	{
		int32 NewPrestigeLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewPrestigeLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnPrestigeLevelUp__DelegateSignature_Statics::NewProp_NewPrestigeLevel = { "NewPrestigeLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnPrestigeLevelUp_Parms, NewPrestigeLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnPrestigeLevelUp__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnPrestigeLevelUp__DelegateSignature_Statics::NewProp_NewPrestigeLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnPrestigeLevelUp__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnPrestigeLevelUp__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnPrestigeLevelUp__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnPrestigeLevelUp__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnPrestigeLevelUp__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnPrestigeLevelUp__DelegateSignature_Statics::_Script_RoughReality_eventOnPrestigeLevelUp_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnPrestigeLevelUp__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnPrestigeLevelUp__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnPrestigeLevelUp__DelegateSignature_Statics::_Script_RoughReality_eventOnPrestigeLevelUp_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnPrestigeLevelUp__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnPrestigeLevelUp__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPrestigeLevelUp_DelegateWrapper(const FMulticastScriptDelegate& OnPrestigeLevelUp, int32 NewPrestigeLevel)
{
	struct _Script_RoughReality_eventOnPrestigeLevelUp_Parms
	{
		int32 NewPrestigeLevel;
	};
	_Script_RoughReality_eventOnPrestigeLevelUp_Parms Parms;
	Parms.NewPrestigeLevel=NewPrestigeLevel;
	OnPrestigeLevelUp.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnPrestigeLevelUp

// Begin Class AProgressionManager Function AddTeeth
struct Z_Construct_UFunction_AProgressionManager_AddTeeth_Statics
{
	struct ProgressionManager_eventAddTeeth_Parms
	{
		int32 Amount;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Economy" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Amount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AProgressionManager_AddTeeth_Statics::NewProp_Amount = { "Amount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventAddTeeth_Parms, Amount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_AddTeeth_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_AddTeeth_Statics::NewProp_Amount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_AddTeeth_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_AddTeeth_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "AddTeeth", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_AddTeeth_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_AddTeeth_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_AddTeeth_Statics::ProgressionManager_eventAddTeeth_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_AddTeeth_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_AddTeeth_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_AddTeeth_Statics::ProgressionManager_eventAddTeeth_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_AddTeeth()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_AddTeeth_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execAddTeeth)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Amount);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddTeeth(Z_Param_Amount);
	P_NATIVE_END;
}
// End Class AProgressionManager Function AddTeeth

// Begin Class AProgressionManager Function CanPrestige
struct Z_Construct_UFunction_AProgressionManager_CanPrestige_Statics
{
	struct ProgressionManager_eventCanPrestige_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prestige" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Prestige System */" },
#endif
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prestige System" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AProgressionManager_CanPrestige_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ProgressionManager_eventCanPrestige_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AProgressionManager_CanPrestige_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ProgressionManager_eventCanPrestige_Parms), &Z_Construct_UFunction_AProgressionManager_CanPrestige_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_CanPrestige_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_CanPrestige_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_CanPrestige_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_CanPrestige_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "CanPrestige", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_CanPrestige_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_CanPrestige_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_CanPrestige_Statics::ProgressionManager_eventCanPrestige_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_CanPrestige_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_CanPrestige_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_CanPrestige_Statics::ProgressionManager_eventCanPrestige_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_CanPrestige()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_CanPrestige_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execCanPrestige)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanPrestige();
	P_NATIVE_END;
}
// End Class AProgressionManager Function CanPrestige

// Begin Class AProgressionManager Function CanPurchaseUpgrade
struct Z_Construct_UFunction_AProgressionManager_CanPurchaseUpgrade_Statics
{
	struct ProgressionManager_eventCanPurchaseUpgrade_Parms
	{
		FName UpgradeID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Upgrades" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Upgrade System */" },
#endif
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Upgrade System" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_UpgradeID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AProgressionManager_CanPurchaseUpgrade_Statics::NewProp_UpgradeID = { "UpgradeID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventCanPurchaseUpgrade_Parms, UpgradeID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AProgressionManager_CanPurchaseUpgrade_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ProgressionManager_eventCanPurchaseUpgrade_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AProgressionManager_CanPurchaseUpgrade_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ProgressionManager_eventCanPurchaseUpgrade_Parms), &Z_Construct_UFunction_AProgressionManager_CanPurchaseUpgrade_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_CanPurchaseUpgrade_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_CanPurchaseUpgrade_Statics::NewProp_UpgradeID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_CanPurchaseUpgrade_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_CanPurchaseUpgrade_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_CanPurchaseUpgrade_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "CanPurchaseUpgrade", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_CanPurchaseUpgrade_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_CanPurchaseUpgrade_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_CanPurchaseUpgrade_Statics::ProgressionManager_eventCanPurchaseUpgrade_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_CanPurchaseUpgrade_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_CanPurchaseUpgrade_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_CanPurchaseUpgrade_Statics::ProgressionManager_eventCanPurchaseUpgrade_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_CanPurchaseUpgrade()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_CanPurchaseUpgrade_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execCanPurchaseUpgrade)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_UpgradeID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanPurchaseUpgrade(Z_Param_UpgradeID);
	P_NATIVE_END;
}
// End Class AProgressionManager Function CanPurchaseUpgrade

// Begin Class AProgressionManager Function CheckUnlockConditions
struct Z_Construct_UFunction_AProgressionManager_CheckUnlockConditions_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Unlocks" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Unlock System */" },
#endif
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Unlock System" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_CheckUnlockConditions_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "CheckUnlockConditions", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_CheckUnlockConditions_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_CheckUnlockConditions_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AProgressionManager_CheckUnlockConditions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_CheckUnlockConditions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execCheckUnlockConditions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CheckUnlockConditions();
	P_NATIVE_END;
}
// End Class AProgressionManager Function CheckUnlockConditions

// Begin Class AProgressionManager Function ForceUnlock
struct Z_Construct_UFunction_AProgressionManager_ForceUnlock_Statics
{
	struct ProgressionManager_eventForceUnlock_Parms
	{
		FName UnlockID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Unlocks" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_UnlockID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AProgressionManager_ForceUnlock_Statics::NewProp_UnlockID = { "UnlockID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventForceUnlock_Parms, UnlockID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_ForceUnlock_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_ForceUnlock_Statics::NewProp_UnlockID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_ForceUnlock_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_ForceUnlock_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "ForceUnlock", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_ForceUnlock_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_ForceUnlock_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_ForceUnlock_Statics::ProgressionManager_eventForceUnlock_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_ForceUnlock_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_ForceUnlock_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_ForceUnlock_Statics::ProgressionManager_eventForceUnlock_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_ForceUnlock()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_ForceUnlock_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execForceUnlock)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_UnlockID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ForceUnlock(Z_Param_UnlockID);
	P_NATIVE_END;
}
// End Class AProgressionManager Function ForceUnlock

// Begin Class AProgressionManager Function GetAllStatModifiers
struct Z_Construct_UFunction_AProgressionManager_GetAllStatModifiers_Statics
{
	struct ProgressionManager_eventGetAllStatModifiers_Parms
	{
		TMap<FString,float> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Stats" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AProgressionManager_GetAllStatModifiers_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AProgressionManager_GetAllStatModifiers_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_AProgressionManager_GetAllStatModifiers_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetAllStatModifiers_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_GetAllStatModifiers_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetAllStatModifiers_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetAllStatModifiers_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetAllStatModifiers_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetAllStatModifiers_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_GetAllStatModifiers_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "GetAllStatModifiers", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_GetAllStatModifiers_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetAllStatModifiers_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_GetAllStatModifiers_Statics::ProgressionManager_eventGetAllStatModifiers_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetAllStatModifiers_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_GetAllStatModifiers_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_GetAllStatModifiers_Statics::ProgressionManager_eventGetAllStatModifiers_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_GetAllStatModifiers()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_GetAllStatModifiers_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execGetAllStatModifiers)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,float>*)Z_Param__Result=P_THIS->GetAllStatModifiers();
	P_NATIVE_END;
}
// End Class AProgressionManager Function GetAllStatModifiers

// Begin Class AProgressionManager Function GetAvailableTeeth
struct Z_Construct_UFunction_AProgressionManager_GetAvailableTeeth_Statics
{
	struct ProgressionManager_eventGetAvailableTeeth_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Economy" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Economy */" },
#endif
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Economy" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AProgressionManager_GetAvailableTeeth_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetAvailableTeeth_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_GetAvailableTeeth_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetAvailableTeeth_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetAvailableTeeth_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_GetAvailableTeeth_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "GetAvailableTeeth", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_GetAvailableTeeth_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetAvailableTeeth_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_GetAvailableTeeth_Statics::ProgressionManager_eventGetAvailableTeeth_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetAvailableTeeth_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_GetAvailableTeeth_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_GetAvailableTeeth_Statics::ProgressionManager_eventGetAvailableTeeth_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_GetAvailableTeeth()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_GetAvailableTeeth_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execGetAvailableTeeth)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetAvailableTeeth();
	P_NATIVE_END;
}
// End Class AProgressionManager Function GetAvailableTeeth

// Begin Class AProgressionManager Function GetAvailableUpgrades
struct Z_Construct_UFunction_AProgressionManager_GetAvailableUpgrades_Statics
{
	struct ProgressionManager_eventGetAvailableUpgrades_Parms
	{
		TArray<FUpgradeNode> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Upgrades" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AProgressionManager_GetAvailableUpgrades_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FUpgradeNode, METADATA_PARAMS(0, nullptr) }; // 1713216908
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AProgressionManager_GetAvailableUpgrades_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetAvailableUpgrades_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1713216908
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_GetAvailableUpgrades_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetAvailableUpgrades_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetAvailableUpgrades_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetAvailableUpgrades_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_GetAvailableUpgrades_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "GetAvailableUpgrades", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_GetAvailableUpgrades_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetAvailableUpgrades_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_GetAvailableUpgrades_Statics::ProgressionManager_eventGetAvailableUpgrades_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetAvailableUpgrades_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_GetAvailableUpgrades_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_GetAvailableUpgrades_Statics::ProgressionManager_eventGetAvailableUpgrades_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_GetAvailableUpgrades()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_GetAvailableUpgrades_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execGetAvailableUpgrades)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FUpgradeNode>*)Z_Param__Result=P_THIS->GetAvailableUpgrades();
	P_NATIVE_END;
}
// End Class AProgressionManager Function GetAvailableUpgrades

// Begin Class AProgressionManager Function GetCurrentPrestigeData
struct Z_Construct_UFunction_AProgressionManager_GetCurrentPrestigeData_Statics
{
	struct ProgressionManager_eventGetCurrentPrestigeData_Parms
	{
		FPrestigeLevel ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prestige" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AProgressionManager_GetCurrentPrestigeData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetCurrentPrestigeData_Parms, ReturnValue), Z_Construct_UScriptStruct_FPrestigeLevel, METADATA_PARAMS(0, nullptr) }; // 3087673465
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_GetCurrentPrestigeData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetCurrentPrestigeData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetCurrentPrestigeData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_GetCurrentPrestigeData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "GetCurrentPrestigeData", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_GetCurrentPrestigeData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetCurrentPrestigeData_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_GetCurrentPrestigeData_Statics::ProgressionManager_eventGetCurrentPrestigeData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetCurrentPrestigeData_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_GetCurrentPrestigeData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_GetCurrentPrestigeData_Statics::ProgressionManager_eventGetCurrentPrestigeData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_GetCurrentPrestigeData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_GetCurrentPrestigeData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execGetCurrentPrestigeData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPrestigeLevel*)Z_Param__Result=P_THIS->GetCurrentPrestigeData();
	P_NATIVE_END;
}
// End Class AProgressionManager Function GetCurrentPrestigeData

// Begin Class AProgressionManager Function GetNextPrestigeData
struct Z_Construct_UFunction_AProgressionManager_GetNextPrestigeData_Statics
{
	struct ProgressionManager_eventGetNextPrestigeData_Parms
	{
		FPrestigeLevel ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prestige" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AProgressionManager_GetNextPrestigeData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetNextPrestigeData_Parms, ReturnValue), Z_Construct_UScriptStruct_FPrestigeLevel, METADATA_PARAMS(0, nullptr) }; // 3087673465
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_GetNextPrestigeData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetNextPrestigeData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetNextPrestigeData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_GetNextPrestigeData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "GetNextPrestigeData", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_GetNextPrestigeData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetNextPrestigeData_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_GetNextPrestigeData_Statics::ProgressionManager_eventGetNextPrestigeData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetNextPrestigeData_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_GetNextPrestigeData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_GetNextPrestigeData_Statics::ProgressionManager_eventGetNextPrestigeData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_GetNextPrestigeData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_GetNextPrestigeData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execGetNextPrestigeData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPrestigeLevel*)Z_Param__Result=P_THIS->GetNextPrestigeData();
	P_NATIVE_END;
}
// End Class AProgressionManager Function GetNextPrestigeData

// Begin Class AProgressionManager Function GetPendingUnlocks
struct Z_Construct_UFunction_AProgressionManager_GetPendingUnlocks_Statics
{
	struct ProgressionManager_eventGetPendingUnlocks_Parms
	{
		TArray<FUnlockCondition> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Unlocks" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AProgressionManager_GetPendingUnlocks_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FUnlockCondition, METADATA_PARAMS(0, nullptr) }; // 1428293075
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AProgressionManager_GetPendingUnlocks_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetPendingUnlocks_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1428293075
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_GetPendingUnlocks_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetPendingUnlocks_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetPendingUnlocks_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetPendingUnlocks_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_GetPendingUnlocks_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "GetPendingUnlocks", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_GetPendingUnlocks_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetPendingUnlocks_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_GetPendingUnlocks_Statics::ProgressionManager_eventGetPendingUnlocks_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetPendingUnlocks_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_GetPendingUnlocks_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_GetPendingUnlocks_Statics::ProgressionManager_eventGetPendingUnlocks_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_GetPendingUnlocks()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_GetPendingUnlocks_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execGetPendingUnlocks)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FUnlockCondition>*)Z_Param__Result=P_THIS->GetPendingUnlocks();
	P_NATIVE_END;
}
// End Class AProgressionManager Function GetPendingUnlocks

// Begin Class AProgressionManager Function GetPrestigeLevel
struct Z_Construct_UFunction_AProgressionManager_GetPrestigeLevel_Statics
{
	struct ProgressionManager_eventGetPrestigeLevel_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prestige" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AProgressionManager_GetPrestigeLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetPrestigeLevel_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_GetPrestigeLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetPrestigeLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetPrestigeLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_GetPrestigeLevel_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "GetPrestigeLevel", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_GetPrestigeLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetPrestigeLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_GetPrestigeLevel_Statics::ProgressionManager_eventGetPrestigeLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetPrestigeLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_GetPrestigeLevel_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_GetPrestigeLevel_Statics::ProgressionManager_eventGetPrestigeLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_GetPrestigeLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_GetPrestigeLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execGetPrestigeLevel)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetPrestigeLevel();
	P_NATIVE_END;
}
// End Class AProgressionManager Function GetPrestigeLevel

// Begin Class AProgressionManager Function GetPrestigeProgress
struct Z_Construct_UFunction_AProgressionManager_GetPrestigeProgress_Statics
{
	struct ProgressionManager_eventGetPrestigeProgress_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prestige" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AProgressionManager_GetPrestigeProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetPrestigeProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_GetPrestigeProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetPrestigeProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetPrestigeProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_GetPrestigeProgress_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "GetPrestigeProgress", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_GetPrestigeProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetPrestigeProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_GetPrestigeProgress_Statics::ProgressionManager_eventGetPrestigeProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetPrestigeProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_GetPrestigeProgress_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_GetPrestigeProgress_Statics::ProgressionManager_eventGetPrestigeProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_GetPrestigeProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_GetPrestigeProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execGetPrestigeProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetPrestigeProgress();
	P_NATIVE_END;
}
// End Class AProgressionManager Function GetPrestigeProgress

// Begin Class AProgressionManager Function GetProgressionManager
struct Z_Construct_UFunction_AProgressionManager_GetProgressionManager_Statics
{
	struct ProgressionManager_eventGetProgressionManager_Parms
	{
		const UObject* WorldContext;
		AProgressionManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progression" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Static Access */" },
#endif
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Static Access" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldContext_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldContext;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AProgressionManager_GetProgressionManager_Statics::NewProp_WorldContext = { "WorldContext", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetProgressionManager_Parms, WorldContext), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldContext_MetaData), NewProp_WorldContext_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AProgressionManager_GetProgressionManager_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetProgressionManager_Parms, ReturnValue), Z_Construct_UClass_AProgressionManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_GetProgressionManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetProgressionManager_Statics::NewProp_WorldContext,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetProgressionManager_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetProgressionManager_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_GetProgressionManager_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "GetProgressionManager", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_GetProgressionManager_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetProgressionManager_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_GetProgressionManager_Statics::ProgressionManager_eventGetProgressionManager_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetProgressionManager_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_GetProgressionManager_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_GetProgressionManager_Statics::ProgressionManager_eventGetProgressionManager_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_GetProgressionManager()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_GetProgressionManager_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execGetProgressionManager)
{
	P_GET_OBJECT(UObject,Z_Param_WorldContext);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(AProgressionManager**)Z_Param__Result=AProgressionManager::GetProgressionManager(Z_Param_WorldContext);
	P_NATIVE_END;
}
// End Class AProgressionManager Function GetProgressionManager

// Begin Class AProgressionManager Function GetPurchasedUpgrades
struct Z_Construct_UFunction_AProgressionManager_GetPurchasedUpgrades_Statics
{
	struct ProgressionManager_eventGetPurchasedUpgrades_Parms
	{
		TArray<FUpgradeNode> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Upgrades" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AProgressionManager_GetPurchasedUpgrades_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FUpgradeNode, METADATA_PARAMS(0, nullptr) }; // 1713216908
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AProgressionManager_GetPurchasedUpgrades_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetPurchasedUpgrades_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1713216908
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_GetPurchasedUpgrades_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetPurchasedUpgrades_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetPurchasedUpgrades_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetPurchasedUpgrades_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_GetPurchasedUpgrades_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "GetPurchasedUpgrades", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_GetPurchasedUpgrades_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetPurchasedUpgrades_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_GetPurchasedUpgrades_Statics::ProgressionManager_eventGetPurchasedUpgrades_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetPurchasedUpgrades_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_GetPurchasedUpgrades_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_GetPurchasedUpgrades_Statics::ProgressionManager_eventGetPurchasedUpgrades_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_GetPurchasedUpgrades()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_GetPurchasedUpgrades_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execGetPurchasedUpgrades)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FUpgradeNode>*)Z_Param__Result=P_THIS->GetPurchasedUpgrades();
	P_NATIVE_END;
}
// End Class AProgressionManager Function GetPurchasedUpgrades

// Begin Class AProgressionManager Function GetTotalStatModifier
struct Z_Construct_UFunction_AProgressionManager_GetTotalStatModifier_Statics
{
	struct ProgressionManager_eventGetTotalStatModifier_Parms
	{
		FString StatName;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Stat Calculations */" },
#endif
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Stat Calculations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StatName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_StatName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AProgressionManager_GetTotalStatModifier_Statics::NewProp_StatName = { "StatName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetTotalStatModifier_Parms, StatName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StatName_MetaData), NewProp_StatName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AProgressionManager_GetTotalStatModifier_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetTotalStatModifier_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_GetTotalStatModifier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetTotalStatModifier_Statics::NewProp_StatName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetTotalStatModifier_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetTotalStatModifier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_GetTotalStatModifier_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "GetTotalStatModifier", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_GetTotalStatModifier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetTotalStatModifier_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_GetTotalStatModifier_Statics::ProgressionManager_eventGetTotalStatModifier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetTotalStatModifier_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_GetTotalStatModifier_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_GetTotalStatModifier_Statics::ProgressionManager_eventGetTotalStatModifier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_GetTotalStatModifier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_GetTotalStatModifier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execGetTotalStatModifier)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_StatName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetTotalStatModifier(Z_Param_StatName);
	P_NATIVE_END;
}
// End Class AProgressionManager Function GetTotalStatModifier

// Begin Class AProgressionManager Function GetUnlockedAchievements
struct Z_Construct_UFunction_AProgressionManager_GetUnlockedAchievements_Statics
{
	struct ProgressionManager_eventGetUnlockedAchievements_Parms
	{
		TArray<FGameplayTag> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Achievements" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AProgressionManager_GetUnlockedAchievements_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(0, nullptr) }; // 1298103297
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AProgressionManager_GetUnlockedAchievements_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetUnlockedAchievements_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1298103297
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_GetUnlockedAchievements_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetUnlockedAchievements_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetUnlockedAchievements_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetUnlockedAchievements_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_GetUnlockedAchievements_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "GetUnlockedAchievements", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_GetUnlockedAchievements_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetUnlockedAchievements_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_GetUnlockedAchievements_Statics::ProgressionManager_eventGetUnlockedAchievements_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetUnlockedAchievements_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_GetUnlockedAchievements_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_GetUnlockedAchievements_Statics::ProgressionManager_eventGetUnlockedAchievements_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_GetUnlockedAchievements()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_GetUnlockedAchievements_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execGetUnlockedAchievements)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FGameplayTag>*)Z_Param__Result=P_THIS->GetUnlockedAchievements();
	P_NATIVE_END;
}
// End Class AProgressionManager Function GetUnlockedAchievements

// Begin Class AProgressionManager Function GetUnlockProgress
struct Z_Construct_UFunction_AProgressionManager_GetUnlockProgress_Statics
{
	struct ProgressionManager_eventGetUnlockProgress_Parms
	{
		FName UnlockID;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Unlocks" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_UnlockID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AProgressionManager_GetUnlockProgress_Statics::NewProp_UnlockID = { "UnlockID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetUnlockProgress_Parms, UnlockID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AProgressionManager_GetUnlockProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetUnlockProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_GetUnlockProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetUnlockProgress_Statics::NewProp_UnlockID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetUnlockProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetUnlockProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_GetUnlockProgress_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "GetUnlockProgress", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_GetUnlockProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetUnlockProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_GetUnlockProgress_Statics::ProgressionManager_eventGetUnlockProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetUnlockProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_GetUnlockProgress_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_GetUnlockProgress_Statics::ProgressionManager_eventGetUnlockProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_GetUnlockProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_GetUnlockProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execGetUnlockProgress)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_UnlockID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetUnlockProgress(Z_Param_UnlockID);
	P_NATIVE_END;
}
// End Class AProgressionManager Function GetUnlockProgress

// Begin Class AProgressionManager Function GetUpgradeCost
struct Z_Construct_UFunction_AProgressionManager_GetUpgradeCost_Statics
{
	struct ProgressionManager_eventGetUpgradeCost_Parms
	{
		FName UpgradeID;
		int32 Level;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Upgrades" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_UpgradeID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Level;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AProgressionManager_GetUpgradeCost_Statics::NewProp_UpgradeID = { "UpgradeID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetUpgradeCost_Parms, UpgradeID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AProgressionManager_GetUpgradeCost_Statics::NewProp_Level = { "Level", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetUpgradeCost_Parms, Level), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AProgressionManager_GetUpgradeCost_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetUpgradeCost_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_GetUpgradeCost_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetUpgradeCost_Statics::NewProp_UpgradeID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetUpgradeCost_Statics::NewProp_Level,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetUpgradeCost_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetUpgradeCost_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_GetUpgradeCost_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "GetUpgradeCost", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_GetUpgradeCost_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetUpgradeCost_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_GetUpgradeCost_Statics::ProgressionManager_eventGetUpgradeCost_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetUpgradeCost_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_GetUpgradeCost_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_GetUpgradeCost_Statics::ProgressionManager_eventGetUpgradeCost_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_GetUpgradeCost()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_GetUpgradeCost_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execGetUpgradeCost)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_UpgradeID);
	P_GET_PROPERTY(FIntProperty,Z_Param_Level);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetUpgradeCost(Z_Param_UpgradeID,Z_Param_Level);
	P_NATIVE_END;
}
// End Class AProgressionManager Function GetUpgradeCost

// Begin Class AProgressionManager Function GetUpgradeData
struct Z_Construct_UFunction_AProgressionManager_GetUpgradeData_Statics
{
	struct ProgressionManager_eventGetUpgradeData_Parms
	{
		FName UpgradeID;
		FUpgradeNode ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Upgrades" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_UpgradeID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AProgressionManager_GetUpgradeData_Statics::NewProp_UpgradeID = { "UpgradeID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetUpgradeData_Parms, UpgradeID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AProgressionManager_GetUpgradeData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetUpgradeData_Parms, ReturnValue), Z_Construct_UScriptStruct_FUpgradeNode, METADATA_PARAMS(0, nullptr) }; // 1713216908
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_GetUpgradeData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetUpgradeData_Statics::NewProp_UpgradeID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetUpgradeData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetUpgradeData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_GetUpgradeData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "GetUpgradeData", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_GetUpgradeData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetUpgradeData_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_GetUpgradeData_Statics::ProgressionManager_eventGetUpgradeData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetUpgradeData_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_GetUpgradeData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_GetUpgradeData_Statics::ProgressionManager_eventGetUpgradeData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_GetUpgradeData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_GetUpgradeData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execGetUpgradeData)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_UpgradeID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FUpgradeNode*)Z_Param__Result=P_THIS->GetUpgradeData(Z_Param_UpgradeID);
	P_NATIVE_END;
}
// End Class AProgressionManager Function GetUpgradeData

// Begin Class AProgressionManager Function GetUpgradeLevel
struct Z_Construct_UFunction_AProgressionManager_GetUpgradeLevel_Statics
{
	struct ProgressionManager_eventGetUpgradeLevel_Parms
	{
		FName UpgradeID;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Upgrades" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_UpgradeID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AProgressionManager_GetUpgradeLevel_Statics::NewProp_UpgradeID = { "UpgradeID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetUpgradeLevel_Parms, UpgradeID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AProgressionManager_GetUpgradeLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetUpgradeLevel_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_GetUpgradeLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetUpgradeLevel_Statics::NewProp_UpgradeID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetUpgradeLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetUpgradeLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_GetUpgradeLevel_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "GetUpgradeLevel", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_GetUpgradeLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetUpgradeLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_GetUpgradeLevel_Statics::ProgressionManager_eventGetUpgradeLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetUpgradeLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_GetUpgradeLevel_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_GetUpgradeLevel_Statics::ProgressionManager_eventGetUpgradeLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_GetUpgradeLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_GetUpgradeLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execGetUpgradeLevel)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_UpgradeID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetUpgradeLevel(Z_Param_UpgradeID);
	P_NATIVE_END;
}
// End Class AProgressionManager Function GetUpgradeLevel

// Begin Class AProgressionManager Function GetWeaponStatModifier
struct Z_Construct_UFunction_AProgressionManager_GetWeaponStatModifier_Statics
{
	struct ProgressionManager_eventGetWeaponStatModifier_Parms
	{
		FName WeaponName;
		FString StatName;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Stats" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StatName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_StatName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AProgressionManager_GetWeaponStatModifier_Statics::NewProp_WeaponName = { "WeaponName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetWeaponStatModifier_Parms, WeaponName), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AProgressionManager_GetWeaponStatModifier_Statics::NewProp_StatName = { "StatName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetWeaponStatModifier_Parms, StatName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StatName_MetaData), NewProp_StatName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AProgressionManager_GetWeaponStatModifier_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventGetWeaponStatModifier_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_GetWeaponStatModifier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetWeaponStatModifier_Statics::NewProp_WeaponName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetWeaponStatModifier_Statics::NewProp_StatName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_GetWeaponStatModifier_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetWeaponStatModifier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_GetWeaponStatModifier_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "GetWeaponStatModifier", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_GetWeaponStatModifier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetWeaponStatModifier_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_GetWeaponStatModifier_Statics::ProgressionManager_eventGetWeaponStatModifier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_GetWeaponStatModifier_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_GetWeaponStatModifier_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_GetWeaponStatModifier_Statics::ProgressionManager_eventGetWeaponStatModifier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_GetWeaponStatModifier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_GetWeaponStatModifier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execGetWeaponStatModifier)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_WeaponName);
	P_GET_PROPERTY(FStrProperty,Z_Param_StatName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetWeaponStatModifier(Z_Param_WeaponName,Z_Param_StatName);
	P_NATIVE_END;
}
// End Class AProgressionManager Function GetWeaponStatModifier

// Begin Class AProgressionManager Function HasAchievement
struct Z_Construct_UFunction_AProgressionManager_HasAchievement_Statics
{
	struct ProgressionManager_eventHasAchievement_Parms
	{
		FGameplayTag AchievementTag;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Achievements" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AchievementTag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_AchievementTag;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AProgressionManager_HasAchievement_Statics::NewProp_AchievementTag = { "AchievementTag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventHasAchievement_Parms, AchievementTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AchievementTag_MetaData), NewProp_AchievementTag_MetaData) }; // 1298103297
void Z_Construct_UFunction_AProgressionManager_HasAchievement_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ProgressionManager_eventHasAchievement_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AProgressionManager_HasAchievement_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ProgressionManager_eventHasAchievement_Parms), &Z_Construct_UFunction_AProgressionManager_HasAchievement_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_HasAchievement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_HasAchievement_Statics::NewProp_AchievementTag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_HasAchievement_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_HasAchievement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_HasAchievement_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "HasAchievement", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_HasAchievement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_HasAchievement_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_HasAchievement_Statics::ProgressionManager_eventHasAchievement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_HasAchievement_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_HasAchievement_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_HasAchievement_Statics::ProgressionManager_eventHasAchievement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_HasAchievement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_HasAchievement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execHasAchievement)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_AchievementTag);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasAchievement(Z_Param_Out_AchievementTag);
	P_NATIVE_END;
}
// End Class AProgressionManager Function HasAchievement

// Begin Class AProgressionManager Function IsUnlocked
struct Z_Construct_UFunction_AProgressionManager_IsUnlocked_Statics
{
	struct ProgressionManager_eventIsUnlocked_Parms
	{
		FName UnlockID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Unlocks" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_UnlockID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AProgressionManager_IsUnlocked_Statics::NewProp_UnlockID = { "UnlockID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventIsUnlocked_Parms, UnlockID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AProgressionManager_IsUnlocked_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ProgressionManager_eventIsUnlocked_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AProgressionManager_IsUnlocked_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ProgressionManager_eventIsUnlocked_Parms), &Z_Construct_UFunction_AProgressionManager_IsUnlocked_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_IsUnlocked_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_IsUnlocked_Statics::NewProp_UnlockID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_IsUnlocked_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_IsUnlocked_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_IsUnlocked_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "IsUnlocked", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_IsUnlocked_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_IsUnlocked_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_IsUnlocked_Statics::ProgressionManager_eventIsUnlocked_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_IsUnlocked_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_IsUnlocked_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_IsUnlocked_Statics::ProgressionManager_eventIsUnlocked_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_IsUnlocked()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_IsUnlocked_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execIsUnlocked)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_UnlockID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsUnlocked(Z_Param_UnlockID);
	P_NATIVE_END;
}
// End Class AProgressionManager Function IsUnlocked

// Begin Class AProgressionManager Function PerformPrestige
struct Z_Construct_UFunction_AProgressionManager_PerformPrestige_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Prestige" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_PerformPrestige_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "PerformPrestige", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_PerformPrestige_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_PerformPrestige_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AProgressionManager_PerformPrestige()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_PerformPrestige_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execPerformPrestige)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PerformPrestige();
	P_NATIVE_END;
}
// End Class AProgressionManager Function PerformPrestige

// Begin Class AProgressionManager Function PurchaseUpgrade
struct Z_Construct_UFunction_AProgressionManager_PurchaseUpgrade_Statics
{
	struct ProgressionManager_eventPurchaseUpgrade_Parms
	{
		FName UpgradeID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Upgrades" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_UpgradeID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AProgressionManager_PurchaseUpgrade_Statics::NewProp_UpgradeID = { "UpgradeID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventPurchaseUpgrade_Parms, UpgradeID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AProgressionManager_PurchaseUpgrade_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ProgressionManager_eventPurchaseUpgrade_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AProgressionManager_PurchaseUpgrade_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ProgressionManager_eventPurchaseUpgrade_Parms), &Z_Construct_UFunction_AProgressionManager_PurchaseUpgrade_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_PurchaseUpgrade_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_PurchaseUpgrade_Statics::NewProp_UpgradeID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_PurchaseUpgrade_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_PurchaseUpgrade_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_PurchaseUpgrade_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "PurchaseUpgrade", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_PurchaseUpgrade_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_PurchaseUpgrade_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_PurchaseUpgrade_Statics::ProgressionManager_eventPurchaseUpgrade_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_PurchaseUpgrade_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_PurchaseUpgrade_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_PurchaseUpgrade_Statics::ProgressionManager_eventPurchaseUpgrade_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_PurchaseUpgrade()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_PurchaseUpgrade_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execPurchaseUpgrade)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_UpgradeID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PurchaseUpgrade(Z_Param_UpgradeID);
	P_NATIVE_END;
}
// End Class AProgressionManager Function PurchaseUpgrade

// Begin Class AProgressionManager Function SpendTeeth
struct Z_Construct_UFunction_AProgressionManager_SpendTeeth_Statics
{
	struct ProgressionManager_eventSpendTeeth_Parms
	{
		int32 Amount;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Economy" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Amount;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AProgressionManager_SpendTeeth_Statics::NewProp_Amount = { "Amount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventSpendTeeth_Parms, Amount), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AProgressionManager_SpendTeeth_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ProgressionManager_eventSpendTeeth_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AProgressionManager_SpendTeeth_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ProgressionManager_eventSpendTeeth_Parms), &Z_Construct_UFunction_AProgressionManager_SpendTeeth_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_SpendTeeth_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_SpendTeeth_Statics::NewProp_Amount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_SpendTeeth_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_SpendTeeth_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_SpendTeeth_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "SpendTeeth", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_SpendTeeth_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_SpendTeeth_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_SpendTeeth_Statics::ProgressionManager_eventSpendTeeth_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_SpendTeeth_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_SpendTeeth_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_SpendTeeth_Statics::ProgressionManager_eventSpendTeeth_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_SpendTeeth()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_SpendTeeth_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execSpendTeeth)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Amount);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SpendTeeth(Z_Param_Amount);
	P_NATIVE_END;
}
// End Class AProgressionManager Function SpendTeeth

// Begin Class AProgressionManager Function UnlockAchievement
struct Z_Construct_UFunction_AProgressionManager_UnlockAchievement_Statics
{
	struct ProgressionManager_eventUnlockAchievement_Parms
	{
		FGameplayTag AchievementTag;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Achievements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Achievement System */" },
#endif
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Achievement System" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AchievementTag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_AchievementTag;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AProgressionManager_UnlockAchievement_Statics::NewProp_AchievementTag = { "AchievementTag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ProgressionManager_eventUnlockAchievement_Parms, AchievementTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AchievementTag_MetaData), NewProp_AchievementTag_MetaData) }; // 1298103297
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AProgressionManager_UnlockAchievement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AProgressionManager_UnlockAchievement_Statics::NewProp_AchievementTag,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_UnlockAchievement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AProgressionManager_UnlockAchievement_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AProgressionManager, nullptr, "UnlockAchievement", nullptr, nullptr, Z_Construct_UFunction_AProgressionManager_UnlockAchievement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_UnlockAchievement_Statics::PropPointers), sizeof(Z_Construct_UFunction_AProgressionManager_UnlockAchievement_Statics::ProgressionManager_eventUnlockAchievement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AProgressionManager_UnlockAchievement_Statics::Function_MetaDataParams), Z_Construct_UFunction_AProgressionManager_UnlockAchievement_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AProgressionManager_UnlockAchievement_Statics::ProgressionManager_eventUnlockAchievement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AProgressionManager_UnlockAchievement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AProgressionManager_UnlockAchievement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AProgressionManager::execUnlockAchievement)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_AchievementTag);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnlockAchievement(Z_Param_Out_AchievementTag);
	P_NATIVE_END;
}
// End Class AProgressionManager Function UnlockAchievement

// Begin Class AProgressionManager
void AProgressionManager::StaticRegisterNativesAProgressionManager()
{
	UClass* Class = AProgressionManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddTeeth", &AProgressionManager::execAddTeeth },
		{ "CanPrestige", &AProgressionManager::execCanPrestige },
		{ "CanPurchaseUpgrade", &AProgressionManager::execCanPurchaseUpgrade },
		{ "CheckUnlockConditions", &AProgressionManager::execCheckUnlockConditions },
		{ "ForceUnlock", &AProgressionManager::execForceUnlock },
		{ "GetAllStatModifiers", &AProgressionManager::execGetAllStatModifiers },
		{ "GetAvailableTeeth", &AProgressionManager::execGetAvailableTeeth },
		{ "GetAvailableUpgrades", &AProgressionManager::execGetAvailableUpgrades },
		{ "GetCurrentPrestigeData", &AProgressionManager::execGetCurrentPrestigeData },
		{ "GetNextPrestigeData", &AProgressionManager::execGetNextPrestigeData },
		{ "GetPendingUnlocks", &AProgressionManager::execGetPendingUnlocks },
		{ "GetPrestigeLevel", &AProgressionManager::execGetPrestigeLevel },
		{ "GetPrestigeProgress", &AProgressionManager::execGetPrestigeProgress },
		{ "GetProgressionManager", &AProgressionManager::execGetProgressionManager },
		{ "GetPurchasedUpgrades", &AProgressionManager::execGetPurchasedUpgrades },
		{ "GetTotalStatModifier", &AProgressionManager::execGetTotalStatModifier },
		{ "GetUnlockedAchievements", &AProgressionManager::execGetUnlockedAchievements },
		{ "GetUnlockProgress", &AProgressionManager::execGetUnlockProgress },
		{ "GetUpgradeCost", &AProgressionManager::execGetUpgradeCost },
		{ "GetUpgradeData", &AProgressionManager::execGetUpgradeData },
		{ "GetUpgradeLevel", &AProgressionManager::execGetUpgradeLevel },
		{ "GetWeaponStatModifier", &AProgressionManager::execGetWeaponStatModifier },
		{ "HasAchievement", &AProgressionManager::execHasAchievement },
		{ "IsUnlocked", &AProgressionManager::execIsUnlocked },
		{ "PerformPrestige", &AProgressionManager::execPerformPrestige },
		{ "PurchaseUpgrade", &AProgressionManager::execPurchaseUpgrade },
		{ "SpendTeeth", &AProgressionManager::execSpendTeeth },
		{ "UnlockAchievement", &AProgressionManager::execUnlockAchievement },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(AProgressionManager);
UClass* Z_Construct_UClass_AProgressionManager_NoRegister()
{
	return AProgressionManager::StaticClass();
}
struct Z_Construct_UClass_AProgressionManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Progression Manager for Rough Reality\n * Handles upgrade trees, unlocks, and meta-progression systems\n */" },
#endif
		{ "IncludePath", "Progression/ProgressionManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Progression Manager for Rough Reality\nHandles upgrade trees, unlocks, and meta-progression systems" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpgradeDataTable_MetaData[] = {
		{ "Category", "Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Data Tables */" },
#endif
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data Tables" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnlockConditionsTable_MetaData[] = {
		{ "Category", "Data" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrestigeLevels_MetaData[] = {
		{ "Category", "Prestige" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Prestige System */" },
#endif
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Prestige System" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentPrestigeLevel_MetaData[] = {
		{ "Category", "Prestige" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnUpgradePurchased_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Events */" },
#endif
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnUnlockAchieved_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPrestigeLevelUp_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SaveGameRef_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Cached save game reference */" },
#endif
		{ "ModuleRelativePath", "Progression/ProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cached save game reference" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_UpgradeDataTable;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_UnlockConditionsTable;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PrestigeLevels_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PrestigeLevels;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentPrestigeLevel;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnUpgradePurchased;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnUnlockAchieved;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPrestigeLevelUp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SaveGameRef;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AProgressionManager_AddTeeth, "AddTeeth" }, // 2631806478
		{ &Z_Construct_UFunction_AProgressionManager_CanPrestige, "CanPrestige" }, // 1150547379
		{ &Z_Construct_UFunction_AProgressionManager_CanPurchaseUpgrade, "CanPurchaseUpgrade" }, // 3196495216
		{ &Z_Construct_UFunction_AProgressionManager_CheckUnlockConditions, "CheckUnlockConditions" }, // 627316751
		{ &Z_Construct_UFunction_AProgressionManager_ForceUnlock, "ForceUnlock" }, // 698791531
		{ &Z_Construct_UFunction_AProgressionManager_GetAllStatModifiers, "GetAllStatModifiers" }, // 834291979
		{ &Z_Construct_UFunction_AProgressionManager_GetAvailableTeeth, "GetAvailableTeeth" }, // 4124008311
		{ &Z_Construct_UFunction_AProgressionManager_GetAvailableUpgrades, "GetAvailableUpgrades" }, // 204180753
		{ &Z_Construct_UFunction_AProgressionManager_GetCurrentPrestigeData, "GetCurrentPrestigeData" }, // 437161351
		{ &Z_Construct_UFunction_AProgressionManager_GetNextPrestigeData, "GetNextPrestigeData" }, // 2950844416
		{ &Z_Construct_UFunction_AProgressionManager_GetPendingUnlocks, "GetPendingUnlocks" }, // 2772164498
		{ &Z_Construct_UFunction_AProgressionManager_GetPrestigeLevel, "GetPrestigeLevel" }, // 1827573000
		{ &Z_Construct_UFunction_AProgressionManager_GetPrestigeProgress, "GetPrestigeProgress" }, // 3740013446
		{ &Z_Construct_UFunction_AProgressionManager_GetProgressionManager, "GetProgressionManager" }, // 1778827599
		{ &Z_Construct_UFunction_AProgressionManager_GetPurchasedUpgrades, "GetPurchasedUpgrades" }, // 1286771609
		{ &Z_Construct_UFunction_AProgressionManager_GetTotalStatModifier, "GetTotalStatModifier" }, // 3460301437
		{ &Z_Construct_UFunction_AProgressionManager_GetUnlockedAchievements, "GetUnlockedAchievements" }, // 3264341105
		{ &Z_Construct_UFunction_AProgressionManager_GetUnlockProgress, "GetUnlockProgress" }, // 2792339678
		{ &Z_Construct_UFunction_AProgressionManager_GetUpgradeCost, "GetUpgradeCost" }, // 719488394
		{ &Z_Construct_UFunction_AProgressionManager_GetUpgradeData, "GetUpgradeData" }, // 1793989818
		{ &Z_Construct_UFunction_AProgressionManager_GetUpgradeLevel, "GetUpgradeLevel" }, // 1719277001
		{ &Z_Construct_UFunction_AProgressionManager_GetWeaponStatModifier, "GetWeaponStatModifier" }, // 1463585857
		{ &Z_Construct_UFunction_AProgressionManager_HasAchievement, "HasAchievement" }, // 2663874778
		{ &Z_Construct_UFunction_AProgressionManager_IsUnlocked, "IsUnlocked" }, // 2354814509
		{ &Z_Construct_UFunction_AProgressionManager_PerformPrestige, "PerformPrestige" }, // 2986881480
		{ &Z_Construct_UFunction_AProgressionManager_PurchaseUpgrade, "PurchaseUpgrade" }, // 271446080
		{ &Z_Construct_UFunction_AProgressionManager_SpendTeeth, "SpendTeeth" }, // 1413593394
		{ &Z_Construct_UFunction_AProgressionManager_UnlockAchievement, "UnlockAchievement" }, // 1403135611
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AProgressionManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProgressionManager_Statics::NewProp_UpgradeDataTable = { "UpgradeDataTable", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProgressionManager, UpgradeDataTable), Z_Construct_UClass_UDataTable_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpgradeDataTable_MetaData), NewProp_UpgradeDataTable_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProgressionManager_Statics::NewProp_UnlockConditionsTable = { "UnlockConditionsTable", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProgressionManager, UnlockConditionsTable), Z_Construct_UClass_UDataTable_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnlockConditionsTable_MetaData), NewProp_UnlockConditionsTable_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AProgressionManager_Statics::NewProp_PrestigeLevels_Inner = { "PrestigeLevels", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPrestigeLevel, METADATA_PARAMS(0, nullptr) }; // 3087673465
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AProgressionManager_Statics::NewProp_PrestigeLevels = { "PrestigeLevels", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProgressionManager, PrestigeLevels), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrestigeLevels_MetaData), NewProp_PrestigeLevels_MetaData) }; // 3087673465
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AProgressionManager_Statics::NewProp_CurrentPrestigeLevel = { "CurrentPrestigeLevel", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProgressionManager, CurrentPrestigeLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentPrestigeLevel_MetaData), NewProp_CurrentPrestigeLevel_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AProgressionManager_Statics::NewProp_OnUpgradePurchased = { "OnUpgradePurchased", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProgressionManager, OnUpgradePurchased), Z_Construct_UDelegateFunction_RoughReality_OnUpgradePurchased__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnUpgradePurchased_MetaData), NewProp_OnUpgradePurchased_MetaData) }; // 1467711956
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AProgressionManager_Statics::NewProp_OnUnlockAchieved = { "OnUnlockAchieved", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProgressionManager, OnUnlockAchieved), Z_Construct_UDelegateFunction_RoughReality_OnUnlockAchieved__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnUnlockAchieved_MetaData), NewProp_OnUnlockAchieved_MetaData) }; // 1914521516
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AProgressionManager_Statics::NewProp_OnPrestigeLevelUp = { "OnPrestigeLevelUp", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProgressionManager, OnPrestigeLevelUp), Z_Construct_UDelegateFunction_RoughReality_OnPrestigeLevelUp__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPrestigeLevelUp_MetaData), NewProp_OnPrestigeLevelUp_MetaData) }; // 1852829507
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AProgressionManager_Statics::NewProp_SaveGameRef = { "SaveGameRef", nullptr, (EPropertyFlags)0x0040000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AProgressionManager, SaveGameRef), Z_Construct_UClass_URoughSaveGame_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SaveGameRef_MetaData), NewProp_SaveGameRef_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AProgressionManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProgressionManager_Statics::NewProp_UpgradeDataTable,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProgressionManager_Statics::NewProp_UnlockConditionsTable,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProgressionManager_Statics::NewProp_PrestigeLevels_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProgressionManager_Statics::NewProp_PrestigeLevels,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProgressionManager_Statics::NewProp_CurrentPrestigeLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProgressionManager_Statics::NewProp_OnUpgradePurchased,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProgressionManager_Statics::NewProp_OnUnlockAchieved,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProgressionManager_Statics::NewProp_OnPrestigeLevelUp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AProgressionManager_Statics::NewProp_SaveGameRef,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AProgressionManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AProgressionManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AProgressionManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AProgressionManager_Statics::ClassParams = {
	&AProgressionManager::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AProgressionManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AProgressionManager_Statics::PropPointers),
	0,
	0x009000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AProgressionManager_Statics::Class_MetaDataParams), Z_Construct_UClass_AProgressionManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AProgressionManager()
{
	if (!Z_Registration_Info_UClass_AProgressionManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AProgressionManager.OuterSingleton, Z_Construct_UClass_AProgressionManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AProgressionManager.OuterSingleton;
}
template<> ROUGHREALITY_API UClass* StaticClass<AProgressionManager>()
{
	return AProgressionManager::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AProgressionManager);
AProgressionManager::~AProgressionManager() {}
// End Class AProgressionManager

// Begin Registration
struct Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Progression_ProgressionManager_h_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FUpgradeNode::StaticStruct, Z_Construct_UScriptStruct_FUpgradeNode_Statics::NewStructOps, TEXT("UpgradeNode"), &Z_Registration_Info_UScriptStruct_UpgradeNode, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FUpgradeNode), 1713216908U) },
		{ FUnlockCondition::StaticStruct, Z_Construct_UScriptStruct_FUnlockCondition_Statics::NewStructOps, TEXT("UnlockCondition"), &Z_Registration_Info_UScriptStruct_UnlockCondition, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FUnlockCondition), 1428293075U) },
		{ FPrestigeLevel::StaticStruct, Z_Construct_UScriptStruct_FPrestigeLevel_Statics::NewStructOps, TEXT("PrestigeLevel"), &Z_Registration_Info_UScriptStruct_PrestigeLevel, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPrestigeLevel), 3087673465U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AProgressionManager, AProgressionManager::StaticClass, TEXT("AProgressionManager"), &Z_Registration_Info_UClass_AProgressionManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AProgressionManager), 1415943928U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Progression_ProgressionManager_h_936901317(TEXT("/Script/RoughReality"),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Progression_ProgressionManager_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Progression_ProgressionManager_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Progression_ProgressionManager_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Progression_ProgressionManager_h_Statics::ScriptStructInfo),
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
