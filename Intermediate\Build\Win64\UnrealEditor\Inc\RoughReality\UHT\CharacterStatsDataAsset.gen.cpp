// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RoughReality/DataAssets/CharacterStatsDataAsset.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeCharacterStatsDataAsset() {}

// Begin Cross Module References
ENGINE_API UClass* Z_Construct_UClass_UParticleSystem_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USoundBase_NoRegister();
ROUGHREALITY_API UClass* Z_Construct_UClass_UCharacterStatsDataAsset();
ROUGHREALITY_API UClass* Z_Construct_UClass_UCharacterStatsDataAsset_NoRegister();
ROUGHREALITY_API UClass* Z_Construct_UClass_URoughRealityDataAsset();
UPackage* Z_Construct_UPackage__Script_RoughReality();
// End Cross Module References

// Begin Class UCharacterStatsDataAsset Function GetEffectiveBulletTimeEnergy
struct Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveBulletTimeEnergy_Statics
{
	struct CharacterStatsDataAsset_eventGetEffectiveBulletTimeEnergy_Parms
	{
		int32 UpgradeLevel;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Stats" },
		{ "CPP_Default_UpgradeLevel", "0" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_UpgradeLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveBulletTimeEnergy_Statics::NewProp_UpgradeLevel = { "UpgradeLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CharacterStatsDataAsset_eventGetEffectiveBulletTimeEnergy_Parms, UpgradeLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveBulletTimeEnergy_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CharacterStatsDataAsset_eventGetEffectiveBulletTimeEnergy_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveBulletTimeEnergy_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveBulletTimeEnergy_Statics::NewProp_UpgradeLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveBulletTimeEnergy_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveBulletTimeEnergy_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveBulletTimeEnergy_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UCharacterStatsDataAsset, nullptr, "GetEffectiveBulletTimeEnergy", nullptr, nullptr, Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveBulletTimeEnergy_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveBulletTimeEnergy_Statics::PropPointers), sizeof(Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveBulletTimeEnergy_Statics::CharacterStatsDataAsset_eventGetEffectiveBulletTimeEnergy_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveBulletTimeEnergy_Statics::Function_MetaDataParams), Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveBulletTimeEnergy_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveBulletTimeEnergy_Statics::CharacterStatsDataAsset_eventGetEffectiveBulletTimeEnergy_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveBulletTimeEnergy()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveBulletTimeEnergy_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UCharacterStatsDataAsset::execGetEffectiveBulletTimeEnergy)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_UpgradeLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetEffectiveBulletTimeEnergy(Z_Param_UpgradeLevel);
	P_NATIVE_END;
}
// End Class UCharacterStatsDataAsset Function GetEffectiveBulletTimeEnergy

// Begin Class UCharacterStatsDataAsset Function GetEffectiveDashDistance
struct Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveDashDistance_Statics
{
	struct CharacterStatsDataAsset_eventGetEffectiveDashDistance_Parms
	{
		int32 UpgradeLevel;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Stats" },
		{ "CPP_Default_UpgradeLevel", "0" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_UpgradeLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveDashDistance_Statics::NewProp_UpgradeLevel = { "UpgradeLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CharacterStatsDataAsset_eventGetEffectiveDashDistance_Parms, UpgradeLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveDashDistance_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CharacterStatsDataAsset_eventGetEffectiveDashDistance_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveDashDistance_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveDashDistance_Statics::NewProp_UpgradeLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveDashDistance_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveDashDistance_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveDashDistance_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UCharacterStatsDataAsset, nullptr, "GetEffectiveDashDistance", nullptr, nullptr, Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveDashDistance_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveDashDistance_Statics::PropPointers), sizeof(Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveDashDistance_Statics::CharacterStatsDataAsset_eventGetEffectiveDashDistance_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveDashDistance_Statics::Function_MetaDataParams), Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveDashDistance_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveDashDistance_Statics::CharacterStatsDataAsset_eventGetEffectiveDashDistance_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveDashDistance()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveDashDistance_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UCharacterStatsDataAsset::execGetEffectiveDashDistance)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_UpgradeLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetEffectiveDashDistance(Z_Param_UpgradeLevel);
	P_NATIVE_END;
}
// End Class UCharacterStatsDataAsset Function GetEffectiveDashDistance

// Begin Class UCharacterStatsDataAsset Function GetEffectiveMaxHealth
struct Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveMaxHealth_Statics
{
	struct CharacterStatsDataAsset_eventGetEffectiveMaxHealth_Parms
	{
		int32 UpgradeLevel;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Get calculated stats based on upgrades */" },
#endif
		{ "CPP_Default_UpgradeLevel", "0" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get calculated stats based on upgrades" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_UpgradeLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveMaxHealth_Statics::NewProp_UpgradeLevel = { "UpgradeLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CharacterStatsDataAsset_eventGetEffectiveMaxHealth_Parms, UpgradeLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveMaxHealth_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CharacterStatsDataAsset_eventGetEffectiveMaxHealth_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveMaxHealth_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveMaxHealth_Statics::NewProp_UpgradeLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveMaxHealth_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveMaxHealth_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveMaxHealth_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UCharacterStatsDataAsset, nullptr, "GetEffectiveMaxHealth", nullptr, nullptr, Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveMaxHealth_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveMaxHealth_Statics::PropPointers), sizeof(Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveMaxHealth_Statics::CharacterStatsDataAsset_eventGetEffectiveMaxHealth_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveMaxHealth_Statics::Function_MetaDataParams), Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveMaxHealth_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveMaxHealth_Statics::CharacterStatsDataAsset_eventGetEffectiveMaxHealth_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveMaxHealth()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveMaxHealth_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UCharacterStatsDataAsset::execGetEffectiveMaxHealth)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_UpgradeLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetEffectiveMaxHealth(Z_Param_UpgradeLevel);
	P_NATIVE_END;
}
// End Class UCharacterStatsDataAsset Function GetEffectiveMaxHealth

// Begin Class UCharacterStatsDataAsset Function GetEffectiveRewindCharges
struct Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveRewindCharges_Statics
{
	struct CharacterStatsDataAsset_eventGetEffectiveRewindCharges_Parms
	{
		int32 UpgradeLevel;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Stats" },
		{ "CPP_Default_UpgradeLevel", "0" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_UpgradeLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveRewindCharges_Statics::NewProp_UpgradeLevel = { "UpgradeLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CharacterStatsDataAsset_eventGetEffectiveRewindCharges_Parms, UpgradeLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveRewindCharges_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(CharacterStatsDataAsset_eventGetEffectiveRewindCharges_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveRewindCharges_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveRewindCharges_Statics::NewProp_UpgradeLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveRewindCharges_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveRewindCharges_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveRewindCharges_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UCharacterStatsDataAsset, nullptr, "GetEffectiveRewindCharges", nullptr, nullptr, Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveRewindCharges_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveRewindCharges_Statics::PropPointers), sizeof(Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveRewindCharges_Statics::CharacterStatsDataAsset_eventGetEffectiveRewindCharges_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveRewindCharges_Statics::Function_MetaDataParams), Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveRewindCharges_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveRewindCharges_Statics::CharacterStatsDataAsset_eventGetEffectiveRewindCharges_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveRewindCharges()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveRewindCharges_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UCharacterStatsDataAsset::execGetEffectiveRewindCharges)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_UpgradeLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetEffectiveRewindCharges(Z_Param_UpgradeLevel);
	P_NATIVE_END;
}
// End Class UCharacterStatsDataAsset Function GetEffectiveRewindCharges

// Begin Class UCharacterStatsDataAsset
void UCharacterStatsDataAsset::StaticRegisterNativesUCharacterStatsDataAsset()
{
	UClass* Class = UCharacterStatsDataAsset::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "GetEffectiveBulletTimeEnergy", &UCharacterStatsDataAsset::execGetEffectiveBulletTimeEnergy },
		{ "GetEffectiveDashDistance", &UCharacterStatsDataAsset::execGetEffectiveDashDistance },
		{ "GetEffectiveMaxHealth", &UCharacterStatsDataAsset::execGetEffectiveMaxHealth },
		{ "GetEffectiveRewindCharges", &UCharacterStatsDataAsset::execGetEffectiveRewindCharges },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UCharacterStatsDataAsset);
UClass* Z_Construct_UClass_UCharacterStatsDataAsset_NoRegister()
{
	return UCharacterStatsDataAsset::StaticClass();
}
struct Z_Construct_UClass_UCharacterStatsDataAsset_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Data Asset for character statistics and configuration\n * Used to configure Rookie's base stats and abilities\n */" },
#endif
		{ "IncludePath", "DataAssets/CharacterStatsDataAsset.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data Asset for character statistics and configuration\nUsed to configure Rookie's base stats and abilities" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHealth_MetaData[] = {
		{ "Category", "Health" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Health Configuration */" },
#endif
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Health Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealthRegenRate_MetaData[] = {
		{ "Category", "Health" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealthRegenDelay_MetaData[] = {
		{ "Category", "Health" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxWalkSpeed_MetaData[] = {
		{ "Category", "Movement" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Movement Configuration */" },
#endif
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Movement Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxRunSpeed_MetaData[] = {
		{ "Category", "Movement" },
		{ "ClampMin", "100.0" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_JumpVelocity_MetaData[] = {
		{ "Category", "Movement" },
		{ "ClampMin", "300.0" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AirControl_MetaData[] = {
		{ "Category", "Movement" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxBulletTimeEnergy_MetaData[] = {
		{ "Category", "Bullet Time" },
		{ "ClampMin", "10.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Bullet Time Configuration */" },
#endif
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Bullet Time Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BulletTimeDrainRate_MetaData[] = {
		{ "Category", "Bullet Time" },
		{ "ClampMin", "1.0" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BulletTimeRechargeRate_MetaData[] = {
		{ "Category", "Bullet Time" },
		{ "ClampMin", "1.0" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BulletTimeScale_MetaData[] = {
		{ "Category", "Bullet Time" },
		{ "ClampMax", "0.9" },
		{ "ClampMin", "0.1" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxRewindCharges_MetaData[] = {
		{ "Category", "Time Rewind" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Time Rewind Configuration */" },
#endif
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Time Rewind Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SnapshotInterval_MetaData[] = {
		{ "Category", "Time Rewind" },
		{ "ClampMin", "0.1" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewindDuration_MetaData[] = {
		{ "Category", "Time Rewind" },
		{ "ClampMin", "1.0" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashDistance_MetaData[] = {
		{ "Category", "Dash" },
		{ "ClampMin", "100.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Dash Configuration */" },
#endif
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dash Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashDuration_MetaData[] = {
		{ "Category", "Dash" },
		{ "ClampMin", "0.1" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashCooldown_MetaData[] = {
		{ "Category", "Dash" },
		{ "ClampMin", "0.5" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bDashThroughEnemies_MetaData[] = {
		{ "Category", "Dash" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DamageMultiplier_MetaData[] = {
		{ "Category", "Combat" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Combat Configuration */" },
#endif
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Combat Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefenseMultiplier_MetaData[] = {
		{ "Category", "Combat" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CriticalHitChance_MetaData[] = {
		{ "Category", "Combat" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CriticalHitMultiplier_MetaData[] = {
		{ "Category", "Combat" },
		{ "ClampMin", "1.0" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionRange_MetaData[] = {
		{ "Category", "Interaction" },
		{ "ClampMin", "50.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Interaction Configuration */" },
#endif
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Interaction Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionTime_MetaData[] = {
		{ "Category", "Interaction" },
		{ "ClampMin", "0.1" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FootstepSound_MetaData[] = {
		{ "Category", "Audio" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Audio Configuration */" },
#endif
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_JumpSound_MetaData[] = {
		{ "Category", "Audio" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LandSound_MetaData[] = {
		{ "Category", "Audio" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashSound_MetaData[] = {
		{ "Category", "Audio" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BulletTimeStartSound_MetaData[] = {
		{ "Category", "Audio" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BulletTimeStopSound_MetaData[] = {
		{ "Category", "Audio" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewindSound_MetaData[] = {
		{ "Category", "Audio" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashEffect_MetaData[] = {
		{ "Category", "VFX" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Visual Effects Configuration */" },
#endif
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visual Effects Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BulletTimeEffect_MetaData[] = {
		{ "Category", "VFX" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewindEffect_MetaData[] = {
		{ "Category", "VFX" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DeathEffect_MetaData[] = {
		{ "Category", "VFX" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealthUpgradeMultiplier_MetaData[] = {
		{ "Category", "Upgrades" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Upgrade scaling factors */" },
#endif
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Upgrade scaling factors" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DashUpgradeMultiplier_MetaData[] = {
		{ "Category", "Upgrades" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BulletTimeUpgradeMultiplier_MetaData[] = {
		{ "Category", "Upgrades" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewindChargesPerUpgrade_MetaData[] = {
		{ "Category", "Upgrades" },
		{ "ClampMin", "0" },
		{ "ModuleRelativePath", "DataAssets/CharacterStatsDataAsset.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHealth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealthRegenRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealthRegenDelay;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxWalkSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxRunSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_JumpVelocity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AirControl;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxBulletTimeEnergy;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BulletTimeDrainRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BulletTimeRechargeRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BulletTimeScale;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxRewindCharges;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SnapshotInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RewindDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DashDistance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DashDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DashCooldown;
	static void NewProp_bDashThroughEnemies_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bDashThroughEnemies;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefenseMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CriticalHitChance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CriticalHitMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InteractionRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InteractionTime;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_FootstepSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_JumpSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_LandSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_DashSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_BulletTimeStartSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_BulletTimeStopSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_RewindSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_DashEffect;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_BulletTimeEffect;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_RewindEffect;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_DeathEffect;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HealthUpgradeMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DashUpgradeMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BulletTimeUpgradeMultiplier;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RewindChargesPerUpgrade;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveBulletTimeEnergy, "GetEffectiveBulletTimeEnergy" }, // **********
		{ &Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveDashDistance, "GetEffectiveDashDistance" }, // 884175148
		{ &Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveMaxHealth, "GetEffectiveMaxHealth" }, // **********
		{ &Z_Construct_UFunction_UCharacterStatsDataAsset_GetEffectiveRewindCharges, "GetEffectiveRewindCharges" }, // 69399327
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UCharacterStatsDataAsset>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_MaxHealth = { "MaxHealth", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, MaxHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHealth_MetaData), NewProp_MaxHealth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_HealthRegenRate = { "HealthRegenRate", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, HealthRegenRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealthRegenRate_MetaData), NewProp_HealthRegenRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_HealthRegenDelay = { "HealthRegenDelay", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, HealthRegenDelay), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealthRegenDelay_MetaData), NewProp_HealthRegenDelay_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_MaxWalkSpeed = { "MaxWalkSpeed", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, MaxWalkSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxWalkSpeed_MetaData), NewProp_MaxWalkSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_MaxRunSpeed = { "MaxRunSpeed", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, MaxRunSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxRunSpeed_MetaData), NewProp_MaxRunSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_JumpVelocity = { "JumpVelocity", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, JumpVelocity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_JumpVelocity_MetaData), NewProp_JumpVelocity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_AirControl = { "AirControl", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, AirControl), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AirControl_MetaData), NewProp_AirControl_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_MaxBulletTimeEnergy = { "MaxBulletTimeEnergy", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, MaxBulletTimeEnergy), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxBulletTimeEnergy_MetaData), NewProp_MaxBulletTimeEnergy_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_BulletTimeDrainRate = { "BulletTimeDrainRate", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, BulletTimeDrainRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BulletTimeDrainRate_MetaData), NewProp_BulletTimeDrainRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_BulletTimeRechargeRate = { "BulletTimeRechargeRate", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, BulletTimeRechargeRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BulletTimeRechargeRate_MetaData), NewProp_BulletTimeRechargeRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_BulletTimeScale = { "BulletTimeScale", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, BulletTimeScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BulletTimeScale_MetaData), NewProp_BulletTimeScale_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_MaxRewindCharges = { "MaxRewindCharges", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, MaxRewindCharges), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxRewindCharges_MetaData), NewProp_MaxRewindCharges_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_SnapshotInterval = { "SnapshotInterval", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, SnapshotInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SnapshotInterval_MetaData), NewProp_SnapshotInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_RewindDuration = { "RewindDuration", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, RewindDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewindDuration_MetaData), NewProp_RewindDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_DashDistance = { "DashDistance", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, DashDistance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashDistance_MetaData), NewProp_DashDistance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_DashDuration = { "DashDuration", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, DashDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashDuration_MetaData), NewProp_DashDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_DashCooldown = { "DashCooldown", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, DashCooldown), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashCooldown_MetaData), NewProp_DashCooldown_MetaData) };
void Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_bDashThroughEnemies_SetBit(void* Obj)
{
	((UCharacterStatsDataAsset*)Obj)->bDashThroughEnemies = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_bDashThroughEnemies = { "bDashThroughEnemies", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UCharacterStatsDataAsset), &Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_bDashThroughEnemies_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bDashThroughEnemies_MetaData), NewProp_bDashThroughEnemies_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_DamageMultiplier = { "DamageMultiplier", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, DamageMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DamageMultiplier_MetaData), NewProp_DamageMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_DefenseMultiplier = { "DefenseMultiplier", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, DefenseMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefenseMultiplier_MetaData), NewProp_DefenseMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_CriticalHitChance = { "CriticalHitChance", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, CriticalHitChance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CriticalHitChance_MetaData), NewProp_CriticalHitChance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_CriticalHitMultiplier = { "CriticalHitMultiplier", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, CriticalHitMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CriticalHitMultiplier_MetaData), NewProp_CriticalHitMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_InteractionRange = { "InteractionRange", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, InteractionRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionRange_MetaData), NewProp_InteractionRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_InteractionTime = { "InteractionTime", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, InteractionTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionTime_MetaData), NewProp_InteractionTime_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_FootstepSound = { "FootstepSound", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, FootstepSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FootstepSound_MetaData), NewProp_FootstepSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_JumpSound = { "JumpSound", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, JumpSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_JumpSound_MetaData), NewProp_JumpSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_LandSound = { "LandSound", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, LandSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LandSound_MetaData), NewProp_LandSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_DashSound = { "DashSound", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, DashSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashSound_MetaData), NewProp_DashSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_BulletTimeStartSound = { "BulletTimeStartSound", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, BulletTimeStartSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BulletTimeStartSound_MetaData), NewProp_BulletTimeStartSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_BulletTimeStopSound = { "BulletTimeStopSound", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, BulletTimeStopSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BulletTimeStopSound_MetaData), NewProp_BulletTimeStopSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_RewindSound = { "RewindSound", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, RewindSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewindSound_MetaData), NewProp_RewindSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_DashEffect = { "DashEffect", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, DashEffect), Z_Construct_UClass_UParticleSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashEffect_MetaData), NewProp_DashEffect_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_BulletTimeEffect = { "BulletTimeEffect", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, BulletTimeEffect), Z_Construct_UClass_UParticleSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BulletTimeEffect_MetaData), NewProp_BulletTimeEffect_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_RewindEffect = { "RewindEffect", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, RewindEffect), Z_Construct_UClass_UParticleSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewindEffect_MetaData), NewProp_RewindEffect_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_DeathEffect = { "DeathEffect", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, DeathEffect), Z_Construct_UClass_UParticleSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DeathEffect_MetaData), NewProp_DeathEffect_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_HealthUpgradeMultiplier = { "HealthUpgradeMultiplier", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, HealthUpgradeMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealthUpgradeMultiplier_MetaData), NewProp_HealthUpgradeMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_DashUpgradeMultiplier = { "DashUpgradeMultiplier", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, DashUpgradeMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DashUpgradeMultiplier_MetaData), NewProp_DashUpgradeMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_BulletTimeUpgradeMultiplier = { "BulletTimeUpgradeMultiplier", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, BulletTimeUpgradeMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BulletTimeUpgradeMultiplier_MetaData), NewProp_BulletTimeUpgradeMultiplier_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_RewindChargesPerUpgrade = { "RewindChargesPerUpgrade", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UCharacterStatsDataAsset, RewindChargesPerUpgrade), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewindChargesPerUpgrade_MetaData), NewProp_RewindChargesPerUpgrade_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UCharacterStatsDataAsset_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_MaxHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_HealthRegenRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_HealthRegenDelay,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_MaxWalkSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_MaxRunSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_JumpVelocity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_AirControl,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_MaxBulletTimeEnergy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_BulletTimeDrainRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_BulletTimeRechargeRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_BulletTimeScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_MaxRewindCharges,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_SnapshotInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_RewindDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_DashDistance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_DashDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_DashCooldown,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_bDashThroughEnemies,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_DamageMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_DefenseMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_CriticalHitChance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_CriticalHitMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_InteractionRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_InteractionTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_FootstepSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_JumpSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_LandSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_DashSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_BulletTimeStartSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_BulletTimeStopSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_RewindSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_DashEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_BulletTimeEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_RewindEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_DeathEffect,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_HealthUpgradeMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_DashUpgradeMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_BulletTimeUpgradeMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UCharacterStatsDataAsset_Statics::NewProp_RewindChargesPerUpgrade,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UCharacterStatsDataAsset_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UCharacterStatsDataAsset_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URoughRealityDataAsset,
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UCharacterStatsDataAsset_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UCharacterStatsDataAsset_Statics::ClassParams = {
	&UCharacterStatsDataAsset::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UCharacterStatsDataAsset_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UCharacterStatsDataAsset_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UCharacterStatsDataAsset_Statics::Class_MetaDataParams), Z_Construct_UClass_UCharacterStatsDataAsset_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UCharacterStatsDataAsset()
{
	if (!Z_Registration_Info_UClass_UCharacterStatsDataAsset.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UCharacterStatsDataAsset.OuterSingleton, Z_Construct_UClass_UCharacterStatsDataAsset_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UCharacterStatsDataAsset.OuterSingleton;
}
template<> ROUGHREALITY_API UClass* StaticClass<UCharacterStatsDataAsset>()
{
	return UCharacterStatsDataAsset::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UCharacterStatsDataAsset);
UCharacterStatsDataAsset::~UCharacterStatsDataAsset() {}
// End Class UCharacterStatsDataAsset

// Begin Registration
struct Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_DataAssets_CharacterStatsDataAsset_h_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UCharacterStatsDataAsset, UCharacterStatsDataAsset::StaticClass, TEXT("UCharacterStatsDataAsset"), &Z_Registration_Info_UClass_UCharacterStatsDataAsset, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UCharacterStatsDataAsset), 1608378657U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_DataAssets_CharacterStatsDataAsset_h_3954774435(TEXT("/Script/RoughReality"),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_DataAssets_CharacterStatsDataAsset_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_DataAssets_CharacterStatsDataAsset_h_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
