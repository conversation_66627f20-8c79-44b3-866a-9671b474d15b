Stats thread started at 0.543295
FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
ICU TimeZone Detection - Raw Offset: -3:00, Platform Override: ''
Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-7AD5F7584EFAFCB5914AD68ACA6D1929
         Session CrashGUID >====================================================
No local boot hotfix file found at: [G:/Gamedev/RoughReality/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
Loading VulkanPC ini files took 0.06 seconds
Loading Mac ini files took 0.07 seconds
Loading Android ini files took 0.07 seconds
Loading Unix ini files took 0.08 seconds
Loading IOS ini files took 0.08 seconds
Loading TVOS ini files took 0.08 seconds
Loading LinuxArm64 ini files took 0.09 seconds
Loading Linux ini files took 0.09 seconds
Loading Windows ini files took 0.05 seconds
Loading VisionOS ini files took 0.05 seconds
Pre-Initializing Audio Device Manager...
AudioInfo: 'OPUS' Registered
Lib vorbis DLL was dynamically loaded.
AudioInfo: 'OGG' Registered
AudioInfo: 'ADPCM' Registered
AudioInfo: 'PCM' Registered
AudioInfo: 'BINKA' Registered
AudioInfo: 'RADA' Registered
Audio Device Manager Pre-Initialized
Looking for build plugins target receipt
Found matching target receipt: G:/Gamedev/RoughReality/Binaries/Win64/RoughRealityEditor.target
Looking for enabled plugins target receipt
Asset registry cache read as 37.9 MiB from G:/Gamedev/RoughReality/Intermediate/CachedAssetRegistry_0.bin
Found matching target receipt: G:/Gamedev/RoughReality/Binaries/Win64/RoughRealityEditor.target
Mounting Engine plugin Bridge
Mounting Engine plugin ChaosCloth
Mounting Engine plugin ChaosVD
Mounting Engine plugin CmdLinkServer
Mounting Engine plugin EnhancedInput
Mounting Engine plugin Fab
Mounting Engine plugin FastBuildController
Mounting Engine plugin MeshPainting
Mounting Engine plugin RenderGraphInsights
Mounting Engine plugin UbaController
Mounting Engine plugin TraceUtilities
Mounting Engine plugin WorldMetrics
Mounting Engine plugin XGEController
Mounting Engine plugin DatasmithContent
Mounting Engine plugin VariantManager
Mounting Engine plugin GLTFExporter
Mounting Engine plugin VariantManagerContent
Mounting Engine plugin Niagara
Mounting Engine plugin NiagaraSimCaching
Mounting Engine plugin InterchangeEditor
Mounting Engine plugin InterchangeAssets
Mounting Engine plugin Interchange
Mounting Engine plugin AndroidMedia
Mounting Engine plugin AvfMedia
Mounting Engine plugin ImgMedia
Mounting Engine plugin MediaCompositing
Mounting Engine plugin MediaPlate
Mounting Engine plugin MediaPlayerEditor
Mounting Engine plugin WebMMedia
Mounting Engine plugin WmfMedia
Mounting Engine plugin TcpMessaging
Mounting Engine plugin UdpMessaging
Mounting Engine plugin EOSShared
Mounting Engine plugin OnlineServices
Mounting Engine plugin OnlineBase
Mounting Engine plugin OnlineSubsystem
Mounting Engine plugin OnlineSubsystemNull
Mounting Engine plugin OnlineSubsystemUtils
Mounting Engine plugin ActorLayerUtilities
Mounting Engine plugin AndroidDeviceProfileSelector
Mounting Engine plugin AndroidPermission
Mounting Engine plugin AndroidMoviePlayer
Mounting Engine plugin AndroidFileServer
Mounting Engine plugin AppleImageUtils
Mounting Engine plugin AppleMoviePlayer
Mounting Engine plugin AssetTags
Mounting Engine plugin AudioCapture
Mounting Engine plugin ArchVisCharacter
Mounting Engine plugin AudioSynesthesia
Mounting Engine plugin AudioWidgets
Mounting Engine plugin ChunkDownloader
Mounting Engine plugin CableComponent
Mounting Engine plugin ComputeFramework
Mounting Engine plugin CustomMeshComponent
Mounting Engine plugin CommonUI
Mounting Engine plugin DataRegistry
Mounting Engine plugin ExampleDeviceProfileSelector
Mounting Engine plugin GameplayAbilities
Mounting Engine plugin GeometryProcessing
Mounting Engine plugin GeometryCache
Mounting Engine plugin GoogleCloudMessaging
Mounting Engine plugin GooglePAD
Mounting Engine plugin HairStrands
Mounting Engine plugin IOSDeviceProfileSelector
Mounting Engine plugin LinuxDeviceProfileSelector
Mounting Engine plugin InputDebugging
Mounting Engine plugin LocationServicesBPLibrary
Mounting Engine plugin Metasound
Mounting Engine plugin MeshModelingToolset
Mounting Engine plugin ModularGameplay
Mounting Engine plugin MobilePatchingUtils
Mounting Engine plugin MsQuic
Mounting Engine plugin PropertyAccessEditor
Mounting Engine plugin ProceduralMeshComponent
Mounting Engine plugin ResonanceAudio
Mounting Engine plugin RigVM
Mounting Engine plugin SignificanceManager
Mounting Engine plugin SoundFields
Mounting Engine plugin StateTree
Mounting Engine plugin WaveTable
Mounting Engine plugin Synthesis
Mounting Engine plugin USDCore
Mounting Engine plugin WebMMoviePlayer
Mounting Engine plugin WindowsDeviceProfileSelector
Mounting Engine plugin WindowsMoviePlayer
Mounting Engine plugin NNEDenoiser
Mounting Engine plugin NNERuntimeORT
Mounting Engine plugin LauncherChunkInstaller
Mounting Engine plugin InterchangeTests
Mounting Engine plugin AISupport
Mounting Engine plugin Paper2D
Mounting Engine plugin EnvironmentQueryEditor
Mounting Engine plugin ACLPlugin
Mounting Engine plugin AnimationData
Mounting Engine plugin AnimationModifierLibrary
Mounting Engine plugin DeformerGraph
Mounting Engine plugin ControlRig
Mounting Engine plugin ControlRigSpline
Mounting Engine plugin BlendSpaceMotionAnalysis
Mounting Engine plugin ControlRigModules
Mounting Engine plugin LiveLink
Mounting Engine plugin IKRig
Mounting Engine plugin CameraShakePreviewer
Mounting Engine plugin RigLogic
Mounting Engine plugin GameplayCameras
Mounting Engine plugin EngineCameras
Mounting Engine plugin OodleNetwork
Mounting Engine plugin CodeLiteSourceCodeAccess
Mounting Engine plugin CLionSourceCodeAccess
Mounting Engine plugin AnimationSharing
Mounting Engine plugin DumpGPUServices
Mounting Engine plugin PixWinPlugin
Mounting Engine plugin KDevelopSourceCodeAccess
Mounting Engine plugin PlasticSourceControl
Mounting Engine plugin GitSourceControl
Mounting Engine plugin PluginUtils
Mounting Engine plugin N10XSourceCodeAccess
Mounting Engine plugin NullSourceCodeAccess
Mounting Engine plugin PerforceSourceControl
Mounting Engine plugin PropertyAccessNode
Mounting Engine plugin RenderDocPlugin
Mounting Engine plugin SubversionSourceControl
Mounting Engine plugin TextureFormatOodle
Mounting Engine plugin UObjectPlugin
Mounting Engine plugin VisualStudioCodeSourceCodeAccess
Mounting Engine plugin RiderSourceCodeAccess
Mounting Engine plugin XCodeSourceCodeAccess
Mounting Engine plugin VisualStudioSourceCodeAccess
Mounting Engine plugin AssetManagerEditor
Mounting Engine plugin ChangelistReview
Mounting Engine plugin BlueprintHeaderView
Mounting Engine plugin ColorGrading
Mounting Engine plugin DataValidation
Mounting Engine plugin CurveEditorTools
Mounting Engine plugin EngineAssetDefinitions
Mounting Engine plugin CryptoKeys
Mounting Engine plugin FacialAnimation
Mounting Engine plugin EditorDebugTools
Mounting Engine plugin EditorScriptingUtilities
Mounting Engine plugin GameplayTagsEditor
Mounting Engine plugin GeometryMode
Mounting Engine plugin MacGraphicsSwitching
Mounting Engine plugin MaterialAnalyzer
Mounting Engine plugin MeshLODToolset
Mounting Engine plugin PluginBrowser
Mounting Engine plugin ProxyLODPlugin
Mounting Engine plugin MobileLauncherProfileWizard
Mounting Engine plugin ModelingToolsEditorMode
Mounting Engine plugin SequencerAnimTools
Mounting Engine plugin StylusInput
Mounting Engine plugin SpeedTreeImporter
Mounting Engine plugin UMGWidgetPreview
Mounting Engine plugin WorldPartitionHLODUtilities
Mounting Engine plugin UVEditor
Mounting Engine plugin AdvancedRenamer
Mounting Engine plugin AutomationUtils
Mounting Engine plugin BackChannel
Mounting Engine plugin ChaosCaching
Mounting Engine plugin ChaosEditor
Mounting Engine plugin ChaosNiagara
Mounting Engine plugin CharacterAI
Mounting Engine plugin ChaosSolverPlugin
Mounting Engine plugin ChaosUserDataPT
Mounting Engine plugin Dataflow
Mounting Engine plugin EditorDataStorage
Mounting Engine plugin EditorPerformance
Mounting Engine plugin EditorTelemetry
Mounting Engine plugin Fracture
Mounting Engine plugin FullBodyIK
Mounting Engine plugin GeometryCollectionPlugin
Mounting Engine plugin GeometryFlow
Mounting Engine plugin LocalizableMessage
Mounting Engine plugin LowLevelNetTrace
Mounting Engine plugin MeshModelingToolsetExp
Mounting Engine plugin NFORDenoise
Mounting Engine plugin PlanarCut
Mounting Engine plugin PlatformCrypto
Mounting Engine plugin PythonScriptPlugin
Mounting Engine plugin SkeletalReduction
Mounting Engine plugin StudioTelemetry
Mounting Engine plugin ToolPresets
Mounting Engine plugin AlembicImporter
Mounting Engine plugin ActorSequence
Mounting Engine plugin LevelSequenceEditor
Mounting Engine plugin TemplateSequence
Mounting Engine plugin SequencerScripting
Mounting Engine plugin Takes
Mounting Engine plugin OnlineSubsystemIOS
Mounting Engine plugin PortableObjectFileDataSource
Mounting Engine plugin MetaHumanSDK
Mounting Engine plugin OnlineSubsystemGooglePlay
Mounting Engine plugin XInputDevice
Mounting Engine plugin SkeletalMeshModelingTools
Mounting Engine plugin LightMixer
Mounting Engine plugin ObjectMixer
Mounting Engine plugin BaseCharacterFXEditor
Mounting Engine plugin ContentBrowserClassDataSource
Mounting Engine plugin ContentBrowserAssetDataSource
Mounting Engine plugin ContentBrowserFileDataSource
Mounting Engine plugin HoldoutComposite
Mounting Engine plugin SQLiteCore
Mounting Engine plugin ConcertMain
Mounting Engine plugin ConcertSyncCore
Mounting Engine plugin ConcertSyncClient
Revision control is disabled
Revision control is disabled
Revision control is disabled
Initializing EOSSDK Version:1.17.0-39599718
Using libcurl 8.4.0
 - built for Windows
 - supports SSL with OpenSSL/1.1.1t
 - supports HTTP deflate (compression) using libz 1.3
 - other features:
     CURL_VERSION_SSL
     CURL_VERSION_LIBZ
     CURL_VERSION_IPV6
     CURL_VERSION_ASYNCHDNS
     CURL_VERSION_LARGEFILE
     CURL_VERSION_HTTP2
 CurlRequestOptions (configurable via config and command line):
 - bVerifyPeer = true  - Libcurl will verify peer certificate
 - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
 - bDontReuseConnections = false  - Libcurl will reuse connections
 - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
 - LocalHostAddr = Default
 - BufferSize = 65536
CreateHttpThread using FCurlMultiPollEventLoopHttpThread
Creating http thread with maximum ********** concurrent requests
WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
OSS: Created online subsystem instance for: NULL
OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
File 'WinPixGpuCapturer.dll' does not exist
PIX capture plugin failed to initialize! Check that the process is launched from PIX.
Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
NFORDenoise function starting up
Starting StudioTelemetry Module
Started StudioTelemetry Session
ExecutableName: UnrealEditor.exe
Build: ++UE5+Release-5.5-***********
Platform=WindowsEditor
MachineId=dadec2e1421bcefd03625fbb5d8813ad
DeviceId=
Engine Version: 5.5.4-40574608+++UE5+Release-5.5
Compatible Engine Version: 5.5.0-37670630+++UE5+Release-5.5
Net CL: 37670630
OS: Windows 11 (24H2) [10.0.26100.4061] (), CPU: AMD Ryzen 5 5600X 6-Core Processor             , GPU: NVIDIA GeForce RTX 3070
Compiled (64-bit): Mar  7 2025 14:49:53
Architecture: x64
Compiled with Visual C++: 19.38.33130.00
Build Configuration: Development
Branch Name: ++UE5+Release-5.5
Command Line: 
Base Directory: F:/Unreal/UE_5.5/Engine/Binaries/Win64/
Allocator: Mimalloc
Installed Engine Build: 1
This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
Number of dev versions registered: 36
  Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
  Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
  Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
  Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
  Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
  Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
  Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
  Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
  Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
  Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
  Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
  Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
  Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
  Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
  Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
  Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
  Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
  Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
  Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
  FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 170
  FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
  FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
  FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 15
  Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 10
  Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
  Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
  Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
  Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
  Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
  Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
  UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 119
  UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 51
  UE5-PrivateFrosty (59DA5D52-1232-4948-B878-597870B8E98B): 8
  Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
  Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
  Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
Branch 'EditorLayout' had been unloaded. Reloading on-demand took 1.18ms
Branch 'Bridge' had been unloaded. Reloading on-demand took 0.17ms
Branch 'ChaosCloth' had been unloaded. Reloading on-demand took 0.17ms
Branch 'CmdLinkServer' had been unloaded. Reloading on-demand took 0.18ms
Branch 'FastBuildController' had been unloaded. Reloading on-demand took 0.17ms
Branch 'MeshPainting' had been unloaded. Reloading on-demand took 0.16ms
Branch 'RenderGraphInsights' had been unloaded. Reloading on-demand took 0.19ms
Branch 'UbaController' had been unloaded. Reloading on-demand took 0.16ms
Branch 'WorldMetrics' had been unloaded. Reloading on-demand took 0.16ms
Branch 'XGEController' had been unloaded. Reloading on-demand took 0.17ms
Branch 'VariantManager' had been unloaded. Reloading on-demand took 0.17ms
Branch 'NiagaraSimCaching' had been unloaded. Reloading on-demand took 0.19ms
Branch 'InterchangeEditor' had been unloaded. Reloading on-demand took 0.18ms
Branch 'AndroidMedia' had been unloaded. Reloading on-demand took 0.18ms
Branch 'AvfMedia' had been unloaded. Reloading on-demand took 0.17ms
Branch 'ImgMedia' had been unloaded. Reloading on-demand took 0.17ms
Branch 'MediaCompositing' had been unloaded. Reloading on-demand took 0.18ms
Branch 'MediaPlate' had been unloaded. Reloading on-demand took 0.16ms
Branch 'MediaPlayerEditor' had been unloaded. Reloading on-demand took 0.17ms
Branch 'WebMMedia' had been unloaded. Reloading on-demand took 0.18ms
Branch 'WmfMedia' had been unloaded. Reloading on-demand took 0.16ms
Branch 'TcpMessaging' had been unloaded. Reloading on-demand took 0.16ms
Branch 'UdpMessaging' had been unloaded. Reloading on-demand took 0.16ms
Branch 'EOSShared' had been unloaded. Reloading on-demand took 0.17ms
Branch 'OnlineServices' had been unloaded. Reloading on-demand took 0.17ms
Branch 'OnlineBase' had been unloaded. Reloading on-demand took 0.16ms
Branch 'OnlineSubsystem' had been unloaded. Reloading on-demand took 0.16ms
Branch 'OnlineSubsystemNull' had been unloaded. Reloading on-demand took 0.17ms
Branch 'OnlineSubsystemUtils' had been unloaded. Reloading on-demand took 0.16ms
Branch 'ActorLayerUtilities' had been unloaded. Reloading on-demand took 0.16ms
Branch 'AndroidDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.17ms
Branch 'AndroidPermission' had been unloaded. Reloading on-demand took 0.16ms
Branch 'AndroidMoviePlayer' had been unloaded. Reloading on-demand took 0.16ms
Branch 'AndroidFileServer' had been unloaded. Reloading on-demand took 0.16ms
Branch 'AppleImageUtils' had been unloaded. Reloading on-demand took 0.16ms
Branch 'AppleMoviePlayer' had been unloaded. Reloading on-demand took 0.16ms
Branch 'AssetTags' had been unloaded. Reloading on-demand took 0.16ms
Branch 'AudioCapture' had been unloaded. Reloading on-demand took 0.25ms
Branch 'ArchVisCharacter' had been unloaded. Reloading on-demand took 0.17ms
Branch 'AudioSynesthesia' had been unloaded. Reloading on-demand took 0.16ms
Branch 'AudioWidgets' had been unloaded. Reloading on-demand took 0.16ms
Branch 'ChunkDownloader' had been unloaded. Reloading on-demand took 0.17ms
Branch 'CableComponent' had been unloaded. Reloading on-demand took 0.17ms
Branch 'ComputeFramework' had been unloaded. Reloading on-demand took 0.17ms
Branch 'CustomMeshComponent' had been unloaded. Reloading on-demand took 0.17ms
Branch 'DataRegistry' had been unloaded. Reloading on-demand took 0.19ms
Branch 'ExampleDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.17ms
Branch 'GeometryProcessing' had been unloaded. Reloading on-demand took 0.18ms
Branch 'GeometryCache' had been unloaded. Reloading on-demand took 0.17ms
Branch 'GoogleCloudMessaging' had been unloaded. Reloading on-demand took 0.17ms
Branch 'GooglePAD' had been unloaded. Reloading on-demand took 0.16ms
Branch 'IOSDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.17ms
Branch 'LinuxDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.17ms
Branch 'InputDebugging' had been unloaded. Reloading on-demand took 0.16ms
Branch 'LocationServicesBPLibrary' had been unloaded. Reloading on-demand took 0.16ms
Branch 'MeshModelingToolset' had been unloaded. Reloading on-demand took 0.17ms
Branch 'ModularGameplay' had been unloaded. Reloading on-demand took 0.16ms
Branch 'MobilePatchingUtils' had been unloaded. Reloading on-demand took 0.18ms
Branch 'MsQuic' had been unloaded. Reloading on-demand took 0.16ms
Branch 'PropertyAccessEditor' had been unloaded. Reloading on-demand took 0.17ms
Branch 'ProceduralMeshComponent' had been unloaded. Reloading on-demand took 0.16ms
Branch 'ResonanceAudio' had been unloaded. Reloading on-demand took 0.16ms
Branch 'SignificanceManager' had been unloaded. Reloading on-demand took 0.17ms
Branch 'SoundFields' had been unloaded. Reloading on-demand took 0.16ms
Branch 'WaveTable' had been unloaded. Reloading on-demand took 0.17ms
Branch 'WebMMoviePlayer' had been unloaded. Reloading on-demand took 0.17ms
Branch 'WindowsDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.18ms
Branch 'WindowsMoviePlayer' had been unloaded. Reloading on-demand took 0.17ms
Branch 'NNEDenoiser' had been unloaded. Reloading on-demand took 0.17ms
Branch 'NNERuntimeORT' had been unloaded. Reloading on-demand took 0.16ms
Branch 'LauncherChunkInstaller' had been unloaded. Reloading on-demand took 0.16ms
Branch 'InterchangeTests' had been unloaded. Reloading on-demand took 0.16ms
Branch 'AISupport' had been unloaded. Reloading on-demand took 0.19ms
Branch 'EnvironmentQueryEditor' had been unloaded. Reloading on-demand took 0.17ms
Branch 'ACLPlugin' had been unloaded. Reloading on-demand took 0.16ms
Branch 'AnimationData' had been unloaded. Reloading on-demand took 0.17ms
Branch 'AnimationModifierLibrary' had been unloaded. Reloading on-demand took 0.17ms
Branch 'ControlRigSpline' had been unloaded. Reloading on-demand took 0.18ms
Branch 'BlendSpaceMotionAnalysis' had been unloaded. Reloading on-demand took 0.17ms
Branch 'ControlRigModules' had been unloaded. Reloading on-demand took 0.18ms
Branch 'CameraShakePreviewer' had been unloaded. Reloading on-demand took 0.17ms
Branch 'RigLogic' had been unloaded. Reloading on-demand took 0.18ms
Branch 'EngineCameras' had been unloaded. Reloading on-demand took 0.17ms
Branch 'OodleNetwork' had been unloaded. Reloading on-demand took 0.17ms
Branch 'CodeLiteSourceCodeAccess' had been unloaded. Reloading on-demand took 0.17ms
Branch 'CLionSourceCodeAccess' had been unloaded. Reloading on-demand took 0.18ms
Branch 'AnimationSharing' had been unloaded. Reloading on-demand took 0.16ms
Branch 'DumpGPUServices' had been unloaded. Reloading on-demand took 0.16ms
Branch 'PixWinPlugin' had been unloaded. Reloading on-demand took 0.16ms
Branch 'KDevelopSourceCodeAccess' had been unloaded. Reloading on-demand took 0.16ms
Branch 'PlasticSourceControl' had been unloaded. Reloading on-demand took 0.16ms
Branch 'GitSourceControl' had been unloaded. Reloading on-demand took 0.16ms
Branch 'PluginUtils' had been unloaded. Reloading on-demand took 0.16ms
Branch 'N10XSourceCodeAccess' had been unloaded. Reloading on-demand took 0.16ms
Branch 'NullSourceCodeAccess' had been unloaded. Reloading on-demand took 0.16ms
Branch 'PerforceSourceControl' had been unloaded. Reloading on-demand took 0.16ms
Branch 'PropertyAccessNode' had been unloaded. Reloading on-demand took 0.17ms
Branch 'RenderDocPlugin' had been unloaded. Reloading on-demand took 0.17ms
Branch 'SubversionSourceControl' had been unloaded. Reloading on-demand took 0.16ms
Branch 'TextureFormatOodle' had been unloaded. Reloading on-demand took 0.17ms
Branch 'UObjectPlugin' had been unloaded. Reloading on-demand took 0.16ms
Branch 'VisualStudioCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.17ms
Branch 'RiderSourceCodeAccess' had been unloaded. Reloading on-demand took 0.27ms
Branch 'XCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.18ms
Branch 'VisualStudioSourceCodeAccess' had been unloaded. Reloading on-demand took 0.17ms
Branch 'AssetManagerEditor' had been unloaded. Reloading on-demand took 0.17ms
Branch 'ChangelistReview' had been unloaded. Reloading on-demand took 0.17ms
Branch 'BlueprintHeaderView' had been unloaded. Reloading on-demand took 0.16ms
Branch 'ColorGrading' had been unloaded. Reloading on-demand took 0.16ms
Branch 'DataValidation' had been unloaded. Reloading on-demand took 0.16ms
Branch 'CurveEditorTools' had been unloaded. Reloading on-demand took 0.16ms
Branch 'EngineAssetDefinitions' had been unloaded. Reloading on-demand took 0.17ms
Branch 'CryptoKeys' had been unloaded. Reloading on-demand took 0.16ms
Branch 'FacialAnimation' had been unloaded. Reloading on-demand took 0.16ms
Branch 'EditorDebugTools' had been unloaded. Reloading on-demand took 0.16ms
Branch 'GameplayTagsEditor' had been unloaded. Reloading on-demand took 0.17ms
Branch 'GeometryMode' had been unloaded. Reloading on-demand took 0.16ms
Branch 'MacGraphicsSwitching' had been unloaded. Reloading on-demand took 0.18ms
Branch 'MaterialAnalyzer' had been unloaded. Reloading on-demand took 0.16ms
Branch 'MeshLODToolset' had been unloaded. Reloading on-demand took 0.17ms
Branch 'PluginBrowser' had been unloaded. Reloading on-demand took 0.17ms
Branch 'ProxyLODPlugin' had been unloaded. Reloading on-demand took 0.16ms
Branch 'MobileLauncherProfileWizard' had been unloaded. Reloading on-demand took 0.17ms
Branch 'ModelingToolsEditorMode' had been unloaded. Reloading on-demand took 0.17ms
Branch 'SequencerAnimTools' had been unloaded. Reloading on-demand took 0.16ms
Branch 'StylusInput' had been unloaded. Reloading on-demand took 0.16ms
Branch 'SpeedTreeImporter' had been unloaded. Reloading on-demand took 0.16ms
Branch 'UMGWidgetPreview' had been unloaded. Reloading on-demand took 0.16ms
Branch 'WorldPartitionHLODUtilities' had been unloaded. Reloading on-demand took 0.17ms
Branch 'UVEditor' had been unloaded. Reloading on-demand took 0.16ms
Branch 'AdvancedRenamer' had been unloaded. Reloading on-demand took 0.16ms
Branch 'AutomationUtils' had been unloaded. Reloading on-demand took 0.16ms
Branch 'BackChannel' had been unloaded. Reloading on-demand took 0.16ms
Branch 'ChaosCaching' had been unloaded. Reloading on-demand took 0.16ms
Branch 'ChaosEditor' had been unloaded. Reloading on-demand took 0.18ms
Branch 'ChaosNiagara' had been unloaded. Reloading on-demand took 0.16ms
Branch 'CharacterAI' had been unloaded. Reloading on-demand took 0.16ms
Branch 'ChaosSolverPlugin' had been unloaded. Reloading on-demand took 0.16ms
Branch 'ChaosUserDataPT' had been unloaded. Reloading on-demand took 0.16ms
Branch 'Dataflow' had been unloaded. Reloading on-demand took 0.17ms
Branch 'EditorDataStorage' had been unloaded. Reloading on-demand took 0.17ms
Branch 'EditorPerformance' had been unloaded. Reloading on-demand took 0.16ms
Branch 'EditorTelemetry' had been unloaded. Reloading on-demand took 0.16ms
Branch 'Fracture' had been unloaded. Reloading on-demand took 0.16ms
Branch 'GeometryCollectionPlugin' had been unloaded. Reloading on-demand took 0.17ms
Branch 'GeometryFlow' had been unloaded. Reloading on-demand took 0.16ms
Branch 'LocalizableMessage' had been unloaded. Reloading on-demand took 0.16ms
Branch 'LowLevelNetTrace' had been unloaded. Reloading on-demand took 0.16ms
Branch 'MeshModelingToolsetExp' had been unloaded. Reloading on-demand took 0.16ms
Branch 'NFORDenoise' had been unloaded. Reloading on-demand took 0.16ms
Branch 'PlanarCut' had been unloaded. Reloading on-demand took 0.17ms
Branch 'PlatformCrypto' had been unloaded. Reloading on-demand took 0.17ms
Branch 'PythonScriptPlugin' had been unloaded. Reloading on-demand took 0.16ms
Branch 'SkeletalReduction' had been unloaded. Reloading on-demand took 0.17ms
Branch 'StudioTelemetry' had been unloaded. Reloading on-demand took 0.16ms
Branch 'AlembicImporter' had been unloaded. Reloading on-demand took 0.17ms
Branch 'ActorSequence' had been unloaded. Reloading on-demand took 0.16ms
Branch 'LevelSequenceEditor' had been unloaded. Reloading on-demand took 0.16ms
Branch 'TemplateSequence' had been unloaded. Reloading on-demand took 0.16ms
Branch 'SequencerScripting' had been unloaded. Reloading on-demand took 0.16ms
Branch 'OnlineSubsystemIOS' had been unloaded. Reloading on-demand took 0.16ms
Branch 'PortableObjectFileDataSource' had been unloaded. Reloading on-demand took 0.17ms
Branch 'OnlineSubsystemGooglePlay' had been unloaded. Reloading on-demand took 0.17ms
Branch 'XInputDevice' had been unloaded. Reloading on-demand took 0.16ms
Branch 'SkeletalMeshModelingTools' had been unloaded. Reloading on-demand took 0.18ms
Branch 'LightMixer' had been unloaded. Reloading on-demand took 0.16ms
Branch 'ObjectMixer' had been unloaded. Reloading on-demand took 0.16ms
Branch 'BaseCharacterFXEditor' had been unloaded. Reloading on-demand took 0.17ms
Branch 'ContentBrowserClassDataSource' had been unloaded. Reloading on-demand took 0.17ms
Branch 'ContentBrowserAssetDataSource' had been unloaded. Reloading on-demand took 0.17ms
Branch 'ContentBrowserFileDataSource' had been unloaded. Reloading on-demand took 0.17ms
Branch 'SQLiteCore' had been unloaded. Reloading on-demand took 0.17ms
Branch 'ConcertMain' had been unloaded. Reloading on-demand took 0.17ms
Branch 'ConcertSyncClient' had been unloaded. Reloading on-demand took 0.17ms
Presizing for max 25165824 objects, including 0 objects not considered by GC.
AsyncLoading2 - Created: Event Driven Loader: false, Async Loading Thread: false, Async Post Load: false
AsyncLoading2 - Initialized
Object subsystem initialized
Set CVar [[con.DebugEarlyDefault:1]]
CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
Set CVar [[r.setres:1280x720]]
CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
CVar [[QualityLevelMapping:high]] deferred - dummy variable created
CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
Set CVar [[r.Nanite.Streaming.AsyncCompute:0	; Temporary workaround for Nanite geometry corruption (FORT-805141)]]
CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
Set CVar [[r.PSOPrecache.GlobalShaders:1]]
Set CVar [[r.DynamicRes.DynamicFrameTime:1]]
Set CVar [[r.VRS.EnableSoftware:1]]
Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
Set CVar [[r.VSync:0]]
Set CVar [[r.RHICmdBypass:0]]
Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
Set CVar [[r.GPUCrashDebugging:0]]
CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
Set CVar [[r.ReflectionMethod:1]]
Set CVar [[r.GenerateMeshDistanceFields:1]]
Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
Set CVar [[r.Lumen.TraceMeshSDFs:0]]
Set CVar [[r.Shadow.Virtual.Enable:1]]
Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
Set CVar [[r.AllowStaticLighting:0]]
Set CVar [[r.SkinCache.CompileShaders:1]]
Set CVar [[r.RayTracing:1]]
Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
Set CVar [[s.AsyncLoadingThreadEnabled:1]]
Set CVar [[s.EventDrivenLoaderEnabled:1]]
Set CVar [[s.WarnIfTimeLimitExceeded:0]]
Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
Set CVar [[s.TimeLimitExceededMinTime:0.005]]
Set CVar [[s.UseBackgroundLevelStreaming:1]]
Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
Set CVar [[s.FlushStreamingOnExit:1]]
CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
Set CVar [[gc.FlushStreamingOnGC:0]]
Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
Set CVar [[gc.AllowParallelGC:1]]
Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
Set CVar [[gc.MaxObjectsInEditor:25165824]]
Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
Set CVar [[gc.CreateGCClusters:1]]
Set CVar [[gc.MinGCClusterSize:5]]
Set CVar [[gc.AssetClustreringEnabled:0]]
Set CVar [[gc.ActorClusteringEnabled:0]]
Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
Set CVar [[gc.GarbageEliminationEnabled:1]]
Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
Set CVar [[r.SkeletalMeshLODBias:0]]
Set CVar [[r.ViewDistanceScale:1.0]]
Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
Set CVar [[r.FXAA.Quality:4]]
Set CVar [[r.TemporalAA.Quality:2]]
Set CVar [[r.TSR.History.R11G11B10:1]]
Set CVar [[r.TSR.History.ScreenPercentage:200]]
Set CVar [[r.TSR.History.UpdateQuality:3]]
Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
Set CVar [[r.TSR.ReprojectionField:1]]
Set CVar [[r.TSR.Resurrection:1]]
Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
Set CVar [[r.LightFunctionQuality:1]]
Set CVar [[r.ShadowQuality:5]]
Set CVar [[r.Shadow.CSM.MaxCascades:10]]
Set CVar [[r.Shadow.MaxResolution:2048]]
Set CVar [[r.Shadow.MaxCSMResolution:2048]]
Set CVar [[r.Shadow.RadiusThreshold:0.01]]
Set CVar [[r.Shadow.DistanceScale:1.0]]
Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
Set CVar [[r.DistanceFieldShadowing:1]]
Set CVar [[r.VolumetricFog:1]]
Set CVar [[r.VolumetricFog.GridPixelSize:8]]
Set CVar [[r.VolumetricFog.GridSizeZ:128]]
Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
Set CVar [[r.LightMaxDrawDistanceScale:1]]
Set CVar [[r.CapsuleShadows:1]]
Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
Set CVar [[r.DistanceFieldAO:1]]
Set CVar [[r.AOQuality:2]]
Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:300]]
Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:200]]
Set CVar [[r.SkyLight.RealTimeReflectionCapture:1]]
Set CVar [[r.RayTracing.Scene.BuildMode:1]]
Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
Set CVar [[r.SSR.Quality:3]]
Set CVar [[r.SSR.HalfResSceneColor:0]]
Set CVar [[r.Lumen.Reflections.Allow:1]]
Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.TonemapMode:1]]
Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
Set CVar [[r.MotionBlurQuality:4]]
Set CVar [[r.MotionBlur.HalfResGather:0]]
Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
Set CVar [[r.AmbientOcclusionMaxQuality:100]]
Set CVar [[r.AmbientOcclusionLevels:-1]]
Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
Set CVar [[r.DepthOfFieldQuality:2]]
Set CVar [[r.RenderTargetPoolMin:400]]
Set CVar [[r.LensFlareQuality:2]]
Set CVar [[r.SceneColorFringeQuality:1]]
Set CVar [[r.EyeAdaptationQuality:2]]
Set CVar [[r.BloomQuality:5]]
Set CVar [[r.Bloom.ScreenPercentage:50.000]]
Set CVar [[r.FastBlurThreshold:100]]
Set CVar [[r.Upscale.Quality:3]]
Set CVar [[r.LightShaftQuality:1]]
Set CVar [[r.Filter.SizeScale:1]]
Set CVar [[r.Tonemapper.Quality:5]]
Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
Applying CVar settings from Section [TextureQuality@3] File [Scalability]
Set CVar [[r.Streaming.MipBias:0]]
Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
Set CVar [[r.Streaming.Boost:1]]
Set CVar [[r.MaxAnisotropy:8]]
Set CVar [[r.VT.MaxAnisotropy:8]]
Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
Set CVar [[r.Streaming.PoolSize:1000]]
Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
Set CVar [[r.TranslucencyLightingVolumeDim:64]]
Set CVar [[r.RefractionQuality:2]]
Set CVar [[r.SceneColorFormat:4]]
Set CVar [[r.DetailMode:3]]
Set CVar [[r.TranslucencyVolumeBlur:1]]
Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
Set CVar [[r.SSS.Scale:1]]
Set CVar [[r.SSS.SampleSet:2]]
Set CVar [[r.SSS.Quality:1]]
Set CVar [[r.SSS.HalfRes:0]]
Set CVar [[r.SSGI.Quality:3]]
Set CVar [[r.EmitterSpawnRateScale:1.0]]
Set CVar [[r.ParticleLightQuality:2]]
Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
Set CVar [[fx.Niagara.QualityLevel:3]]
Set CVar [[r.Refraction.OffsetQuality:1]]
Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:1]]
Set CVar [[r.HeterogeneousVolumes.MaxStepCount:512]]
Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:512]]
Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
Set CVar [[foliage.DensityScale:1.0]]
Set CVar [[grass.DensityScale:1.0]]
Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
Set CVar [[r.AnisotropicMaterials:1]]
Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
Using Default RHI: D3D12
Using Highest Feature Level of D3D12: SM6
Loading RHI module D3D12RHI
Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
Found D3D12 adapter 0: NVIDIA GeForce RTX 3070 (VendorId: 10de, DeviceId: 2484, SubSysId: 146b10de, Revision: 00a1
  Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
  Adapter has 8018MB of dedicated video memory, 0MB of dedicated system memory, and 32731MB of shared system memory, 2 output[s]
  Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
     Driver Date: 5-14-2025
Found D3D12 adapter 1: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
  Max supported Feature Level 12_1, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
  Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32731MB of shared system memory, 0 output[s]
DirectX Agility SDK runtime found.
Chosen D3D12 Adapter Id = 0
RHI D3D12 with Feature Level SM6 is supported and will be used.
Selected Device Profile: [WindowsEditor]
Platform has ~ 64 GB [68641980416 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
Going up to parent DeviceProfile [Windows]
Going up to parent DeviceProfile []
Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
Set CVar [[r.DumpShaderDebugInfo:2]]
Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
Applying CVar settings from Section [ConsoleVariables] File [Engine]
Set CVar [[memory.MemoryPressureCriticalThresholdMB:512]]
Applying CVar settings from Section [ConsoleVariables] File [G:/Gamedev/RoughReality/Saved/Config/WindowsEditor/Editor.ini]
Computer: DESKTOP-68ONPC8
User: julio
CPU Page size=4096, Cores=6
High frequency timer resolution =10.000000 MHz
Process is running as part of a Windows Job with separate resource limits
Memory total: Physical=63.9GB (64GB approx) Virtual=80.7GB
Platform Memory Stats for WindowsEditor
Process Physical Memory: 601.58 MB used, 661.30 MB peak
Process Virtual Memory: 622.56 MB used, 625.54 MB peak
Physical Memory: 29664.52 MB used,  35797.58 MB free, 65462.09 MB total
Virtual Memory: 66844.92 MB used,  15825.84 MB free, 82670.75 MB total
Metadata set : extradevelopmentmemorymb="0"
WindowsPlatformFeatures enabled
Chaos Debug Draw Startup
Physics initialised using underlying interface: Chaos
Using OS detected language (en-GB).
Using OS detected locale (pt-BR).
No specific localization for 'en-GB' exists, so 'en' will be used for the language.
Setting process to per monitor DPI aware
Available input methods:
  - English (United Kingdom) - (Keyboard).
  - English (United Kingdom) - (Keyboard).
Activated input method: English (United Kingdom) - (Keyboard).
New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
Slate User Registered.  User Index 0, Is Virtual User: 0
Using Default RHI: D3D12
Using Highest Feature Level of D3D12: SM6
Loading RHI module D3D12RHI
Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
RHI D3D12 with Feature Level SM6 is supported and will be used.
Creating D3D12 RHI with Max Feature Level SM6
Attached monitors:
    resolution: 3840x2160, work area: (0, 0) -> (3840, 2088), device: '\\.\DISPLAY1' [PRIMARY]
    resolution: 1920x1080, work area: (-1920, 287) -> (0, 1319), device: '\\.\DISPLAY2'
Found 2 attached monitors.
Gathering driver information using Windows Setup API
RHI Adapter Info:
            Name: NVIDIA GeForce RTX 3070
  Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
     Driver Date: 5-14-2025
    GPU DeviceId: 0x2484 (for the marketing name, search the web for "GPU Device Id")
InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
Aftermath initialized
Emitting draw events for PIX profiling.
Aftermath enabled. Active feature flags: 
 - Feature: EnableResourceTracking
ID3D12Device1 is supported.
ID3D12Device2 is supported.
ID3D12Device3 is supported.
ID3D12Device4 is supported.
ID3D12Device5 is supported.
ID3D12Device6 is supported.
ID3D12Device7 is supported.
ID3D12Device8 is supported.
ID3D12Device9 is supported.
ID3D12Device10 is supported.
ID3D12Device11 is supported.
ID3D12Device12 is supported.
Bindless resources are supported
Stencil ref from pixel shader is not supported
Raster order views are supported
Wave Operations are supported (wave size: min=32 max=32).
D3D12 ray tracing tier 1.1 and bindless resources are supported.
Mesh shader tier 1.0 is supported
AtomicInt64OnTypedResource is supported
AtomicInt64OnGroupShared is supported
AtomicInt64OnDescriptorHeapResource is supported
Shader Model 6.6 atomic64 is supported
Work Graphs are supported
[GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000725B5970B00)
[GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000725B5970D80)
[GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000725B5971000)
Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
Texture pool is 4567 MB (70% of 6525 MB)
Async texture creation enabled
RHI has support for 64 bit atomics
HDR output is supported on adapter 0, display 0:
		MinLuminance = 0.392300
		MaxLuminance = 295.380310
		MaxFullFrameLuminance = 237.846802
HDR output is supported on adapter 0, display 1:
		MinLuminance = 0.466400
		MaxLuminance = 486.111298
		MaxFullFrameLuminance = 486.111298
Current RHI supports per-draw and screenspace Variable Rate Shading
Initializing FReadOnlyCVARCache
Running Turnkey SDK detection: ' -ScriptsForProject="G:/Gamedev/RoughReality/RoughReality.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="G:/Gamedev/RoughReality/Intermediate/TurnkeyReport_0.log" -log="G:/Gamedev/RoughReality/Intermediate/TurnkeyLog_0.log" -project="G:/Gamedev/RoughReality/RoughReality.uproject"  -platform=all'
Running Serialized UAT: [ cmd.exe /c ""F:/Unreal/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="G:/Gamedev/RoughReality/RoughReality.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="G:/Gamedev/RoughReality/Intermediate/TurnkeyReport_0.log" -log="G:/Gamedev/RoughReality/Intermediate/TurnkeyLog_0.log" -project="G:/Gamedev/RoughReality/RoughReality.uproject"  -platform=all" ]
Loaded Base TextureFormat: TextureFormatASTC
Loaded Base TextureFormat: TextureFormatDXT
Loaded Base TextureFormat: TextureFormatETC2
Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
Loaded Base TextureFormat: TextureFormatUncompressed
Oodle Texture TFO init; latest sdk version = 2.9.12
Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
Loaded Base TextureFormat: TextureFormatOodle
Loaded TargetPlatform 'Android'
Loaded TargetPlatform 'Android_ASTC'
Loaded TargetPlatform 'Android_DXT'
Loaded TargetPlatform 'Android_ETC2'
Loaded TargetPlatform 'AndroidClient'
Loaded TargetPlatform 'Android_ASTCClient'
Loaded TargetPlatform 'Android_DXTClient'
Loaded TargetPlatform 'Android_ETC2Client'
Loaded TargetPlatform 'Android_Multi'
Loaded TargetPlatform 'Android_MultiClient'
Loaded TargetPlatform 'IOS'
Loaded TargetPlatform 'IOSClient'
Loaded TargetPlatform 'Linux'
Loaded TargetPlatform 'LinuxEditor'
Loaded TargetPlatform 'LinuxServer'
Loaded TargetPlatform 'LinuxClient'
Loaded TargetPlatform 'LinuxArm64'
Loaded TargetPlatform 'LinuxArm64Server'
Loaded TargetPlatform 'LinuxArm64Client'
Loaded TargetPlatform 'Mac'
Loaded TargetPlatform 'MacEditor'
Loaded TargetPlatform 'MacServer'
Loaded TargetPlatform 'MacClient'
Loaded TargetPlatform 'TVOS'
Loaded TargetPlatform 'TVOSClient'
Loaded TargetPlatform 'Windows'
Loaded TargetPlatform 'WindowsEditor'
Loaded TargetPlatform 'WindowsServer'
Loaded TargetPlatform 'WindowsClient'
Building Assets For WindowsEditor
Unable to find shader format SF_METAL from hinted modules, loading all potential format modules to find it
Loaded format module MetalShaderFormat
  SF_METAL
  SF_METAL_MRT
  SF_METAL_TVOS
  SF_METAL_MRT_TVOS
  SF_METAL_SM5
  SF_METAL_SM6
  SF_METAL_SIM
  SF_METAL_MACES3_1
  SF_METAL_MRT_MAC
Loaded format module ShaderFormatD3D
  PCD3D_SM6
  PCD3D_SM5
  PCD3D_ES31
Loaded format module ShaderFormatOpenGL
  GLSL_150_ES31
  GLSL_ES3_1_ANDROID
Loaded format module ShaderFormatVectorVM
  VVM_1_0
Loaded format module VulkanShaderFormat
  SF_VULKAN_SM5
  SF_VULKAN_ES31_ANDROID
  SF_VULKAN_ES31
  SF_VULKAN_SM5_ANDROID
  SF_VULKAN_SM6
Ray tracing is enabled (dynamic). Reason: r.RayTracing=1 and r.RayTracing.EnableOnDemand=1.
Ray tracing shaders are enabled.
Memory: Max Cache Size: -1 MB
FDerivedDataBackendGraph: Pak pak cache file G:/Gamedev/RoughReality/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
Unable to find inner node Pak for hierarchy Hierarchy.
FDerivedDataBackendGraph: CompressedPak pak cache file G:/Gamedev/RoughReality/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
Unable to find inner node CompressedPak for hierarchy Hierarchy.
../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1559 MiB)
FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
Unable to find inner node EnterprisePak for hierarchy Hierarchy.
Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
InTree version at 'F:/Unreal/UE_5.5/Engine/Binaries/Win64/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
No current process using the data dir found, launching a new instance
Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 42464 --child-id Zen_42464_Startup'
Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
Local ZenServer AutoLaunch initialization completed in 0.872 seconds
ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.05 seconds.
C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.05ms. RandomReadSpeed=19.76MBs, RandomWriteSpeed=184.39MBs. Assigned SpeedClass 'Local'
Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
ZenShared: Disabled because Host is set to 'None'
Unable to find inner node ZenShared for hierarchy Hierarchy.
Shared: Disabled because no path is configured.
Unable to find inner node Shared for hierarchy Hierarchy.
Cloud: Disabled because Host is set to 'None'
Unable to find inner node Cloud for hierarchy Hierarchy.
Guid format shader working directory is 22 characters bigger than the processId version (G:/Gamedev/RoughReality/Intermediate/Shaders/WorkingDirectory/42464/).
Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/34E4CDF34C740802128E55A715781413/'.
Cannot use XGE Controller as Incredibuild is not installed on this machine.
Using Local Shader Compiler with 9 workers.
Compiling shader autogen file: G:/Gamedev/RoughReality/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
Autogen file is unchanged, skipping write.
Using FreeType 2.10.0
SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
FAssetRegistry took 0.0032 seconds to start up
EditorDomain is Disabled
AssetDataGatherer spent 0.000s loading caches G:/Gamedev/RoughReality/Intermediate/CachedAssetRegistry_*.bin.
FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
Texture Encode Speed: FinalIfAvailable (editor).
Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
Shared linear texture encoding: Disabled
Deviceprofile LinuxArm64Editor not found.
Deviceprofile LinuxArm64 not found.
Active device profile: [00000725D301E800][00000725C481A000 66] WindowsEditor
Metadata set : deviceprofile="WindowsEditor"
Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 18 to allow recursive sync load to finish
Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 19 to allow recursive sync load to finish
Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 20 to allow recursive sync load to finish
Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 21 to allow recursive sync load to finish
Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 23 to allow recursive sync load to finish
Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 24 to allow recursive sync load to finish
Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 26 to allow recursive sync load to finish
Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 27 to allow recursive sync load to finish
Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Merging postload groups of package /Engine/EngineMaterials/DefaultDiffuse with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 29 to allow recursive sync load to finish
Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 30 to allow recursive sync load to finish
Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 28 to allow recursive sync load to finish
Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 31 to allow recursive sync load to finish
Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 25 to allow recursive sync load to finish
Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 32 to allow recursive sync load to finish
Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 22 to allow recursive sync load to finish
Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 33 to allow recursive sync load to finish
Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
Using QuadricMeshReduction for automatic static mesh reduction
Using SkeletalMeshReduction for automatic skeletal mesh reduction
Using ProxyLODMeshReduction for automatic mesh merging
No distributed automatic mesh merging module available
No distributed automatic mesh merging module available
Branch 'PIEPreviewSettings' had been unloaded. Reloading on-demand took 1.17ms
Branch 'GameplayTagsList' had been unloaded. Reloading on-demand took 1.19ms
Branch 'TemplateDefs' had been unloaded. Reloading on-demand took 1.32ms
Branch 'TemplateCategories' had been unloaded. Reloading on-demand took 1.28ms
VirtualizationSystem name found in ini file: None
FNullVirtualizationSystem mounted, virtualization will be disabled
Starting LiveCoding
LiveCodingConsole Arguments: UnrealEditor Win64 Development
First instance in process group "UE_RoughReality_0xcfb68d21", spawning console
Border
BreadcrumbButton
Brushes.Title
Default
Icons.Save
Icons.Toolbar.Settings
ListView
SoftwareCursor_CardinalCross
SoftwareCursor_Grab
TableView.DarkRow
TableView.Row
TreeView
Waiting for server
FWorldPartitionClassDescRegistry::Initialize started...
FWorldPartitionClassDescRegistry::Initialize took 1.839 ms
Branch 'Mass' had been unloaded. Reloading on-demand took 1.49ms
XR: Instanced Stereo Rendering is Disabled
XR: MultiViewport is Disabled
XR: Mobile Multiview is Disabled
Successfully initialized, removing startup thread
Niagara Debugger Client Initialized | Session: 042E4D085C944C7D8000000000001C00 | Instance: 28F4827A423533D711BCB8B1F522D31C (DESKTOP-68ONPC8-42464).
Initializing TcpMessaging bridge
Work queue size set to 1024.
Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
Unicast socket bound to '0.0.0.0:62577'.
Added local interface '172.31.0.1' to multicast group '230.0.0.1:6666'
Added local interface '192.168.15.2' to multicast group '230.0.0.1:6666'
UGameplayTagsManager::InitializeManager -  0.000 s
MetaSound Page Target Initialized to 'Default'
Registering Engine Module Parameter Interfaces...
MetaSound Engine Initialized
Available graphics and compute adapters:
0: NVIDIA GeForce RTX 3070 (Compute, Graphics)
1: Microsoft Basic Render Driver (Compute, Graphics)
No NPU adapter found!
Branch 'TranslationPickerSettings' had been unloaded. Reloading on-demand took 1.52ms
FPlatformStackWalk::StackWalkAndDump -  0.061 s
=== Handled ensure: ===

Ensure condition failed: false  [File:D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp] [Line: 2015] 
Requested Gameplay Tag GameState.Menu was not found, tags must be loaded from config or registered as a native tag
Stack: 
[Callstack] 0x00007fff4c0a6a6f UnrealEditor-GameplayTags.dll!UnknownFunction []
[Callstack] 0x00007fff4c08b9d5 UnrealEditor-GameplayTags.dll!UnknownFunction []
[Callstack] 0x00007fff4c08b573 UnrealEditor-GameplayTags.dll!UnknownFunction []
[Callstack] 0x00000296901a1669 UnrealEditor-RoughReality.dll!ARoughRealityGameModeBase::ARoughRealityGameModeBase() [G:\Gamedev\RoughReality\Source\RoughReality\Core\RoughRealityGameModeBase.cpp:17]
[Callstack] 0x00007fff6a9b36fc UnrealEditor-CoreUObject.dll!UnknownFunction []
[Callstack] 0x00007fff6a9d0273 UnrealEditor-CoreUObject.dll!UnknownFunction []
[Callstack] 0x00007fff6a9b32e4 UnrealEditor-CoreUObject.dll!UnknownFunction []
[Callstack] 0x00007fff6a9d0273 UnrealEditor-CoreUObject.dll!UnknownFunction []
[Callstack] 0x00007fff6adfbff6 UnrealEditor-CoreUObject.dll!UnknownFunction []
[Callstack] 0x00007fff6adc9c1d UnrealEditor-CoreUObject.dll!UnknownFunction []
[Callstack] 0x00007fff6adad645 UnrealEditor-CoreUObject.dll!UnknownFunction []
[Callstack] 0x00007fff2567f5fd UnrealEditor-Core.dll!UnknownFunction []
[Callstack] 0x00007fff256a4593 UnrealEditor-Core.dll!UnknownFunction []
[Callstack] 0x00007fff6db6a234 UnrealEditor-Projects.dll!UnknownFunction []
[Callstack] 0x00007fff6db6a46b UnrealEditor-Projects.dll!UnknownFunction []
[Callstack] 0x00007ff76d14a699 UnrealEditor.exe!UnknownFunction []
[Callstack] 0x00007ff76d14dbbe UnrealEditor.exe!UnknownFunction []
[Callstack] 0x00007ff76d145566 UnrealEditor.exe!UnknownFunction []
[Callstack] 0x00007ff76d14589a UnrealEditor.exe!UnknownFunction []
[Callstack] 0x00007ff76d149114 UnrealEditor.exe!UnknownFunction []
[Callstack] 0x00007ff76d15bd04 UnrealEditor.exe!UnknownFunction []
[Callstack] 0x00007ff76d15f0ba UnrealEditor.exe!UnknownFunction []
[Callstack] 0x00007ff819a4e8d7 KERNEL32.DLL!UnknownFunction []
[Callstack] 0x00007ff81b23c5dc ntdll.dll!UnknownFunction []

               SubmitErrorReport -  0.000 s
                   SendNewReport -  0.922 s
            FDebug::EnsureFailed -  1.013 s
FPlatformStackWalk::StackWalkAndDump -  0.019 s
=== Handled ensure: ===

Ensure condition failed: false  [File:D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp] [Line: 2015] 
Requested Gameplay Tag GameState.Playing was not found, tags must be loaded from config or registered as a native tag
Stack: 
[Callstack] 0x00007fff4c0a6a6f UnrealEditor-GameplayTags.dll!UnknownFunction []
[Callstack] 0x00007fff4c08b9d5 UnrealEditor-GameplayTags.dll!UnknownFunction []
[Callstack] 0x00007fff4c08b573 UnrealEditor-GameplayTags.dll!UnknownFunction []
[Callstack] 0x00000296901a16a7 UnrealEditor-RoughReality.dll!ARoughRealityGameModeBase::ARoughRealityGameModeBase() [G:\Gamedev\RoughReality\Source\RoughReality\Core\RoughRealityGameModeBase.cpp:18]
[Callstack] 0x00007fff6a9b36fc UnrealEditor-CoreUObject.dll!UnknownFunction []
[Callstack] 0x00007fff6a9d0273 UnrealEditor-CoreUObject.dll!UnknownFunction []
[Callstack] 0x00007fff6a9b32e4 UnrealEditor-CoreUObject.dll!UnknownFunction []
[Callstack] 0x00007fff6a9d0273 UnrealEditor-CoreUObject.dll!UnknownFunction []
[Callstack] 0x00007fff6adfbff6 UnrealEditor-CoreUObject.dll!UnknownFunction []
[Callstack] 0x00007fff6adc9c1d UnrealEditor-CoreUObject.dll!UnknownFunction []
[Callstack] 0x00007fff6adad645 UnrealEditor-CoreUObject.dll!UnknownFunction []
[Callstack] 0x00007fff2567f5fd UnrealEditor-Core.dll!UnknownFunction []
[Callstack] 0x00007fff256a4593 UnrealEditor-Core.dll!UnknownFunction []
[Callstack] 0x00007fff6db6a234 UnrealEditor-Projects.dll!UnknownFunction []
[Callstack] 0x00007fff6db6a46b UnrealEditor-Projects.dll!UnknownFunction []
[Callstack] 0x00007ff76d14a699 UnrealEditor.exe!UnknownFunction []
[Callstack] 0x00007ff76d14dbbe UnrealEditor.exe!UnknownFunction []
[Callstack] 0x00007ff76d145566 UnrealEditor.exe!UnknownFunction []
[Callstack] 0x00007ff76d14589a UnrealEditor.exe!UnknownFunction []
[Callstack] 0x00007ff76d149114 UnrealEditor.exe!UnknownFunction []
[Callstack] 0x00007ff76d15bd04 UnrealEditor.exe!UnknownFunction []
[Callstack] 0x00007ff76d15f0ba UnrealEditor.exe!UnknownFunction []
[Callstack] 0x00007ff819a4e8d7 KERNEL32.DLL!UnknownFunction []
[Callstack] 0x00007ff81b23c5dc ntdll.dll!UnknownFunction []

               SubmitErrorReport -  0.000 s
Completed SDK detection: ExitCode = 0
                   SendNewReport -  3.273 s
            FDebug::EnsureFailed -  3.325 s
FPlatformStackWalk::StackWalkAndDump -  0.020 s
=== Handled ensure: ===

Ensure condition failed: false  [File:D:\build\++UE5\Sync\Engine\Source\Runtime\GameplayTags\Private\GameplayTagsManager.cpp] [Line: 2015] 
Requested Gameplay Tag GameState.Paused was not found, tags must be loaded from config or registered as a native tag
Stack: 
[Callstack] 0x00007fff4c0a6a6f UnrealEditor-GameplayTags.dll!UnknownFunction []
[Callstack] 0x00007fff4c08b9d5 UnrealEditor-GameplayTags.dll!UnknownFunction []
[Callstack] 0x00007fff4c08b573 UnrealEditor-GameplayTags.dll!UnknownFunction []
[Callstack] 0x00000296901a16e5 UnrealEditor-RoughReality.dll!ARoughRealityGameModeBase::ARoughRealityGameModeBase() [G:\Gamedev\RoughReality\Source\RoughReality\Core\RoughRealityGameModeBase.cpp:19]
[Callstack] 0x00007fff6a9b36fc UnrealEditor-CoreUObject.dll!UnknownFunction []
[Callstack] 0x00007fff6a9d0273 UnrealEditor-CoreUObject.dll!UnknownFunction []
[Callstack] 0x00007fff6a9b32e4 UnrealEditor-CoreUObject.dll!UnknownFunction []
[Callstack] 0x00007fff6a9d0273 UnrealEditor-CoreUObject.dll!UnknownFunction []
[Callstack] 0x00007fff6adfbff6 UnrealEditor-CoreUObject.dll!UnknownFunction []
[Callstack] 0x00007fff6adc9c1d UnrealEditor-CoreUObject.dll!UnknownFunction []
[Callstack] 0x00007fff6adad645 UnrealEditor-CoreUObject.dll!UnknownFunction []
[Callstack] 0x00007fff2567f5fd UnrealEditor-Core.dll!UnknownFunction []
[Callstack] 0x00007fff256a4593 UnrealEditor-Core.dll!UnknownFunction []
[Callstack] 0x00007fff6db6a234 UnrealEditor-Projects.dll!UnknownFunction []
[Callstack] 0x00007fff6db6a46b UnrealEditor-Projects.dll!UnknownFunction []
[Callstack] 0x00007ff76d14a699 UnrealEditor.exe!UnknownFunction []
[Callstack] 0x00007ff76d14dbbe UnrealEditor.exe!UnknownFunction []
[Callstack] 0x00007ff76d145566 UnrealEditor.exe!UnknownFunction []
[Callstack] 0x00007ff76d14589a UnrealEditor.exe!UnknownFunction []
[Callstack] 0x00007ff76d149114 UnrealEditor.exe!UnknownFunction []
[Callstack] 0x00007ff76d15bd04 UnrealEditor.exe!UnknownFunction []
[Callstack] 0x00007ff76d15f0ba UnrealEditor.exe!UnknownFunction []
[Callstack] 0x00007ff819a4e8d7 KERNEL32.DLL!UnknownFunction []
[Callstack] 0x00007ff81b23c5dc ntdll.dll!UnknownFunction []

               SubmitErrorReport -  0.000 s
