{"Version": "1.2", "Data": {"Source": "g:\\gamedev\\roughreality\\source\\roughreality\\roughrealitycharacter.cpp", "ProvidedModule": "", "PCH": "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\x64\\roughrealityeditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.cpp20.h.pch", "Includes": ["g:\\gamedev\\roughreality\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\roughreality\\definitions.roughreality.h", "g:\\gamedev\\roughreality\\source\\roughreality\\roughrealitycharacter.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\character.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\charactermovementreplication.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\net\\core\\public\\net\\core\\nettoken\\nettokenexportcontext.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\experimental\\iris\\core\\public\\iris\\serialization\\irisobjectreferencepackagemap.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\iriscore\\uht\\irisobjectreferencepackagemap.generated.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\charactermovementreplication.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\rootmotionsource.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\rootmotionsource.generated.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\character.generated.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\roughrealitycharacter.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\camera\\cameracomponent.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\cameracomponent.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\components\\capsulecomponent.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\components\\shapecomponent.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\shapecomponent.generated.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\capsulecomponent.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\charactermovementcomponent.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationavoidancetypes.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationavoidancetypes.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\public\\ai\\rvoavoidanceinterface.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\rvoavoidanceinterface.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\pawnmovementcomponent.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\navmovementcomponent.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\navmovementinterface.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navmovementinterface.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\pathfollowingagentinterface.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\pathfollowingagentinterface.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\movementcomponent.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\movementcomponent.generated.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navmovementcomponent.generated.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\pawnmovementcomponent.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\interfaces\\networkpredictioninterface.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\networkpredictioninterface.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\public\\charactermovementcomponentasync.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\charactermovementcomponentasync.generated.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\charactermovementcomponent.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\springarmcomponent.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\springarmcomponent.generated.h", "f:\\unreal\\ue_5.5\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\enhancedinputcomponent.h", "f:\\unreal\\ue_5.5\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\inputaction.h", "f:\\unreal\\ue_5.5\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\inputmodifiers.h", "f:\\unreal\\ue_5.5\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\inputactionvalue.h", "f:\\unreal\\ue_5.5\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\inputactionvalue.generated.h", "f:\\unreal\\ue_5.5\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\inputmodifiers.generated.h", "f:\\unreal\\ue_5.5\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\inputtriggers.h", "f:\\unreal\\ue_5.5\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\inputtriggers.generated.h", "f:\\unreal\\ue_5.5\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\inputaction.generated.h", "f:\\unreal\\ue_5.5\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\enhancedinputcomponent.generated.h", "f:\\unreal\\ue_5.5\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\enhancedinputsubsystems.h", "f:\\unreal\\ue_5.5\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\enhancedinputsubsysteminterface.h", "f:\\unreal\\ue_5.5\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\enhancedplayerinput.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\playerinput.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\public\\gesturerecognizer.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\public\\keystate.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\playerinput.generated.h", "f:\\unreal\\ue_5.5\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\enhancedplayerinput.generated.h", "f:\\unreal\\ue_5.5\\engine\\plugins\\enhancedinput\\source\\enhancedinput\\public\\playermappablekeyslot.h", "f:\\unreal\\ue_5.5\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\playermappablekeyslot.generated.h", "f:\\unreal\\ue_5.5\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\enhancedinputsubsysteminterface.generated.h", "f:\\unreal\\ue_5.5\\engine\\plugins\\enhancedinput\\intermediate\\build\\win64\\unrealeditor\\inc\\enhancedinput\\uht\\enhancedinputsubsystems.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}