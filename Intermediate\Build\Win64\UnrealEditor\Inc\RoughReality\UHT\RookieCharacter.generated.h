// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Characters/RookieCharacter.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
 
class AWeaponBase;
#ifdef ROUGHREALITY_RookieCharacter_generated_h
#error "RookieCharacter.generated.h already included, missing '#pragma once' in RookieCharacter.h"
#endif
#define ROUGHREALITY_RookieCharacter_generated_h

#define FID_RoughReality_Source_RoughReality_Characters_RookieCharacter_h_21_DELEGATE \
ROUGHREALITY_API void FOnHealthChanged_DelegateWrapper(const FMulticastScriptDelegate& OnHealthChanged, float NewHealthPercent);


#define FID_RoughReality_Source_RoughReality_Characters_RookieCharacter_h_22_DELEGATE \
ROUGHREALITY_API void FOnBulletTimeChanged_DelegateWrapper(const FMulticastScriptDelegate& OnBulletTimeChanged, float NewBulletTimePercent);


#define FID_RoughReality_Source_RoughReality_Characters_RookieCharacter_h_23_DELEGATE \
ROUGHREALITY_API void FOnRewindChargesChanged_DelegateWrapper(const FMulticastScriptDelegate& OnRewindChargesChanged, int32 NewCharges);


#define FID_RoughReality_Source_RoughReality_Characters_RookieCharacter_h_24_DELEGATE \
ROUGHREALITY_API void FOnWeaponChanged_DelegateWrapper(const FMulticastScriptDelegate& OnWeaponChanged, AWeaponBase* NewWeapon);


#define FID_RoughReality_Source_RoughReality_Characters_RookieCharacter_h_29_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTimeSnapshot_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FTimeSnapshot>();

#define FID_RoughReality_Source_RoughReality_Characters_RookieCharacter_h_63_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execDashTimelineFinished); \
	DECLARE_FUNCTION(execDashTimelineUpdate); \
	DECLARE_FUNCTION(execRecordPlayerAction); \
	DECLARE_FUNCTION(execSetLODLevel); \
	DECLARE_FUNCTION(execUpdatePerformanceSettings); \
	DECLARE_FUNCTION(execUnregisterFromGameSystems); \
	DECLARE_FUNCTION(execRegisterWithGameSystems); \
	DECLARE_FUNCTION(execSwitchToPreviousWeapon); \
	DECLARE_FUNCTION(execSwitchToNextWeapon); \
	DECLARE_FUNCTION(execAddWeaponToInventory); \
	DECLARE_FUNCTION(execEquipWeapon); \
	DECLARE_FUNCTION(execCanPerformDash); \
	DECLARE_FUNCTION(execPerformDashMovement); \
	DECLARE_FUNCTION(execAddRewindCharge); \
	DECLARE_FUNCTION(execCanRewind); \
	DECLARE_FUNCTION(execPerformTimeRewind); \
	DECLARE_FUNCTION(execGetBulletTimePercent); \
	DECLARE_FUNCTION(execCanUseBulletTime); \
	DECLARE_FUNCTION(execDeactivateBulletTime); \
	DECLARE_FUNCTION(execActivateBulletTime); \
	DECLARE_FUNCTION(execGetHealthPercent); \
	DECLARE_FUNCTION(execIsDead); \
	DECLARE_FUNCTION(execHeal); \
	DECLARE_FUNCTION(execTakeDamageSimple);


#define FID_RoughReality_Source_RoughReality_Characters_RookieCharacter_h_63_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesARookieCharacter(); \
	friend struct Z_Construct_UClass_ARookieCharacter_Statics; \
public: \
	DECLARE_CLASS(ARookieCharacter, ACharacter, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/RoughReality"), NO_API) \
	DECLARE_SERIALIZER(ARookieCharacter)


#define FID_RoughReality_Source_RoughReality_Characters_RookieCharacter_h_63_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	ARookieCharacter(ARookieCharacter&&); \
	ARookieCharacter(const ARookieCharacter&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ARookieCharacter); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ARookieCharacter); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ARookieCharacter) \
	NO_API virtual ~ARookieCharacter();


#define FID_RoughReality_Source_RoughReality_Characters_RookieCharacter_h_60_PROLOG
#define FID_RoughReality_Source_RoughReality_Characters_RookieCharacter_h_63_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_RoughReality_Source_RoughReality_Characters_RookieCharacter_h_63_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_Characters_RookieCharacter_h_63_INCLASS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_Characters_RookieCharacter_h_63_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ROUGHREALITY_API UClass* StaticClass<class ARookieCharacter>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_RoughReality_Source_RoughReality_Characters_RookieCharacter_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
