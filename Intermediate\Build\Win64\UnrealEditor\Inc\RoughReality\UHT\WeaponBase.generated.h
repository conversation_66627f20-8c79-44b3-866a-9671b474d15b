// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Weapons/WeaponBase.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class ARookieCharacter;
class AWeaponBase;
struct FGameplayTagContainer;
#ifdef ROUGHREALITY_WeaponBase_generated_h
#error "WeaponBase.generated.h already included, missing '#pragma once' in WeaponBase.h"
#endif
#define ROUGHREALITY_WeaponBase_generated_h

#define FID_RoughReality_Source_RoughReality_Weapons_WeaponBase_h_19_DELEGATE \
ROUGHREALITY_API void FOnAmmoChanged_DelegateWrapper(const FMulticastScriptDelegate& OnAmmoChanged, int32 NewAmmoCount);


#define FID_RoughReality_Source_RoughReality_Weapons_WeaponBase_h_20_DELEGATE \
ROUGHREALITY_API void FOnWeaponFired_DelegateWrapper(const FMulticastScriptDelegate& OnWeaponFired, AWeaponBase* Weapon);


#define FID_RoughReality_Source_RoughReality_Weapons_WeaponBase_h_21_DELEGATE \
ROUGHREALITY_API void FOnWeaponReloaded_DelegateWrapper(const FMulticastScriptDelegate& OnWeaponReloaded, AWeaponBase* Weapon);


#define FID_RoughReality_Source_RoughReality_Weapons_WeaponBase_h_47_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FWeaponStats_Statics; \
	ROUGHREALITY_API static class UScriptStruct* StaticStruct();


template<> ROUGHREALITY_API UScriptStruct* StaticStruct<struct FWeaponStats>();

#define FID_RoughReality_Source_RoughReality_Weapons_WeaponBase_h_97_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCreateBulletTracer); \
	DECLARE_FUNCTION(execSpawnShellEjection); \
	DECLARE_FUNCTION(execPlayRandomFireSound); \
	DECLARE_FUNCTION(execPlayRandomMuzzleFlash); \
	DECLARE_FUNCTION(execGetCurrentLODLevel); \
	DECLARE_FUNCTION(execSetLODLevel); \
	DECLARE_FUNCTION(execUpdateLOD); \
	DECLARE_FUNCTION(execGetWeaponTags); \
	DECLARE_FUNCTION(execGetWeaponName); \
	DECLARE_FUNCTION(execGetMagazinePercent); \
	DECLARE_FUNCTION(execGetAmmoPercent); \
	DECLARE_FUNCTION(execNeedsReload); \
	DECLARE_FUNCTION(execIsEmpty); \
	DECLARE_FUNCTION(execCanReload); \
	DECLARE_FUNCTION(execCanFire); \
	DECLARE_FUNCTION(execOnUnequipped); \
	DECLARE_FUNCTION(execOnEquipped); \
	DECLARE_FUNCTION(execReload); \
	DECLARE_FUNCTION(execStopFire); \
	DECLARE_FUNCTION(execStartFire);


#define FID_RoughReality_Source_RoughReality_Weapons_WeaponBase_h_97_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAWeaponBase(); \
	friend struct Z_Construct_UClass_AWeaponBase_Statics; \
public: \
	DECLARE_CLASS(AWeaponBase, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/RoughReality"), NO_API) \
	DECLARE_SERIALIZER(AWeaponBase)


#define FID_RoughReality_Source_RoughReality_Weapons_WeaponBase_h_97_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	AWeaponBase(AWeaponBase&&); \
	AWeaponBase(const AWeaponBase&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AWeaponBase); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AWeaponBase); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AWeaponBase) \
	NO_API virtual ~AWeaponBase();


#define FID_RoughReality_Source_RoughReality_Weapons_WeaponBase_h_94_PROLOG
#define FID_RoughReality_Source_RoughReality_Weapons_WeaponBase_h_97_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_RoughReality_Source_RoughReality_Weapons_WeaponBase_h_97_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_Weapons_WeaponBase_h_97_INCLASS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_Weapons_WeaponBase_h_97_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ROUGHREALITY_API UClass* StaticClass<class AWeaponBase>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_RoughReality_Source_RoughReality_Weapons_WeaponBase_h


#define FOREACH_ENUM_EWEAPONSTATE(op) \
	op(EWeaponState::Idle) \
	op(EWeaponState::Firing) \
	op(EWeaponState::Reloading) \
	op(EWeaponState::Equipping) \
	op(EWeaponState::Unequipping) 

enum class EWeaponState : uint8;
template<> struct TIsUEnumClass<EWeaponState> { enum { Value = true }; };
template<> ROUGHREALITY_API UEnum* StaticEnum<EWeaponState>();

#define FOREACH_ENUM_EWEAPONTYPE(op) \
	op(EWeaponType::Pistol) \
	op(EWeaponType::AssaultRifle) \
	op(EWeaponType::Shotgun) \
	op(EWeaponType::RocketLauncher) \
	op(EWeaponType::Chaingun) \
	op(EWeaponType::Sniper) 

enum class EWeaponType : uint8;
template<> struct TIsUEnumClass<EWeaponType> { enum { Value = true }; };
template<> ROUGHREALITY_API UEnum* StaticEnum<EWeaponType>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
