// Copyright Epic Games, Inc. All Rights Reserved.

#include "ObjectPoolManager.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Kismet/GameplayStatics.h"

AObjectPoolManager* AObjectPoolManager::Instance = nullptr;

AObjectPoolManager::AObjectPoolManager()
{
	PrimaryActorTick.bCanEverTick = true;
	PrimaryActorTick.TickInterval = 1.0f; // Tick once per second for optimization

	TotalObjectsSpawned = 0;
	TotalObjectsReturned = 0;
	TotalPoolHits = 0;
	TotalPoolMisses = 0;
	LastOptimizationTime = 0.0f;
}

void AObjectPoolManager::BeginPlay()
{
	Super::BeginPlay();
	
	Instance = this;
	InitializeAllPools();
	
	UE_LOG(LogTemp, Log, TEXT("Object Pool Manager initialized with %d pool configurations"), PoolConfigurations.Num());
}

void AObjectPoolManager::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	ProcessAutoReturns(DeltaTime);

	// Periodic optimization
	float CurrentTime = GetWorld()->GetTimeSeconds();
	if (CurrentTime - LastOptimizationTime > OptimizationInterval)
	{
		OptimizeAllPools();
		LastOptimizationTime = CurrentTime;
	}
}

void AObjectPoolManager::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	ClearAllPools();
	Instance = nullptr;
	Super::EndPlay(EndPlayReason);
}

void AObjectPoolManager::InitializePool(FName PoolName, const FObjectPoolConfiguration& Config)
{
	if (PoolName == NAME_None)
	{
		UE_LOG(LogTemp, Error, TEXT("Cannot initialize pool with empty name"));
		return;
	}

	// Cache configuration
	PoolConfigCache.Add(PoolName, Config);

	// Clear existing pool if it exists
	if (ObjectPools.Contains(PoolName))
	{
		ClearPool(PoolName);
	}

	// Create new pool
	TArray<FPooledObjectInfo>& Pool = ObjectPools.Add(PoolName);

	// Load actor class
	UClass* ActorClass = Config.ActorClass.LoadSynchronous();
	if (!ActorClass)
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to load actor class for pool %s"), *PoolName.ToString());
		return;
	}

	// Prewarm pool if requested
	if (Config.bPrewarmPool)
	{
		for (int32 i = 0; i < Config.InitialPoolSize; i++)
		{
			AActor* NewActor = CreatePooledObject(PoolName, ActorClass);
			if (NewActor)
			{
				Pool.Add(FPooledObjectInfo(NewActor, PoolName));
			}
		}
	}

	UE_LOG(LogTemp, Log, TEXT("Initialized pool '%s' with %d objects"), *PoolName.ToString(), Pool.Num());
}

void AObjectPoolManager::InitializeAllPools()
{
	for (const FObjectPoolConfiguration& Config : PoolConfigurations)
	{
		InitializePool(Config.PoolName, Config);
	}
}

void AObjectPoolManager::ClearPool(FName PoolName)
{
	TArray<FPooledObjectInfo>* Pool = ObjectPools.Find(PoolName);
	if (!Pool)
	{
		return;
	}

	// Destroy all objects in pool
	for (FPooledObjectInfo& ObjectInfo : *Pool)
	{
		if (IsValid(ObjectInfo.Actor))
		{
			ActiveObjects.Remove(ObjectInfo.Actor);
			ObjectInfo.Actor->Destroy();
		}
	}

	Pool->Empty();
	UE_LOG(LogTemp, Log, TEXT("Cleared pool '%s'"), *PoolName.ToString());
}

void AObjectPoolManager::ClearAllPools()
{
	for (auto& PoolPair : ObjectPools)
	{
		ClearPool(PoolPair.Key);
	}
	ObjectPools.Empty();
	ActiveObjects.Empty();
	DelayedReturnTimers.Empty();
}

AActor* AObjectPoolManager::SpawnPooledObject(FName PoolName, const FVector& Location, const FRotator& Rotation, AActor* OwnerActor)
{
	TArray<FPooledObjectInfo>* Pool = ObjectPools.Find(PoolName);
	if (!Pool)
	{
		UE_LOG(LogTemp, Error, TEXT("Pool '%s' not found"), *PoolName.ToString());
		TotalPoolMisses++;
		return nullptr;
	}

	FObjectPoolConfiguration* Config = PoolConfigCache.Find(PoolName);
	if (!Config)
	{
		UE_LOG(LogTemp, Error, TEXT("Configuration for pool '%s' not found"), *PoolName.ToString());
		return nullptr;
	}

	// Find available object in pool
	for (FPooledObjectInfo& ObjectInfo : *Pool)
	{
		if (!ObjectInfo.bInUse && IsValid(ObjectInfo.Actor))
		{
			// Reuse existing object
			ObjectInfo.bInUse = true;
			ObjectInfo.SpawnTime = GetWorld()->GetTimeSeconds();
			
			AActor* Actor = ObjectInfo.Actor;
			Actor->SetActorLocationAndRotation(Location, Rotation);
			Actor->SetActorHiddenInGame(false);
			Actor->SetActorEnableCollision(true);
			Actor->SetActorTickEnabled(true);
			
			if (OwnerActor)
			{
				Actor->SetOwner(OwnerActor);
			}

			ActiveObjects.Add(Actor, PoolName);
			TotalObjectsSpawned++;
			TotalPoolHits++;

			OnObjectSpawned.Broadcast(Actor, PoolName);
			return Actor;
		}
	}

	// No available objects, try to grow pool
	if (Config->bCanGrowPool && Pool->Num() < Config->MaxPoolSize)
	{
		UClass* ActorClass = Config->ActorClass.LoadSynchronous();
		if (ActorClass)
		{
			AActor* NewActor = CreatePooledObject(PoolName, ActorClass);
			if (NewActor)
			{
				FPooledObjectInfo NewObjectInfo(NewActor, PoolName);
				NewObjectInfo.bInUse = true;
				NewObjectInfo.SpawnTime = GetWorld()->GetTimeSeconds();
				
				Pool->Add(NewObjectInfo);
				
				NewActor->SetActorLocationAndRotation(Location, Rotation);
				if (OwnerActor)
				{
					NewActor->SetOwner(OwnerActor);
				}

				ActiveObjects.Add(NewActor, PoolName);
				TotalObjectsSpawned++;
				TotalPoolMisses++;

				OnObjectSpawned.Broadcast(NewActor, PoolName);
				return NewActor;
			}
		}
	}

	UE_LOG(LogTemp, Warning, TEXT("Failed to spawn object from pool '%s' - pool exhausted"), *PoolName.ToString());
	TotalPoolMisses++;
	return nullptr;
}

void AObjectPoolManager::ReturnPooledObject(AActor* Object)
{
	if (!IsValid(Object))
	{
		return;
	}

	FName* PoolName = ActiveObjects.Find(Object);
	if (!PoolName)
	{
		UE_LOG(LogTemp, Warning, TEXT("Attempted to return object that wasn't spawned from pool"));
		return;
	}

	ReturnObjectToPool(Object, *PoolName);
}

void AObjectPoolManager::ReturnPooledObjectDelayed(AActor* Object, float Delay)
{
	if (!IsValid(Object) || Delay <= 0.0f)
	{
		ReturnPooledObject(Object);
		return;
	}

	FTimerHandle& TimerHandle = DelayedReturnTimers.FindOrAdd(Object);
	GetWorldTimerManager().SetTimer(TimerHandle, FTimerDelegate::CreateUFunction(this, FName("OnDelayedReturn"), Object), Delay, false);
}

int32 AObjectPoolManager::GetPoolSize(FName PoolName) const
{
	const TArray<FPooledObjectInfo>* Pool = ObjectPools.Find(PoolName);
	return Pool ? Pool->Num() : 0;
}

int32 AObjectPoolManager::GetAvailableObjects(FName PoolName) const
{
	const TArray<FPooledObjectInfo>* Pool = ObjectPools.Find(PoolName);
	if (!Pool)
	{
		return 0;
	}

	int32 Available = 0;
	for (const FPooledObjectInfo& ObjectInfo : *Pool)
	{
		if (!ObjectInfo.bInUse && IsValid(ObjectInfo.Actor))
		{
			Available++;
		}
	}
	return Available;
}

int32 AObjectPoolManager::GetObjectsInUse(FName PoolName) const
{
	const TArray<FPooledObjectInfo>* Pool = ObjectPools.Find(PoolName);
	if (!Pool)
	{
		return 0;
	}

	int32 InUse = 0;
	for (const FPooledObjectInfo& ObjectInfo : *Pool)
	{
		if (ObjectInfo.bInUse)
		{
			InUse++;
		}
	}
	return InUse;
}

bool AObjectPoolManager::IsPoolInitialized(FName PoolName) const
{
	return ObjectPools.Contains(PoolName);
}

float AObjectPoolManager::GetPoolHitRate() const
{
	int32 TotalRequests = TotalPoolHits + TotalPoolMisses;
	return TotalRequests > 0 ? (float)TotalPoolHits / TotalRequests : 0.0f;
}

void AObjectPoolManager::PrewarmPool(FName PoolName, int32 Count)
{
	TArray<FPooledObjectInfo>* Pool = ObjectPools.Find(PoolName);
	FObjectPoolConfiguration* Config = PoolConfigCache.Find(PoolName);
	
	if (!Pool || !Config)
	{
		return;
	}

	UClass* ActorClass = Config->ActorClass.LoadSynchronous();
	if (!ActorClass)
	{
		return;
	}

	int32 CurrentSize = Pool->Num();
	int32 TargetSize = FMath::Min(CurrentSize + Count, Config->MaxPoolSize);

	for (int32 i = CurrentSize; i < TargetSize; i++)
	{
		AActor* NewActor = CreatePooledObject(PoolName, ActorClass);
		if (NewActor)
		{
			Pool->Add(FPooledObjectInfo(NewActor, PoolName));
		}
	}

	UE_LOG(LogTemp, Log, TEXT("Prewarmed pool '%s' to %d objects"), *PoolName.ToString(), Pool->Num());
}

void AObjectPoolManager::TrimPool(FName PoolName, int32 TargetSize)
{
	TArray<FPooledObjectInfo>* Pool = ObjectPools.Find(PoolName);
	if (!Pool || TargetSize >= Pool->Num())
	{
		return;
	}

	// Remove unused objects from the end
	for (int32 i = Pool->Num() - 1; i >= TargetSize; i--)
	{
		FPooledObjectInfo& ObjectInfo = (*Pool)[i];
		if (!ObjectInfo.bInUse && IsValid(ObjectInfo.Actor))
		{
			ObjectInfo.Actor->Destroy();
			Pool->RemoveAt(i);
		}
	}

	UE_LOG(LogTemp, Log, TEXT("Trimmed pool '%s' to %d objects"), *PoolName.ToString(), Pool->Num());
}

void AObjectPoolManager::OptimizeAllPools()
{
	for (auto& PoolPair : ObjectPools)
	{
		FName PoolName = PoolPair.Key;
		TArray<FPooledObjectInfo>& Pool = PoolPair.Value;

		// Remove invalid objects
		for (int32 i = Pool.Num() - 1; i >= 0; i--)
		{
			if (!IsValid(Pool[i].Actor))
			{
				Pool.RemoveAt(i);
			}
		}

		// Trim oversized pools
		FObjectPoolConfiguration* Config = PoolConfigCache.Find(PoolName);
		if (Config && Pool.Num() > Config->MaxPoolSize)
		{
			TrimPool(PoolName, Config->MaxPoolSize);
		}
	}

	UE_LOG(LogTemp, VeryVerbose, TEXT("Optimized all object pools"));
}

AObjectPoolManager* AObjectPoolManager::GetObjectPoolManager(const UObject* WorldContext)
{
	if (Instance && IsValid(Instance))
	{
		return Instance;
	}

	// Try to find existing instance
	UWorld* World = GEngine->GetWorldFromContextObject(WorldContext, EGetWorldErrorMode::LogAndReturnNull);
	if (World)
	{
		for (TActorIterator<AObjectPoolManager> ActorItr(World); ActorItr; ++ActorItr)
		{
			Instance = *ActorItr;
			return Instance;
		}
	}

	return nullptr;
}

AActor* AObjectPoolManager::CreatePooledObject(FName PoolName, UClass* ActorClass)
{
	FActorSpawnParameters SpawnParams;
	SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;

	AActor* NewActor = GetWorld()->SpawnActor<AActor>(ActorClass, FVector::ZeroVector, FRotator::ZeroRotator, SpawnParams);
	if (NewActor)
	{
		// Initialize as pooled object
		NewActor->SetActorHiddenInGame(true);
		NewActor->SetActorEnableCollision(false);
		NewActor->SetActorTickEnabled(false);
	}

	return NewActor;
}

void AObjectPoolManager::ReturnObjectToPool(AActor* Object, FName PoolName)
{
	TArray<FPooledObjectInfo>* Pool = ObjectPools.Find(PoolName);
	if (!Pool)
	{
		return;
	}

	// Find object in pool
	for (FPooledObjectInfo& ObjectInfo : *Pool)
	{
		if (ObjectInfo.Actor == Object)
		{
			ObjectInfo.bInUse = false;
			ObjectInfo.SpawnTime = 0.0f;

			// Reset object state
			Object->SetActorHiddenInGame(true);
			Object->SetActorEnableCollision(false);
			Object->SetActorTickEnabled(false);
			Object->SetOwner(nullptr);

			ActiveObjects.Remove(Object);
			TotalObjectsReturned++;

			OnObjectReturned.Broadcast(Object, PoolName);
			return;
		}
	}
}

FPooledObjectInfo* AObjectPoolManager::FindPooledObjectInfo(AActor* Object)
{
	FName* PoolName = ActiveObjects.Find(Object);
	if (!PoolName)
	{
		return nullptr;
	}

	TArray<FPooledObjectInfo>* Pool = ObjectPools.Find(*PoolName);
	if (!Pool)
	{
		return nullptr;
	}

	for (FPooledObjectInfo& ObjectInfo : *Pool)
	{
		if (ObjectInfo.Actor == Object)
		{
			return &ObjectInfo;
		}
	}

	return nullptr;
}

void AObjectPoolManager::ProcessAutoReturns(float DeltaTime)
{
	float CurrentTime = GetWorld()->GetTimeSeconds();

	for (auto& PoolPair : ObjectPools)
	{
		FName PoolName = PoolPair.Key;
		TArray<FPooledObjectInfo>& Pool = PoolPair.Value;

		FObjectPoolConfiguration* Config = PoolConfigCache.Find(PoolName);
		if (!Config || Config->AutoReturnTime <= 0.0f)
		{
			continue;
		}

		for (FPooledObjectInfo& ObjectInfo : Pool)
		{
			if (ObjectInfo.bInUse && ObjectInfo.SpawnTime > 0.0f)
			{
				float TimeAlive = CurrentTime - ObjectInfo.SpawnTime;
				if (TimeAlive >= Config->AutoReturnTime)
				{
					ReturnObjectToPool(ObjectInfo.Actor, PoolName);
				}
			}
		}
	}
}

void AObjectPoolManager::OnDelayedReturn(AActor* Object)
{
	DelayedReturnTimers.Remove(Object);
	ReturnPooledObject(Object);
}
