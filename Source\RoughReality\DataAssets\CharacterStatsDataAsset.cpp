// Copyright Epic Games, Inc. All Rights Reserved.

#include "CharacterStatsDataAsset.h"

UCharacterStatsDataAsset::UCharacterStatsDataAsset()
{
	// Set default values
	AssetID = FName("DefaultCharacterStats");
	DisplayName = FText::FromString("Default Character Stats");
	Description = FText::FromString("Default configuration for character statistics and abilities");

	// Health defaults
	MaxHealth = 100.0f;
	HealthRegenRate = 0.0f;
	HealthRegenDelay = 5.0f;

	// Movement defaults
	MaxWalkSpeed = 500.0f;
	MaxRunSpeed = 800.0f;
	JumpVelocity = 700.0f;
	AirControl = 0.35f;

	// Bullet Time defaults
	MaxBulletTimeEnergy = 100.0f;
	BulletTimeDrainRate = 25.0f;
	BulletTimeRechargeRate = 15.0f;
	BulletTimeScale = 0.3f;

	// Time Rewind defaults
	MaxRewindCharges = 3;
	SnapshotInterval = 0.2f;
	RewindDuration = 3.0f;

	// Dash defaults
	DashDistance = 1000.0f;
	DashDuration = 0.3f;
	DashCooldown = 2.0f;
	bDashThroughEnemies = false;

	// Combat defaults
	DamageMultiplier = 1.0f;
	DefenseMultiplier = 1.0f;
	CriticalHitChance = 0.1f;
	CriticalHitMultiplier = 2.0f;

	// Interaction defaults
	InteractionRange = 200.0f;
	InteractionTime = 1.0f;

	// Upgrade scaling defaults
	HealthUpgradeMultiplier = 0.2f;
	DashUpgradeMultiplier = 0.15f;
	BulletTimeUpgradeMultiplier = 0.25f;
	RewindChargesPerUpgrade = 1;
}

bool UCharacterStatsDataAsset::ValidateAsset() const
{
	bool bIsValid = Super::ValidateAsset();

	// Validate health values
	if (MaxHealth <= 0.0f)
	{
		UE_LOG(LogTemp, Error, TEXT("CharacterStatsDataAsset %s: MaxHealth must be greater than 0"), *GetName());
		bIsValid = false;
	}

	// Validate movement values
	if (MaxWalkSpeed <= 0.0f)
	{
		UE_LOG(LogTemp, Error, TEXT("CharacterStatsDataAsset %s: MaxWalkSpeed must be greater than 0"), *GetName());
		bIsValid = false;
	}

	if (JumpVelocity <= 0.0f)
	{
		UE_LOG(LogTemp, Error, TEXT("CharacterStatsDataAsset %s: JumpVelocity must be greater than 0"), *GetName());
		bIsValid = false;
	}

	// Validate bullet time values
	if (MaxBulletTimeEnergy <= 0.0f)
	{
		UE_LOG(LogTemp, Error, TEXT("CharacterStatsDataAsset %s: MaxBulletTimeEnergy must be greater than 0"), *GetName());
		bIsValid = false;
	}

	if (BulletTimeScale <= 0.0f || BulletTimeScale >= 1.0f)
	{
		UE_LOG(LogTemp, Error, TEXT("CharacterStatsDataAsset %s: BulletTimeScale must be between 0 and 1"), *GetName());
		bIsValid = false;
	}

	// Validate time rewind values
	if (MaxRewindCharges <= 0)
	{
		UE_LOG(LogTemp, Error, TEXT("CharacterStatsDataAsset %s: MaxRewindCharges must be greater than 0"), *GetName());
		bIsValid = false;
	}

	if (SnapshotInterval <= 0.0f)
	{
		UE_LOG(LogTemp, Error, TEXT("CharacterStatsDataAsset %s: SnapshotInterval must be greater than 0"), *GetName());
		bIsValid = false;
	}

	if (RewindDuration <= 0.0f)
	{
		UE_LOG(LogTemp, Error, TEXT("CharacterStatsDataAsset %s: RewindDuration must be greater than 0"), *GetName());
		bIsValid = false;
	}

	// Validate dash values
	if (DashDistance <= 0.0f)
	{
		UE_LOG(LogTemp, Error, TEXT("CharacterStatsDataAsset %s: DashDistance must be greater than 0"), *GetName());
		bIsValid = false;
	}

	if (DashDuration <= 0.0f)
	{
		UE_LOG(LogTemp, Error, TEXT("CharacterStatsDataAsset %s: DashDuration must be greater than 0"), *GetName());
		bIsValid = false;
	}

	if (DashCooldown < 0.0f)
	{
		UE_LOG(LogTemp, Error, TEXT("CharacterStatsDataAsset %s: DashCooldown cannot be negative"), *GetName());
		bIsValid = false;
	}

	return bIsValid;
}

float UCharacterStatsDataAsset::GetEffectiveMaxHealth(int32 UpgradeLevel) const
{
	return MaxHealth * (1.0f + (HealthUpgradeMultiplier * UpgradeLevel));
}

float UCharacterStatsDataAsset::GetEffectiveDashDistance(int32 UpgradeLevel) const
{
	return DashDistance * (1.0f + (DashUpgradeMultiplier * UpgradeLevel));
}

float UCharacterStatsDataAsset::GetEffectiveBulletTimeEnergy(int32 UpgradeLevel) const
{
	return MaxBulletTimeEnergy * (1.0f + (BulletTimeUpgradeMultiplier * UpgradeLevel));
}

int32 UCharacterStatsDataAsset::GetEffectiveRewindCharges(int32 UpgradeLevel) const
{
	return MaxRewindCharges + (RewindChargesPerUpgrade * UpgradeLevel);
}
