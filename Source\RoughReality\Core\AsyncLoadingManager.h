// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Engine/StreamableManager.h"
#include "Engine/AssetManager.h"
#include "AsyncLoadingManager.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAssetLoaded, TSoftObjectPtr<UObject>, LoadedAsset);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAssetLoadFailed, TSoftObjectPtr<UObject>, FailedAsset);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnLoadingProgress, float, Progress, FString, CurrentAsset);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnLoadingComplete);

USTRUCT(BlueprintType)
struct FAssetLoadRequest
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadWrite, Category = "Loading")
	TSoftObjectPtr<UObject> AssetToLoad;

	UPROPERTY(BlueprintReadWrite, Category = "Loading")
	int32 Priority = 0;

	UPROPERTY(BlueprintReadWrite, Category = "Loading")
	bool bIsRequired = true;

	UPROPERTY(BlueprintReadWrite, Category = "Loading")
	FString LoadGroup;

	FAssetLoadRequest()
	{
		Priority = 0;
		bIsRequired = true;
		LoadGroup = TEXT("Default");
	}

	FAssetLoadRequest(const TSoftObjectPtr<UObject>& InAsset, int32 InPriority = 0, bool bInIsRequired = true, const FString& InLoadGroup = TEXT("Default"))
	{
		AssetToLoad = InAsset;
		Priority = InPriority;
		bIsRequired = bInIsRequired;
		LoadGroup = InLoadGroup;
	}
};

USTRUCT(BlueprintType)
struct FLoadingGroup
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadWrite, Category = "Loading Group")
	FString GroupName;

	UPROPERTY(BlueprintReadWrite, Category = "Loading Group")
	TArray<FAssetLoadRequest> AssetRequests;

	UPROPERTY(BlueprintReadWrite, Category = "Loading Group")
	bool bLoadInBackground = true;

	UPROPERTY(BlueprintReadWrite, Category = "Loading Group")
	bool bShowProgress = true;

	UPROPERTY(BlueprintReadWrite, Category = "Loading Group")
	float TimeoutSeconds = 30.0f;

	FLoadingGroup()
	{
		GroupName = TEXT("Default");
		bLoadInBackground = true;
		bShowProgress = true;
		TimeoutSeconds = 30.0f;
	}
};

USTRUCT(BlueprintType)
struct FLoadingStatistics
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadOnly, Category = "Statistics")
	int32 TotalAssetsRequested = 0;

	UPROPERTY(BlueprintReadOnly, Category = "Statistics")
	int32 AssetsLoaded = 0;

	UPROPERTY(BlueprintReadOnly, Category = "Statistics")
	int32 AssetsFailed = 0;

	UPROPERTY(BlueprintReadOnly, Category = "Statistics")
	float TotalLoadTime = 0.0f;

	UPROPERTY(BlueprintReadOnly, Category = "Statistics")
	float AverageLoadTime = 0.0f;

	UPROPERTY(BlueprintReadOnly, Category = "Statistics")
	int32 CacheHits = 0;

	UPROPERTY(BlueprintReadOnly, Category = "Statistics")
	int32 CacheMisses = 0;

	FLoadingStatistics()
	{
		TotalAssetsRequested = 0;
		AssetsLoaded = 0;
		AssetsFailed = 0;
		TotalLoadTime = 0.0f;
		AverageLoadTime = 0.0f;
		CacheHits = 0;
		CacheMisses = 0;
	}
};

/**
 * Async Loading Manager for Rough Reality
 * Handles efficient asset loading with progress tracking and caching
 */
UCLASS(BlueprintType, Blueprintable)
class ROUGHREALITY_API AAsyncLoadingManager : public AActor
{
	GENERATED_BODY()

public:
	AAsyncLoadingManager();

protected:
	virtual void BeginPlay() override;
	virtual void Tick(float DeltaTime) override;
	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

public:
	/** Streamable Manager */
	FStreamableManager StreamableManager;

	/** Loading Configuration */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Configuration")
	bool bEnableAssetCaching = true;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Configuration")
	int32 MaxCacheSize = 100;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Configuration")
	float DefaultTimeout = 30.0f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Configuration")
	bool bPreloadCommonAssets = true;

	/** Events */
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnAssetLoaded OnAssetLoaded;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnAssetLoadFailed OnAssetLoadFailed;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnLoadingProgress OnLoadingProgress;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnLoadingComplete OnLoadingComplete;

	/** Loading Statistics */
	UPROPERTY(BlueprintReadOnly, Category = "Statistics")
	FLoadingStatistics LoadingStats;

public:
	/** Single Asset Loading */
	UFUNCTION(BlueprintCallable, Category = "Asset Loading")
	void LoadAssetAsync(const TSoftObjectPtr<UObject>& AssetToLoad, int32 Priority = 0);

	UFUNCTION(BlueprintCallable, Category = "Asset Loading")
	UObject* LoadAssetSync(const TSoftObjectPtr<UObject>& AssetToLoad);

	UFUNCTION(BlueprintCallable, Category = "Asset Loading")
	bool IsAssetLoaded(const TSoftObjectPtr<UObject>& Asset) const;

	UFUNCTION(BlueprintCallable, Category = "Asset Loading")
	void UnloadAsset(const TSoftObjectPtr<UObject>& Asset);

	/** Batch Loading */
	UFUNCTION(BlueprintCallable, Category = "Batch Loading")
	void LoadAssetsAsync(const TArray<FAssetLoadRequest>& AssetRequests, const FString& GroupName = TEXT("Default"));

	UFUNCTION(BlueprintCallable, Category = "Batch Loading")
	void LoadAssetGroup(const FLoadingGroup& LoadGroup);

	UFUNCTION(BlueprintCallable, Category = "Batch Loading")
	void CancelLoadGroup(const FString& GroupName);

	/** Preloading */
	UFUNCTION(BlueprintCallable, Category = "Preloading")
	void PreloadSectorAssets(int32 SectorIndex);

	UFUNCTION(BlueprintCallable, Category = "Preloading")
	void PreloadWeaponAssets(const TArray<FString>& WeaponNames);

	UFUNCTION(BlueprintCallable, Category = "Preloading")
	void PreloadCommonAssets();

	/** Cache Management */
	UFUNCTION(BlueprintCallable, Category = "Cache")
	void ClearAssetCache();

	UFUNCTION(BlueprintCallable, Category = "Cache")
	void TrimAssetCache();

	UFUNCTION(BlueprintCallable, Category = "Cache")
	int32 GetCacheSize() const;

	UFUNCTION(BlueprintCallable, Category = "Cache")
	float GetCacheHitRate() const;

	/** Progress Tracking */
	UFUNCTION(BlueprintCallable, Category = "Progress")
	float GetLoadingProgress(const FString& GroupName = TEXT("Default")) const;

	UFUNCTION(BlueprintCallable, Category = "Progress")
	bool IsLoadingInProgress(const FString& GroupName = TEXT("Default")) const;

	UFUNCTION(BlueprintCallable, Category = "Progress")
	TArray<FString> GetActiveLoadGroups() const;

	/** Memory Management */
	UFUNCTION(BlueprintCallable, Category = "Memory")
	void ForceGarbageCollection();

	UFUNCTION(BlueprintCallable, Category = "Memory")
	int32 GetMemoryUsageMB() const;

	UFUNCTION(BlueprintCallable, Category = "Memory")
	void OptimizeMemoryUsage();

	/** Statistics */
	UFUNCTION(BlueprintCallable, Category = "Statistics")
	FLoadingStatistics GetLoadingStatistics() const;

	UFUNCTION(BlueprintCallable, Category = "Statistics")
	void ResetStatistics();

	UFUNCTION(BlueprintCallable, Category = "Statistics")
	void LogLoadingStatistics();

	/** Static Access */
	UFUNCTION(BlueprintCallable, Category = "Asset Loading")
	static AAsyncLoadingManager* GetAsyncLoadingManager(const UObject* WorldContext);

protected:
	/** Internal Functions */
	void ProcessLoadingQueue();
	void OnAssetLoadComplete(const FSoftObjectPath& AssetPath);
	void OnAssetLoadFailed(const FSoftObjectPath& AssetPath);
	void UpdateLoadingProgress();
	void CleanupCompletedLoads();

	/** Asset Cache */
	UPROPERTY()
	TMap<FSoftObjectPath, UObject*> AssetCache;

	/** Active Loading Handles */
	TMap<FString, TSharedPtr<FStreamableHandle>> ActiveLoadHandles;

	/** Loading Groups */
	UPROPERTY()
	TMap<FString, FLoadingGroup> LoadingGroups;

	/** Loading Progress */
	UPROPERTY()
	TMap<FString, float> GroupProgress;

	/** Common Assets to Preload */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Preloading")
	TArray<TSoftObjectPtr<UObject>> CommonAssets;

	/** Sector Asset Maps */
	TMap<int32, TArray<TSoftObjectPtr<UObject>>> SectorAssetMaps;

private:
	/** Singleton instance */
	static AAsyncLoadingManager* Instance;

	/** Loading timers */
	TMap<FSoftObjectPath, float> LoadStartTimes;

	/** Cache management */
	TArray<FSoftObjectPath> CacheAccessOrder;
	void UpdateCacheAccess(const FSoftObjectPath& AssetPath);
	void EvictOldestCacheEntry();
};
