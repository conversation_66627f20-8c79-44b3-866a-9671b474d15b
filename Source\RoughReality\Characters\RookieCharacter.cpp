// Copyright Epic Games, Inc. All Rights Reserved.

#include "RookieCharacter.h"
#include "Camera/CameraComponent.h"
#include "Components/CapsuleComponent.h"
#include "Components/InputComponent.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "GameFramework/Controller.h"
#include "GameFramework/SpringArmComponent.h"
#include "EnhancedInputComponent.h"
#include "EnhancedInputSubsystems.h"
#include "Components/TimelineComponent.h"
#include "Engine/Engine.h"
#include "Kismet/KismetMathLibrary.h"
#include "../Weapons/WeaponBase.h"
#include "../DataAssets/CharacterStatsDataAsset.h"
#include "../Analytics/GameAnalyticsManager.h"
#include "../VFX/VisualEffectsManager.h"

ARookieCharacter::ARookieCharacter()
{
	// Set size for collision capsule
	GetCapsuleComponent()->InitCapsuleSize(42.f, 96.0f);

	// Don't rotate when the controller rotates. Let that just affect the camera.
	bUseControllerRotationPitch = false;
	bUseControllerRotationYaw = false;
	bUseControllerRotationRoll = false;

	// Configure character movement
	GetCharacterMovement()->bOrientRotationToMovement = true; // Character moves in the direction of input...	
	GetCharacterMovement()->RotationRate = FRotator(0.0f, 500.0f, 0.0f); // ...at this rotation rate

	// Note: For faster iteration times these variables, and many more, can be tweaked in the Character Blueprint
	// instead of recompiling to adjust them
	GetCharacterMovement()->JumpZVelocity = 700.f;
	GetCharacterMovement()->AirControl = 0.35f;
	GetCharacterMovement()->MaxWalkSpeed = 500.f;
	GetCharacterMovement()->MinAnalogWalkSpeed = 20.f;
	GetCharacterMovement()->BrakingDecelerationWalking = 2000.f;
	GetCharacterMovement()->BrakingDecelerationFalling = 1500.0f;

	// Create a camera boom (pulls in towards the player if there is a collision)
	CameraBoom = CreateDefaultSubobject<USpringArmComponent>(TEXT("CameraBoom"));
	CameraBoom->SetupAttachment(RootComponent);
	CameraBoom->TargetArmLength = 400.0f; // The camera follows at this distance behind the character	
	CameraBoom->bUsePawnControlRotation = true; // Rotate the arm based on the controller

	// Create a follow camera
	FollowCamera = CreateDefaultSubobject<UCameraComponent>(TEXT("FollowCamera"));
	FollowCamera->SetupAttachment(CameraBoom, USpringArmComponent::SocketName); // Attach the camera to the end of the boom and let the boom adjust to match the controller orientation
	FollowCamera->bUsePawnControlRotation = false; // Camera does not rotate relative to arm

	// Create dash timeline
	DashTimeline = CreateDefaultSubobject<UTimelineComponent>(TEXT("DashTimeline"));

	// Initialize character stats
	CurrentHealth = MaxHealth;
	BulletTimeEnergy = MaxBulletTimeEnergy;
	RewindCharges = MaxRewindCharges;

	// Set tick enabled
	PrimaryActorTick.bCanEverTick = true;
}

void ARookieCharacter::BeginPlay()
{
	Super::BeginPlay();

	// Add Input Mapping Context
	if (APlayerController* PlayerController = Cast<APlayerController>(Controller))
	{
		if (UEnhancedInputLocalPlayerSubsystem* Subsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(PlayerController->GetLocalPlayer()))
		{
			Subsystem->AddMappingContext(DefaultMappingContext, 0);
		}
	}

	// Setup dash timeline
	if (DashCurve && DashTimeline)
	{
		FOnTimelineFloat DashUpdateDelegate;
		FOnTimelineEvent DashFinishedDelegate;
		
		DashUpdateDelegate.BindUFunction(this, FName("DashTimelineUpdate"));
		DashFinishedDelegate.BindUFunction(this, FName("DashTimelineFinished"));
		
		DashTimeline->AddInterpFloat(DashCurve, DashUpdateDelegate);
		DashTimeline->SetTimelineFinishedFunc(DashFinishedDelegate);
		DashTimeline->SetTimelineLength(DashDuration);
	}

	// Initialize character stats from data asset
	if (CharacterStats)
	{
		MaxHealth = CharacterStats->MaxHealth;
		CurrentHealth = MaxHealth;
		MaxBulletTimeEnergy = CharacterStats->MaxBulletTimeEnergy;
		BulletTimeEnergy = MaxBulletTimeEnergy;
		MaxRewindCharges = CharacterStats->MaxRewindCharges;
		RewindCharges = MaxRewindCharges;
		DashDistance = CharacterStats->DashDistance;
		DashCooldown = CharacterStats->DashCooldown;
	}

	// Broadcast initial values
	OnHealthChanged.Broadcast(GetHealthPercent());
	OnBulletTimeChanged.Broadcast(GetBulletTimePercent());
	OnRewindChargesChanged.Broadcast(RewindCharges);
}

void ARookieCharacter::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	UpdateBulletTime(DeltaTime);
	UpdateTimeSnapshots(DeltaTime);
	UpdateDashCooldown(DeltaTime);
}

void ARookieCharacter::NotifyControllerChanged()
{
	Super::NotifyControllerChanged();

	// Add Input Mapping Context
	if (APlayerController* PlayerController = Cast<APlayerController>(Controller))
	{
		if (UEnhancedInputLocalPlayerSubsystem* Subsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(PlayerController->GetLocalPlayer()))
		{
			Subsystem->AddMappingContext(DefaultMappingContext, 0);
		}
	}
}

void ARookieCharacter::SetupPlayerInputComponent(UInputComponent* PlayerInputComponent)
{
	// Set up action bindings
	if (UEnhancedInputComponent* EnhancedInputComponent = CastChecked<UEnhancedInputComponent>(PlayerInputComponent))
	{
		// Moving
		EnhancedInputComponent->BindAction(MoveAction, ETriggerEvent::Triggered, this, &ARookieCharacter::Move);

		// Looking
		EnhancedInputComponent->BindAction(LookAction, ETriggerEvent::Triggered, this, &ARookieCharacter::Look);

		// Jumping
		EnhancedInputComponent->BindAction(JumpAction, ETriggerEvent::Started, this, &ACharacter::Jump);
		EnhancedInputComponent->BindAction(JumpAction, ETriggerEvent::Completed, this, &ACharacter::StopJumping);

		// Firing
		EnhancedInputComponent->BindAction(FireAction, ETriggerEvent::Started, this, &ARookieCharacter::StartFire);
		EnhancedInputComponent->BindAction(FireAction, ETriggerEvent::Completed, this, &ARookieCharacter::StopFire);

		// Bullet Time
		EnhancedInputComponent->BindAction(BulletTimeAction, ETriggerEvent::Started, this, &ARookieCharacter::StartBulletTime);
		EnhancedInputComponent->BindAction(BulletTimeAction, ETriggerEvent::Completed, this, &ARookieCharacter::StopBulletTime);

		// Time Rewind
		EnhancedInputComponent->BindAction(RewindAction, ETriggerEvent::Started, this, &ARookieCharacter::PerformRewind);

		// Dash
		EnhancedInputComponent->BindAction(DashAction, ETriggerEvent::Started, this, &ARookieCharacter::PerformDash);

		// Interact
		EnhancedInputComponent->BindAction(InteractAction, ETriggerEvent::Started, this, &ARookieCharacter::Interact);

		// Weapon Switch
		EnhancedInputComponent->BindAction(WeaponSwitchAction, ETriggerEvent::Started, this, &ARookieCharacter::SwitchWeapon);
	}
}

void ARookieCharacter::Move(const FInputActionValue& Value)
{
	// Input is a Vector2D
	FVector2D MovementVector = Value.Get<FVector2D>();

	if (Controller != nullptr)
	{
		// Find out which way is forward
		const FRotator Rotation = Controller->GetControlRotation();
		const FRotator YawRotation(0, Rotation.Yaw, 0);

		// Get forward vector
		const FVector ForwardDirection = FRotationMatrix(YawRotation).GetUnitAxis(EAxis::X);
	
		// Get right vector 
		const FVector RightDirection = FRotationMatrix(YawRotation).GetUnitAxis(EAxis::Y);

		// Add movement 
		AddMovementInput(ForwardDirection, MovementVector.Y);
		AddMovementInput(RightDirection, MovementVector.X);
	}
}

void ARookieCharacter::Look(const FInputActionValue& Value)
{
	// Input is a Vector2D
	FVector2D LookAxisVector = Value.Get<FVector2D>();

	if (Controller != nullptr)
	{
		// Add yaw and pitch input to controller
		AddControllerYawInput(LookAxisVector.X);
		AddControllerPitchInput(LookAxisVector.Y);
	}
}

void ARookieCharacter::StartFire(const FInputActionValue& Value)
{
	if (CurrentWeapon)
	{
		CurrentWeapon->StartFire();
	}
}

void ARookieCharacter::StopFire(const FInputActionValue& Value)
{
	if (CurrentWeapon)
	{
		CurrentWeapon->StopFire();
	}
}

void ARookieCharacter::StartBulletTime(const FInputActionValue& Value)
{
	ActivateBulletTime();
}

void ARookieCharacter::StopBulletTime(const FInputActionValue& Value)
{
	DeactivateBulletTime();
}

void ARookieCharacter::PerformRewind(const FInputActionValue& Value)
{
	PerformTimeRewind();
}

void ARookieCharacter::PerformDash(const FInputActionValue& Value)
{
	PerformDashMovement();
}

void ARookieCharacter::Interact(const FInputActionValue& Value)
{
	// TODO: Implement interaction system
	UE_LOG(LogTemp, Log, TEXT("Interact pressed"));
}

void ARookieCharacter::SwitchWeapon(const FInputActionValue& Value)
{
	SwitchToNextWeapon();
}

// Health System Implementation
void ARookieCharacter::TakeDamage(float DamageAmount)
{
	if (IsDead()) return;

	CurrentHealth = FMath::Clamp(CurrentHealth - DamageAmount, 0.0f, MaxHealth);
	OnHealthChanged.Broadcast(GetHealthPercent());

	UE_LOG(LogTemp, Log, TEXT("Rookie took %f damage. Health: %f/%f"), DamageAmount, CurrentHealth, MaxHealth);

	if (IsDead())
	{
		// Handle death
		UE_LOG(LogTemp, Log, TEXT("Rookie died!"));
		// TODO: Trigger death event
	}
}

void ARookieCharacter::Heal(float HealAmount)
{
	if (IsDead()) return;

	CurrentHealth = FMath::Clamp(CurrentHealth + HealAmount, 0.0f, MaxHealth);
	OnHealthChanged.Broadcast(GetHealthPercent());

	UE_LOG(LogTemp, Log, TEXT("Rookie healed %f. Health: %f/%f"), HealAmount, CurrentHealth, MaxHealth);
}

bool ARookieCharacter::IsDead() const
{
	return CurrentHealth <= 0.0f;
}

float ARookieCharacter::GetHealthPercent() const
{
	return MaxHealth > 0.0f ? CurrentHealth / MaxHealth : 0.0f;
}

// Bullet Time Implementation
void ARookieCharacter::ActivateBulletTime()
{
	if (CanUseBulletTime() && !bIsBulletTimeActive)
	{
		bIsBulletTimeActive = true;
		GetWorld()->GetWorldSettings()->SetTimeDilation(BulletTimeScale);
		UE_LOG(LogTemp, Log, TEXT("Bullet Time activated"));
	}
}

void ARookieCharacter::DeactivateBulletTime()
{
	if (bIsBulletTimeActive)
	{
		bIsBulletTimeActive = false;
		GetWorld()->GetWorldSettings()->SetTimeDilation(1.0f);
		UE_LOG(LogTemp, Log, TEXT("Bullet Time deactivated"));
	}
}

bool ARookieCharacter::CanUseBulletTime() const
{
	return BulletTimeEnergy > 0.0f && !IsDead();
}

float ARookieCharacter::GetBulletTimePercent() const
{
	return MaxBulletTimeEnergy > 0.0f ? BulletTimeEnergy / MaxBulletTimeEnergy : 0.0f;
}

// Time Rewind Implementation
void ARookieCharacter::PerformTimeRewind()
{
	if (!CanRewind()) return;

	// Find snapshot from RewindDuration seconds ago
	float TargetTime = GetWorld()->GetTimeSeconds() - RewindDuration;
	FTimeSnapshot* BestSnapshot = nullptr;
	float BestTimeDiff = FLT_MAX;

	for (FTimeSnapshot& Snapshot : TimeSnapshots)
	{
		float TimeDiff = FMath::Abs(Snapshot.Timestamp - TargetTime);
		if (TimeDiff < BestTimeDiff)
		{
			BestTimeDiff = TimeDiff;
			BestSnapshot = &Snapshot;
		}
	}

	if (BestSnapshot)
	{
		// Restore state
		SetActorLocation(BestSnapshot->Location);
		SetActorRotation(BestSnapshot->Rotation);
		GetCharacterMovement()->Velocity = BestSnapshot->Velocity;
		CurrentHealth = BestSnapshot->Health;

		// Use a rewind charge
		RewindCharges--;
		OnRewindChargesChanged.Broadcast(RewindCharges);
		OnHealthChanged.Broadcast(GetHealthPercent());

		UE_LOG(LogTemp, Log, TEXT("Time rewind performed. Charges remaining: %d"), RewindCharges);
	}
}

bool ARookieCharacter::CanRewind() const
{
	return RewindCharges > 0 && !IsDead() && TimeSnapshots.Num() > 0;
}

void ARookieCharacter::AddRewindCharge()
{
	RewindCharges = FMath::Clamp(RewindCharges + 1, 0, MaxRewindCharges);
	OnRewindChargesChanged.Broadcast(RewindCharges);
}

// Dash Implementation
void ARookieCharacter::PerformDashMovement()
{
	if (!CanPerformDash()) return;

	// Get movement input direction
	FVector InputDirection = GetLastMovementInputVector();
	if (InputDirection.IsNearlyZero())
	{
		// If no input, dash forward
		InputDirection = GetActorForwardVector();
	}

	InputDirection.Normalize();

	DashStartLocation = GetActorLocation();
	DashTargetLocation = DashStartLocation + (InputDirection * DashDistance);

	// Start dash
	bIsDashing = true;
	bCanDash = false;
	DashCooldownTimer = DashCooldown;

	if (DashTimeline)
	{
		DashTimeline->PlayFromStart();
	}

	UE_LOG(LogTemp, Log, TEXT("Dash started"));
}

bool ARookieCharacter::CanPerformDash() const
{
	return bCanDash && !IsDead() && !bIsDashing;
}

// Weapon Management Implementation
void ARookieCharacter::EquipWeapon(AWeaponBase* NewWeapon)
{
	if (!NewWeapon) return;

	// Unequip current weapon
	if (CurrentWeapon)
	{
		CurrentWeapon->OnUnequipped();
	}

	CurrentWeapon = NewWeapon;
	CurrentWeapon->OnEquipped(this);
	OnWeaponChanged.Broadcast(CurrentWeapon);

	UE_LOG(LogTemp, Log, TEXT("Weapon equipped: %s"), *NewWeapon->GetName());
}

void ARookieCharacter::AddWeaponToInventory(AWeaponBase* Weapon)
{
	if (!Weapon) return;

	WeaponInventory.AddUnique(Weapon);

	// If no weapon equipped, equip this one
	if (!CurrentWeapon)
	{
		EquipWeapon(Weapon);
		CurrentWeaponIndex = WeaponInventory.Num() - 1;
	}

	UE_LOG(LogTemp, Log, TEXT("Weapon added to inventory: %s"), *Weapon->GetName());
}

void ARookieCharacter::SwitchToNextWeapon()
{
	if (WeaponInventory.Num() <= 1) return;

	CurrentWeaponIndex = (CurrentWeaponIndex + 1) % WeaponInventory.Num();

	if (WeaponInventory.IsValidIndex(CurrentWeaponIndex))
	{
		EquipWeapon(WeaponInventory[CurrentWeaponIndex]);
	}
}

void ARookieCharacter::SwitchToPreviousWeapon()
{
	if (WeaponInventory.Num() <= 1) return;

	CurrentWeaponIndex = (CurrentWeaponIndex - 1 + WeaponInventory.Num()) % WeaponInventory.Num();

	if (WeaponInventory.IsValidIndex(CurrentWeaponIndex))
	{
		EquipWeapon(WeaponInventory[CurrentWeaponIndex]);
	}
}

// Internal Update Systems
void ARookieCharacter::UpdateBulletTime(float DeltaTime)
{
	if (bIsBulletTimeActive)
	{
		// Drain bullet time energy
		BulletTimeEnergy = FMath::Clamp(BulletTimeEnergy - (BulletTimeDrainRate * DeltaTime), 0.0f, MaxBulletTimeEnergy);

		// Auto-deactivate if energy depleted
		if (BulletTimeEnergy <= 0.0f)
		{
			DeactivateBulletTime();
		}
	}
	else
	{
		// Recharge bullet time energy
		BulletTimeEnergy = FMath::Clamp(BulletTimeEnergy + (BulletTimeRechargeRate * DeltaTime), 0.0f, MaxBulletTimeEnergy);
	}

	OnBulletTimeChanged.Broadcast(GetBulletTimePercent());
}

void ARookieCharacter::UpdateTimeSnapshots(float DeltaTime)
{
	float CurrentTime = GetWorld()->GetTimeSeconds();

	// Create snapshot at intervals
	if (CurrentTime - LastSnapshotTime >= SnapshotInterval)
	{
		FTimeSnapshot NewSnapshot;
		NewSnapshot.Location = GetActorLocation();
		NewSnapshot.Rotation = GetActorRotation();
		NewSnapshot.Velocity = GetCharacterMovement()->Velocity;
		NewSnapshot.Health = CurrentHealth;
		NewSnapshot.Timestamp = CurrentTime;

		TimeSnapshots.Add(NewSnapshot);
		LastSnapshotTime = CurrentTime;

		// Remove old snapshots (keep only last RewindDuration + 1 second worth)
		float CutoffTime = CurrentTime - (RewindDuration + 1.0f);
		TimeSnapshots.RemoveAll([CutoffTime](const FTimeSnapshot& Snapshot)
		{
			return Snapshot.Timestamp < CutoffTime;
		});
	}
}

void ARookieCharacter::UpdateDashCooldown(float DeltaTime)
{
	if (!bCanDash)
	{
		DashCooldownTimer -= DeltaTime;
		if (DashCooldownTimer <= 0.0f)
		{
			bCanDash = true;
			DashCooldownTimer = 0.0f;
		}
	}
}

// Timeline Functions
void ARookieCharacter::DashTimelineUpdate(float Value)
{
	if (bIsDashing)
	{
		FVector CurrentLocation = FMath::Lerp(DashStartLocation, DashTargetLocation, Value);
		SetActorLocation(CurrentLocation);
	}
}

void ARookieCharacter::DashTimelineFinished()
{
	bIsDashing = false;
	UE_LOG(LogTemp, Log, TEXT("Dash completed"));
}

// Integration with Enhanced Systems
void ARookieCharacter::RegisterWithGameSystems()
{
	// Register with analytics system
	if (AGameAnalyticsManager* Analytics = AGameAnalyticsManager::GetAnalyticsManager(this))
	{
		TMap<FString, FString> EmptyParams;
		Analytics->LogPlayerAction("PlayerSpawned", "Character", GetActorLocation(), EmptyParams);
	}

	// Register with visual effects system
	if (AVisualEffectsManager* VFX = AVisualEffectsManager::GetVisualEffectsManager(this))
	{
		VFX->SetVisualState(EGameVisualState::Normal);
	}

	UE_LOG(LogTemp, Log, TEXT("Rookie character registered with game systems"));
}

void ARookieCharacter::UnregisterFromGameSystems()
{
	// Cleanup any system registrations
	UE_LOG(LogTemp, Log, TEXT("Rookie character unregistered from game systems"));
}

void ARookieCharacter::UpdatePerformanceSettings(int32 QualityLevel)
{
	// Adjust character-specific performance settings based on quality level
	switch (QualityLevel)
	{
	case 0: // Low
		PrimaryActorTick.TickInterval = 0.033f; // 30 FPS
		break;
	case 1: // Medium
		PrimaryActorTick.TickInterval = 0.016f; // 60 FPS
		break;
	case 2: // High
	case 3: // Ultra
	default:
		PrimaryActorTick.TickInterval = 0.0f; // Full rate
		break;
	}

	UE_LOG(LogTemp, Log, TEXT("Character performance settings updated to quality level %d"), QualityLevel);
}

void ARookieCharacter::SetLODLevel(int32 LODLevel)
{
	// Adjust character LOD based on distance/performance requirements
	if (GetMesh())
	{
		GetMesh()->SetForcedLOD(LODLevel + 1); // UE LOD is 1-based
	}

	// Adjust tick frequency based on LOD
	switch (LODLevel)
	{
	case 0: // High detail
		PrimaryActorTick.TickInterval = 0.0f;
		break;
	case 1: // Medium detail
		PrimaryActorTick.TickInterval = 0.016f;
		break;
	case 2: // Low detail
		PrimaryActorTick.TickInterval = 0.033f;
		break;
	default:
		PrimaryActorTick.TickInterval = 0.05f; // Very low detail
		break;
	}
}

void ARookieCharacter::RecordPlayerAction(const FString& ActionType, const TMap<FString, FString>& Parameters)
{
	if (AGameAnalyticsManager* Analytics = AGameAnalyticsManager::GetAnalyticsManager(this))
	{
		Analytics->LogPlayerAction(ActionType, "Player", GetActorLocation(), Parameters);
	}
}
