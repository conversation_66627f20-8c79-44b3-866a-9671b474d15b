﻿Log file open, 05/31/25 14:40:42
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=27740)
LogWindows: Warning: Failed to set completion port for job object "UE.ShaderCompileWorker.JobGroup": The parameter is incorrect.
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: RoughReality
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Display Control listening on port 36394
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.5-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.5.4-40574608+++UE5+Release-5.5"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (24H2) [10.0.26100.4061] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 5 5600X 6-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" G:\Gamedev\RoughReality\RoughReality.uproject -AUTH_LOGIN=unused -AUTH_PASSWORD=4be42d1d76eb457088d1fcd64095a9a1 -AUTH_TYPE=exchangecode -epicapp=UE_5.5 -epicenv=Prod -EpicPortal -epicusername=juliocacko -epicuserid=c7ca589710dd40c8846aeace6f9d7b04 -epiclocale=en -epicsandboxid=ue""
LogCsvProfiler: Display: Metadata set : loginid="dadec2e1421bcefd03625fbb5d8813ad"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.351498
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: -3:00, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-421A00D946423FE51742BDBDAE41107C
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [G:/Gamedev/RoughReality/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogConfig: Display: Loading Mac ini files took 0.08 seconds
LogConfig: Display: Loading VulkanPC ini files took 0.09 seconds
LogConfig: Display: Loading IOS ini files took 0.09 seconds
LogConfig: Display: Loading Android ini files took 0.09 seconds
LogConfig: Display: Loading Unix ini files took 0.11 seconds
LogConfig: Display: Loading TVOS ini files took 0.11 seconds
LogPluginManager: Found matching target receipt: G:/Gamedev/RoughReality/Binaries/Win64/RoughRealityEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogConfig: Display: Loading LinuxArm64 ini files took 0.13 seconds
LogConfig: Display: Loading Linux ini files took 0.13 seconds
LogConfig: Display: Loading Windows ini files took 0.06 seconds
LogPluginManager: Found matching target receipt: G:/Gamedev/RoughReality/Binaries/Win64/RoughRealityEditor.target
LogConfig: Display: Loading VisionOS ini files took 0.06 seconds
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin StudioTelemetry
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin LiveLink
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin USDCore
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin HoldoutComposite
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin SQLiteCore
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin ConcertMain
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin ConcertSyncClient
LogPluginManager: Mounting Engine plugin ConcertSyncCore
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogNFORDenoise: NFORDenoise function starting up
LogStudioTelemetry: Display: Starting StudioTelemetry Module
LogStudioTelemetry: Started StudioTelemetry Session
LogEOSSDK: Initializing EOSSDK Version:1.17.0-39599718
LogInit: Using libcurl 8.4.0
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum ********** concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.5-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=dadec2e1421bcefd03625fbb5d8813ad
LogInit: DeviceId=
LogInit: Engine Version: 5.5.4-40574608+++UE5+Release-5.5
LogInit: Compatible Engine Version: 5.5.0-37670630+++UE5+Release-5.5
LogInit: Net CL: 37670630
LogInit: OS: Windows 11 (24H2) [10.0.26100.4061] (), CPU: AMD Ryzen 5 5600X 6-Core Processor             , GPU: NVIDIA GeForce RTX 3070
LogInit: Compiled (64-bit): Mar  7 2025 14:49:53
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.5
LogInit: Command Line: -AUTH_LOGIN=unused -AUTH_PASSWORD=4be42d1d76eb457088d1fcd64095a9a1 -AUTH_TYPE=exchangecode -epicapp=UE_5.5 -epicenv=Prod -EpicPortal -epicusername=juliocacko -epicuserid=c7ca589710dd40c8846aeace6f9d7b04 -epiclocale=en -epicsandboxid=ue
LogInit: Base Directory: F:/Unreal/UE_5.5/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 36
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 170
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 15
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 10
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 119
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 51
LogDevObjectVersion:   UE5-PrivateFrosty (59DA5D52-1232-4948-B878-597870B8E98B): 8
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogConfig: Branch 'EditorLayout' had been unloaded. Reloading on-demand took 1.25ms
LogConfig: Branch 'Bridge' had been unloaded. Reloading on-demand took 0.21ms
LogConfig: Branch 'ChaosCloth' had been unloaded. Reloading on-demand took 0.21ms
LogConfig: Branch 'CmdLinkServer' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'FastBuildController' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'MeshPainting' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'RenderGraphInsights' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'UbaController' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'WorldMetrics' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'XGEController' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'AdvancedRenamer' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'AutomationUtils' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'BackChannel' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'ChaosCaching' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'ChaosEditor' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'ChaosNiagara' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'ChaosSolverPlugin' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'ChaosUserDataPT' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'CharacterAI' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'Dataflow' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'EditorDataStorage' had been unloaded. Reloading on-demand took 0.22ms
LogConfig: Branch 'EditorPerformance' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'EditorTelemetry' had been unloaded. Reloading on-demand took 0.20ms
LogConfig: Branch 'Fracture' had been unloaded. Reloading on-demand took 0.22ms
LogConfig: Branch 'GeometryCollectionPlugin' had been unloaded. Reloading on-demand took 0.20ms
LogConfig: Branch 'GeometryFlow' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'LocalizableMessage' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'LowLevelNetTrace' had been unloaded. Reloading on-demand took 0.20ms
LogConfig: Branch 'MeshModelingToolsetExp' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'NFORDenoise' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'PlatformCrypto' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'PlanarCut' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'PythonScriptPlugin' had been unloaded. Reloading on-demand took 0.21ms
LogConfig: Branch 'StudioTelemetry' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'SkeletalReduction' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'NiagaraSimCaching' had been unloaded. Reloading on-demand took 0.20ms
LogConfig: Branch 'AlembicImporter' had been unloaded. Reloading on-demand took 0.28ms
LogConfig: Branch 'AndroidMedia' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'AvfMedia' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'ImgMedia' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'MediaCompositing' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'MediaPlate' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'MediaPlayerEditor' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'WebMMedia' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'TcpMessaging' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'UdpMessaging' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'WmfMedia' had been unloaded. Reloading on-demand took 0.16ms
LogConfig: Branch 'EOSShared' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'OnlineBase' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'OnlineServices' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'OnlineSubsystem' had been unloaded. Reloading on-demand took 0.20ms
LogConfig: Branch 'OnlineSubsystemNull' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'OnlineSubsystemUtils' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'LauncherChunkInstaller' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'InterchangeTests' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'AISupport' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'EnvironmentQueryEditor' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'AnimationData' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'ACLPlugin' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'AnimationModifierLibrary' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'ControlRigSpline' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'ControlRigModules' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'EngineCameras' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'CameraShakePreviewer' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'OodleNetwork' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'AnimationSharing' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'CodeLiteSourceCodeAccess' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'CLionSourceCodeAccess' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'DumpGPUServices' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'GitSourceControl' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'KDevelopSourceCodeAccess' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'N10XSourceCodeAccess' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'NullSourceCodeAccess' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'PixWinPlugin' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'PerforceSourceControl' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'PropertyAccessNode' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'PluginUtils' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'RenderDocPlugin' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'RigLogic' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'RiderSourceCodeAccess' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'UObjectPlugin' had been unloaded. Reloading on-demand took 0.16ms
LogConfig: Branch 'SubversionSourceControl' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'VisualStudioSourceCodeAccess' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'BlendSpaceMotionAnalysis' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'VisualStudioCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'TextureFormatOodle' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'XCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'AssetManagerEditor' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'ChangelistReview' had been unloaded. Reloading on-demand took 0.25ms
LogConfig: Branch 'ColorGrading' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'CryptoKeys' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'DataValidation' had been unloaded. Reloading on-demand took 0.20ms
LogConfig: Branch 'CurveEditorTools' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'EditorDebugTools' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'PlasticSourceControl' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'BlueprintHeaderView' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'FacialAnimation' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'GameplayTagsEditor' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'EngineAssetDefinitions' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'GeometryMode' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'MacGraphicsSwitching' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'MaterialAnalyzer' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'MeshLODToolset' had been unloaded. Reloading on-demand took 0.24ms
LogConfig: Branch 'MobileLauncherProfileWizard' had been unloaded. Reloading on-demand took 0.21ms
LogConfig: Branch 'ModelingToolsEditorMode' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'PluginBrowser' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'SequencerAnimTools' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'SpeedTreeImporter' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'StylusInput' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'UMGWidgetPreview' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'UVEditor' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'WorldPartitionHLODUtilities' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'ProxyLODPlugin' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'VariantManager' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'ActorSequence' had been unloaded. Reloading on-demand took 0.21ms
LogConfig: Branch 'InterchangeEditor' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'LevelSequenceEditor' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'TemplateSequence' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'NNEDenoiser' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'ActorLayerUtilities' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'AndroidDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'AndroidFileServer' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'NNERuntimeORT' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'AppleImageUtils' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'AppleMoviePlayer' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'SequencerScripting' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'AssetTags' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'AudioCapture' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'AndroidMoviePlayer' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'AudioWidgets' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'AudioSynesthesia' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'ChunkDownloader' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'ArchVisCharacter' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'CustomMeshComponent' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'ExampleDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'ComputeFramework' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'GeometryCache' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'GeometryProcessing' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'GoogleCloudMessaging' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'GooglePAD' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'AndroidPermission' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'InputDebugging' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'IOSDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'LinuxDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'LocationServicesBPLibrary' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'MeshModelingToolset' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'MsQuic' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'PropertyAccessEditor' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'ProceduralMeshComponent' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'ResonanceAudio' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'SignificanceManager' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'SoundFields' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'MobilePatchingUtils' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'WaveTable' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'WebMMoviePlayer' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'WindowsDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'WindowsMoviePlayer' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'CableComponent' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'BaseCharacterFXEditor' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'LightMixer' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'ObjectMixer' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'SQLiteCore' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'PortableObjectFileDataSource' had been unloaded. Reloading on-demand took 0.28ms
LogConfig: Branch 'ConcertMain' had been unloaded. Reloading on-demand took 0.22ms
LogConfig: Branch 'XInputDevice' had been unloaded. Reloading on-demand took 0.21ms
LogConfig: Branch 'ContentBrowserAssetDataSource' had been unloaded. Reloading on-demand took 0.20ms
LogConfig: Branch 'ContentBrowserClassDataSource' had been unloaded. Reloading on-demand took 0.25ms
LogConfig: Branch 'ContentBrowserFileDataSource' had been unloaded. Reloading on-demand took 0.29ms
LogConfig: Branch 'SkeletalMeshModelingTools' had been unloaded. Reloading on-demand took 0.30ms
LogConfig: Branch 'OnlineSubsystemGooglePlay' had been unloaded. Reloading on-demand took 0.31ms
LogConfig: Branch 'OnlineSubsystemIOS' had been unloaded. Reloading on-demand took 0.25ms
LogConfig: Branch 'ConcertSyncClient' had been unloaded. Reloading on-demand took 0.27ms
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogStreaming: Display: AsyncLoading2 - Created: Event Driven Loader: false, Async Loading Thread: false, Async Post Load: false
LogStreaming: Display: AsyncLoading2 - Initialized
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: Set CVar [[r.Nanite.Streaming.AsyncCompute:0	; Temporary workaround for Nanite geometry corruption (FORT-805141)]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.DynamicRes.DynamicFrameTime:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[r.RHICmdBypass:0]]
[2025.05.31-17.40.43:064][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs:0]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.05.31-17.40.43:064][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.05.31-17.40.43:064][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.05.31-17.40.43:064][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.05.31-17.40.43:064][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.05.31-17.40.43:064][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.05.31-17.40.43:065][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.05.31-17.40.43:065][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.05.31-17.40.43:065][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.05.31-17.40.43:065][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.05.31-17.40.43:065][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.05.31-17.40.43:065][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.05.31-17.40.43:065][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.05.31-17.40.43:065][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.05.31-17.40.43:065][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.05.31-17.40.43:065][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.05.31-17.40.43:065][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.05.31-17.40.43:065][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.05.31-17.40.43:065][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.05.31-17.40.43:065][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.05.31-17.40.43:065][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.05.31-17.40.43:065][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.05.31-17.40.43:065][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.05.31-17.40.43:065][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.05.31-17.40.43:065][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.05.31-17.40.43:065][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.05.31-17.40.43:065][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.05.31-17.40.43:065][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.05.31-17.40.43:065][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.05.31-17.40.43:065][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.05.31-17.40.43:065][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.05.31-17.40.43:065][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.05.31-17.40.43:067][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:300]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:200]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.SkyLight.RealTimeReflectionCapture:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.TonemapMode:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.05.31-17.40.43:067][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:512]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:512]]
[2025.05.31-17.40.43:067][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.05.31-17.40.43:067][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.05.31-17.40.43:067][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.05.31-17.40.43:067][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.05.31-17.40.43:069][  0]LogRHI: Using Default RHI: D3D12
[2025.05.31-17.40.43:069][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.05.31-17.40.43:069][  0]LogRHI: Loading RHI module D3D12RHI
[2025.05.31-17.40.43:074][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.05.31-17.40.43:074][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.05.31-17.40.43:314][  0]LogD3D12RHI: Found D3D12 adapter 0: NVIDIA GeForce RTX 3070 (VendorId: 10de, DeviceId: 2484, SubSysId: 146b10de, Revision: 00a1
[2025.05.31-17.40.43:314][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.05.31-17.40.43:314][  0]LogD3D12RHI:   Adapter has 8018MB of dedicated video memory, 0MB of dedicated system memory, and 32731MB of shared system memory, 2 output[s]
[2025.05.31-17.40.43:315][  0]LogD3D12RHI:   Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
[2025.05.31-17.40.43:315][  0]LogD3D12RHI:      Driver Date: 5-14-2025
[2025.05.31-17.40.43:320][  0]LogD3D12RHI: Found D3D12 adapter 1: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.05.31-17.40.43:320][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.05.31-17.40.43:320][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32731MB of shared system memory, 0 output[s]
[2025.05.31-17.40.43:320][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.05.31-17.40.43:320][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.05.31-17.40.43:320][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.05.31-17.40.43:320][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.05.31-17.40.43:320][  0]LogHAL: Display: Platform has ~ 64 GB [68641980416 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.05.31-17.40.43:320][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.05.31-17.40.43:320][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.31-17.40.43:320][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.05.31-17.40.43:320][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.05.31-17.40.43:320][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.05.31-17.40.43:321][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.05.31-17.40.43:321][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.05.31-17.40.43:321][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.05.31-17.40.43:321][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.05.31-17.40.43:321][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.05.31-17.40.43:321][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.05.31-17.40.43:321][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.05.31-17.40.43:321][  0]LogConfig: Set CVar [[memory.MemoryPressureCriticalThresholdMB:512]]
[2025.05.31-17.40.43:321][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [G:/Gamedev/RoughReality/Saved/Config/WindowsEditor/Editor.ini]
[2025.05.31-17.40.43:321][  0]LogInit: Computer: DESKTOP-68ONPC8
[2025.05.31-17.40.43:321][  0]LogInit: User: julio
[2025.05.31-17.40.43:321][  0]LogInit: CPU Page size=4096, Cores=6
[2025.05.31-17.40.43:321][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.05.31-17.40.43:663][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=78.4GB
[2025.05.31-17.40.43:663][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.05.31-17.40.43:663][  0]LogMemory: Process Physical Memory: 553.61 MB used, 596.61 MB peak
[2025.05.31-17.40.43:663][  0]LogMemory: Process Virtual Memory: 570.07 MB used, 573.41 MB peak
[2025.05.31-17.40.43:663][  0]LogMemory: Physical Memory: 29125.04 MB used,  36337.06 MB free, 65462.09 MB total
[2025.05.31-17.40.43:663][  0]LogMemory: Virtual Memory: 53290.21 MB used,  27019.88 MB free, 80310.09 MB total
[2025.05.31-17.40.43:663][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.05.31-17.40.43:667][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.05.31-17.40.43:677][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.05.31-17.40.43:677][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.05.31-17.40.43:677][  0]LogInit: Using OS detected language (en-GB).
[2025.05.31-17.40.43:679][  0]LogInit: Using OS detected locale (pt-BR).
[2025.05.31-17.40.43:681][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.05.31-17.40.43:681][  0]LogInit: Setting process to per monitor DPI aware
[2025.05.31-17.40.43:993][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.05.31-17.40.43:993][  0]LogWindowsTextInputMethodSystem:   - English (United Kingdom) - (Keyboard).
[2025.05.31-17.40.43:993][  0]LogWindowsTextInputMethodSystem:   - English (United Kingdom) - (Keyboard).
[2025.05.31-17.40.43:993][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United Kingdom) - (Keyboard).
[2025.05.31-17.40.44:008][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.05.31-17.40.44:008][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.05.31-17.40.44:155][  0]LogRHI: Using Default RHI: D3D12
[2025.05.31-17.40.44:155][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.05.31-17.40.44:155][  0]LogRHI: Loading RHI module D3D12RHI
[2025.05.31-17.40.44:155][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.05.31-17.40.44:155][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.05.31-17.40.44:155][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.05.31-17.40.44:157][  0]LogWindows: Attached monitors:
[2025.05.31-17.40.44:157][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2088), device: '\\.\DISPLAY1' [PRIMARY]
[2025.05.31-17.40.44:157][  0]LogWindows:     resolution: 1920x1080, work area: (-1920, 287) -> (0, 1319), device: '\\.\DISPLAY2'
[2025.05.31-17.40.44:157][  0]LogWindows: Found 2 attached monitors.
[2025.05.31-17.40.44:157][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.05.31-17.40.44:157][  0]LogRHI: RHI Adapter Info:
[2025.05.31-17.40.44:157][  0]LogRHI:             Name: NVIDIA GeForce RTX 3070
[2025.05.31-17.40.44:157][  0]LogRHI:   Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
[2025.05.31-17.40.44:157][  0]LogRHI:      Driver Date: 5-14-2025
[2025.05.31-17.40.44:157][  0]LogD3D12RHI:     GPU DeviceId: 0x2484 (for the marketing name, search the web for "GPU Device Id")
[2025.05.31-17.40.44:157][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.05.31-17.40.44:257][  0]LogNvidiaAftermath: Aftermath initialized
[2025.05.31-17.40.44:257][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.05.31-17.40.44:344][  0]LogNvidiaAftermath: Aftermath enabled. Active feature flags: 
[2025.05.31-17.40.44:344][  0]LogNvidiaAftermath:  - Feature: EnableResourceTracking
[2025.05.31-17.40.44:344][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.05.31-17.40.44:344][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.05.31-17.40.44:344][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.05.31-17.40.44:344][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.05.31-17.40.44:344][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.05.31-17.40.44:344][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.05.31-17.40.44:344][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.05.31-17.40.44:344][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.05.31-17.40.44:344][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.05.31-17.40.44:344][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.05.31-17.40.44:344][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.05.31-17.40.44:344][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.05.31-17.40.44:344][  0]LogD3D12RHI: Bindless resources are supported
[2025.05.31-17.40.44:344][  0]LogD3D12RHI: Stencil ref from pixel shader is not supported
[2025.05.31-17.40.44:344][  0]LogD3D12RHI: Raster order views are supported
[2025.05.31-17.40.44:344][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=32).
[2025.05.31-17.40.44:344][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.05.31-17.40.44:344][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.05.31-17.40.44:344][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.05.31-17.40.44:344][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.05.31-17.40.44:344][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.05.31-17.40.44:344][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.05.31-17.40.44:344][  0]LogD3D12RHI: Work Graphs are supported
[2025.05.31-17.40.44:411][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000008481E0B0B00)
[2025.05.31-17.40.44:413][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000008481E0B0D80)
[2025.05.31-17.40.44:413][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x000008481E0B1000)
[2025.05.31-17.40.44:413][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.05.31-17.40.44:413][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.05.31-17.40.44:414][  0]LogRHI: Texture pool is 4567 MB (70% of 6525 MB)
[2025.05.31-17.40.44:414][  0]LogD3D12RHI: Async texture creation enabled
[2025.05.31-17.40.44:414][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.05.31-17.40.44:414][  0]LogD3D12RHI: HDR output is supported on adapter 0, display 0:
[2025.05.31-17.40.44:414][  0]LogD3D12RHI: 		MinLuminance = 0.392300
[2025.05.31-17.40.44:414][  0]LogD3D12RHI: 		MaxLuminance = 295.380310
[2025.05.31-17.40.44:414][  0]LogD3D12RHI: 		MaxFullFrameLuminance = 237.846802
[2025.05.31-17.40.44:414][  0]LogD3D12RHI: HDR output is supported on adapter 0, display 1:
[2025.05.31-17.40.44:414][  0]LogD3D12RHI: 		MinLuminance = 0.466400
[2025.05.31-17.40.44:414][  0]LogD3D12RHI: 		MaxLuminance = 486.111298
[2025.05.31-17.40.44:414][  0]LogD3D12RHI: 		MaxFullFrameLuminance = 486.111298
[2025.05.31-17.40.44:423][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.05.31-17.40.44:425][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.05.31-17.40.44:435][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="G:/Gamedev/RoughReality/RoughReality.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="G:/Gamedev/RoughReality/Intermediate/TurnkeyReport_0.log" -log="G:/Gamedev/RoughReality/Intermediate/TurnkeyLog_0.log" -project="G:/Gamedev/RoughReality/RoughReality.uproject"  -platform=all'
[2025.05.31-17.40.44:435][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""F:/Unreal/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="G:/Gamedev/RoughReality/RoughReality.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="G:/Gamedev/RoughReality/Intermediate/TurnkeyReport_0.log" -log="G:/Gamedev/RoughReality/Intermediate/TurnkeyLog_0.log" -project="G:/Gamedev/RoughReality/RoughReality.uproject"  -platform=all" ]
[2025.05.31-17.40.44:468][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.05.31-17.40.44:468][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.05.31-17.40.44:468][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.05.31-17.40.44:468][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.05.31-17.40.44:468][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.05.31-17.40.44:468][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.12
[2025.05.31-17.40.44:468][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.05.31-17.40.44:469][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.05.31-17.40.44:470][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.05.31-17.40.44:511][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.05.31-17.40.44:511][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.05.31-17.40.44:511][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.05.31-17.40.44:511][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.05.31-17.40.44:511][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.05.31-17.40.44:511][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.05.31-17.40.44:511][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.05.31-17.40.44:511][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.05.31-17.40.44:511][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.05.31-17.40.44:511][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.05.31-17.40.44:535][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.05.31-17.40.44:535][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.05.31-17.40.44:559][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.05.31-17.40.44:559][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.05.31-17.40.44:559][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.05.31-17.40.44:559][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.05.31-17.40.44:580][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64'
[2025.05.31-17.40.44:580][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Server'
[2025.05.31-17.40.44:580][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Client'
[2025.05.31-17.40.44:605][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.05.31-17.40.44:605][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.05.31-17.40.44:605][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.05.31-17.40.44:605][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.05.31-17.40.44:638][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.05.31-17.40.44:638][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.05.31-17.40.44:665][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.05.31-17.40.44:665][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.05.31-17.40.44:665][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.05.31-17.40.44:665][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.05.31-17.40.44:665][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.05.31-17.40.44:720][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL from hinted modules, loading all potential format modules to find it
[2025.05.31-17.40.44:723][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.05.31-17.40.44:723][  0]LogTargetPlatformManager:   SF_METAL
[2025.05.31-17.40.44:723][  0]LogTargetPlatformManager:   SF_METAL_MRT
[2025.05.31-17.40.44:723][  0]LogTargetPlatformManager:   SF_METAL_TVOS
[2025.05.31-17.40.44:723][  0]LogTargetPlatformManager:   SF_METAL_MRT_TVOS
[2025.05.31-17.40.44:723][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.05.31-17.40.44:723][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.05.31-17.40.44:723][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.05.31-17.40.44:723][  0]LogTargetPlatformManager:   SF_METAL_MACES3_1
[2025.05.31-17.40.44:723][  0]LogTargetPlatformManager:   SF_METAL_MRT_MAC
[2025.05.31-17.40.44:723][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.05.31-17.40.44:723][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.05.31-17.40.44:723][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.05.31-17.40.44:723][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.05.31-17.40.44:723][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.05.31-17.40.44:723][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.05.31-17.40.44:723][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.05.31-17.40.44:723][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.05.31-17.40.44:723][  0]LogTargetPlatformManager:   VVM_1_0
[2025.05.31-17.40.44:723][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.05.31-17.40.44:723][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.05.31-17.40.44:723][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.05.31-17.40.44:723][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.05.31-17.40.44:723][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.05.31-17.40.44:723][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.05.31-17.40.44:723][  0]LogRendererCore: Ray tracing is enabled (dynamic). Reason: r.RayTracing=1 and r.RayTracing.EnableOnDemand=1.
[2025.05.31-17.40.44:723][  0]LogRendererCore: Ray tracing shaders are enabled.
[2025.05.31-17.40.44:725][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.05.31-17.40.44:725][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file G:/Gamedev/RoughReality/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.05.31-17.40.44:725][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.05.31-17.40.44:725][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file G:/Gamedev/RoughReality/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.05.31-17.40.44:725][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.05.31-17.40.44:794][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1559 MiB)
[2025.05.31-17.40.44:794][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.05.31-17.40.44:794][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.05.31-17.40.44:794][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.05.31-17.40.44:795][  0]LogZenServiceInstance: InTree version at 'F:/Unreal/UE_5.5/Engine/Binaries/Win64/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.05.31-17.40.44:795][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.05.31-17.40.44:795][  0]LogZenServiceInstance: Found existing instance running on port 8558 matching our settings, no actions needed
[2025.05.31-17.40.44:798][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.05.31-17.40.44:798][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.003 seconds
[2025.05.31-17.40.44:799][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.05.31-17.40.44:807][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.01 seconds.
[2025.05.31-17.40.44:807][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.04ms. RandomReadSpeed=695.20MBs, RandomWriteSpeed=172.49MBs. Assigned SpeedClass 'Local'
[2025.05.31-17.40.44:808][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.05.31-17.40.44:808][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.05.31-17.40.44:808][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.05.31-17.40.44:808][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.05.31-17.40.44:808][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.05.31-17.40.44:808][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.05.31-17.40.44:808][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.05.31-17.40.44:809][  0]LogShaderCompilers: Guid format shader working directory is 22 characters bigger than the processId version (G:/Gamedev/RoughReality/Intermediate/Shaders/WorkingDirectory/41440/).
[2025.05.31-17.40.44:809][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/50C212304D3209C4536C418F764FA395/'.
[2025.05.31-17.40.44:809][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.05.31-17.40.44:809][  0]LogShaderCompilers: Display: Using Local Shader Compiler with 9 workers.
[2025.05.31-17.40.44:811][  0]LogShaderCompilers: Display: Compiling shader autogen file: G:/Gamedev/RoughReality/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.05.31-17.40.44:812][  0]LogShaderCompilers: Display: Failed to delete old shader autogen file: G:/Gamedev/RoughReality/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.05.31-17.40.44:812][  0]LogShaderCompilers: Display: Shader autogen file written: G:/Gamedev/RoughReality/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.05.31-17.40.45:854][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.05.31-17.40.46:227][  0]LogSlate: Using FreeType 2.10.0
[2025.05.31-17.40.46:227][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.05.31-17.40.46:228][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.31-17.40.46:228][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.31-17.40.46:230][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.31-17.40.46:230][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.31-17.40.46:230][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.31-17.40.46:230][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.31-17.40.46:247][  0]LogAssetRegistry: FAssetRegistry took 0.0026 seconds to start up
[2025.05.31-17.40.46:249][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.05.31-17.40.46:253][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.000s loading caches G:/Gamedev/RoughReality/Intermediate/CachedAssetRegistry_*.bin.
[2025.05.31-17.40.46:549][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.05.31-17.40.46:554][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.05.31-17.40.46:554][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.05.31-17.40.46:554][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.05.31-17.40.46:572][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.05.31-17.40.46:572][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.05.31-17.40.46:598][  0]LogDeviceProfileManager: Active device profile: [000008483B292A00][000008483B518000 66] WindowsEditor
[2025.05.31-17.40.46:598][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.05.31-17.40.46:598][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.05.31-17.40.46:601][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="G:/Gamedev/RoughReality/RoughReality.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="G:/Gamedev/RoughReality/Intermediate/TurnkeyReport_1.log" -log="G:/Gamedev/RoughReality/Intermediate/TurnkeyLog_1.log" -project="G:/Gamedev/RoughReality/RoughReality.uproject"  -Device=Win64@DESKTOP-68ONPC8'
[2025.05.31-17.40.46:601][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""F:/Unreal/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="G:/Gamedev/RoughReality/RoughReality.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="G:/Gamedev/RoughReality/Intermediate/TurnkeyReport_1.log" -log="G:/Gamedev/RoughReality/Intermediate/TurnkeyLog_1.log" -project="G:/Gamedev/RoughReality/RoughReality.uproject"  -Device=Win64@DESKTOP-68ONPC8" -nocompile -nocompileuat ]
[2025.05.31-17.40.46:638][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.31-17.40.46:639][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 18 to allow recursive sync load to finish
[2025.05.31-17.40.46:639][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.31-17.40.46:639][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.31-17.40.46:639][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 19 to allow recursive sync load to finish
[2025.05.31-17.40.46:639][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.31-17.40.46:639][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.31-17.40.46:640][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 20 to allow recursive sync load to finish
[2025.05.31-17.40.46:640][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.31-17.40.46:640][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.31-17.40.46:640][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 21 to allow recursive sync load to finish
[2025.05.31-17.40.46:640][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.31-17.40.46:640][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.31-17.40.46:640][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.31-17.40.46:641][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 23 to allow recursive sync load to finish
[2025.05.31-17.40.46:641][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.31-17.40.46:641][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.31-17.40.46:641][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 24 to allow recursive sync load to finish
[2025.05.31-17.40.46:641][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.31-17.40.46:641][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.31-17.40.46:641][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.31-17.40.46:641][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 26 to allow recursive sync load to finish
[2025.05.31-17.40.46:641][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.31-17.40.46:641][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.31-17.40.46:641][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 27 to allow recursive sync load to finish
[2025.05.31-17.40.46:641][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.31-17.40.46:642][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.31-17.40.46:642][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDiffuse with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.31-17.40.46:643][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.31-17.40.46:643][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 29 to allow recursive sync load to finish
[2025.05.31-17.40.46:643][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.05.31-17.40.46:643][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.31-17.40.46:643][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 30 to allow recursive sync load to finish
[2025.05.31-17.40.46:643][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.05.31-17.40.46:643][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.31-17.40.46:643][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 28 to allow recursive sync load to finish
[2025.05.31-17.40.46:643][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.31-17.40.46:644][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.31-17.40.46:644][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 31 to allow recursive sync load to finish
[2025.05.31-17.40.46:644][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.31-17.40.46:644][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 25 to allow recursive sync load to finish
[2025.05.31-17.40.46:644][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.31-17.40.46:644][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.31-17.40.46:644][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 32 to allow recursive sync load to finish
[2025.05.31-17.40.46:644][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.31-17.40.46:644][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 22 to allow recursive sync load to finish
[2025.05.31-17.40.46:644][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.31-17.40.46:644][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.31-17.40.46:644][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 33 to allow recursive sync load to finish
[2025.05.31-17.40.46:644][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.31-17.40.46:750][  0]LogMaterial: Display: Missing cached shadermap for WorldGridMaterial in PCD3D_SM6, Default, SM6, Editor (DDC key hash: 4f67331e8e1b6323fe931815b9a00561f7168e80), compiling. Is special engine material.
[2025.05.31-17.40.46:847][  0]LogMaterial: Display: Missing cached shadermap for DefaultDeferredDecalMaterial in PCD3D_SM6, Default, SM6, Editor (DDC key hash: 8e588fc2035f1af0bab271a8b4372e21e1038a10), compiling. 
[2025.05.31-17.40.46:869][  0]LogMaterial: Display: Missing cached shadermap for DefaultLightFunctionMaterial in PCD3D_SM6, Default, SM6, Editor (DDC key hash: 2951f2988e388320b50ad38d10919ce8d654ee1b), compiling. 
[2025.05.31-17.40.47:975][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.05.31-17.40.48:745][  0]LogMaterial: Display: Missing cached shadermap for DefaultPostProcessMaterial in PCD3D_SM6, Default, SM6, Editor (DDC key hash: fd1cc4c60b737eb6da8ea105a9d089250f4bff0a), compiling. 
[2025.05.31-17.40.48:801][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.05.31-17.40.48:801][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.05.31-17.40.48:801][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.05.31-17.40.48:801][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.05.31-17.40.48:801][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.05.31-17.40.49:046][  0]LogTurnkeySupport: Turnkey Device: Win64@DESKTOP-68ONPC8: (Name=DESKTOP-68ONPC8, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.26100.0, Flags="Device_InstallSoftwareValid")
[2025.05.31-17.40.49:122][  0]LogConfig: Branch 'PIEPreviewSettings' had been unloaded. Reloading on-demand took 1.08ms
[2025.05.31-17.40.49:142][  0]LogConfig: Branch 'GameplayTagsList' had been unloaded. Reloading on-demand took 1.21ms
[2025.05.31-17.40.49:155][  0]LogConfig: Branch 'TemplateDefs' had been unloaded. Reloading on-demand took 1.23ms
[2025.05.31-17.40.49:159][  0]LogConfig: Branch 'TemplateCategories' had been unloaded. Reloading on-demand took 1.10ms
[2025.05.31-17.40.49:325][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.05.31-17.40.49:325][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.05.31-17.40.49:330][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.05.31-17.40.49:330][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.05.31-17.40.49:331][  0]LogLiveCoding: Display: First instance in process group "UE_RoughReality_0xcfb68d21", spawning console
[2025.05.31-17.40.49:336][  0]LogLiveCoding: Display: Waiting for server
[2025.05.31-17.40.49:352][  0]LogSlate: Border
[2025.05.31-17.40.49:352][  0]LogSlate: BreadcrumbButton
[2025.05.31-17.40.49:352][  0]LogSlate: Brushes.Title
[2025.05.31-17.40.49:352][  0]LogSlate: Default
[2025.05.31-17.40.49:352][  0]LogSlate: Icons.Save
[2025.05.31-17.40.49:352][  0]LogSlate: Icons.Toolbar.Settings
[2025.05.31-17.40.49:352][  0]LogSlate: ListView
[2025.05.31-17.40.49:352][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.05.31-17.40.49:352][  0]LogSlate: SoftwareCursor_Grab
[2025.05.31-17.40.49:352][  0]LogSlate: TableView.DarkRow
[2025.05.31-17.40.49:352][  0]LogSlate: TableView.Row
[2025.05.31-17.40.49:352][  0]LogSlate: TreeView
[2025.05.31-17.40.49:471][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.05.31-17.40.49:473][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 2.790 ms
[2025.05.31-17.40.49:491][  0]LogConfig: Branch 'Mass' had been unloaded. Reloading on-demand took 2.07ms
[2025.05.31-17.40.49:521][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.05.31-17.40.49:521][  0]LogInit: XR: MultiViewport is Disabled
[2025.05.31-17.40.49:521][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.05.31-17.40.49:606][  0]LogConfig: Branch 'TranslationPickerSettings' had been unloaded. Reloading on-demand took 1.17ms
[2025.05.31-17.40.49:757][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: E9AE4C08F7A4410F800000000000C100 | Instance: B848241E4F0A77D39727EF84B4860454 (DESKTOP-68ONPC8-41440).
[2025.05.31-17.40.49:824][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.05.31-17.40.49:829][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.05.31-17.40.49:830][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.05.31-17.40.49:830][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:52544'.
[2025.05.31-17.40.49:832][  0]LogUdpMessaging: Display: Added local interface '172.31.0.1' to multicast group '230.0.0.1:6666'
[2025.05.31-17.40.49:832][  0]LogUdpMessaging: Display: Added local interface '192.168.15.2' to multicast group '230.0.0.1:6666'
[2025.05.31-17.40.50:200][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.05.31-17.40.50:201][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 3070 (Compute, Graphics)
[2025.05.31-17.40.50:201][  0]LogNNERuntimeORT: 1: Microsoft Basic Render Driver (Compute, Graphics)
[2025.05.31-17.40.50:201][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.05.31-17.40.50:318][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.05.31-17.40.50:318][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.05.31-17.40.50:331][  0]LogMetaSound: MetaSound Engine Initialized
[2025.05.31-17.40.50:504][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.05.31-17.40.51:753][  0]SourceControl: Revision control is disabled
[2025.05.31-17.40.51:771][  0]SourceControl: Revision control is disabled
[2025.05.31-17.40.51:797][  0]LogConfig: Branch 'ObjectMixerSerializedData' had been unloaded. Reloading on-demand took 1.09ms
[2025.05.31-17.40.51:807][  0]LogConfig: Branch 'Crypto' had been unloaded. Reloading on-demand took 1.07ms
[2025.05.31-17.40.51:920][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.31-17.40.51:920][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.31-17.40.51:926][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.05.31-17.40.51:947][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.05.31-17.40.51:947][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.05.31-17.40.51:993][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.05.31-17.40.52:014][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/glf/resources/plugInfo.json'
[2025.05.31-17.40.52:014][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hgiGL/resources/plugInfo.json'
[2025.05.31-17.40.52:014][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShade/resources/plugInfo.json'
[2025.05.31-17.40.52:015][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImagingGL/resources/plugInfo.json'
[2025.05.31-17.40.52:015][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hd/resources/plugInfo.json'
[2025.05.31-17.40.52:015][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdf/resources/plugInfo.json'
[2025.05.31-17.40.52:015][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMedia/resources/plugInfo.json'
[2025.05.31-17.40.52:015][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdGp/resources/plugInfo.json'
[2025.05.31-17.40.52:017][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkel/resources/plugInfo.json'
[2025.05.31-17.40.52:017][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdPhysics/resources/plugInfo.json'
[2025.05.31-17.40.52:017][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdUI/resources/plugInfo.json'
[2025.05.31-17.40.52:018][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdStorm/resources/plugInfo.json'
[2025.05.31-17.40.52:018][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdAbc/resources/plugInfo.json'
[2025.05.31-17.40.52:018][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRender/resources/plugInfo.json'
[2025.05.31-17.40.52:018][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdHydra/resources/plugInfo.json'
[2025.05.31-17.40.52:018][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdx/resources/plugInfo.json'
[2025.05.31-17.40.52:019][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hio/resources/plugInfo.json'
[2025.05.31-17.40.52:019][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdLux/resources/plugInfo.json'
[2025.05.31-17.40.52:019][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShaders/resources/plugInfo.json'
[2025.05.31-17.40.52:020][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdrGlslfx/resources/plugInfo.json'
[2025.05.31-17.40.52:020][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRiPxrImaging/resources/plugInfo.json'
[2025.05.31-17.40.52:020][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVolImaging/resources/plugInfo.json'
[2025.05.31-17.40.52:020][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProc/resources/plugInfo.json'
[2025.05.31-17.40.52:021][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/plugInfo.json'
[2025.05.31-17.40.52:021][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProcImaging/resources/plugInfo.json'
[2025.05.31-17.40.52:022][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImaging/resources/plugInfo.json'
[2025.05.31-17.40.52:022][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkelImaging/resources/plugInfo.json'
[2025.05.31-17.40.52:022][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMtlx/resources/plugInfo.json'
[2025.05.31-17.40.52:022][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVol/resources/plugInfo.json'
[2025.05.31-17.40.52:022][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ar/resources/plugInfo.json'
[2025.05.31-17.40.52:024][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdSt/resources/plugInfo.json'
[2025.05.31-17.40.52:024][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ndr/resources/plugInfo.json'
[2025.05.31-17.40.52:024][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRi/resources/plugInfo.json'
[2025.05.31-17.40.52:025][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdGeom/resources/plugInfo.json'
[2025.05.31-17.40.52:025][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/Unreal/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/codegenTemplates/plugInfo.json'
[2025.05.31-17.40.52:067][  0]LogCollectionManager: Loaded 0 collections in 0.000742 seconds
[2025.05.31-17.40.52:070][  0]LogFileCache: Scanning file cache for directory 'G:/Gamedev/RoughReality/Saved/Collections/' took 0.00s
[2025.05.31-17.40.52:085][  0]LogFileCache: Scanning file cache for directory 'G:/Gamedev/RoughReality/Content/Developers/julio/Collections/' took 0.00s
[2025.05.31-17.40.52:087][  0]LogFileCache: Scanning file cache for directory 'G:/Gamedev/RoughReality/Content/Collections/' took 0.00s
[2025.05.31-17.40.52:147][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.31-17.40.52:147][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.31-17.40.52:150][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.31-17.40.52:150][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.31-17.40.52:150][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.31-17.40.52:150][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.31-17.40.52:177][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.31-17.40.52:177][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.31-17.40.52:192][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-39599718 booting at 2025-05-31T17:40:52.192Z using C
[2025.05.31-17.40.52:192][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.26100.3912.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=RoughReality, ProductVersion=++UE5+Release-5.5-***********, IsServer=false, Flags=DisableOverlay]
[2025.05.31-17.40.52:192][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.05.31-17.40.52:192][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.05.31-17.40.52:197][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.05.31-17.40.52:197][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.05.31-17.40.52:197][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.05.31-17.40.52:197][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000049
[2025.05.31-17.40.52:197][  0]LogFab: Display: Logging in using persist
[2025.05.31-17.40.52:244][  0]LogUObjectArray: 45573 objects as part of root set at end of initial load.
[2025.05.31-17.40.52:244][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.05.31-17.40.52:255][  0]LogStreaming: Display: AsyncLoading2 - NotifyRegistrationComplete: Registered 36308 public script object entries (984.54 KB)
[2025.05.31-17.40.52:255][  0]LogStreaming: Display: AsyncLoading2 - Thread Started: false, IsInitialLoad: false
[2025.05.31-17.40.52:368][  0]LogEngine: Initializing Engine...
[2025.05.31-17.40.52:370][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.05.31-17.40.52:371][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.05.31-17.40.52:445][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.05.31-17.40.52:462][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.05.31-17.40.52:471][  0]LogNetVersion: Set ProjectVersion to *******. Version Checksum will be recalculated on next use.
[2025.05.31-17.40.52:471][  0]LogInit: Texture streaming: Enabled
[2025.05.31-17.40.52:481][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.5.4-40574608+++UE5+Release-5.5
[2025.05.31-17.40.52:485][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.05.31-17.40.52:494][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.05.31-17.40.52:495][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.05.31-17.40.52:495][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.05.31-17.40.52:495][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.05.31-17.40.52:495][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.05.31-17.40.52:495][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.05.31-17.40.52:495][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.05.31-17.40.52:495][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.05.31-17.40.52:495][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.05.31-17.40.52:495][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.05.31-17.40.52:495][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.05.31-17.40.52:495][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.05.31-17.40.52:495][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.05.31-17.40.52:495][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.05.31-17.40.52:495][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.05.31-17.40.52:501][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.05.31-17.40.52:531][  0]LogAudioMixer: Display: Using Audio Hardware Device Speakers (3- Raptor HS40)
[2025.05.31-17.40.52:532][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.05.31-17.40.52:533][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.05.31-17.40.52:533][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.05.31-17.40.52:534][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.05.31-17.40.52:534][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.05.31-17.40.52:535][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.05.31-17.40.52:535][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.05.31-17.40.52:535][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.05.31-17.40.52:535][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.05.31-17.40.52:535][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.05.31-17.40.52:542][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.05.31-17.40.52:547][  0]LogInit: Undo buffer set to 256 MB
[2025.05.31-17.40.52:547][  0]LogInit: Transaction tracking system initialized
[2025.05.31-17.40.52:636][  0]LogConfig: Branch 'LocalizationServiceSettings' had been unloaded. Reloading on-demand took 1.23ms
[2025.05.31-17.40.52:639][  0]LocalizationService: Localization service is disabled
[2025.05.31-17.40.52:652][  0]LogTimingProfiler: Initialize
[2025.05.31-17.40.52:652][  0]LogTimingProfiler: OnSessionChanged
[2025.05.31-17.40.52:652][  0]LoadingProfiler: Initialize
[2025.05.31-17.40.52:652][  0]LoadingProfiler: OnSessionChanged
[2025.05.31-17.40.52:652][  0]LogNetworkingProfiler: Initialize
[2025.05.31-17.40.52:652][  0]LogNetworkingProfiler: OnSessionChanged
[2025.05.31-17.40.52:652][  0]LogMemoryProfiler: Initialize
[2025.05.31-17.40.52:652][  0]LogMemoryProfiler: OnSessionChanged
[2025.05.31-17.40.52:834][  0]LogFileCache: Scanning file cache for directory 'G:/Gamedev/RoughReality/Content/' took 0.01s
[2025.05.31-17.40.52:860][  0]LogPython: Using Python 3.11.8
[2025.05.31-17.40.53:835][  0]LogPython: Display: No enabled plugins with python dependencies found, skipping
[2025.05.31-17.40.53:847][  0]LogRenderer: Requested compilation of Path Tracing RTPSOs (1 permutations).
[2025.05.31-17.40.53:938][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.05.31-17.40.53:938][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.05.31-17.40.53:947][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.05.31-17.40.53:998][  0]LogEditorDataStorage: Initializing
[2025.05.31-17.40.53:999][  0]LogEditorDataStorage: Initialized
[2025.05.31-17.40.54:021][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.05.31-17.40.54:025][  0]SourceControl: Revision control is disabled
[2025.05.31-17.40.54:025][  0]LogUnrealEdMisc: Loading editor; pre map load, took 12.046
[2025.05.31-17.40.54:032][  0]LogFactory: FactoryCreateFile: PackFactory with PackFactory (0 0 F:/Unreal/UE_5.5/FeaturePacks/StarterContent.upack)
[2025.05.31-17.40.54:033][  0]LogPackFactory: Finished extracting 8 files (including 0 errors).
[2025.05.31-17.40.54:033][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Architecture/Floor_400x400.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Architecture/Floor_400x400.uasset"
[2025.05.31-17.40.54:034][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Architecture/Pillar_50x500.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Architecture/Pillar_50x500.uasset"
[2025.05.31-17.40.54:050][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Architecture/SM_AssetPlatform.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Architecture/SM_AssetPlatform.uasset"
[2025.05.31-17.40.54:050][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Architecture/Wall_400x200.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Architecture/Wall_400x200.uasset"
[2025.05.31-17.40.54:051][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Architecture/Wall_400x300.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Architecture/Wall_400x300.uasset"
[2025.05.31-17.40.54:051][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Architecture/Wall_400x400.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Architecture/Wall_400x400.uasset"
[2025.05.31-17.40.54:052][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Architecture/Wall_500x500.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Architecture/Wall_500x500.uasset"
[2025.05.31-17.40.54:052][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Architecture/Wall_Door_400x300.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Architecture/Wall_Door_400x300.uasset"
[2025.05.31-17.40.54:052][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Architecture/Wall_Door_400x400.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Architecture/Wall_Door_400x400.uasset"
[2025.05.31-17.40.54:053][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Architecture/Wall_Window_400x300.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Architecture/Wall_Window_400x300.uasset"
[2025.05.31-17.40.54:053][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Architecture/Wall_Window_400x400.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Architecture/Wall_Window_400x400.uasset"
[2025.05.31-17.40.54:055][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Collapse01.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Audio/Collapse01.uasset"
[2025.05.31-17.40.54:055][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Collapse02.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Audio/Collapse02.uasset"
[2025.05.31-17.40.54:057][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Collapse_Cue.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Audio/Collapse_Cue.uasset"
[2025.05.31-17.40.54:057][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Explosion01.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Audio/Explosion01.uasset"
[2025.05.31-17.40.54:057][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Explosion02.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Audio/Explosion02.uasset"
[2025.05.31-17.40.54:060][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Explosion_Cue.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Audio/Explosion_Cue.uasset"
[2025.05.31-17.40.54:060][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Fire01.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Audio/Fire01.uasset"
[2025.05.31-17.40.54:060][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Fire01_Cue.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Audio/Fire01_Cue.uasset"
[2025.05.31-17.40.54:060][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Fire_Sparks01.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Audio/Fire_Sparks01.uasset"
[2025.05.31-17.40.54:060][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Fire_Sparks01_Cue.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Audio/Fire_Sparks01_Cue.uasset"
[2025.05.31-17.40.54:061][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Light01.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Audio/Light01.uasset"
[2025.05.31-17.40.54:061][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Blueprint_CeilingLight.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Blueprints/Blueprint_CeilingLight.uasset"
[2025.05.31-17.40.54:062][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Light01_Cue.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Audio/Light01_Cue.uasset"
[2025.05.31-17.40.54:063][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Light02.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Audio/Light02.uasset"
[2025.05.31-17.40.54:065][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Blueprint_Effect_Explosion.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Blueprints/Blueprint_Effect_Explosion.uasset"
[2025.05.31-17.40.54:065][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Light02_Cue.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Audio/Light02_Cue.uasset"
[2025.05.31-17.40.54:065][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Smoke01.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Audio/Smoke01.uasset"
[2025.05.31-17.40.54:068][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Blueprint_Effect_Fire.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Blueprints/Blueprint_Effect_Fire.uasset"
[2025.05.31-17.40.54:068][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Smoke01_Cue.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Audio/Smoke01_Cue.uasset"
[2025.05.31-17.40.54:068][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Blueprint_Effect_Smoke.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Blueprints/Blueprint_Effect_Smoke.uasset"
[2025.05.31-17.40.54:069][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Starter_Background_Cue.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Audio/Starter_Background_Cue.uasset"
[2025.05.31-17.40.54:070][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Blueprint_Effect_Sparks.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Blueprints/Blueprint_Effect_Sparks.uasset"
[2025.05.31-17.40.54:072][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Starter_Birds01.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Audio/Starter_Birds01.uasset"
[2025.05.31-17.40.54:072][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Blueprint_Effect_Steam.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Blueprints/Blueprint_Effect_Steam.uasset"
[2025.05.31-17.40.54:074][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Maps/Advanced_Lighting.umap" to "G:/Gamedev/RoughReality/Content/StarterContent/Maps/Advanced_Lighting.umap"
[2025.05.31-17.40.54:079][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Starter_Music01.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Audio/Starter_Music01.uasset"
[2025.05.31-17.40.54:080][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Blueprint_WallSconce.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Blueprints/Blueprint_WallSconce.uasset"
[2025.05.31-17.40.54:080][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Starter_Music_Cue.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Audio/Starter_Music_Cue.uasset"
[2025.05.31-17.40.54:081][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Maps/Advanced_Lighting_BuiltData.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Maps/Advanced_Lighting_BuiltData.uasset"
[2025.05.31-17.40.54:082][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/BP_LightStudio.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Blueprints/BP_LightStudio.uasset"
[2025.05.31-17.40.54:082][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Maps/Minimal_Default.umap" to "G:/Gamedev/RoughReality/Content/StarterContent/Maps/Minimal_Default.umap"
[2025.05.31-17.40.54:084][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Starter_Wind05.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Audio/Starter_Wind05.uasset"
[2025.05.31-17.40.54:087][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Starter_Wind06.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Audio/Starter_Wind06.uasset"
[2025.05.31-17.40.54:088][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Maps/Minimal_Default_BuiltData.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Maps/Minimal_Default_BuiltData.uasset"
[2025.05.31-17.40.54:089][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Steam01.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Audio/Steam01.uasset"
[2025.05.31-17.40.54:090][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Maps/StarterMap.umap" to "G:/Gamedev/RoughReality/Content/StarterContent/Maps/StarterMap.umap"
[2025.05.31-17.40.54:090][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Audio/Steam01_Cue.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Audio/Steam01_Cue.uasset"
[2025.05.31-17.40.54:121][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/HDRI/HDRI_Epic_Courtyard_Daylight.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/HDRI/HDRI_Epic_Courtyard_Daylight.uasset"
[2025.05.31-17.40.54:143][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Maps/StarterMap_BuiltData.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Maps/StarterMap_BuiltData.uasset"
[2025.05.31-17.40.54:144][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_AssetPlatform.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_AssetPlatform.uasset"
[2025.05.31-17.40.54:144][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Basic_Floor.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Basic_Floor.uasset"
[2025.05.31-17.40.54:145][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Basic_Wall.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Basic_Wall.uasset"
[2025.05.31-17.40.54:145][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Brick_Clay_Beveled.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Brick_Clay_Beveled.uasset"
[2025.05.31-17.40.54:147][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Brick_Clay_New.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Brick_Clay_New.uasset"
[2025.05.31-17.40.54:147][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Brick_Clay_Old.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Brick_Clay_Old.uasset"
[2025.05.31-17.40.54:147][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/P_Ambient_Dust.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Particles/P_Ambient_Dust.uasset"
[2025.05.31-17.40.54:148][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Brick_Cut_Stone.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Brick_Cut_Stone.uasset"
[2025.05.31-17.40.54:148][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/P_Explosion.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Particles/P_Explosion.uasset"
[2025.05.31-17.40.54:149][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Brick_Hewn_Stone.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Brick_Hewn_Stone.uasset"
[2025.05.31-17.40.54:150][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/P_Fire.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Particles/P_Fire.uasset"
[2025.05.31-17.40.54:150][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Ceramic_Tile_Checker.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Ceramic_Tile_Checker.uasset"
[2025.05.31-17.40.54:152][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/P_Smoke.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Particles/P_Smoke.uasset"
[2025.05.31-17.40.54:152][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_CobbleStone_Pebble.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_CobbleStone_Pebble.uasset"
[2025.05.31-17.40.54:154][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/P_Sparks.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Particles/P_Sparks.uasset"
[2025.05.31-17.40.54:154][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_CobbleStone_Rough.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_CobbleStone_Rough.uasset"
[2025.05.31-17.40.54:154][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/P_Steam_Lit.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Particles/P_Steam_Lit.uasset"
[2025.05.31-17.40.54:154][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_CobbleStone_Smooth.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_CobbleStone_Smooth.uasset"
[2025.05.31-17.40.54:156][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_ColorGrid_LowSpec.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_ColorGrid_LowSpec.uasset"
[2025.05.31-17.40.54:156][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Concrete_Grime.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Concrete_Grime.uasset"
[2025.05.31-17.40.54:156][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Concrete_Panels.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Concrete_Panels.uasset"
[2025.05.31-17.40.54:156][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Concrete_Poured.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Concrete_Poured.uasset"
[2025.05.31-17.40.54:161][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Concrete_Tiles.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Concrete_Tiles.uasset"
[2025.05.31-17.40.54:161][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Glass.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Glass.uasset"
[2025.05.31-17.40.54:161][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Ground_Grass.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Ground_Grass.uasset"
[2025.05.31-17.40.54:161][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Ground_Gravel.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Ground_Gravel.uasset"
[2025.05.31-17.40.54:161][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Ground_Moss.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Ground_Moss.uasset"
[2025.05.31-17.40.54:162][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Metal_Brushed_Nickel.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Metal_Brushed_Nickel.uasset"
[2025.05.31-17.40.54:162][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Metal_Burnished_Steel.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Metal_Burnished_Steel.uasset"
[2025.05.31-17.40.54:162][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Metal_Chrome.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Metal_Chrome.uasset"
[2025.05.31-17.40.54:167][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Metal_Copper.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Metal_Copper.uasset"
[2025.05.31-17.40.54:169][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Metal_Gold.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Metal_Gold.uasset"
[2025.05.31-17.40.54:169][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Metal_Rust.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Metal_Rust.uasset"
[2025.05.31-17.40.54:170][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/MaterialSphere.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/MaterialSphere.uasset"
[2025.05.31-17.40.54:170][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_Bush.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/SM_Bush.uasset"
[2025.05.31-17.40.54:171][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_Chair.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/SM_Chair.uasset"
[2025.05.31-17.40.54:172][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Metal_Steel.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Metal_Steel.uasset"
[2025.05.31-17.40.54:172][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Rock_Basalt.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Rock_Basalt.uasset"
[2025.05.31-17.40.54:173][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_CornerFrame.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/SM_CornerFrame.uasset"
[2025.05.31-17.40.54:173][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Rock_Marble_Polished.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Rock_Marble_Polished.uasset"
[2025.05.31-17.40.54:174][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_Couch.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/SM_Couch.uasset"
[2025.05.31-17.40.54:175][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Rock_Sandstone.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Rock_Sandstone.uasset"
[2025.05.31-17.40.54:176][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_Door.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/SM_Door.uasset"
[2025.05.31-17.40.54:176][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Rock_Slate.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Rock_Slate.uasset"
[2025.05.31-17.40.54:177][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Tech_Checker_Dot.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Tech_Checker_Dot.uasset"
[2025.05.31-17.40.54:177][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_DoorFrame.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/SM_DoorFrame.uasset"
[2025.05.31-17.40.54:177][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Tech_Hex_Tile.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Tech_Hex_Tile.uasset"
[2025.05.31-17.40.54:179][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_GlassWindow.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/SM_GlassWindow.uasset"
[2025.05.31-17.40.54:180][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Tech_Hex_Tile_Pulse.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Tech_Hex_Tile_Pulse.uasset"
[2025.05.31-17.40.54:180][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Tech_Panel.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Tech_Panel.uasset"
[2025.05.31-17.40.54:181][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_Lamp_Ceiling.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/SM_Lamp_Ceiling.uasset"
[2025.05.31-17.40.54:181][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Water_Lake.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Water_Lake.uasset"
[2025.05.31-17.40.54:181][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_Lamp_Wall.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/SM_Lamp_Wall.uasset"
[2025.05.31-17.40.54:182][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Water_Ocean.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Water_Ocean.uasset"
[2025.05.31-17.40.54:182][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Wood_Floor_Walnut_Polished.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Wood_Floor_Walnut_Polished.uasset"
[2025.05.31-17.40.54:183][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_PillarFrame.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/SM_PillarFrame.uasset"
[2025.05.31-17.40.54:183][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Wood_Floor_Walnut_Worn.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Wood_Floor_Walnut_Worn.uasset"
[2025.05.31-17.40.54:184][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Wood_Oak.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Wood_Oak.uasset"
[2025.05.31-17.40.54:184][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_PillarFrame300.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/SM_PillarFrame300.uasset"
[2025.05.31-17.40.54:185][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Wood_Pine.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Wood_Pine.uasset"
[2025.05.31-17.40.54:185][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_Rock.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/SM_Rock.uasset"
[2025.05.31-17.40.54:186][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Materials/M_Wood_Walnut.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Materials/M_Wood_Walnut.uasset"
[2025.05.31-17.40.54:186][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Cone.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Shapes/Shape_Cone.uasset"
[2025.05.31-17.40.54:187][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_Shelf.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/SM_Shelf.uasset"
[2025.05.31-17.40.54:190][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_Stairs.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/SM_Stairs.uasset"
[2025.05.31-17.40.54:191][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Cube.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Shapes/Shape_Cube.uasset"
[2025.05.31-17.40.54:191][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_Statue.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/SM_Statue.uasset"
[2025.05.31-17.40.54:194][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Cylinder.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Shapes/Shape_Cylinder.uasset"
[2025.05.31-17.40.54:195][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_TableRound.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/SM_TableRound.uasset"
[2025.05.31-17.40.54:195][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/SM_WindowFrame.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/SM_WindowFrame.uasset"
[2025.05.31-17.40.54:197][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_NarrowCapsule.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Shapes/Shape_NarrowCapsule.uasset"
[2025.05.31-17.40.54:197][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Pipe.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Shapes/Shape_Pipe.uasset"
[2025.05.31-17.40.54:200][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Pipe_180.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Shapes/Shape_Pipe_180.uasset"
[2025.05.31-17.40.54:200][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Pipe_90.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Shapes/Shape_Pipe_90.uasset"
[2025.05.31-17.40.54:201][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Plane.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Shapes/Shape_Plane.uasset"
[2025.05.31-17.40.54:201][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_QuadPyramid.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Shapes/Shape_QuadPyramid.uasset"
[2025.05.31-17.40.54:201][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Sphere.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Shapes/Shape_Sphere.uasset"
[2025.05.31-17.40.54:201][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Torus.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Shapes/Shape_Torus.uasset"
[2025.05.31-17.40.54:201][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Trim.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Shapes/Shape_Trim.uasset"
[2025.05.31-17.40.54:201][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Trim_90_In.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Shapes/Shape_Trim_90_In.uasset"
[2025.05.31-17.40.54:202][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Trim_90_Out.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Shapes/Shape_Trim_90_Out.uasset"
[2025.05.31-17.40.54:208][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Clay_Beveled_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Brick_Clay_Beveled_D.uasset"
[2025.05.31-17.40.54:208][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_TriPyramid.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Shapes/Shape_TriPyramid.uasset"
[2025.05.31-17.40.54:218][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Clay_Beveled_M.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Brick_Clay_Beveled_M.uasset"
[2025.05.31-17.40.54:218][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Tube.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Shapes/Shape_Tube.uasset"
[2025.05.31-17.40.54:224][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Clay_Beveled_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Brick_Clay_Beveled_N.uasset"
[2025.05.31-17.40.54:225][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Wedge_A.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Shapes/Shape_Wedge_A.uasset"
[2025.05.31-17.40.54:229][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Clay_New_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Brick_Clay_New_D.uasset"
[2025.05.31-17.40.54:230][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_Wedge_B.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Shapes/Shape_Wedge_B.uasset"
[2025.05.31-17.40.54:234][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Clay_New_M.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Brick_Clay_New_M.uasset"
[2025.05.31-17.40.54:238][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Clay_New_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Brick_Clay_New_N.uasset"
[2025.05.31-17.40.54:238][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Shapes/Shape_WideCapsule.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Shapes/Shape_WideCapsule.uasset"
[2025.05.31-17.40.54:244][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Clay_Old_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Brick_Clay_Old_D.uasset"
[2025.05.31-17.40.54:245][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.05.31-17.40.54:248][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Clay_Old_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Brick_Clay_Old_N.uasset"
[2025.05.31-17.40.54:255][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Cut_Stone_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Brick_Cut_Stone_D.uasset"
[2025.05.31-17.40.54:258][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Cut_Stone_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Brick_Cut_Stone_N.uasset"
[2025.05.31-17.40.54:262][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Hewn_Stone_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Brick_Hewn_Stone_D.uasset"
[2025.05.31-17.40.54:265][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Hewn_Stone_M.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Brick_Hewn_Stone_M.uasset"
[2025.05.31-17.40.54:268][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Brick_Hewn_Stone_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Brick_Hewn_Stone_N.uasset"
[2025.05.31-17.40.54:288][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Burst_M.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Burst_M.uasset"
[2025.05.31-17.40.54:289][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Bush_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Bush_D.uasset"
[2025.05.31-17.40.54:289][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Bush_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Bush_N.uasset"
[2025.05.31-17.40.54:290][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Ceramic_Tile_M.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Ceramic_Tile_M.uasset"
[2025.05.31-17.40.54:290][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Ceramic_Tile_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Ceramic_Tile_N.uasset"
[2025.05.31-17.40.54:291][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Chair_M.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Chair_M.uasset"
[2025.05.31-17.40.54:293][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Chair_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Chair_N.uasset"
[2025.05.31-17.40.54:293][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Checker_Noise_M.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Checker_Noise_M.uasset"
[2025.05.31-17.40.54:299][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_CobbleStone_Pebble_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_CobbleStone_Pebble_D.uasset"
[2025.05.31-17.40.54:300][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_CobbleStone_Pebble_M.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_CobbleStone_Pebble_M.uasset"
[2025.05.31-17.40.54:305][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_CobbleStone_Pebble_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_CobbleStone_Pebble_N.uasset"
[2025.05.31-17.40.54:307][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_CobbleStone_Rough_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_CobbleStone_Rough_D.uasset"
[2025.05.31-17.40.54:308][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_CobbleStone_Rough_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_CobbleStone_Rough_N.uasset"
[2025.05.31-17.40.54:311][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_CobbleStone_Smooth_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_CobbleStone_Smooth_D.uasset"
[2025.05.31-17.40.54:314][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_CobbleStone_Smooth_M.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_CobbleStone_Smooth_M.uasset"
[2025.05.31-17.40.54:335][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_CobbleStone_Smooth_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_CobbleStone_Smooth_N.uasset"
[2025.05.31-17.40.54:340][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Concrete_Grime_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Concrete_Grime_D.uasset"
[2025.05.31-17.40.54:343][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Concrete_Panels_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Concrete_Panels_D.uasset"
[2025.05.31-17.40.54:347][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Concrete_Panels_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Concrete_Panels_N.uasset"
[2025.05.31-17.40.54:351][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Concrete_Poured_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Concrete_Poured_D.uasset"
[2025.05.31-17.40.54:354][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Concrete_Poured_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Concrete_Poured_N.uasset"
[2025.05.31-17.40.54:357][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Concrete_Tiles_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Concrete_Tiles_D.uasset"
[2025.05.31-17.40.54:360][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Concrete_Tiles_M.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Concrete_Tiles_M.uasset"
[2025.05.31-17.40.54:363][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Concrete_Tiles_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Concrete_Tiles_N.uasset"
[2025.05.31-17.40.54:377][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Concrete_Tiles_Variation_M.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Concrete_Tiles_Variation_M.uasset"
[2025.05.31-17.40.54:379][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Detail_Rocky_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Detail_Rocky_N.uasset"
[2025.05.31-17.40.54:379][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Door_M.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Door_M.uasset"
[2025.05.31-17.40.54:380][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Door_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Door_N.uasset"
[2025.05.31-17.40.54:381][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Dust_Particle_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Dust_Particle_D.uasset"
[2025.05.31-17.40.54:383][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Explosion_SubUV.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Explosion_SubUV.uasset"
[2025.05.31-17.40.54:384][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Fire_SubUV.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Fire_SubUV.uasset"
[2025.05.31-17.40.54:385][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Fire_Tiled_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Fire_Tiled_D.uasset"
[2025.05.31-17.40.54:387][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Frame_M.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Frame_M.uasset"
[2025.05.31-17.40.54:388][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Frame_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Frame_N.uasset"
[2025.05.31-17.40.54:388][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Gradinet_01.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Gradinet_01.uasset"
[2025.05.31-17.40.54:395][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Ground_Grass_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Ground_Grass_D.uasset"
[2025.05.31-17.40.54:403][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Ground_Grass_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Ground_Grass_N.uasset"
[2025.05.31-17.40.54:409][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Ground_Gravel_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Ground_Gravel_D.uasset"
[2025.05.31-17.40.54:412][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Ground_Gravel_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Ground_Gravel_N.uasset"
[2025.05.31-17.40.54:417][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_ground_Moss_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_ground_Moss_D.uasset"
[2025.05.31-17.40.54:422][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Ground_Moss_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Ground_Moss_N.uasset"
[2025.05.31-17.40.54:423][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Lamp_M.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Lamp_M.uasset"
[2025.05.31-17.40.54:424][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Lamp_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Lamp_N.uasset"
[2025.05.31-17.40.54:429][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_MacroVariation.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_MacroVariation.uasset"
[2025.05.31-17.40.54:433][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Metal_Aluminum_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Metal_Aluminum_D.uasset"
[2025.05.31-17.40.54:436][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Metal_Copper_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Metal_Copper_D.uasset"
[2025.05.31-17.40.54:440][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Metal_Gold_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Metal_Gold_D.uasset"
[2025.05.31-17.40.54:442][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Metal_Gold_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Metal_Gold_N.uasset"
[2025.05.31-17.40.54:446][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Metal_Rust_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Metal_Rust_D.uasset"
[2025.05.31-17.40.54:450][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Metal_Rust_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Metal_Rust_N.uasset"
[2025.05.31-17.40.54:451][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Metal_Steel_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Metal_Steel_D.uasset"
[2025.05.31-17.40.54:453][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Metal_Steel_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Metal_Steel_N.uasset"
[2025.05.31-17.40.54:457][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Perlin_Noise_M.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Perlin_Noise_M.uasset"
[2025.05.31-17.40.54:459][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_RockMesh_M.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_RockMesh_M.uasset"
[2025.05.31-17.40.54:461][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_RockMesh_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_RockMesh_N.uasset"
[2025.05.31-17.40.54:465][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Rock_Basalt_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Rock_Basalt_D.uasset"
[2025.05.31-17.40.54:469][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Rock_Basalt_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Rock_Basalt_N.uasset"
[2025.05.31-17.40.54:471][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Rock_Marble_Polished_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Rock_Marble_Polished_D.uasset"
[2025.05.31-17.40.54:475][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Rock_Sandstone_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Rock_Sandstone_D.uasset"
[2025.05.31-17.40.54:479][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Rock_Sandstone_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Rock_Sandstone_N.uasset"
[2025.05.31-17.40.54:484][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Rock_Slate_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Rock_Slate_D.uasset"
[2025.05.31-17.40.54:487][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Rock_Slate_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Rock_Slate_N.uasset"
[2025.05.31-17.40.54:492][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Rock_Smooth_Granite_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Rock_Smooth_Granite_D.uasset"
[2025.05.31-17.40.54:493][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Shelf_M.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Shelf_M.uasset"
[2025.05.31-17.40.54:494][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Shelf_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Shelf_N.uasset"
[2025.05.31-17.40.54:494][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Single_Tile_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Single_Tile_N.uasset"
[2025.05.31-17.40.54:495][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Smoke_SubUV.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Smoke_SubUV.uasset"
[2025.05.31-17.40.54:496][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Smoke_Tiled_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Smoke_Tiled_D.uasset"
[2025.05.31-17.40.54:496][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Spark_Core.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Spark_Core.uasset"
[2025.05.31-17.40.54:499][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Statue_M.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Statue_M.uasset"
[2025.05.31-17.40.54:500][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Statue_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Statue_N.uasset"
[2025.05.31-17.40.54:500][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_TableRound_M.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_TableRound_M.uasset"
[2025.05.31-17.40.54:551][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_TableRound_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_TableRound_N.uasset"
[2025.05.31-17.40.54:551][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Tech_Dot_M.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Tech_Dot_M.uasset"
[2025.05.31-17.40.54:551][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Tech_Dot_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Tech_Dot_N.uasset"
[2025.05.31-17.40.54:552][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Tech_Hex_Tile_M.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Tech_Hex_Tile_M.uasset"
[2025.05.31-17.40.54:553][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Tech_Hex_Tile_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Tech_Hex_Tile_N.uasset"
[2025.05.31-17.40.54:554][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Tech_Panel_M.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Tech_Panel_M.uasset"
[2025.05.31-17.40.54:555][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Tech_Panel_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Tech_Panel_N.uasset"
[2025.05.31-17.40.54:557][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Water_M.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Water_M.uasset"
[2025.05.31-17.40.54:559][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Water_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Water_N.uasset"
[2025.05.31-17.40.54:561][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Wood_Floor_Walnut_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Wood_Floor_Walnut_D.uasset"
[2025.05.31-17.40.54:563][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Wood_Floor_Walnut_M.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Wood_Floor_Walnut_M.uasset"
[2025.05.31-17.40.54:565][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Wood_Floor_Walnut_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Wood_Floor_Walnut_N.uasset"
[2025.05.31-17.40.54:568][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Wood_Oak_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Wood_Oak_D.uasset"
[2025.05.31-17.40.54:571][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Wood_Oak_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Wood_Oak_N.uasset"
[2025.05.31-17.40.54:575][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Wood_Pine_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Wood_Pine_D.uasset"
[2025.05.31-17.40.54:578][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Wood_Pine_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Wood_Pine_N.uasset"
[2025.05.31-17.40.54:594][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Wood_Walnut_D.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Wood_Walnut_D.uasset"
[2025.05.31-17.40.54:597][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Textures/T_Wood_Walnut_N.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Textures/T_Wood_Walnut_N.uasset"
[2025.05.31-17.40.54:598][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/Materials/M_Bush.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/Materials/M_Bush.uasset"
[2025.05.31-17.40.54:598][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/Materials/M_Chair.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/Materials/M_Chair.uasset"
[2025.05.31-17.40.54:598][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/Materials/M_Door.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/Materials/M_Door.uasset"
[2025.05.31-17.40.54:599][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/Materials/M_Frame.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/Materials/M_Frame.uasset"
[2025.05.31-17.40.54:599][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/Materials/M_Lamp.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/Materials/M_Lamp.uasset"
[2025.05.31-17.40.54:600][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/Materials/M_Rock.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/Materials/M_Rock.uasset"
[2025.05.31-17.40.54:600][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/Materials/M_Shelf.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/Materials/M_Shelf.uasset"
[2025.05.31-17.40.54:600][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/Materials/M_Statue.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/Materials/M_Statue.uasset"
[2025.05.31-17.40.54:601][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/Materials/M_StatueGlass.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/Materials/M_StatueGlass.uasset"
[2025.05.31-17.40.54:602][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Props/Materials/M_TableRound.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Props/Materials/M_TableRound.uasset"
[2025.05.31-17.40.54:602][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Assets/FogBrightnessLUT.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Blueprints/Assets/FogBrightnessLUT.uasset"
[2025.05.31-17.40.54:604][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Assets/M_LightStage_Arrows.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Blueprints/Assets/M_LightStage_Arrows.uasset"
[2025.05.31-17.40.54:605][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Assets/M_LightStage_Skybox_Black.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Blueprints/Assets/M_LightStage_Skybox_Black.uasset"
[2025.05.31-17.40.54:605][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Assets/M_LightStage_Skybox_HDRI.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Blueprints/Assets/M_LightStage_Skybox_HDRI.uasset"
[2025.05.31-17.40.54:605][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Assets/M_LightStage_Skybox_Master.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Blueprints/Assets/M_LightStage_Skybox_Master.uasset"
[2025.05.31-17.40.54:607][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Assets/Skybox.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Blueprints/Assets/Skybox.uasset"
[2025.05.31-17.40.54:608][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Assets/SM_Arrows.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Blueprints/Assets/SM_Arrows.uasset"
[2025.05.31-17.40.54:608][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/Materials/M_Burst.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Particles/Materials/M_Burst.uasset"
[2025.05.31-17.40.54:609][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Blueprints/Assets/SunlightColorLUT.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Blueprints/Assets/SunlightColorLUT.uasset"
[2025.05.31-17.40.54:609][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/Materials/M_Dust_Particle.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Particles/Materials/M_Dust_Particle.uasset"
[2025.05.31-17.40.54:610][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/Materials/M_explosion_subUV.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Particles/Materials/M_explosion_subUV.uasset"
[2025.05.31-17.40.54:610][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/Materials/M_Fire_SubUV.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Particles/Materials/M_Fire_SubUV.uasset"
[2025.05.31-17.40.54:611][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/Materials/m_flare_01.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Particles/Materials/m_flare_01.uasset"
[2025.05.31-17.40.54:611][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/Materials/M_Heat_Distortion.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Particles/Materials/M_Heat_Distortion.uasset"
[2025.05.31-17.40.54:612][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/Materials/M_Radial_Gradient.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Particles/Materials/M_Radial_Gradient.uasset"
[2025.05.31-17.40.54:612][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/Materials/M_radial_ramp.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Particles/Materials/M_radial_ramp.uasset"
[2025.05.31-17.40.54:613][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/Materials/M_smoke_subUV.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Particles/Materials/M_smoke_subUV.uasset"
[2025.05.31-17.40.54:614][  0]LogPackFactory: Copied "../../../Samples/StarterContent/Content/StarterContent/Particles/Materials/M_Spark.uasset" to "G:/Gamedev/RoughReality/Content/StarterContent/Particles/Materials/M_Spark.uasset"
[2025.05.31-17.40.54:737][  0]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.05.31-17.40.54:739][  0]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/StarterContent/Architecture/Floor_400x400] ([1] browsable assets)...
[2025.05.31-17.40.54:739][  0]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.05.31-17.40.54:747][  0]LogNNEDenoiser: ApplySettings: bDenoiserEnabled 1
[2025.05.31-17.40.54:750][  0]LogNNEDenoiser: Loaded input mapping from NNEDIM_ColorAlbedoNormal_Alpha
[2025.05.31-17.40.54:750][  0]LogNNEDenoiser: Loaded output mapping from NNEDOM_Output_Alpha
[2025.05.31-17.40.54:750][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.05.31-17.40.54:799][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.05.31-17.40.54:799][  0]LogNNEDenoiser: Create denoiser from asset /NNEDenoiser/NNED_Oidn2-3_Balanced_Alpha.NNED_Oidn2-3_Balanced_Alpha...
[2025.05.31-17.40.54:800][  0]LogNNEDenoiser: Loaded input mapping from NNEDTIM_ColorAlbedoNormal_Alpha
[2025.05.31-17.40.54:800][  0]LogNNEDenoiser: Loaded output mapping from NNEDTOM_Output_Alpha
[2025.05.31-17.40.54:800][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.05.31-17.40.54:845][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.05.31-17.40.54:846][  0]LogNNEDenoiser: Create temporal denoiser from asset /NNEDenoiser/NNEDT_Oidn2-3_Balanced_Alpha.NNEDT_Oidn2-3_Balanced_Alpha...
[2025.05.31-17.40.54:870][  0]LogAssetRegistry: Display: Asset registry cache written as 37.9 MiB to G:/Gamedev/RoughReality/Intermediate/CachedAssetRegistry_*.bin
[2025.05.31-17.40.54:998][  0]OBJ SavePackage:     Rendered thumbnail for [StaticMesh /Game/StarterContent/Architecture/Floor_400x400.Floor_400x400]
[2025.05.31-17.40.54:998][  0]OBJ SavePackage: Finished generating thumbnails for package [/Game/StarterContent/Architecture/Floor_400x400]
[2025.05.31-17.40.54:998][  0]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/StarterContent/Architecture/Floor_400x400" FILE="G:/Gamedev/RoughReality/Content/StarterContent/Architecture/Floor_400x400.uasset" SILENT=true
[2025.05.31-17.40.55:000][  0]LogSavePackage: Moving output files for package: /Game/StarterContent/Architecture/Floor_400x400
[2025.05.31-17.40.55:001][  0]LogSavePackage: Moving 'G:/Gamedev/RoughReality/Saved/Floor_400x400D836ACD64C9D2A069EF1AEB80907A843.tmp' to 'G:/Gamedev/RoughReality/Content/StarterContent/Architecture/Floor_400x400.uasset'
[2025.05.31-17.40.55:002][  0]LogFileHelpers: InternalPromptForCheckoutAndSave took 263.974 ms
[2025.05.31-17.40.55:002][  0]LogFeaturePack: Inserted 1 feature packs
[2025.05.31-17.40.55:075][  0]Cmd: MAP LOAD FILE="G:/Gamedev/RoughReality/Content/ThirdPerson/Maps/ThirdPersonMap.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.05.31-17.40.55:078][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.05.31-17.40.55:078][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.31-17.40.55:087][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.05.31-17.40.55:088][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.76ms
[2025.05.31-17.40.55:097][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'ThirdPersonMap'.
[2025.05.31-17.40.55:097][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world ThirdPersonMap
[2025.05.31-17.40.55:098][  0]LogWorldPartition: ULevel::OnLevelLoaded(ThirdPersonMap)(bIsOwningWorldGameWorld=0, bIsOwningWorldPartitioned=1, InitializeForMainWorld=1, InitializeForEditor=1, InitializeForGame=0)
[2025.05.31-17.40.55:098][  0]LogWorldPartition: Display: WorldPartition initialize started...
[2025.05.31-17.40.55:098][  0]LogWorldPartition: UWorldPartition::Initialize : World = /Game/ThirdPerson/Maps/ThirdPersonMap.ThirdPersonMap, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.05.31-17.40.55:140][  0]LogWorldPartition: Display: WorldPartition initialize took 41.619 ms
[2025.05.31-17.40.55:145][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.05.31-17.40.55:155][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.54ms
[2025.05.31-17.40.55:155][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.05.31-17.40.55:157][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 0,516ms to complete.
[2025.05.31-17.40.55:162][  0]LogUnrealEdMisc: Total Editor Startup Time, took 13.183
[2025.05.31-17.40.55:289][  0]LogActorFactory: Loading ActorFactory Class /Script/Engine.LevelInstance
[2025.05.31-17.40.55:309][  0]LogSlate: The tab "LevelEditorToolBar" attempted to spawn in layout 'LevelEditor_Layout_v1.8' but failed for some reason. It will not be displayed.
[2025.05.31-17.40.55:349][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.31-17.40.55:387][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.31-17.40.55:423][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.31-17.40.55:460][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.31-17.40.55:470][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.31-17.40.55:471][  0]LogPakFile: Display: Mounted Pak file 'F:/Unreal/UE_5.5/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.05.31-17.40.55:471][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.31-17.40.55:471][  0]LogPakFile: Display: Mounted Pak file 'F:/Unreal/UE_5.5/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.05.31-17.40.55:472][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.31-17.40.55:472][  0]LogPakFile: Display: Mounted Pak file 'F:/Unreal/UE_5.5/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.05.31-17.40.55:472][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.31-17.40.55:472][  0]LogPakFile: Display: Mounted Pak file 'F:/Unreal/UE_5.5/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.05.31-17.40.55:473][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.31-17.40.55:473][  0]LogPakFile: Display: Mounted Pak file 'F:/Unreal/UE_5.5/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.05.31-17.40.55:473][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.31-17.40.55:473][  0]LogPakFile: Display: Mounted Pak file 'F:/Unreal/UE_5.5/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.05.31-17.40.55:473][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.31-17.40.55:474][  0]LogPakFile: Display: Mounted Pak file 'F:/Unreal/UE_5.5/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.05.31-17.40.55:474][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.31-17.40.55:474][  0]LogPakFile: Display: Mounted Pak file 'F:/Unreal/UE_5.5/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.05.31-17.40.55:474][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.31-17.40.55:474][  0]LogPakFile: Display: Mounted Pak file 'F:/Unreal/UE_5.5/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.05.31-17.40.55:475][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.31-17.40.55:475][  0]LogPakFile: Display: Mounted Pak file 'F:/Unreal/UE_5.5/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.05.31-17.40.55:611][  0]LogSlate: Took 0.000204 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.05.31-17.40.55:615][  0]LogSlate: Took 0.000143 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.05.31-17.40.55:617][  0]LogSlate: Took 0.000153 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.05.31-17.40.55:617][  0]LogSlate: Took 0.000109 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.05.31-17.40.55:678][  0]LogSlate: Took 0.000203 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.05.31-17.40.55:779][  0]LogD3D12RHI: Creating RTPSO with 21 shaders (0 cached, 21 new) took 72.82 ms. Compile time 45.14 ms, link time 27.58 ms.
[2025.05.31-17.40.55:891][  0]LogStall: Startup...
[2025.05.31-17.40.55:902][  0]LogStall: Startup complete.
[2025.05.31-17.40.55:935][  0]LogLoad: (Engine Initialization) Total time: 13.96 seconds
[2025.05.31-17.40.56:520][  0]LogContentValidation: Display: Starting to validate 1 assets
[2025.05.31-17.40.56:520][  0]LogContentValidation: Enabled validators:
[2025.05.31-17.40.56:520][  0]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.05.31-17.40.56:520][  0]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.05.31-17.40.56:520][  0]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.05.31-17.40.56:520][  0]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.05.31-17.40.56:520][  0]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.05.31-17.40.56:520][  0]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.05.31-17.40.56:520][  0]AssetCheck: /Game/StarterContent/Architecture/Floor_400x400 Validating asset
[2025.05.31-17.40.56:565][  0]LogAssetRegistry: AssetRegistryGather time 0.7917s: AssetDataDiscovery 0.0326s, AssetDataGather 0.7043s, StoreResults 0.0548s. Wall time 10.3200s.
	NumCachedDirectories 0. NumUncachedDirectories 1624. NumCachedFiles 0. NumUncachedFiles 7964.
	BackgroundTickInterruptions 0.
[2025.05.31-17.40.56:587][  0]LogSourceControl: Uncontrolled asset enumeration started...
[2025.05.31-17.40.56:587][  0]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.05.31-17.40.56:588][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.05.31-17.40.56:588][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.05.31-17.40.56:632][  0]LogAutomationController: Ignoring very large delta of 17227469.16 seconds in calls to FAutomationControllerManager::Tick() and not penalizing unresponsive tests
[2025.05.31-17.40.56:632][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.31-17.40.56:635][  0]LogPython: Display: Running start-up script F:/Unreal/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.05.31-17.40.56:665][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.05.31-17.40.56:884][  0]LogPython: Display: Running start-up script F:/Unreal/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 249.113 ms
[2025.05.31-17.40.56:938][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: RoughRealityEditor Win64 Development
[2025.05.31-17.40.57:003][  1]LogSourceControl: Uncontrolled asset enumeration finished in 0.416484 seconds (Found 7672 uncontrolled assets)
[2025.05.31-17.40.57:863][  9]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 5.333396
[2025.05.31-17.40.57:864][  9]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.05.31-17.40.57:865][  9]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 5.666637
[2025.05.31-17.40.59:198][ 13]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.05.31-17.40.59:864][ 15]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 7.334307
[2025.05.31-17.40.59:865][ 15]LogEOSSDK: LogEOS: SDK Config Data - Watermark: *********
[2025.05.31-17.40.59:865][ 15]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 7.334307, Update Interval: 341.068146
[2025.05.31-17.41.00:864][ 18]LogEOSSDK: LogEOSAuth: UpdateUserAuthToken: User ClientId: xyz...459 AccountId: c7c...b04 Access[Expires: 2025.05.31-19.40.57 Remaining: 7200.84] Refresh[Expires: 2025-11-27T13:48:26.366Z Remaining: ********.37] State: Valid
[2025.05.31-17.41.01:198][ 19]LogEOSSDK: LogEOSAuth: UserAuthGenerated: Received FUserAuthToken
[2025.05.31-17.41.02:198][ 22]LogEOSSDK: LogEOSAuth: UserAuthGenerated: Login complete: EOS_Success
[2025.05.31-17.41.02:198][ 22]LogFab: Display: User logged in
[2025.05.31-17.41.02:198][ 22]LogFab: Display: User client id: xyza7891REBVsEqSJRRNXmlS7EQHM459
[2025.05.31-17.41.02:864][ 24]LogEOSSDK: Warning: LogEOS: Error response received from backend. ServiceName=[Friend], OperationName=[GetBlockList], Url=[<Redacted>], HttpStatus=[403], ErrorCode=[errors.com.epicgames.common.insufficient_scopes], NumericErrorCode=[1056], ErrorMessage=[Insufficient access scopes. Expected: [friends_list]], CorrId=[EOS-FAKmUgLJLUqosHARbSnKQw-ZQ8V7X36sEyXLiBufOWZog]
[2025.05.31-17.41.12:737][148]LogUObjectHash: Compacting FUObjectHashTables data took   0.50ms
[2025.05.31-17.41.12:819][148]LogStall: Shutdown...
[2025.05.31-17.41.12:819][148]LogStall: Shutdown complete.
[2025.05.31-17.41.12:840][148]LogSlate: Window 'RoughReality - Unreal Editor' being destroyed
[2025.05.31-17.41.12:841][148]LogWindowsTextInputMethodSystem: Activated input method: English (United Kingdom) - (Keyboard).
[2025.05.31-17.41.12:910][148]Cmd: QUIT_EDITOR
[2025.05.31-17.41.12:910][149]LogCore: Engine exit requested (reason: UUnrealEdEngine::CloseEditor())
[2025.05.31-17.41.12:912][149]LogCore: Engine exit requested (reason: EngineExit() was called; note: exit was already requested)
[2025.05.31-17.41.12:913][149]LogStaticMesh: Abandoning remaining async distance field tasks for shutdown
[2025.05.31-17.41.12:913][149]LogStaticMesh: Abandoning remaining async card representation tasks for shutdown
[2025.05.31-17.41.12:914][149]LogWorld: UWorld::CleanupWorld for ThirdPersonMap, bSessionEnded=true, bCleanupResources=true
[2025.05.31-17.41.12:914][149]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.31-17.41.12:914][149]LogWorldPartition: UWorldPartition::Uninitialize : World = /Game/ThirdPerson/Maps/ThirdPersonMap.ThirdPersonMap
[2025.05.31-17.41.12:918][149]LogStylusInput: Shutting down StylusInput subsystem.
[2025.05.31-17.41.12:919][149]LogLevelSequenceEditor: LevelSequenceEditor subsystem deinitialized.
[2025.05.31-17.41.12:921][149]LogWorld: UWorld::CleanupWorld for World_0, bSessionEnded=true, bCleanupResources=true
[2025.05.31-17.41.12:921][149]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.31-17.41.12:921][149]LogStudioTelemetry: Ended StudioTelemetry Session
[2025.05.31-17.41.12:923][149]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroying ET Analytics provider
[2025.05.31-17.41.12:923][149]LogAnalytics: Display: [UEEditor.Rocket.Release] Ended ET Analytics provider session
[2025.05.31-17.41.12:923][149]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroyed ET Analytics provider
[2025.05.31-17.41.12:924][149]LogAudio: Display: Beginning Audio Device Manager Shutdown (Module: AudioMixerXAudio2)...
[2025.05.31-17.41.12:924][149]LogAudio: Display: Destroying 1 Remaining Audio Device(s)...
[2025.05.31-17.41.12:924][149]LogAudio: Display: Audio Device unregistered from world 'ThirdPersonMap'.
[2025.05.31-17.41.12:924][149]LogAudio: Display: Shutting down audio device while 1 references to it are still alive. For more information, compile with INSTRUMENT_AUDIODEVICE_HANDLES.
[2025.05.31-17.41.12:924][149]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.05.31-17.41.12:927][149]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.05.31-17.41.12:933][149]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID -1
[2025.05.31-17.41.12:933][149]LogAudio: Display: Audio Device Manager Shutdown
[2025.05.31-17.41.12:937][149]LogSlate: Slate User Destroyed.  User Index 0, Is Virtual User: 0
[2025.05.31-17.41.12:938][149]LogExit: Preparing to exit.
[2025.05.31-17.41.12:954][149]LogUObjectHash: Compacting FUObjectHashTables data took   0.50ms
[2025.05.31-17.41.13:958][149]LogEditorDataStorage: Deinitializing
[2025.05.31-17.41.14:014][149]LogDemo: Cleaned up 0 splitscreen connections, owner deletion: enabled
[2025.05.31-17.41.14:024][149]LogExit: Editor shut down
[2025.05.31-17.41.14:025][149]LogExit: Transaction tracking system shut down
[2025.05.31-17.41.14:088][149]LogExit: Object subsystem successfully closed.
[2025.05.31-17.41.14:134][149]LogShaderCompilers: Display: Shaders left to compile 0
[2025.05.31-17.41.14:713][149]LogMemoryProfiler: Shutdown
[2025.05.31-17.41.14:714][149]LogNetworkingProfiler: Shutdown
[2025.05.31-17.41.14:714][149]LoadingProfiler: Shutdown
[2025.05.31-17.41.14:714][149]LogTimingProfiler: Shutdown
[2025.05.31-17.41.15:030][149]LogChaosDD: Chaos Debug Draw Shutdown
[2025.05.31-17.41.15:031][149]RenderDocPlugin: plugin has been unloaded.
[2025.05.31-17.41.15:053][149]LogHttp: Warning: [FHttpManager::Shutdown] Unbinding delegates for 1 outstanding Http Requests:
[2025.05.31-17.41.15:053][149]LogHttp: Warning: 	verb=[POST] url=[https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data?SessionID=%7BD0DFCCB8-46FC-57B5-378A-A0B3D24B0298%7D&AppID=UEEditor.Rocket.Release&AppVersion=5.5.4-40574608%2B%2B%2BUE5%2BRelease-5.5&UserID=dadec2e1421bcefd03625fbb5d8813ad%7Cc7ca589710dd40c8846aeace6f9d7b04%7C0f3089af-0ff1-45cd-a7ec-8b9906aa7ea1&AppEnvironment=datacollector-binary&UploadType=eteventstream] refs=[2] status=Processing
[2025.05.31-17.41.15:564][149]LogEOSSDK: FEOSSDKManager::Shutdown EOS_Shutdown Result=[EOS_Success]
[2025.05.31-17.41.15:569][149]LogStudioTelemetry: Display: Shutdown StudioTelemetry Module
[2025.05.31-17.41.15:569][149]LogNFORDenoise: NFORDenoise function shutting down
[2025.05.31-17.41.15:571][149]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
[2025.05.31-17.41.15:571][149]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
[2025.05.31-17.41.15:571][149]LogPakFile: Destroying PakPlatformFile
[2025.05.31-17.41.15:706][149]LogD3D12RHI: ~FD3D12DynamicRHI
[2025.05.31-17.41.15:728][149]LogExit: Exiting.
[2025.05.31-17.41.15:750][149]Log file closed, 05/31/25 14:41:15
