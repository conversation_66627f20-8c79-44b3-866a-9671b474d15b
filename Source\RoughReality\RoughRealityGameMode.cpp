// Copyright Epic Games, Inc. All Rights Reserved.

#include "RoughRealityGameMode.h"
#include "RoughRealityCharacter.h"
#include "UObject/ConstructorHelpers.h"

ARoughRealityGameMode::ARoughRealityGameMode()
{
	// set default pawn class to our Blueprinted character
	static ConstructorHelpers::FClassFinder<APawn> PlayerPawnBPClass(TEXT("/Game/ThirdPerson/Blueprints/BP_ThirdPersonCharacter"));
	if (PlayerPawnBPClass.Class != NULL)
	{
		DefaultPawnClass = PlayerPawnBPClass.Class;
	}
}
