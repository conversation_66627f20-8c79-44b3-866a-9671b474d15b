// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Characters/RookieCharacter.h"
#include "RoughRealityCharacter.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogTemplateCharacter, Log, All);

/**
 * Legacy character class - redirects to the enhanced RookieCharacter
 * This maintains compatibility with existing Blueprint references
 */
UCLASS(config=Game, BlueprintType, Blueprintable, Deprecated)
class ADEPRECATED_ARoughRealityCharacter : public ARookieCharacter
{
	GENERATED_BODY()

public:
	UDEPRECATED_ARoughRealityCharacter();
};

// Type alias for backward compatibility
typedef ARookieCharacter ARoughRealityCharacter;

