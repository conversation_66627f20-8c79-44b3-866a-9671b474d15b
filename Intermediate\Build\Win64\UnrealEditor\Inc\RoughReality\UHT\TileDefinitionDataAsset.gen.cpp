// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RoughReality/DataAssets/TileDefinitionDataAsset.h"
#include "Runtime/GameplayTags/Classes/GameplayTagContainer.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeTileDefinitionDataAsset() {}

// Begin Cross Module References
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USoundBase_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTag();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
ROUGHREALITY_API UClass* Z_Construct_UClass_URoughRealityDataAsset();
ROUGHREALITY_API UClass* Z_Construct_UClass_UTileDefinitionDataAsset();
ROUGHREALITY_API UClass* Z_Construct_UClass_UTileDefinitionDataAsset_NoRegister();
ROUGHREALITY_API UEnum* Z_Construct_UEnum_RoughReality_ETileType();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FTileConnectionPoint();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FTileSpawnPoint();
UPackage* Z_Construct_UPackage__Script_RoughReality();
// End Cross Module References

// Begin ScriptStruct FTileConnectionPoint
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_TileConnectionPoint;
class UScriptStruct* FTileConnectionPoint::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_TileConnectionPoint.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_TileConnectionPoint.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FTileConnectionPoint, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("TileConnectionPoint"));
	}
	return Z_Registration_Info_UScriptStruct_TileConnectionPoint.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FTileConnectionPoint>()
{
	return FTileConnectionPoint::StaticStruct();
}
struct Z_Construct_UScriptStruct_FTileConnectionPoint_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RelativeLocation_MetaData[] = {
		{ "Category", "Connection" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RelativeRotation_MetaData[] = {
		{ "Category", "Connection" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectionTags_MetaData[] = {
		{ "Category", "Connection" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsEntrance_MetaData[] = {
		{ "Category", "Connection" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsExit_MetaData[] = {
		{ "Category", "Connection" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_RelativeLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RelativeRotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ConnectionTags;
	static void NewProp_bIsEntrance_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEntrance;
	static void NewProp_bIsExit_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsExit;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FTileConnectionPoint>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTileConnectionPoint_Statics::NewProp_RelativeLocation = { "RelativeLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTileConnectionPoint, RelativeLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RelativeLocation_MetaData), NewProp_RelativeLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTileConnectionPoint_Statics::NewProp_RelativeRotation = { "RelativeRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTileConnectionPoint, RelativeRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RelativeRotation_MetaData), NewProp_RelativeRotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTileConnectionPoint_Statics::NewProp_ConnectionTags = { "ConnectionTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTileConnectionPoint, ConnectionTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectionTags_MetaData), NewProp_ConnectionTags_MetaData) }; // 3352185621
void Z_Construct_UScriptStruct_FTileConnectionPoint_Statics::NewProp_bIsEntrance_SetBit(void* Obj)
{
	((FTileConnectionPoint*)Obj)->bIsEntrance = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FTileConnectionPoint_Statics::NewProp_bIsEntrance = { "bIsEntrance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FTileConnectionPoint), &Z_Construct_UScriptStruct_FTileConnectionPoint_Statics::NewProp_bIsEntrance_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsEntrance_MetaData), NewProp_bIsEntrance_MetaData) };
void Z_Construct_UScriptStruct_FTileConnectionPoint_Statics::NewProp_bIsExit_SetBit(void* Obj)
{
	((FTileConnectionPoint*)Obj)->bIsExit = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FTileConnectionPoint_Statics::NewProp_bIsExit = { "bIsExit", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FTileConnectionPoint), &Z_Construct_UScriptStruct_FTileConnectionPoint_Statics::NewProp_bIsExit_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsExit_MetaData), NewProp_bIsExit_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FTileConnectionPoint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTileConnectionPoint_Statics::NewProp_RelativeLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTileConnectionPoint_Statics::NewProp_RelativeRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTileConnectionPoint_Statics::NewProp_ConnectionTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTileConnectionPoint_Statics::NewProp_bIsEntrance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTileConnectionPoint_Statics::NewProp_bIsExit,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTileConnectionPoint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FTileConnectionPoint_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	&NewStructOps,
	"TileConnectionPoint",
	Z_Construct_UScriptStruct_FTileConnectionPoint_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTileConnectionPoint_Statics::PropPointers),
	sizeof(FTileConnectionPoint),
	alignof(FTileConnectionPoint),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTileConnectionPoint_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FTileConnectionPoint_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FTileConnectionPoint()
{
	if (!Z_Registration_Info_UScriptStruct_TileConnectionPoint.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_TileConnectionPoint.InnerSingleton, Z_Construct_UScriptStruct_FTileConnectionPoint_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_TileConnectionPoint.InnerSingleton;
}
// End ScriptStruct FTileConnectionPoint

// Begin ScriptStruct FTileSpawnPoint
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_TileSpawnPoint;
class UScriptStruct* FTileSpawnPoint::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_TileSpawnPoint.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_TileSpawnPoint.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FTileSpawnPoint, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("TileSpawnPoint"));
	}
	return Z_Registration_Info_UScriptStruct_TileSpawnPoint.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FTileSpawnPoint>()
{
	return FTileSpawnPoint::StaticStruct();
}
struct Z_Construct_UScriptStruct_FTileSpawnPoint_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RelativeLocation_MetaData[] = {
		{ "Category", "Spawn" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RelativeRotation_MetaData[] = {
		{ "Category", "Spawn" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnTags_MetaData[] = {
		{ "Category", "Spawn" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorToSpawn_MetaData[] = {
		{ "Category", "Spawn" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnChance_MetaData[] = {
		{ "Category", "Spawn" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_RelativeLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RelativeRotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpawnTags;
	static const UECodeGen_Private::FSoftClassPropertyParams NewProp_ActorToSpawn;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpawnChance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FTileSpawnPoint>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTileSpawnPoint_Statics::NewProp_RelativeLocation = { "RelativeLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTileSpawnPoint, RelativeLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RelativeLocation_MetaData), NewProp_RelativeLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTileSpawnPoint_Statics::NewProp_RelativeRotation = { "RelativeRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTileSpawnPoint, RelativeRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RelativeRotation_MetaData), NewProp_RelativeRotation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FTileSpawnPoint_Statics::NewProp_SpawnTags = { "SpawnTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTileSpawnPoint, SpawnTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnTags_MetaData), NewProp_SpawnTags_MetaData) }; // 3352185621
const UECodeGen_Private::FSoftClassPropertyParams Z_Construct_UScriptStruct_FTileSpawnPoint_Statics::NewProp_ActorToSpawn = { "ActorToSpawn", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftClass, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTileSpawnPoint, ActorToSpawn), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorToSpawn_MetaData), NewProp_ActorToSpawn_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FTileSpawnPoint_Statics::NewProp_SpawnChance = { "SpawnChance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FTileSpawnPoint, SpawnChance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnChance_MetaData), NewProp_SpawnChance_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FTileSpawnPoint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTileSpawnPoint_Statics::NewProp_RelativeLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTileSpawnPoint_Statics::NewProp_RelativeRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTileSpawnPoint_Statics::NewProp_SpawnTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTileSpawnPoint_Statics::NewProp_ActorToSpawn,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FTileSpawnPoint_Statics::NewProp_SpawnChance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTileSpawnPoint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FTileSpawnPoint_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	&NewStructOps,
	"TileSpawnPoint",
	Z_Construct_UScriptStruct_FTileSpawnPoint_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTileSpawnPoint_Statics::PropPointers),
	sizeof(FTileSpawnPoint),
	alignof(FTileSpawnPoint),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FTileSpawnPoint_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FTileSpawnPoint_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FTileSpawnPoint()
{
	if (!Z_Registration_Info_UScriptStruct_TileSpawnPoint.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_TileSpawnPoint.InnerSingleton, Z_Construct_UScriptStruct_FTileSpawnPoint_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_TileSpawnPoint.InnerSingleton;
}
// End ScriptStruct FTileSpawnPoint

// Begin Class UTileDefinitionDataAsset Function CanSpawnAtLevel
struct Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnAtLevel_Statics
{
	struct TileDefinitionDataAsset_eventCanSpawnAtLevel_Parms
	{
		int32 SectorLevel;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tile" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SectorLevel;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnAtLevel_Statics::NewProp_SectorLevel = { "SectorLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(TileDefinitionDataAsset_eventCanSpawnAtLevel_Parms, SectorLevel), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnAtLevel_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((TileDefinitionDataAsset_eventCanSpawnAtLevel_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnAtLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TileDefinitionDataAsset_eventCanSpawnAtLevel_Parms), &Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnAtLevel_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnAtLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnAtLevel_Statics::NewProp_SectorLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnAtLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnAtLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnAtLevel_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UTileDefinitionDataAsset, nullptr, "CanSpawnAtLevel", nullptr, nullptr, Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnAtLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnAtLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnAtLevel_Statics::TileDefinitionDataAsset_eventCanSpawnAtLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnAtLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnAtLevel_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnAtLevel_Statics::TileDefinitionDataAsset_eventCanSpawnAtLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnAtLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnAtLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTileDefinitionDataAsset::execCanSpawnAtLevel)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SectorLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanSpawnAtLevel(Z_Param_SectorLevel);
	P_NATIVE_END;
}
// End Class UTileDefinitionDataAsset Function CanSpawnAtLevel

// Begin Class UTileDefinitionDataAsset Function CanSpawnInSector
struct Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnInSector_Statics
{
	struct TileDefinitionDataAsset_eventCanSpawnInSector_Parms
	{
		FGameplayTagContainer SectorTags;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tile" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Utility Functions */" },
#endif
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility Functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SectorTags_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SectorTags;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnInSector_Statics::NewProp_SectorTags = { "SectorTags", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(TileDefinitionDataAsset_eventCanSpawnInSector_Parms, SectorTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SectorTags_MetaData), NewProp_SectorTags_MetaData) }; // 3352185621
void Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnInSector_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((TileDefinitionDataAsset_eventCanSpawnInSector_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnInSector_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TileDefinitionDataAsset_eventCanSpawnInSector_Parms), &Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnInSector_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnInSector_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnInSector_Statics::NewProp_SectorTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnInSector_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnInSector_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnInSector_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UTileDefinitionDataAsset, nullptr, "CanSpawnInSector", nullptr, nullptr, Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnInSector_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnInSector_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnInSector_Statics::TileDefinitionDataAsset_eventCanSpawnInSector_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnInSector_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnInSector_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnInSector_Statics::TileDefinitionDataAsset_eventCanSpawnInSector_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnInSector()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnInSector_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTileDefinitionDataAsset::execCanSpawnInSector)
{
	P_GET_STRUCT_REF(FGameplayTagContainer,Z_Param_Out_SectorTags);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanSpawnInSector(Z_Param_Out_SectorTags);
	P_NATIVE_END;
}
// End Class UTileDefinitionDataAsset Function CanSpawnInSector

// Begin Class UTileDefinitionDataAsset Function GetConnectionPointCount
struct Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointCount_Statics
{
	struct TileDefinitionDataAsset_eventGetConnectionPointCount_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tile" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(TileDefinitionDataAsset_eventGetConnectionPointCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointCount_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UTileDefinitionDataAsset, nullptr, "GetConnectionPointCount", nullptr, nullptr, Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointCount_Statics::TileDefinitionDataAsset_eventGetConnectionPointCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointCount_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointCount_Statics::TileDefinitionDataAsset_eventGetConnectionPointCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTileDefinitionDataAsset::execGetConnectionPointCount)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetConnectionPointCount();
	P_NATIVE_END;
}
// End Class UTileDefinitionDataAsset Function GetConnectionPointCount

// Begin Class UTileDefinitionDataAsset Function GetConnectionPointsByTag
struct Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointsByTag_Statics
{
	struct TileDefinitionDataAsset_eventGetConnectionPointsByTag_Parms
	{
		FGameplayTag Tag;
		TArray<FTileConnectionPoint> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tile" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Tag;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointsByTag_Statics::NewProp_Tag = { "Tag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(TileDefinitionDataAsset_eventGetConnectionPointsByTag_Parms, Tag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tag_MetaData), NewProp_Tag_MetaData) }; // 1298103297
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointsByTag_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTileConnectionPoint, METADATA_PARAMS(0, nullptr) }; // 2506187561
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointsByTag_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(TileDefinitionDataAsset_eventGetConnectionPointsByTag_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2506187561
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointsByTag_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointsByTag_Statics::NewProp_Tag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointsByTag_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointsByTag_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointsByTag_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointsByTag_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UTileDefinitionDataAsset, nullptr, "GetConnectionPointsByTag", nullptr, nullptr, Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointsByTag_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointsByTag_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointsByTag_Statics::TileDefinitionDataAsset_eventGetConnectionPointsByTag_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointsByTag_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointsByTag_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointsByTag_Statics::TileDefinitionDataAsset_eventGetConnectionPointsByTag_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointsByTag()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointsByTag_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTileDefinitionDataAsset::execGetConnectionPointsByTag)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_Tag);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FTileConnectionPoint>*)Z_Param__Result=P_THIS->GetConnectionPointsByTag(Z_Param_Out_Tag);
	P_NATIVE_END;
}
// End Class UTileDefinitionDataAsset Function GetConnectionPointsByTag

// Begin Class UTileDefinitionDataAsset Function GetRandomSpawnLocation
struct Z_Construct_UFunction_UTileDefinitionDataAsset_GetRandomSpawnLocation_Statics
{
	struct TileDefinitionDataAsset_eventGetRandomSpawnLocation_Parms
	{
		FGameplayTag SpawnTag;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tile" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnTag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SpawnTag;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UTileDefinitionDataAsset_GetRandomSpawnLocation_Statics::NewProp_SpawnTag = { "SpawnTag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(TileDefinitionDataAsset_eventGetRandomSpawnLocation_Parms, SpawnTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnTag_MetaData), NewProp_SpawnTag_MetaData) }; // 1298103297
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UTileDefinitionDataAsset_GetRandomSpawnLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(TileDefinitionDataAsset_eventGetRandomSpawnLocation_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTileDefinitionDataAsset_GetRandomSpawnLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTileDefinitionDataAsset_GetRandomSpawnLocation_Statics::NewProp_SpawnTag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTileDefinitionDataAsset_GetRandomSpawnLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_GetRandomSpawnLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTileDefinitionDataAsset_GetRandomSpawnLocation_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UTileDefinitionDataAsset, nullptr, "GetRandomSpawnLocation", nullptr, nullptr, Z_Construct_UFunction_UTileDefinitionDataAsset_GetRandomSpawnLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_GetRandomSpawnLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTileDefinitionDataAsset_GetRandomSpawnLocation_Statics::TileDefinitionDataAsset_eventGetRandomSpawnLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_GetRandomSpawnLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTileDefinitionDataAsset_GetRandomSpawnLocation_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UTileDefinitionDataAsset_GetRandomSpawnLocation_Statics::TileDefinitionDataAsset_eventGetRandomSpawnLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTileDefinitionDataAsset_GetRandomSpawnLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTileDefinitionDataAsset_GetRandomSpawnLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTileDefinitionDataAsset::execGetRandomSpawnLocation)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_SpawnTag);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetRandomSpawnLocation(Z_Param_Out_SpawnTag);
	P_NATIVE_END;
}
// End Class UTileDefinitionDataAsset Function GetRandomSpawnLocation

// Begin Class UTileDefinitionDataAsset Function GetSpawnPointsByTag
struct Z_Construct_UFunction_UTileDefinitionDataAsset_GetSpawnPointsByTag_Statics
{
	struct TileDefinitionDataAsset_eventGetSpawnPointsByTag_Parms
	{
		FGameplayTag Tag;
		TArray<FTileSpawnPoint> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tile" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Tag;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UTileDefinitionDataAsset_GetSpawnPointsByTag_Statics::NewProp_Tag = { "Tag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(TileDefinitionDataAsset_eventGetSpawnPointsByTag_Parms, Tag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tag_MetaData), NewProp_Tag_MetaData) }; // 1298103297
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UTileDefinitionDataAsset_GetSpawnPointsByTag_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTileSpawnPoint, METADATA_PARAMS(0, nullptr) }; // 1554998645
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UTileDefinitionDataAsset_GetSpawnPointsByTag_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(TileDefinitionDataAsset_eventGetSpawnPointsByTag_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1554998645
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTileDefinitionDataAsset_GetSpawnPointsByTag_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTileDefinitionDataAsset_GetSpawnPointsByTag_Statics::NewProp_Tag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTileDefinitionDataAsset_GetSpawnPointsByTag_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTileDefinitionDataAsset_GetSpawnPointsByTag_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_GetSpawnPointsByTag_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTileDefinitionDataAsset_GetSpawnPointsByTag_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UTileDefinitionDataAsset, nullptr, "GetSpawnPointsByTag", nullptr, nullptr, Z_Construct_UFunction_UTileDefinitionDataAsset_GetSpawnPointsByTag_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_GetSpawnPointsByTag_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTileDefinitionDataAsset_GetSpawnPointsByTag_Statics::TileDefinitionDataAsset_eventGetSpawnPointsByTag_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_GetSpawnPointsByTag_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTileDefinitionDataAsset_GetSpawnPointsByTag_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UTileDefinitionDataAsset_GetSpawnPointsByTag_Statics::TileDefinitionDataAsset_eventGetSpawnPointsByTag_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTileDefinitionDataAsset_GetSpawnPointsByTag()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTileDefinitionDataAsset_GetSpawnPointsByTag_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTileDefinitionDataAsset::execGetSpawnPointsByTag)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_Tag);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FTileSpawnPoint>*)Z_Param__Result=P_THIS->GetSpawnPointsByTag(Z_Param_Out_Tag);
	P_NATIVE_END;
}
// End Class UTileDefinitionDataAsset Function GetSpawnPointsByTag

// Begin Class UTileDefinitionDataAsset Function GetTileTypeString
struct Z_Construct_UFunction_UTileDefinitionDataAsset_GetTileTypeString_Statics
{
	struct TileDefinitionDataAsset_eventGetTileTypeString_Parms
	{
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tile" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UTileDefinitionDataAsset_GetTileTypeString_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(TileDefinitionDataAsset_eventGetTileTypeString_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTileDefinitionDataAsset_GetTileTypeString_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTileDefinitionDataAsset_GetTileTypeString_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_GetTileTypeString_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTileDefinitionDataAsset_GetTileTypeString_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UTileDefinitionDataAsset, nullptr, "GetTileTypeString", nullptr, nullptr, Z_Construct_UFunction_UTileDefinitionDataAsset_GetTileTypeString_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_GetTileTypeString_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTileDefinitionDataAsset_GetTileTypeString_Statics::TileDefinitionDataAsset_eventGetTileTypeString_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_GetTileTypeString_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTileDefinitionDataAsset_GetTileTypeString_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UTileDefinitionDataAsset_GetTileTypeString_Statics::TileDefinitionDataAsset_eventGetTileTypeString_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTileDefinitionDataAsset_GetTileTypeString()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTileDefinitionDataAsset_GetTileTypeString_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTileDefinitionDataAsset::execGetTileTypeString)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetTileTypeString();
	P_NATIVE_END;
}
// End Class UTileDefinitionDataAsset Function GetTileTypeString

// Begin Class UTileDefinitionDataAsset Function HasEntrancePoint
struct Z_Construct_UFunction_UTileDefinitionDataAsset_HasEntrancePoint_Statics
{
	struct TileDefinitionDataAsset_eventHasEntrancePoint_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tile" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UTileDefinitionDataAsset_HasEntrancePoint_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((TileDefinitionDataAsset_eventHasEntrancePoint_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UTileDefinitionDataAsset_HasEntrancePoint_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TileDefinitionDataAsset_eventHasEntrancePoint_Parms), &Z_Construct_UFunction_UTileDefinitionDataAsset_HasEntrancePoint_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTileDefinitionDataAsset_HasEntrancePoint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTileDefinitionDataAsset_HasEntrancePoint_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_HasEntrancePoint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTileDefinitionDataAsset_HasEntrancePoint_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UTileDefinitionDataAsset, nullptr, "HasEntrancePoint", nullptr, nullptr, Z_Construct_UFunction_UTileDefinitionDataAsset_HasEntrancePoint_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_HasEntrancePoint_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTileDefinitionDataAsset_HasEntrancePoint_Statics::TileDefinitionDataAsset_eventHasEntrancePoint_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_HasEntrancePoint_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTileDefinitionDataAsset_HasEntrancePoint_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UTileDefinitionDataAsset_HasEntrancePoint_Statics::TileDefinitionDataAsset_eventHasEntrancePoint_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTileDefinitionDataAsset_HasEntrancePoint()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTileDefinitionDataAsset_HasEntrancePoint_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTileDefinitionDataAsset::execHasEntrancePoint)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasEntrancePoint();
	P_NATIVE_END;
}
// End Class UTileDefinitionDataAsset Function HasEntrancePoint

// Begin Class UTileDefinitionDataAsset Function HasExitPoint
struct Z_Construct_UFunction_UTileDefinitionDataAsset_HasExitPoint_Statics
{
	struct TileDefinitionDataAsset_eventHasExitPoint_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tile" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UTileDefinitionDataAsset_HasExitPoint_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((TileDefinitionDataAsset_eventHasExitPoint_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UTileDefinitionDataAsset_HasExitPoint_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TileDefinitionDataAsset_eventHasExitPoint_Parms), &Z_Construct_UFunction_UTileDefinitionDataAsset_HasExitPoint_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTileDefinitionDataAsset_HasExitPoint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTileDefinitionDataAsset_HasExitPoint_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_HasExitPoint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTileDefinitionDataAsset_HasExitPoint_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UTileDefinitionDataAsset, nullptr, "HasExitPoint", nullptr, nullptr, Z_Construct_UFunction_UTileDefinitionDataAsset_HasExitPoint_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_HasExitPoint_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTileDefinitionDataAsset_HasExitPoint_Statics::TileDefinitionDataAsset_eventHasExitPoint_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_HasExitPoint_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTileDefinitionDataAsset_HasExitPoint_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UTileDefinitionDataAsset_HasExitPoint_Statics::TileDefinitionDataAsset_eventHasExitPoint_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTileDefinitionDataAsset_HasExitPoint()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTileDefinitionDataAsset_HasExitPoint_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTileDefinitionDataAsset::execHasExitPoint)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasExitPoint();
	P_NATIVE_END;
}
// End Class UTileDefinitionDataAsset Function HasExitPoint

// Begin Class UTileDefinitionDataAsset Function IsCompatibleWithAdjacent
struct Z_Construct_UFunction_UTileDefinitionDataAsset_IsCompatibleWithAdjacent_Statics
{
	struct TileDefinitionDataAsset_eventIsCompatibleWithAdjacent_Parms
	{
		FGameplayTagContainer AdjacentTags;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Tile" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdjacentTags_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_AdjacentTags;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UTileDefinitionDataAsset_IsCompatibleWithAdjacent_Statics::NewProp_AdjacentTags = { "AdjacentTags", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(TileDefinitionDataAsset_eventIsCompatibleWithAdjacent_Parms, AdjacentTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdjacentTags_MetaData), NewProp_AdjacentTags_MetaData) }; // 3352185621
void Z_Construct_UFunction_UTileDefinitionDataAsset_IsCompatibleWithAdjacent_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((TileDefinitionDataAsset_eventIsCompatibleWithAdjacent_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UTileDefinitionDataAsset_IsCompatibleWithAdjacent_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(TileDefinitionDataAsset_eventIsCompatibleWithAdjacent_Parms), &Z_Construct_UFunction_UTileDefinitionDataAsset_IsCompatibleWithAdjacent_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UTileDefinitionDataAsset_IsCompatibleWithAdjacent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTileDefinitionDataAsset_IsCompatibleWithAdjacent_Statics::NewProp_AdjacentTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UTileDefinitionDataAsset_IsCompatibleWithAdjacent_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_IsCompatibleWithAdjacent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UTileDefinitionDataAsset_IsCompatibleWithAdjacent_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UTileDefinitionDataAsset, nullptr, "IsCompatibleWithAdjacent", nullptr, nullptr, Z_Construct_UFunction_UTileDefinitionDataAsset_IsCompatibleWithAdjacent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_IsCompatibleWithAdjacent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UTileDefinitionDataAsset_IsCompatibleWithAdjacent_Statics::TileDefinitionDataAsset_eventIsCompatibleWithAdjacent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UTileDefinitionDataAsset_IsCompatibleWithAdjacent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UTileDefinitionDataAsset_IsCompatibleWithAdjacent_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UTileDefinitionDataAsset_IsCompatibleWithAdjacent_Statics::TileDefinitionDataAsset_eventIsCompatibleWithAdjacent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UTileDefinitionDataAsset_IsCompatibleWithAdjacent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UTileDefinitionDataAsset_IsCompatibleWithAdjacent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UTileDefinitionDataAsset::execIsCompatibleWithAdjacent)
{
	P_GET_STRUCT_REF(FGameplayTagContainer,Z_Param_Out_AdjacentTags);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsCompatibleWithAdjacent(Z_Param_Out_AdjacentTags);
	P_NATIVE_END;
}
// End Class UTileDefinitionDataAsset Function IsCompatibleWithAdjacent

// Begin Class UTileDefinitionDataAsset
void UTileDefinitionDataAsset::StaticRegisterNativesUTileDefinitionDataAsset()
{
	UClass* Class = UTileDefinitionDataAsset::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CanSpawnAtLevel", &UTileDefinitionDataAsset::execCanSpawnAtLevel },
		{ "CanSpawnInSector", &UTileDefinitionDataAsset::execCanSpawnInSector },
		{ "GetConnectionPointCount", &UTileDefinitionDataAsset::execGetConnectionPointCount },
		{ "GetConnectionPointsByTag", &UTileDefinitionDataAsset::execGetConnectionPointsByTag },
		{ "GetRandomSpawnLocation", &UTileDefinitionDataAsset::execGetRandomSpawnLocation },
		{ "GetSpawnPointsByTag", &UTileDefinitionDataAsset::execGetSpawnPointsByTag },
		{ "GetTileTypeString", &UTileDefinitionDataAsset::execGetTileTypeString },
		{ "HasEntrancePoint", &UTileDefinitionDataAsset::execHasEntrancePoint },
		{ "HasExitPoint", &UTileDefinitionDataAsset::execHasExitPoint },
		{ "IsCompatibleWithAdjacent", &UTileDefinitionDataAsset::execIsCompatibleWithAdjacent },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UTileDefinitionDataAsset);
UClass* Z_Construct_UClass_UTileDefinitionDataAsset_NoRegister()
{
	return UTileDefinitionDataAsset::StaticClass();
}
struct Z_Construct_UClass_UTileDefinitionDataAsset_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Data Asset for tile definitions used in procedural level generation\n * Defines the properties, connections, and spawn points for level tiles\n */" },
#endif
		{ "IncludePath", "DataAssets/TileDefinitionDataAsset.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data Asset for tile definitions used in procedural level generation\nDefines the properties, connections, and spawn points for level tiles" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TileType_MetaData[] = {
		{ "Category", "Tile Type" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tile Type */" },
#endif
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tile Type" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TileMesh_MetaData[] = {
		{ "Category", "Visuals" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tile Mesh */" },
#endif
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tile Mesh" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TileBlueprint_MetaData[] = {
		{ "Category", "Visuals" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tile Blueprint (alternative to mesh) */" },
#endif
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tile Blueprint (alternative to mesh)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TileSize_MetaData[] = {
		{ "Category", "Dimensions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tile Dimensions */" },
#endif
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tile Dimensions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TileBounds_MetaData[] = {
		{ "Category", "Dimensions" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConnectionPoints_MetaData[] = {
		{ "Category", "Connections" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Connection Points */" },
#endif
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Connection Points" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnemySpawnPoints_MetaData[] = {
		{ "Category", "Spawning" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Spawn Points for enemies, items, etc. */" },
#endif
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spawn Points for enemies, items, etc." },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ItemSpawnPoints_MetaData[] = {
		{ "Category", "Spawning" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractableSpawnPoints_MetaData[] = {
		{ "Category", "Spawning" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanRotate_MetaData[] = {
		{ "Category", "Rules" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Tile Rules */" },
#endif
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tile Rules" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanMirror_MetaData[] = {
		{ "Category", "Rules" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxInstancesPerLevel_MetaData[] = {
		{ "Category", "Rules" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SpawnWeight_MetaData[] = {
		{ "Category", "Rules" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// -1 = unlimited\n" },
#endif
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "-1 = unlimited" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredSectorTags_MetaData[] = {
		{ "Category", "Rules" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Required and Forbidden Tags */" },
#endif
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Required and Forbidden Tags" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ForbiddenSectorTags_MetaData[] = {
		{ "Category", "Rules" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredAdjacentTags_MetaData[] = {
		{ "Category", "Rules" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinSectorLevel_MetaData[] = {
		{ "Category", "Difficulty" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Difficulty Scaling */" },
#endif
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Difficulty Scaling" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxSectorLevel_MetaData[] = {
		{ "Category", "Difficulty" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DifficultyMultiplier_MetaData[] = {
		{ "Category", "Difficulty" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AmbientSound_MetaData[] = {
		{ "Category", "Audio" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Audio */" },
#endif
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Audio" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MusicTrack_MetaData[] = {
		{ "Category", "Audio" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AmbientLightColor_MetaData[] = {
		{ "Category", "Lighting" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Lighting */" },
#endif
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Lighting" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AmbientLightIntensity_MetaData[] = {
		{ "Category", "Lighting" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bOverrideAmbientLight_MetaData[] = {
		{ "Category", "Lighting" },
		{ "ModuleRelativePath", "DataAssets/TileDefinitionDataAsset.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TileType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TileType;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_TileMesh;
	static const UECodeGen_Private::FSoftClassPropertyParams NewProp_TileBlueprint;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TileSize;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TileBounds;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ConnectionPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ConnectionPoints;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EnemySpawnPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EnemySpawnPoints;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ItemSpawnPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ItemSpawnPoints;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InteractableSpawnPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InteractableSpawnPoints;
	static void NewProp_bCanRotate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanRotate;
	static void NewProp_bCanMirror_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanMirror;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxInstancesPerLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SpawnWeight;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RequiredSectorTags;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ForbiddenSectorTags;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RequiredAdjacentTags;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MinSectorLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxSectorLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DifficultyMultiplier;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AmbientSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_MusicTrack;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AmbientLightColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AmbientLightIntensity;
	static void NewProp_bOverrideAmbientLight_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bOverrideAmbientLight;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnAtLevel, "CanSpawnAtLevel" }, // 2439307742
		{ &Z_Construct_UFunction_UTileDefinitionDataAsset_CanSpawnInSector, "CanSpawnInSector" }, // 1836089421
		{ &Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointCount, "GetConnectionPointCount" }, // 589735805
		{ &Z_Construct_UFunction_UTileDefinitionDataAsset_GetConnectionPointsByTag, "GetConnectionPointsByTag" }, // 346894564
		{ &Z_Construct_UFunction_UTileDefinitionDataAsset_GetRandomSpawnLocation, "GetRandomSpawnLocation" }, // 3885641738
		{ &Z_Construct_UFunction_UTileDefinitionDataAsset_GetSpawnPointsByTag, "GetSpawnPointsByTag" }, // 2793322477
		{ &Z_Construct_UFunction_UTileDefinitionDataAsset_GetTileTypeString, "GetTileTypeString" }, // 3225072742
		{ &Z_Construct_UFunction_UTileDefinitionDataAsset_HasEntrancePoint, "HasEntrancePoint" }, // 118957196
		{ &Z_Construct_UFunction_UTileDefinitionDataAsset_HasExitPoint, "HasExitPoint" }, // 2482052633
		{ &Z_Construct_UFunction_UTileDefinitionDataAsset_IsCompatibleWithAdjacent, "IsCompatibleWithAdjacent" }, // 4100950720
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UTileDefinitionDataAsset>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_TileType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_TileType = { "TileType", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UTileDefinitionDataAsset, TileType), Z_Construct_UEnum_RoughReality_ETileType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TileType_MetaData), NewProp_TileType_MetaData) }; // 3250710945
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_TileMesh = { "TileMesh", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UTileDefinitionDataAsset, TileMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TileMesh_MetaData), NewProp_TileMesh_MetaData) };
const UECodeGen_Private::FSoftClassPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_TileBlueprint = { "TileBlueprint", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftClass, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UTileDefinitionDataAsset, TileBlueprint), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TileBlueprint_MetaData), NewProp_TileBlueprint_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_TileSize = { "TileSize", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UTileDefinitionDataAsset, TileSize), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TileSize_MetaData), NewProp_TileSize_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_TileBounds = { "TileBounds", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UTileDefinitionDataAsset, TileBounds), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TileBounds_MetaData), NewProp_TileBounds_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_ConnectionPoints_Inner = { "ConnectionPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTileConnectionPoint, METADATA_PARAMS(0, nullptr) }; // 2506187561
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_ConnectionPoints = { "ConnectionPoints", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UTileDefinitionDataAsset, ConnectionPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConnectionPoints_MetaData), NewProp_ConnectionPoints_MetaData) }; // 2506187561
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_EnemySpawnPoints_Inner = { "EnemySpawnPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTileSpawnPoint, METADATA_PARAMS(0, nullptr) }; // 1554998645
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_EnemySpawnPoints = { "EnemySpawnPoints", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UTileDefinitionDataAsset, EnemySpawnPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnemySpawnPoints_MetaData), NewProp_EnemySpawnPoints_MetaData) }; // 1554998645
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_ItemSpawnPoints_Inner = { "ItemSpawnPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTileSpawnPoint, METADATA_PARAMS(0, nullptr) }; // 1554998645
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_ItemSpawnPoints = { "ItemSpawnPoints", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UTileDefinitionDataAsset, ItemSpawnPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ItemSpawnPoints_MetaData), NewProp_ItemSpawnPoints_MetaData) }; // 1554998645
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_InteractableSpawnPoints_Inner = { "InteractableSpawnPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FTileSpawnPoint, METADATA_PARAMS(0, nullptr) }; // 1554998645
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_InteractableSpawnPoints = { "InteractableSpawnPoints", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UTileDefinitionDataAsset, InteractableSpawnPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractableSpawnPoints_MetaData), NewProp_InteractableSpawnPoints_MetaData) }; // 1554998645
void Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_bCanRotate_SetBit(void* Obj)
{
	((UTileDefinitionDataAsset*)Obj)->bCanRotate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_bCanRotate = { "bCanRotate", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UTileDefinitionDataAsset), &Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_bCanRotate_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanRotate_MetaData), NewProp_bCanRotate_MetaData) };
void Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_bCanMirror_SetBit(void* Obj)
{
	((UTileDefinitionDataAsset*)Obj)->bCanMirror = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_bCanMirror = { "bCanMirror", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UTileDefinitionDataAsset), &Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_bCanMirror_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanMirror_MetaData), NewProp_bCanMirror_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_MaxInstancesPerLevel = { "MaxInstancesPerLevel", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UTileDefinitionDataAsset, MaxInstancesPerLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxInstancesPerLevel_MetaData), NewProp_MaxInstancesPerLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_SpawnWeight = { "SpawnWeight", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UTileDefinitionDataAsset, SpawnWeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SpawnWeight_MetaData), NewProp_SpawnWeight_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_RequiredSectorTags = { "RequiredSectorTags", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UTileDefinitionDataAsset, RequiredSectorTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredSectorTags_MetaData), NewProp_RequiredSectorTags_MetaData) }; // 3352185621
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_ForbiddenSectorTags = { "ForbiddenSectorTags", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UTileDefinitionDataAsset, ForbiddenSectorTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ForbiddenSectorTags_MetaData), NewProp_ForbiddenSectorTags_MetaData) }; // 3352185621
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_RequiredAdjacentTags = { "RequiredAdjacentTags", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UTileDefinitionDataAsset, RequiredAdjacentTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredAdjacentTags_MetaData), NewProp_RequiredAdjacentTags_MetaData) }; // 3352185621
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_MinSectorLevel = { "MinSectorLevel", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UTileDefinitionDataAsset, MinSectorLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinSectorLevel_MetaData), NewProp_MinSectorLevel_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_MaxSectorLevel = { "MaxSectorLevel", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UTileDefinitionDataAsset, MaxSectorLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxSectorLevel_MetaData), NewProp_MaxSectorLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_DifficultyMultiplier = { "DifficultyMultiplier", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UTileDefinitionDataAsset, DifficultyMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DifficultyMultiplier_MetaData), NewProp_DifficultyMultiplier_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_AmbientSound = { "AmbientSound", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UTileDefinitionDataAsset, AmbientSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AmbientSound_MetaData), NewProp_AmbientSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_MusicTrack = { "MusicTrack", nullptr, (EPropertyFlags)0x0014000000000015, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UTileDefinitionDataAsset, MusicTrack), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MusicTrack_MetaData), NewProp_MusicTrack_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_AmbientLightColor = { "AmbientLightColor", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UTileDefinitionDataAsset, AmbientLightColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AmbientLightColor_MetaData), NewProp_AmbientLightColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_AmbientLightIntensity = { "AmbientLightIntensity", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UTileDefinitionDataAsset, AmbientLightIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AmbientLightIntensity_MetaData), NewProp_AmbientLightIntensity_MetaData) };
void Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_bOverrideAmbientLight_SetBit(void* Obj)
{
	((UTileDefinitionDataAsset*)Obj)->bOverrideAmbientLight = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_bOverrideAmbientLight = { "bOverrideAmbientLight", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UTileDefinitionDataAsset), &Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_bOverrideAmbientLight_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bOverrideAmbientLight_MetaData), NewProp_bOverrideAmbientLight_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UTileDefinitionDataAsset_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_TileType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_TileType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_TileMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_TileBlueprint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_TileSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_TileBounds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_ConnectionPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_ConnectionPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_EnemySpawnPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_EnemySpawnPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_ItemSpawnPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_ItemSpawnPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_InteractableSpawnPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_InteractableSpawnPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_bCanRotate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_bCanMirror,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_MaxInstancesPerLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_SpawnWeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_RequiredSectorTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_ForbiddenSectorTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_RequiredAdjacentTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_MinSectorLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_MaxSectorLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_DifficultyMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_AmbientSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_MusicTrack,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_AmbientLightColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_AmbientLightIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UTileDefinitionDataAsset_Statics::NewProp_bOverrideAmbientLight,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UTileDefinitionDataAsset_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UTileDefinitionDataAsset_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_URoughRealityDataAsset,
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UTileDefinitionDataAsset_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UTileDefinitionDataAsset_Statics::ClassParams = {
	&UTileDefinitionDataAsset::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UTileDefinitionDataAsset_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UTileDefinitionDataAsset_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UTileDefinitionDataAsset_Statics::Class_MetaDataParams), Z_Construct_UClass_UTileDefinitionDataAsset_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UTileDefinitionDataAsset()
{
	if (!Z_Registration_Info_UClass_UTileDefinitionDataAsset.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UTileDefinitionDataAsset.OuterSingleton, Z_Construct_UClass_UTileDefinitionDataAsset_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UTileDefinitionDataAsset.OuterSingleton;
}
template<> ROUGHREALITY_API UClass* StaticClass<UTileDefinitionDataAsset>()
{
	return UTileDefinitionDataAsset::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UTileDefinitionDataAsset);
UTileDefinitionDataAsset::~UTileDefinitionDataAsset() {}
// End Class UTileDefinitionDataAsset

// Begin Registration
struct Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_DataAssets_TileDefinitionDataAsset_h_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FTileConnectionPoint::StaticStruct, Z_Construct_UScriptStruct_FTileConnectionPoint_Statics::NewStructOps, TEXT("TileConnectionPoint"), &Z_Registration_Info_UScriptStruct_TileConnectionPoint, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FTileConnectionPoint), 2506187561U) },
		{ FTileSpawnPoint::StaticStruct, Z_Construct_UScriptStruct_FTileSpawnPoint_Statics::NewStructOps, TEXT("TileSpawnPoint"), &Z_Registration_Info_UScriptStruct_TileSpawnPoint, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FTileSpawnPoint), 1554998645U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UTileDefinitionDataAsset, UTileDefinitionDataAsset::StaticClass, TEXT("UTileDefinitionDataAsset"), &Z_Registration_Info_UClass_UTileDefinitionDataAsset, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UTileDefinitionDataAsset), 2526713698U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_DataAssets_TileDefinitionDataAsset_h_3809872870(TEXT("/Script/RoughReality"),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_DataAssets_TileDefinitionDataAsset_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_DataAssets_TileDefinitionDataAsset_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_DataAssets_TileDefinitionDataAsset_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_DataAssets_TileDefinitionDataAsset_h_Statics::ScriptStructInfo),
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
