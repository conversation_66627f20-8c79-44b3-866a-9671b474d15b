// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RoughReality/VFX/VisualEffectsManager.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeVisualEffectsManager() {}

// Begin Cross Module References
COREUOBJECT_API UClass* Z_Construct_UClass_UObject_NoRegister();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_UCurveFloat_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialParameterCollection_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UPostProcessComponent_NoRegister();
ROUGHREALITY_API UClass* Z_Construct_UClass_AVisualEffectsManager();
ROUGHREALITY_API UClass* Z_Construct_UClass_AVisualEffectsManager_NoRegister();
ROUGHREALITY_API UEnum* Z_Construct_UEnum_RoughReality_EGameVisualState();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_OnVisualStateChanged__DelegateSignature();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FBulletTimeEffectSettings();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FVisualEffectSettings();
UPackage* Z_Construct_UPackage__Script_RoughReality();
// End Cross Module References

// Begin Enum EGameVisualState
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EGameVisualState;
static UEnum* EGameVisualState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EGameVisualState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EGameVisualState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_RoughReality_EGameVisualState, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("EGameVisualState"));
	}
	return Z_Registration_Info_UEnum_EGameVisualState.OuterSingleton;
}
template<> ROUGHREALITY_API UEnum* StaticEnum<EGameVisualState>()
{
	return EGameVisualState_StaticEnum();
}
struct Z_Construct_UEnum_RoughReality_EGameVisualState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "BulletTime.Name", "EGameVisualState::BulletTime" },
		{ "Critical.Name", "EGameVisualState::Critical" },
		{ "Damaged.Name", "EGameVisualState::Damaged" },
		{ "Death.Name", "EGameVisualState::Death" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
		{ "Normal.Name", "EGameVisualState::Normal" },
		{ "Victory.Name", "EGameVisualState::Victory" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EGameVisualState::Normal", (int64)EGameVisualState::Normal },
		{ "EGameVisualState::BulletTime", (int64)EGameVisualState::BulletTime },
		{ "EGameVisualState::Damaged", (int64)EGameVisualState::Damaged },
		{ "EGameVisualState::Critical", (int64)EGameVisualState::Critical },
		{ "EGameVisualState::Death", (int64)EGameVisualState::Death },
		{ "EGameVisualState::Victory", (int64)EGameVisualState::Victory },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_RoughReality_EGameVisualState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	"EGameVisualState",
	"EGameVisualState",
	Z_Construct_UEnum_RoughReality_EGameVisualState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_RoughReality_EGameVisualState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_RoughReality_EGameVisualState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_RoughReality_EGameVisualState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_RoughReality_EGameVisualState()
{
	if (!Z_Registration_Info_UEnum_EGameVisualState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EGameVisualState.InnerSingleton, Z_Construct_UEnum_RoughReality_EGameVisualState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EGameVisualState.InnerSingleton;
}
// End Enum EGameVisualState

// Begin ScriptStruct FVisualEffectSettings
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_VisualEffectSettings;
class UScriptStruct* FVisualEffectSettings::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_VisualEffectSettings.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_VisualEffectSettings.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FVisualEffectSettings, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("VisualEffectSettings"));
	}
	return Z_Registration_Info_UScriptStruct_VisualEffectSettings.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FVisualEffectSettings>()
{
	return FVisualEffectSettings::StaticStruct();
}
struct Z_Construct_UScriptStruct_FVisualEffectSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Saturation_MetaData[] = {
		{ "Category", "Visual Effect" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Contrast_MetaData[] = {
		{ "Category", "Visual Effect" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Brightness_MetaData[] = {
		{ "Category", "Visual Effect" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ColorTint_MetaData[] = {
		{ "Category", "Visual Effect" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChromaticAberration_MetaData[] = {
		{ "Category", "Visual Effect" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Vignette_MetaData[] = {
		{ "Category", "Visual Effect" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MotionBlur_MetaData[] = {
		{ "Category", "Visual Effect" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DepthOfField_MetaData[] = {
		{ "Category", "Visual Effect" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BloomIntensity_MetaData[] = {
		{ "Category", "Visual Effect" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilmGrain_MetaData[] = {
		{ "Category", "Visual Effect" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Saturation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Contrast;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Brightness;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ColorTint;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ChromaticAberration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Vignette;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MotionBlur;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DepthOfField;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BloomIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FilmGrain;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FVisualEffectSettings>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::NewProp_Saturation = { "Saturation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVisualEffectSettings, Saturation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Saturation_MetaData), NewProp_Saturation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::NewProp_Contrast = { "Contrast", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVisualEffectSettings, Contrast), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Contrast_MetaData), NewProp_Contrast_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::NewProp_Brightness = { "Brightness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVisualEffectSettings, Brightness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Brightness_MetaData), NewProp_Brightness_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::NewProp_ColorTint = { "ColorTint", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVisualEffectSettings, ColorTint), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ColorTint_MetaData), NewProp_ColorTint_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::NewProp_ChromaticAberration = { "ChromaticAberration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVisualEffectSettings, ChromaticAberration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChromaticAberration_MetaData), NewProp_ChromaticAberration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::NewProp_Vignette = { "Vignette", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVisualEffectSettings, Vignette), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Vignette_MetaData), NewProp_Vignette_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::NewProp_MotionBlur = { "MotionBlur", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVisualEffectSettings, MotionBlur), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MotionBlur_MetaData), NewProp_MotionBlur_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::NewProp_DepthOfField = { "DepthOfField", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVisualEffectSettings, DepthOfField), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DepthOfField_MetaData), NewProp_DepthOfField_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::NewProp_BloomIntensity = { "BloomIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVisualEffectSettings, BloomIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BloomIntensity_MetaData), NewProp_BloomIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::NewProp_FilmGrain = { "FilmGrain", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FVisualEffectSettings, FilmGrain), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilmGrain_MetaData), NewProp_FilmGrain_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::NewProp_Saturation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::NewProp_Contrast,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::NewProp_Brightness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::NewProp_ColorTint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::NewProp_ChromaticAberration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::NewProp_Vignette,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::NewProp_MotionBlur,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::NewProp_DepthOfField,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::NewProp_BloomIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::NewProp_FilmGrain,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	&NewStructOps,
	"VisualEffectSettings",
	Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::PropPointers),
	sizeof(FVisualEffectSettings),
	alignof(FVisualEffectSettings),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FVisualEffectSettings()
{
	if (!Z_Registration_Info_UScriptStruct_VisualEffectSettings.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_VisualEffectSettings.InnerSingleton, Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_VisualEffectSettings.InnerSingleton;
}
// End ScriptStruct FVisualEffectSettings

// Begin ScriptStruct FBulletTimeEffectSettings
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_BulletTimeEffectSettings;
class UScriptStruct* FBulletTimeEffectSettings::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_BulletTimeEffectSettings.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_BulletTimeEffectSettings.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FBulletTimeEffectSettings, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("BulletTimeEffectSettings"));
	}
	return Z_Registration_Info_UScriptStruct_BulletTimeEffectSettings.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FBulletTimeEffectSettings>()
{
	return FBulletTimeEffectSettings::StaticStruct();
}
struct Z_Construct_UScriptStruct_FBulletTimeEffectSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DesaturationAmount_MetaData[] = {
		{ "Category", "Bullet Time" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ContrastBoost_MetaData[] = {
		{ "Category", "Bullet Time" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TintColor_MetaData[] = {
		{ "Category", "Bullet Time" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TrailIntensity_MetaData[] = {
		{ "Category", "Bullet Time" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeDistortion_MetaData[] = {
		{ "Category", "Bullet Time" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EdgeGlow_MetaData[] = {
		{ "Category", "Bullet Time" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionDuration_MetaData[] = {
		{ "Category", "Bullet Time" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DesaturationAmount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ContrastBoost;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TintColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TrailIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeDistortion;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EdgeGlow;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TransitionDuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FBulletTimeEffectSettings>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FBulletTimeEffectSettings_Statics::NewProp_DesaturationAmount = { "DesaturationAmount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBulletTimeEffectSettings, DesaturationAmount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DesaturationAmount_MetaData), NewProp_DesaturationAmount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FBulletTimeEffectSettings_Statics::NewProp_ContrastBoost = { "ContrastBoost", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBulletTimeEffectSettings, ContrastBoost), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ContrastBoost_MetaData), NewProp_ContrastBoost_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FBulletTimeEffectSettings_Statics::NewProp_TintColor = { "TintColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBulletTimeEffectSettings, TintColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TintColor_MetaData), NewProp_TintColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FBulletTimeEffectSettings_Statics::NewProp_TrailIntensity = { "TrailIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBulletTimeEffectSettings, TrailIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TrailIntensity_MetaData), NewProp_TrailIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FBulletTimeEffectSettings_Statics::NewProp_TimeDistortion = { "TimeDistortion", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBulletTimeEffectSettings, TimeDistortion), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeDistortion_MetaData), NewProp_TimeDistortion_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FBulletTimeEffectSettings_Statics::NewProp_EdgeGlow = { "EdgeGlow", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBulletTimeEffectSettings, EdgeGlow), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EdgeGlow_MetaData), NewProp_EdgeGlow_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FBulletTimeEffectSettings_Statics::NewProp_TransitionDuration = { "TransitionDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBulletTimeEffectSettings, TransitionDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionDuration_MetaData), NewProp_TransitionDuration_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FBulletTimeEffectSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBulletTimeEffectSettings_Statics::NewProp_DesaturationAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBulletTimeEffectSettings_Statics::NewProp_ContrastBoost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBulletTimeEffectSettings_Statics::NewProp_TintColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBulletTimeEffectSettings_Statics::NewProp_TrailIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBulletTimeEffectSettings_Statics::NewProp_TimeDistortion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBulletTimeEffectSettings_Statics::NewProp_EdgeGlow,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBulletTimeEffectSettings_Statics::NewProp_TransitionDuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBulletTimeEffectSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FBulletTimeEffectSettings_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	&NewStructOps,
	"BulletTimeEffectSettings",
	Z_Construct_UScriptStruct_FBulletTimeEffectSettings_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBulletTimeEffectSettings_Statics::PropPointers),
	sizeof(FBulletTimeEffectSettings),
	alignof(FBulletTimeEffectSettings),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBulletTimeEffectSettings_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FBulletTimeEffectSettings_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FBulletTimeEffectSettings()
{
	if (!Z_Registration_Info_UScriptStruct_BulletTimeEffectSettings.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_BulletTimeEffectSettings.InnerSingleton, Z_Construct_UScriptStruct_FBulletTimeEffectSettings_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_BulletTimeEffectSettings.InnerSingleton;
}
// End ScriptStruct FBulletTimeEffectSettings

// Begin Delegate FOnVisualStateChanged
struct Z_Construct_UDelegateFunction_RoughReality_OnVisualStateChanged__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventOnVisualStateChanged_Parms
	{
		EGameVisualState OldState;
		EGameVisualState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldState;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_RoughReality_OnVisualStateChanged__DelegateSignature_Statics::NewProp_OldState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnVisualStateChanged__DelegateSignature_Statics::NewProp_OldState = { "OldState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnVisualStateChanged_Parms, OldState), Z_Construct_UEnum_RoughReality_EGameVisualState, METADATA_PARAMS(0, nullptr) }; // 3351377574
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_RoughReality_OnVisualStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_RoughReality_OnVisualStateChanged__DelegateSignature_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventOnVisualStateChanged_Parms, NewState), Z_Construct_UEnum_RoughReality_EGameVisualState, METADATA_PARAMS(0, nullptr) }; // 3351377574
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_OnVisualStateChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnVisualStateChanged__DelegateSignature_Statics::NewProp_OldState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnVisualStateChanged__DelegateSignature_Statics::NewProp_OldState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnVisualStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_OnVisualStateChanged__DelegateSignature_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnVisualStateChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_OnVisualStateChanged__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "OnVisualStateChanged__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_OnVisualStateChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnVisualStateChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_OnVisualStateChanged__DelegateSignature_Statics::_Script_RoughReality_eventOnVisualStateChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_OnVisualStateChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_OnVisualStateChanged__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_OnVisualStateChanged__DelegateSignature_Statics::_Script_RoughReality_eventOnVisualStateChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_OnVisualStateChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_OnVisualStateChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnVisualStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnVisualStateChanged, EGameVisualState OldState, EGameVisualState NewState)
{
	struct _Script_RoughReality_eventOnVisualStateChanged_Parms
	{
		EGameVisualState OldState;
		EGameVisualState NewState;
	};
	_Script_RoughReality_eventOnVisualStateChanged_Parms Parms;
	Parms.OldState=OldState;
	Parms.NewState=NewState;
	OnVisualStateChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnVisualStateChanged

// Begin Class AVisualEffectsManager Function ApplyDamageEffect
struct Z_Construct_UFunction_AVisualEffectsManager_ApplyDamageEffect_Statics
{
	struct VisualEffectsManager_eventApplyDamageEffect_Parms
	{
		float Intensity;
		float Duration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Custom Effects */" },
#endif
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom Effects" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AVisualEffectsManager_ApplyDamageEffect_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventApplyDamageEffect_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AVisualEffectsManager_ApplyDamageEffect_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventApplyDamageEffect_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AVisualEffectsManager_ApplyDamageEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_ApplyDamageEffect_Statics::NewProp_Intensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_ApplyDamageEffect_Statics::NewProp_Duration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_ApplyDamageEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AVisualEffectsManager_ApplyDamageEffect_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AVisualEffectsManager, nullptr, "ApplyDamageEffect", nullptr, nullptr, Z_Construct_UFunction_AVisualEffectsManager_ApplyDamageEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_ApplyDamageEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_AVisualEffectsManager_ApplyDamageEffect_Statics::VisualEffectsManager_eventApplyDamageEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_ApplyDamageEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_AVisualEffectsManager_ApplyDamageEffect_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AVisualEffectsManager_ApplyDamageEffect_Statics::VisualEffectsManager_eventApplyDamageEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AVisualEffectsManager_ApplyDamageEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AVisualEffectsManager_ApplyDamageEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AVisualEffectsManager::execApplyDamageEffect)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyDamageEffect(Z_Param_Intensity,Z_Param_Duration);
	P_NATIVE_END;
}
// End Class AVisualEffectsManager Function ApplyDamageEffect

// Begin Class AVisualEffectsManager Function ApplyExplosionEffect
struct Z_Construct_UFunction_AVisualEffectsManager_ApplyExplosionEffect_Statics
{
	struct VisualEffectsManager_eventApplyExplosionEffect_Parms
	{
		FVector Location;
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Effects" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AVisualEffectsManager_ApplyExplosionEffect_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventApplyExplosionEffect_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AVisualEffectsManager_ApplyExplosionEffect_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventApplyExplosionEffect_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AVisualEffectsManager_ApplyExplosionEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_ApplyExplosionEffect_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_ApplyExplosionEffect_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_ApplyExplosionEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AVisualEffectsManager_ApplyExplosionEffect_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AVisualEffectsManager, nullptr, "ApplyExplosionEffect", nullptr, nullptr, Z_Construct_UFunction_AVisualEffectsManager_ApplyExplosionEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_ApplyExplosionEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_AVisualEffectsManager_ApplyExplosionEffect_Statics::VisualEffectsManager_eventApplyExplosionEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_ApplyExplosionEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_AVisualEffectsManager_ApplyExplosionEffect_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AVisualEffectsManager_ApplyExplosionEffect_Statics::VisualEffectsManager_eventApplyExplosionEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AVisualEffectsManager_ApplyExplosionEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AVisualEffectsManager_ApplyExplosionEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AVisualEffectsManager::execApplyExplosionEffect)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyExplosionEffect(Z_Param_Out_Location,Z_Param_Intensity);
	P_NATIVE_END;
}
// End Class AVisualEffectsManager Function ApplyExplosionEffect

// Begin Class AVisualEffectsManager Function ApplyHealEffect
struct Z_Construct_UFunction_AVisualEffectsManager_ApplyHealEffect_Statics
{
	struct VisualEffectsManager_eventApplyHealEffect_Parms
	{
		float Duration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Effects" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AVisualEffectsManager_ApplyHealEffect_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventApplyHealEffect_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AVisualEffectsManager_ApplyHealEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_ApplyHealEffect_Statics::NewProp_Duration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_ApplyHealEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AVisualEffectsManager_ApplyHealEffect_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AVisualEffectsManager, nullptr, "ApplyHealEffect", nullptr, nullptr, Z_Construct_UFunction_AVisualEffectsManager_ApplyHealEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_ApplyHealEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_AVisualEffectsManager_ApplyHealEffect_Statics::VisualEffectsManager_eventApplyHealEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_ApplyHealEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_AVisualEffectsManager_ApplyHealEffect_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AVisualEffectsManager_ApplyHealEffect_Statics::VisualEffectsManager_eventApplyHealEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AVisualEffectsManager_ApplyHealEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AVisualEffectsManager_ApplyHealEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AVisualEffectsManager::execApplyHealEffect)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyHealEffect(Z_Param_Duration);
	P_NATIVE_END;
}
// End Class AVisualEffectsManager Function ApplyHealEffect

// Begin Class AVisualEffectsManager Function ApplyScreenShake
struct Z_Construct_UFunction_AVisualEffectsManager_ApplyScreenShake_Statics
{
	struct VisualEffectsManager_eventApplyScreenShake_Parms
	{
		float Intensity;
		float Duration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Custom Effects" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AVisualEffectsManager_ApplyScreenShake_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventApplyScreenShake_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AVisualEffectsManager_ApplyScreenShake_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventApplyScreenShake_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AVisualEffectsManager_ApplyScreenShake_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_ApplyScreenShake_Statics::NewProp_Intensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_ApplyScreenShake_Statics::NewProp_Duration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_ApplyScreenShake_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AVisualEffectsManager_ApplyScreenShake_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AVisualEffectsManager, nullptr, "ApplyScreenShake", nullptr, nullptr, Z_Construct_UFunction_AVisualEffectsManager_ApplyScreenShake_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_ApplyScreenShake_Statics::PropPointers), sizeof(Z_Construct_UFunction_AVisualEffectsManager_ApplyScreenShake_Statics::VisualEffectsManager_eventApplyScreenShake_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_ApplyScreenShake_Statics::Function_MetaDataParams), Z_Construct_UFunction_AVisualEffectsManager_ApplyScreenShake_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AVisualEffectsManager_ApplyScreenShake_Statics::VisualEffectsManager_eventApplyScreenShake_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AVisualEffectsManager_ApplyScreenShake()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AVisualEffectsManager_ApplyScreenShake_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AVisualEffectsManager::execApplyScreenShake)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyScreenShake(Z_Param_Intensity,Z_Param_Duration);
	P_NATIVE_END;
}
// End Class AVisualEffectsManager Function ApplyScreenShake

// Begin Class AVisualEffectsManager Function ApplySectorVisualTheme
struct Z_Construct_UFunction_AVisualEffectsManager_ApplySectorVisualTheme_Statics
{
	struct VisualEffectsManager_eventApplySectorVisualTheme_Parms
	{
		int32 SectorIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sector Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Sector-Specific Effects */" },
#endif
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sector-Specific Effects" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_SectorIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AVisualEffectsManager_ApplySectorVisualTheme_Statics::NewProp_SectorIndex = { "SectorIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventApplySectorVisualTheme_Parms, SectorIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AVisualEffectsManager_ApplySectorVisualTheme_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_ApplySectorVisualTheme_Statics::NewProp_SectorIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_ApplySectorVisualTheme_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AVisualEffectsManager_ApplySectorVisualTheme_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AVisualEffectsManager, nullptr, "ApplySectorVisualTheme", nullptr, nullptr, Z_Construct_UFunction_AVisualEffectsManager_ApplySectorVisualTheme_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_ApplySectorVisualTheme_Statics::PropPointers), sizeof(Z_Construct_UFunction_AVisualEffectsManager_ApplySectorVisualTheme_Statics::VisualEffectsManager_eventApplySectorVisualTheme_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_ApplySectorVisualTheme_Statics::Function_MetaDataParams), Z_Construct_UFunction_AVisualEffectsManager_ApplySectorVisualTheme_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AVisualEffectsManager_ApplySectorVisualTheme_Statics::VisualEffectsManager_eventApplySectorVisualTheme_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AVisualEffectsManager_ApplySectorVisualTheme()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AVisualEffectsManager_ApplySectorVisualTheme_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AVisualEffectsManager::execApplySectorVisualTheme)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_SectorIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplySectorVisualTheme(Z_Param_SectorIndex);
	P_NATIVE_END;
}
// End Class AVisualEffectsManager Function ApplySectorVisualTheme

// Begin Class AVisualEffectsManager Function ApplyVisualEffectSettings
struct Z_Construct_UFunction_AVisualEffectsManager_ApplyVisualEffectSettings_Statics
{
	struct VisualEffectsManager_eventApplyVisualEffectSettings_Parms
	{
		FVisualEffectSettings Settings;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Post Process" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Post Process Control */" },
#endif
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Post Process Control" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Settings_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Settings;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AVisualEffectsManager_ApplyVisualEffectSettings_Statics::NewProp_Settings = { "Settings", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventApplyVisualEffectSettings_Parms, Settings), Z_Construct_UScriptStruct_FVisualEffectSettings, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Settings_MetaData), NewProp_Settings_MetaData) }; // 2717571534
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AVisualEffectsManager_ApplyVisualEffectSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_ApplyVisualEffectSettings_Statics::NewProp_Settings,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_ApplyVisualEffectSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AVisualEffectsManager_ApplyVisualEffectSettings_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AVisualEffectsManager, nullptr, "ApplyVisualEffectSettings", nullptr, nullptr, Z_Construct_UFunction_AVisualEffectsManager_ApplyVisualEffectSettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_ApplyVisualEffectSettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_AVisualEffectsManager_ApplyVisualEffectSettings_Statics::VisualEffectsManager_eventApplyVisualEffectSettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_ApplyVisualEffectSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_AVisualEffectsManager_ApplyVisualEffectSettings_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AVisualEffectsManager_ApplyVisualEffectSettings_Statics::VisualEffectsManager_eventApplyVisualEffectSettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AVisualEffectsManager_ApplyVisualEffectSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AVisualEffectsManager_ApplyVisualEffectSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AVisualEffectsManager::execApplyVisualEffectSettings)
{
	P_GET_STRUCT_REF(FVisualEffectSettings,Z_Param_Out_Settings);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyVisualEffectSettings(Z_Param_Out_Settings);
	P_NATIVE_END;
}
// End Class AVisualEffectsManager Function ApplyVisualEffectSettings

// Begin Class AVisualEffectsManager Function BlendVisualEffectSettings
struct Z_Construct_UFunction_AVisualEffectsManager_BlendVisualEffectSettings_Statics
{
	struct VisualEffectsManager_eventBlendVisualEffectSettings_Parms
	{
		FVisualEffectSettings FromSettings;
		FVisualEffectSettings ToSettings;
		float Alpha;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Post Process" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FromSettings_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ToSettings_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_FromSettings;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ToSettings;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Alpha;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AVisualEffectsManager_BlendVisualEffectSettings_Statics::NewProp_FromSettings = { "FromSettings", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventBlendVisualEffectSettings_Parms, FromSettings), Z_Construct_UScriptStruct_FVisualEffectSettings, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FromSettings_MetaData), NewProp_FromSettings_MetaData) }; // 2717571534
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AVisualEffectsManager_BlendVisualEffectSettings_Statics::NewProp_ToSettings = { "ToSettings", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventBlendVisualEffectSettings_Parms, ToSettings), Z_Construct_UScriptStruct_FVisualEffectSettings, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ToSettings_MetaData), NewProp_ToSettings_MetaData) }; // 2717571534
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AVisualEffectsManager_BlendVisualEffectSettings_Statics::NewProp_Alpha = { "Alpha", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventBlendVisualEffectSettings_Parms, Alpha), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AVisualEffectsManager_BlendVisualEffectSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_BlendVisualEffectSettings_Statics::NewProp_FromSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_BlendVisualEffectSettings_Statics::NewProp_ToSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_BlendVisualEffectSettings_Statics::NewProp_Alpha,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_BlendVisualEffectSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AVisualEffectsManager_BlendVisualEffectSettings_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AVisualEffectsManager, nullptr, "BlendVisualEffectSettings", nullptr, nullptr, Z_Construct_UFunction_AVisualEffectsManager_BlendVisualEffectSettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_BlendVisualEffectSettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_AVisualEffectsManager_BlendVisualEffectSettings_Statics::VisualEffectsManager_eventBlendVisualEffectSettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_BlendVisualEffectSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_AVisualEffectsManager_BlendVisualEffectSettings_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AVisualEffectsManager_BlendVisualEffectSettings_Statics::VisualEffectsManager_eventBlendVisualEffectSettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AVisualEffectsManager_BlendVisualEffectSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AVisualEffectsManager_BlendVisualEffectSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AVisualEffectsManager::execBlendVisualEffectSettings)
{
	P_GET_STRUCT_REF(FVisualEffectSettings,Z_Param_Out_FromSettings);
	P_GET_STRUCT_REF(FVisualEffectSettings,Z_Param_Out_ToSettings);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Alpha);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->BlendVisualEffectSettings(Z_Param_Out_FromSettings,Z_Param_Out_ToSettings,Z_Param_Alpha);
	P_NATIVE_END;
}
// End Class AVisualEffectsManager Function BlendVisualEffectSettings

// Begin Class AVisualEffectsManager Function CreateBulletTrail
struct Z_Construct_UFunction_AVisualEffectsManager_CreateBulletTrail_Statics
{
	struct VisualEffectsManager_eventCreateBulletTrail_Parms
	{
		FVector StartLocation;
		FVector EndLocation;
		FString WeaponType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Effects" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StartLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EndLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_StartLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EndLocation;
	static const UECodeGen_Private::FStrPropertyParams NewProp_WeaponType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AVisualEffectsManager_CreateBulletTrail_Statics::NewProp_StartLocation = { "StartLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventCreateBulletTrail_Parms, StartLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StartLocation_MetaData), NewProp_StartLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AVisualEffectsManager_CreateBulletTrail_Statics::NewProp_EndLocation = { "EndLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventCreateBulletTrail_Parms, EndLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EndLocation_MetaData), NewProp_EndLocation_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AVisualEffectsManager_CreateBulletTrail_Statics::NewProp_WeaponType = { "WeaponType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventCreateBulletTrail_Parms, WeaponType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponType_MetaData), NewProp_WeaponType_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AVisualEffectsManager_CreateBulletTrail_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_CreateBulletTrail_Statics::NewProp_StartLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_CreateBulletTrail_Statics::NewProp_EndLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_CreateBulletTrail_Statics::NewProp_WeaponType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_CreateBulletTrail_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AVisualEffectsManager_CreateBulletTrail_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AVisualEffectsManager, nullptr, "CreateBulletTrail", nullptr, nullptr, Z_Construct_UFunction_AVisualEffectsManager_CreateBulletTrail_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_CreateBulletTrail_Statics::PropPointers), sizeof(Z_Construct_UFunction_AVisualEffectsManager_CreateBulletTrail_Statics::VisualEffectsManager_eventCreateBulletTrail_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_CreateBulletTrail_Statics::Function_MetaDataParams), Z_Construct_UFunction_AVisualEffectsManager_CreateBulletTrail_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AVisualEffectsManager_CreateBulletTrail_Statics::VisualEffectsManager_eventCreateBulletTrail_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AVisualEffectsManager_CreateBulletTrail()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AVisualEffectsManager_CreateBulletTrail_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AVisualEffectsManager::execCreateBulletTrail)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_StartLocation);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_EndLocation);
	P_GET_PROPERTY(FStrProperty,Z_Param_WeaponType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateBulletTrail(Z_Param_Out_StartLocation,Z_Param_Out_EndLocation,Z_Param_WeaponType);
	P_NATIVE_END;
}
// End Class AVisualEffectsManager Function CreateBulletTrail

// Begin Class AVisualEffectsManager Function DisableBulletTimeEffects
struct Z_Construct_UFunction_AVisualEffectsManager_DisableBulletTimeEffects_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Bullet Time" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AVisualEffectsManager_DisableBulletTimeEffects_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AVisualEffectsManager, nullptr, "DisableBulletTimeEffects", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_DisableBulletTimeEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_AVisualEffectsManager_DisableBulletTimeEffects_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AVisualEffectsManager_DisableBulletTimeEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AVisualEffectsManager_DisableBulletTimeEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AVisualEffectsManager::execDisableBulletTimeEffects)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DisableBulletTimeEffects();
	P_NATIVE_END;
}
// End Class AVisualEffectsManager Function DisableBulletTimeEffects

// Begin Class AVisualEffectsManager Function EnableBulletTimeEffects
struct Z_Construct_UFunction_AVisualEffectsManager_EnableBulletTimeEffects_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Bullet Time" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Bullet Time Effects */" },
#endif
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Bullet Time Effects" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AVisualEffectsManager_EnableBulletTimeEffects_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AVisualEffectsManager, nullptr, "EnableBulletTimeEffects", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_EnableBulletTimeEffects_Statics::Function_MetaDataParams), Z_Construct_UFunction_AVisualEffectsManager_EnableBulletTimeEffects_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AVisualEffectsManager_EnableBulletTimeEffects()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AVisualEffectsManager_EnableBulletTimeEffects_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AVisualEffectsManager::execEnableBulletTimeEffects)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableBulletTimeEffects();
	P_NATIVE_END;
}
// End Class AVisualEffectsManager Function EnableBulletTimeEffects

// Begin Class AVisualEffectsManager Function EnableLODSystem
struct Z_Construct_UFunction_AVisualEffectsManager_EnableLODSystem_Statics
{
	struct VisualEffectsManager_eventEnableLODSystem_Parms
	{
		bool bEnable;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnable;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AVisualEffectsManager_EnableLODSystem_Statics::NewProp_bEnable_SetBit(void* Obj)
{
	((VisualEffectsManager_eventEnableLODSystem_Parms*)Obj)->bEnable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AVisualEffectsManager_EnableLODSystem_Statics::NewProp_bEnable = { "bEnable", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(VisualEffectsManager_eventEnableLODSystem_Parms), &Z_Construct_UFunction_AVisualEffectsManager_EnableLODSystem_Statics::NewProp_bEnable_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AVisualEffectsManager_EnableLODSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_EnableLODSystem_Statics::NewProp_bEnable,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_EnableLODSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AVisualEffectsManager_EnableLODSystem_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AVisualEffectsManager, nullptr, "EnableLODSystem", nullptr, nullptr, Z_Construct_UFunction_AVisualEffectsManager_EnableLODSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_EnableLODSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_AVisualEffectsManager_EnableLODSystem_Statics::VisualEffectsManager_eventEnableLODSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_EnableLODSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_AVisualEffectsManager_EnableLODSystem_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AVisualEffectsManager_EnableLODSystem_Statics::VisualEffectsManager_eventEnableLODSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AVisualEffectsManager_EnableLODSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AVisualEffectsManager_EnableLODSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AVisualEffectsManager::execEnableLODSystem)
{
	P_GET_UBOOL(Z_Param_bEnable);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableLODSystem(Z_Param_bEnable);
	P_NATIVE_END;
}
// End Class AVisualEffectsManager Function EnableLODSystem

// Begin Class AVisualEffectsManager Function GetCurrentVisualState
struct Z_Construct_UFunction_AVisualEffectsManager_GetCurrentVisualState_Statics
{
	struct VisualEffectsManager_eventGetCurrentVisualState_Parms
	{
		EGameVisualState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Visual Effects" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AVisualEffectsManager_GetCurrentVisualState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AVisualEffectsManager_GetCurrentVisualState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventGetCurrentVisualState_Parms, ReturnValue), Z_Construct_UEnum_RoughReality_EGameVisualState, METADATA_PARAMS(0, nullptr) }; // 3351377574
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AVisualEffectsManager_GetCurrentVisualState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_GetCurrentVisualState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_GetCurrentVisualState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_GetCurrentVisualState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AVisualEffectsManager_GetCurrentVisualState_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AVisualEffectsManager, nullptr, "GetCurrentVisualState", nullptr, nullptr, Z_Construct_UFunction_AVisualEffectsManager_GetCurrentVisualState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_GetCurrentVisualState_Statics::PropPointers), sizeof(Z_Construct_UFunction_AVisualEffectsManager_GetCurrentVisualState_Statics::VisualEffectsManager_eventGetCurrentVisualState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_GetCurrentVisualState_Statics::Function_MetaDataParams), Z_Construct_UFunction_AVisualEffectsManager_GetCurrentVisualState_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AVisualEffectsManager_GetCurrentVisualState_Statics::VisualEffectsManager_eventGetCurrentVisualState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AVisualEffectsManager_GetCurrentVisualState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AVisualEffectsManager_GetCurrentVisualState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AVisualEffectsManager::execGetCurrentVisualState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EGameVisualState*)Z_Param__Result=P_THIS->GetCurrentVisualState();
	P_NATIVE_END;
}
// End Class AVisualEffectsManager Function GetCurrentVisualState

// Begin Class AVisualEffectsManager Function GetVisualEffectsManager
struct Z_Construct_UFunction_AVisualEffectsManager_GetVisualEffectsManager_Statics
{
	struct VisualEffectsManager_eventGetVisualEffectsManager_Parms
	{
		const UObject* WorldContext;
		AVisualEffectsManager* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Visual Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Static Access */" },
#endif
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Static Access" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldContext_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldContext;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AVisualEffectsManager_GetVisualEffectsManager_Statics::NewProp_WorldContext = { "WorldContext", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventGetVisualEffectsManager_Parms, WorldContext), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldContext_MetaData), NewProp_WorldContext_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AVisualEffectsManager_GetVisualEffectsManager_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventGetVisualEffectsManager_Parms, ReturnValue), Z_Construct_UClass_AVisualEffectsManager_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AVisualEffectsManager_GetVisualEffectsManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_GetVisualEffectsManager_Statics::NewProp_WorldContext,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_GetVisualEffectsManager_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_GetVisualEffectsManager_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AVisualEffectsManager_GetVisualEffectsManager_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AVisualEffectsManager, nullptr, "GetVisualEffectsManager", nullptr, nullptr, Z_Construct_UFunction_AVisualEffectsManager_GetVisualEffectsManager_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_GetVisualEffectsManager_Statics::PropPointers), sizeof(Z_Construct_UFunction_AVisualEffectsManager_GetVisualEffectsManager_Statics::VisualEffectsManager_eventGetVisualEffectsManager_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_GetVisualEffectsManager_Statics::Function_MetaDataParams), Z_Construct_UFunction_AVisualEffectsManager_GetVisualEffectsManager_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AVisualEffectsManager_GetVisualEffectsManager_Statics::VisualEffectsManager_eventGetVisualEffectsManager_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AVisualEffectsManager_GetVisualEffectsManager()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AVisualEffectsManager_GetVisualEffectsManager_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AVisualEffectsManager::execGetVisualEffectsManager)
{
	P_GET_OBJECT(UObject,Z_Param_WorldContext);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(AVisualEffectsManager**)Z_Param__Result=AVisualEffectsManager::GetVisualEffectsManager(Z_Param_WorldContext);
	P_NATIVE_END;
}
// End Class AVisualEffectsManager Function GetVisualEffectsManager

// Begin Class AVisualEffectsManager Function SetEffectQuality
struct Z_Construct_UFunction_AVisualEffectsManager_SetEffectQuality_Statics
{
	struct VisualEffectsManager_eventSetEffectQuality_Parms
	{
		int32 QualityLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Performance Optimization */" },
#endif
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Performance Optimization" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_QualityLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AVisualEffectsManager_SetEffectQuality_Statics::NewProp_QualityLevel = { "QualityLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventSetEffectQuality_Parms, QualityLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AVisualEffectsManager_SetEffectQuality_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_SetEffectQuality_Statics::NewProp_QualityLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_SetEffectQuality_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AVisualEffectsManager_SetEffectQuality_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AVisualEffectsManager, nullptr, "SetEffectQuality", nullptr, nullptr, Z_Construct_UFunction_AVisualEffectsManager_SetEffectQuality_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_SetEffectQuality_Statics::PropPointers), sizeof(Z_Construct_UFunction_AVisualEffectsManager_SetEffectQuality_Statics::VisualEffectsManager_eventSetEffectQuality_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_SetEffectQuality_Statics::Function_MetaDataParams), Z_Construct_UFunction_AVisualEffectsManager_SetEffectQuality_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AVisualEffectsManager_SetEffectQuality_Statics::VisualEffectsManager_eventSetEffectQuality_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AVisualEffectsManager_SetEffectQuality()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AVisualEffectsManager_SetEffectQuality_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AVisualEffectsManager::execSetEffectQuality)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_QualityLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetEffectQuality(Z_Param_QualityLevel);
	P_NATIVE_END;
}
// End Class AVisualEffectsManager Function SetEffectQuality

// Begin Class AVisualEffectsManager Function SetGlobalScalarParameter
struct Z_Construct_UFunction_AVisualEffectsManager_SetGlobalScalarParameter_Statics
{
	struct VisualEffectsManager_eventSetGlobalScalarParameter_Parms
	{
		FName ParameterName;
		float Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Parameters" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Material Parameter Control */" },
#endif
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material Parameter Control" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ParameterName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AVisualEffectsManager_SetGlobalScalarParameter_Statics::NewProp_ParameterName = { "ParameterName", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventSetGlobalScalarParameter_Parms, ParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterName_MetaData), NewProp_ParameterName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AVisualEffectsManager_SetGlobalScalarParameter_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventSetGlobalScalarParameter_Parms, Value), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AVisualEffectsManager_SetGlobalScalarParameter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_SetGlobalScalarParameter_Statics::NewProp_ParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_SetGlobalScalarParameter_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_SetGlobalScalarParameter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AVisualEffectsManager_SetGlobalScalarParameter_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AVisualEffectsManager, nullptr, "SetGlobalScalarParameter", nullptr, nullptr, Z_Construct_UFunction_AVisualEffectsManager_SetGlobalScalarParameter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_SetGlobalScalarParameter_Statics::PropPointers), sizeof(Z_Construct_UFunction_AVisualEffectsManager_SetGlobalScalarParameter_Statics::VisualEffectsManager_eventSetGlobalScalarParameter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_SetGlobalScalarParameter_Statics::Function_MetaDataParams), Z_Construct_UFunction_AVisualEffectsManager_SetGlobalScalarParameter_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AVisualEffectsManager_SetGlobalScalarParameter_Statics::VisualEffectsManager_eventSetGlobalScalarParameter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AVisualEffectsManager_SetGlobalScalarParameter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AVisualEffectsManager_SetGlobalScalarParameter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AVisualEffectsManager::execSetGlobalScalarParameter)
{
	P_GET_PROPERTY_REF(FNameProperty,Z_Param_Out_ParameterName);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetGlobalScalarParameter(Z_Param_Out_ParameterName,Z_Param_Value);
	P_NATIVE_END;
}
// End Class AVisualEffectsManager Function SetGlobalScalarParameter

// Begin Class AVisualEffectsManager Function SetGlobalVectorParameter
struct Z_Construct_UFunction_AVisualEffectsManager_SetGlobalVectorParameter_Statics
{
	struct VisualEffectsManager_eventSetGlobalVectorParameter_Parms
	{
		FName ParameterName;
		FLinearColor Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Parameters" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Value_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ParameterName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AVisualEffectsManager_SetGlobalVectorParameter_Statics::NewProp_ParameterName = { "ParameterName", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventSetGlobalVectorParameter_Parms, ParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterName_MetaData), NewProp_ParameterName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AVisualEffectsManager_SetGlobalVectorParameter_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventSetGlobalVectorParameter_Parms, Value), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Value_MetaData), NewProp_Value_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AVisualEffectsManager_SetGlobalVectorParameter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_SetGlobalVectorParameter_Statics::NewProp_ParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_SetGlobalVectorParameter_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_SetGlobalVectorParameter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AVisualEffectsManager_SetGlobalVectorParameter_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AVisualEffectsManager, nullptr, "SetGlobalVectorParameter", nullptr, nullptr, Z_Construct_UFunction_AVisualEffectsManager_SetGlobalVectorParameter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_SetGlobalVectorParameter_Statics::PropPointers), sizeof(Z_Construct_UFunction_AVisualEffectsManager_SetGlobalVectorParameter_Statics::VisualEffectsManager_eventSetGlobalVectorParameter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_SetGlobalVectorParameter_Statics::Function_MetaDataParams), Z_Construct_UFunction_AVisualEffectsManager_SetGlobalVectorParameter_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AVisualEffectsManager_SetGlobalVectorParameter_Statics::VisualEffectsManager_eventSetGlobalVectorParameter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AVisualEffectsManager_SetGlobalVectorParameter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AVisualEffectsManager_SetGlobalVectorParameter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AVisualEffectsManager::execSetGlobalVectorParameter)
{
	P_GET_PROPERTY_REF(FNameProperty,Z_Param_Out_ParameterName);
	P_GET_STRUCT_REF(FLinearColor,Z_Param_Out_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetGlobalVectorParameter(Z_Param_Out_ParameterName,Z_Param_Out_Value);
	P_NATIVE_END;
}
// End Class AVisualEffectsManager Function SetGlobalVectorParameter

// Begin Class AVisualEffectsManager Function SetSectorAmbientColor
struct Z_Construct_UFunction_AVisualEffectsManager_SetSectorAmbientColor_Statics
{
	struct VisualEffectsManager_eventSetSectorAmbientColor_Parms
	{
		FLinearColor Color;
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sector Effects" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Color_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Color;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AVisualEffectsManager_SetSectorAmbientColor_Statics::NewProp_Color = { "Color", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventSetSectorAmbientColor_Parms, Color), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Color_MetaData), NewProp_Color_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AVisualEffectsManager_SetSectorAmbientColor_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventSetSectorAmbientColor_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AVisualEffectsManager_SetSectorAmbientColor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_SetSectorAmbientColor_Statics::NewProp_Color,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_SetSectorAmbientColor_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_SetSectorAmbientColor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AVisualEffectsManager_SetSectorAmbientColor_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AVisualEffectsManager, nullptr, "SetSectorAmbientColor", nullptr, nullptr, Z_Construct_UFunction_AVisualEffectsManager_SetSectorAmbientColor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_SetSectorAmbientColor_Statics::PropPointers), sizeof(Z_Construct_UFunction_AVisualEffectsManager_SetSectorAmbientColor_Statics::VisualEffectsManager_eventSetSectorAmbientColor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_SetSectorAmbientColor_Statics::Function_MetaDataParams), Z_Construct_UFunction_AVisualEffectsManager_SetSectorAmbientColor_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AVisualEffectsManager_SetSectorAmbientColor_Statics::VisualEffectsManager_eventSetSectorAmbientColor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AVisualEffectsManager_SetSectorAmbientColor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AVisualEffectsManager_SetSectorAmbientColor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AVisualEffectsManager::execSetSectorAmbientColor)
{
	P_GET_STRUCT_REF(FLinearColor,Z_Param_Out_Color);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetSectorAmbientColor(Z_Param_Out_Color,Z_Param_Intensity);
	P_NATIVE_END;
}
// End Class AVisualEffectsManager Function SetSectorAmbientColor

// Begin Class AVisualEffectsManager Function SetSectorFogSettings
struct Z_Construct_UFunction_AVisualEffectsManager_SetSectorFogSettings_Statics
{
	struct VisualEffectsManager_eventSetSectorFogSettings_Parms
	{
		float Density;
		FLinearColor Color;
		float StartDistance;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Sector Effects" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Color_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Density;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Color;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StartDistance;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AVisualEffectsManager_SetSectorFogSettings_Statics::NewProp_Density = { "Density", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventSetSectorFogSettings_Parms, Density), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AVisualEffectsManager_SetSectorFogSettings_Statics::NewProp_Color = { "Color", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventSetSectorFogSettings_Parms, Color), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Color_MetaData), NewProp_Color_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AVisualEffectsManager_SetSectorFogSettings_Statics::NewProp_StartDistance = { "StartDistance", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventSetSectorFogSettings_Parms, StartDistance), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AVisualEffectsManager_SetSectorFogSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_SetSectorFogSettings_Statics::NewProp_Density,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_SetSectorFogSettings_Statics::NewProp_Color,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_SetSectorFogSettings_Statics::NewProp_StartDistance,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_SetSectorFogSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AVisualEffectsManager_SetSectorFogSettings_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AVisualEffectsManager, nullptr, "SetSectorFogSettings", nullptr, nullptr, Z_Construct_UFunction_AVisualEffectsManager_SetSectorFogSettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_SetSectorFogSettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_AVisualEffectsManager_SetSectorFogSettings_Statics::VisualEffectsManager_eventSetSectorFogSettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_SetSectorFogSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_AVisualEffectsManager_SetSectorFogSettings_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AVisualEffectsManager_SetSectorFogSettings_Statics::VisualEffectsManager_eventSetSectorFogSettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AVisualEffectsManager_SetSectorFogSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AVisualEffectsManager_SetSectorFogSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AVisualEffectsManager::execSetSectorFogSettings)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Density);
	P_GET_STRUCT_REF(FLinearColor,Z_Param_Out_Color);
	P_GET_PROPERTY(FFloatProperty,Z_Param_StartDistance);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetSectorFogSettings(Z_Param_Density,Z_Param_Out_Color,Z_Param_StartDistance);
	P_NATIVE_END;
}
// End Class AVisualEffectsManager Function SetSectorFogSettings

// Begin Class AVisualEffectsManager Function SetVisualState
struct Z_Construct_UFunction_AVisualEffectsManager_SetVisualState_Statics
{
	struct VisualEffectsManager_eventSetVisualState_Parms
	{
		EGameVisualState NewState;
		float InTransitionDuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Visual Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Visual State Management */" },
#endif
		{ "CPP_Default_InTransitionDuration", "-1.000000" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visual State Management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InTransitionDuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AVisualEffectsManager_SetVisualState_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AVisualEffectsManager_SetVisualState_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventSetVisualState_Parms, NewState), Z_Construct_UEnum_RoughReality_EGameVisualState, METADATA_PARAMS(0, nullptr) }; // 3351377574
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AVisualEffectsManager_SetVisualState_Statics::NewProp_InTransitionDuration = { "InTransitionDuration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventSetVisualState_Parms, InTransitionDuration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AVisualEffectsManager_SetVisualState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_SetVisualState_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_SetVisualState_Statics::NewProp_NewState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_SetVisualState_Statics::NewProp_InTransitionDuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_SetVisualState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AVisualEffectsManager_SetVisualState_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AVisualEffectsManager, nullptr, "SetVisualState", nullptr, nullptr, Z_Construct_UFunction_AVisualEffectsManager_SetVisualState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_SetVisualState_Statics::PropPointers), sizeof(Z_Construct_UFunction_AVisualEffectsManager_SetVisualState_Statics::VisualEffectsManager_eventSetVisualState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_SetVisualState_Statics::Function_MetaDataParams), Z_Construct_UFunction_AVisualEffectsManager_SetVisualState_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AVisualEffectsManager_SetVisualState_Statics::VisualEffectsManager_eventSetVisualState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AVisualEffectsManager_SetVisualState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AVisualEffectsManager_SetVisualState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AVisualEffectsManager::execSetVisualState)
{
	P_GET_ENUM(EGameVisualState,Z_Param_NewState);
	P_GET_PROPERTY(FFloatProperty,Z_Param_InTransitionDuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetVisualState(EGameVisualState(Z_Param_NewState),Z_Param_InTransitionDuration);
	P_NATIVE_END;
}
// End Class AVisualEffectsManager Function SetVisualState

// Begin Class AVisualEffectsManager Function SetVisualStateImmediate
struct Z_Construct_UFunction_AVisualEffectsManager_SetVisualStateImmediate_Statics
{
	struct VisualEffectsManager_eventSetVisualStateImmediate_Parms
	{
		EGameVisualState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Visual Effects" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AVisualEffectsManager_SetVisualStateImmediate_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AVisualEffectsManager_SetVisualStateImmediate_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventSetVisualStateImmediate_Parms, NewState), Z_Construct_UEnum_RoughReality_EGameVisualState, METADATA_PARAMS(0, nullptr) }; // 3351377574
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AVisualEffectsManager_SetVisualStateImmediate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_SetVisualStateImmediate_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_SetVisualStateImmediate_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_SetVisualStateImmediate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AVisualEffectsManager_SetVisualStateImmediate_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AVisualEffectsManager, nullptr, "SetVisualStateImmediate", nullptr, nullptr, Z_Construct_UFunction_AVisualEffectsManager_SetVisualStateImmediate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_SetVisualStateImmediate_Statics::PropPointers), sizeof(Z_Construct_UFunction_AVisualEffectsManager_SetVisualStateImmediate_Statics::VisualEffectsManager_eventSetVisualStateImmediate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_SetVisualStateImmediate_Statics::Function_MetaDataParams), Z_Construct_UFunction_AVisualEffectsManager_SetVisualStateImmediate_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AVisualEffectsManager_SetVisualStateImmediate_Statics::VisualEffectsManager_eventSetVisualStateImmediate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AVisualEffectsManager_SetVisualStateImmediate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AVisualEffectsManager_SetVisualStateImmediate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AVisualEffectsManager::execSetVisualStateImmediate)
{
	P_GET_ENUM(EGameVisualState,Z_Param_NewState);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetVisualStateImmediate(EGameVisualState(Z_Param_NewState));
	P_NATIVE_END;
}
// End Class AVisualEffectsManager Function SetVisualStateImmediate

// Begin Class AVisualEffectsManager Function TriggerImpactEffect
struct Z_Construct_UFunction_AVisualEffectsManager_TriggerImpactEffect_Statics
{
	struct VisualEffectsManager_eventTriggerImpactEffect_Parms
	{
		FVector Location;
		FVector Normal;
		FString SurfaceType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Effects" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Normal_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SurfaceType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Normal;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SurfaceType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AVisualEffectsManager_TriggerImpactEffect_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventTriggerImpactEffect_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AVisualEffectsManager_TriggerImpactEffect_Statics::NewProp_Normal = { "Normal", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventTriggerImpactEffect_Parms, Normal), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Normal_MetaData), NewProp_Normal_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AVisualEffectsManager_TriggerImpactEffect_Statics::NewProp_SurfaceType = { "SurfaceType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventTriggerImpactEffect_Parms, SurfaceType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SurfaceType_MetaData), NewProp_SurfaceType_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AVisualEffectsManager_TriggerImpactEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_TriggerImpactEffect_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_TriggerImpactEffect_Statics::NewProp_Normal,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_TriggerImpactEffect_Statics::NewProp_SurfaceType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_TriggerImpactEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AVisualEffectsManager_TriggerImpactEffect_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AVisualEffectsManager, nullptr, "TriggerImpactEffect", nullptr, nullptr, Z_Construct_UFunction_AVisualEffectsManager_TriggerImpactEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_TriggerImpactEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_AVisualEffectsManager_TriggerImpactEffect_Statics::VisualEffectsManager_eventTriggerImpactEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_TriggerImpactEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_AVisualEffectsManager_TriggerImpactEffect_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AVisualEffectsManager_TriggerImpactEffect_Statics::VisualEffectsManager_eventTriggerImpactEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AVisualEffectsManager_TriggerImpactEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AVisualEffectsManager_TriggerImpactEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AVisualEffectsManager::execTriggerImpactEffect)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Normal);
	P_GET_PROPERTY(FStrProperty,Z_Param_SurfaceType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TriggerImpactEffect(Z_Param_Out_Location,Z_Param_Out_Normal,Z_Param_SurfaceType);
	P_NATIVE_END;
}
// End Class AVisualEffectsManager Function TriggerImpactEffect

// Begin Class AVisualEffectsManager Function TriggerMuzzleFlashEffect
struct Z_Construct_UFunction_AVisualEffectsManager_TriggerMuzzleFlashEffect_Statics
{
	struct VisualEffectsManager_eventTriggerMuzzleFlashEffect_Parms
	{
		FVector Location;
		FRotator Rotation;
		FString WeaponType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapon Effects" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Weapon-Specific Effects */" },
#endif
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weapon-Specific Effects" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponType_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FStrPropertyParams NewProp_WeaponType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AVisualEffectsManager_TriggerMuzzleFlashEffect_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventTriggerMuzzleFlashEffect_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AVisualEffectsManager_TriggerMuzzleFlashEffect_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventTriggerMuzzleFlashEffect_Parms, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AVisualEffectsManager_TriggerMuzzleFlashEffect_Statics::NewProp_WeaponType = { "WeaponType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventTriggerMuzzleFlashEffect_Parms, WeaponType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponType_MetaData), NewProp_WeaponType_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AVisualEffectsManager_TriggerMuzzleFlashEffect_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_TriggerMuzzleFlashEffect_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_TriggerMuzzleFlashEffect_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_TriggerMuzzleFlashEffect_Statics::NewProp_WeaponType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_TriggerMuzzleFlashEffect_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AVisualEffectsManager_TriggerMuzzleFlashEffect_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AVisualEffectsManager, nullptr, "TriggerMuzzleFlashEffect", nullptr, nullptr, Z_Construct_UFunction_AVisualEffectsManager_TriggerMuzzleFlashEffect_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_TriggerMuzzleFlashEffect_Statics::PropPointers), sizeof(Z_Construct_UFunction_AVisualEffectsManager_TriggerMuzzleFlashEffect_Statics::VisualEffectsManager_eventTriggerMuzzleFlashEffect_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_TriggerMuzzleFlashEffect_Statics::Function_MetaDataParams), Z_Construct_UFunction_AVisualEffectsManager_TriggerMuzzleFlashEffect_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AVisualEffectsManager_TriggerMuzzleFlashEffect_Statics::VisualEffectsManager_eventTriggerMuzzleFlashEffect_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AVisualEffectsManager_TriggerMuzzleFlashEffect()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AVisualEffectsManager_TriggerMuzzleFlashEffect_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AVisualEffectsManager::execTriggerMuzzleFlashEffect)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FRotator,Z_Param_Out_Rotation);
	P_GET_PROPERTY(FStrProperty,Z_Param_WeaponType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TriggerMuzzleFlashEffect(Z_Param_Out_Location,Z_Param_Out_Rotation,Z_Param_WeaponType);
	P_NATIVE_END;
}
// End Class AVisualEffectsManager Function TriggerMuzzleFlashEffect

// Begin Class AVisualEffectsManager Function UpdateBulletTimeIntensity
struct Z_Construct_UFunction_AVisualEffectsManager_UpdateBulletTimeIntensity_Statics
{
	struct VisualEffectsManager_eventUpdateBulletTimeIntensity_Parms
	{
		float Intensity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Bullet Time" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Intensity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AVisualEffectsManager_UpdateBulletTimeIntensity_Statics::NewProp_Intensity = { "Intensity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventUpdateBulletTimeIntensity_Parms, Intensity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AVisualEffectsManager_UpdateBulletTimeIntensity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_UpdateBulletTimeIntensity_Statics::NewProp_Intensity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_UpdateBulletTimeIntensity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AVisualEffectsManager_UpdateBulletTimeIntensity_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AVisualEffectsManager, nullptr, "UpdateBulletTimeIntensity", nullptr, nullptr, Z_Construct_UFunction_AVisualEffectsManager_UpdateBulletTimeIntensity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_UpdateBulletTimeIntensity_Statics::PropPointers), sizeof(Z_Construct_UFunction_AVisualEffectsManager_UpdateBulletTimeIntensity_Statics::VisualEffectsManager_eventUpdateBulletTimeIntensity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_UpdateBulletTimeIntensity_Statics::Function_MetaDataParams), Z_Construct_UFunction_AVisualEffectsManager_UpdateBulletTimeIntensity_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AVisualEffectsManager_UpdateBulletTimeIntensity_Statics::VisualEffectsManager_eventUpdateBulletTimeIntensity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AVisualEffectsManager_UpdateBulletTimeIntensity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AVisualEffectsManager_UpdateBulletTimeIntensity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AVisualEffectsManager::execUpdateBulletTimeIntensity)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_Intensity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateBulletTimeIntensity(Z_Param_Intensity);
	P_NATIVE_END;
}
// End Class AVisualEffectsManager Function UpdateBulletTimeIntensity

// Begin Class AVisualEffectsManager Function UpdateEffectLOD
struct Z_Construct_UFunction_AVisualEffectsManager_UpdateEffectLOD_Statics
{
	struct VisualEffectsManager_eventUpdateEffectLOD_Parms
	{
		float DistanceToPlayer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Performance" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DistanceToPlayer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AVisualEffectsManager_UpdateEffectLOD_Statics::NewProp_DistanceToPlayer = { "DistanceToPlayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualEffectsManager_eventUpdateEffectLOD_Parms, DistanceToPlayer), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AVisualEffectsManager_UpdateEffectLOD_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AVisualEffectsManager_UpdateEffectLOD_Statics::NewProp_DistanceToPlayer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_UpdateEffectLOD_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AVisualEffectsManager_UpdateEffectLOD_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AVisualEffectsManager, nullptr, "UpdateEffectLOD", nullptr, nullptr, Z_Construct_UFunction_AVisualEffectsManager_UpdateEffectLOD_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_UpdateEffectLOD_Statics::PropPointers), sizeof(Z_Construct_UFunction_AVisualEffectsManager_UpdateEffectLOD_Statics::VisualEffectsManager_eventUpdateEffectLOD_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_UpdateEffectLOD_Statics::Function_MetaDataParams), Z_Construct_UFunction_AVisualEffectsManager_UpdateEffectLOD_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AVisualEffectsManager_UpdateEffectLOD_Statics::VisualEffectsManager_eventUpdateEffectLOD_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AVisualEffectsManager_UpdateEffectLOD()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AVisualEffectsManager_UpdateEffectLOD_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AVisualEffectsManager::execUpdateEffectLOD)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DistanceToPlayer);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateEffectLOD(Z_Param_DistanceToPlayer);
	P_NATIVE_END;
}
// End Class AVisualEffectsManager Function UpdateEffectLOD

// Begin Class AVisualEffectsManager Function UpdateTimeParameters
struct Z_Construct_UFunction_AVisualEffectsManager_UpdateTimeParameters_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Material Parameters" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AVisualEffectsManager_UpdateTimeParameters_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AVisualEffectsManager, nullptr, "UpdateTimeParameters", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AVisualEffectsManager_UpdateTimeParameters_Statics::Function_MetaDataParams), Z_Construct_UFunction_AVisualEffectsManager_UpdateTimeParameters_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AVisualEffectsManager_UpdateTimeParameters()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AVisualEffectsManager_UpdateTimeParameters_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AVisualEffectsManager::execUpdateTimeParameters)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateTimeParameters();
	P_NATIVE_END;
}
// End Class AVisualEffectsManager Function UpdateTimeParameters

// Begin Class AVisualEffectsManager
void AVisualEffectsManager::StaticRegisterNativesAVisualEffectsManager()
{
	UClass* Class = AVisualEffectsManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyDamageEffect", &AVisualEffectsManager::execApplyDamageEffect },
		{ "ApplyExplosionEffect", &AVisualEffectsManager::execApplyExplosionEffect },
		{ "ApplyHealEffect", &AVisualEffectsManager::execApplyHealEffect },
		{ "ApplyScreenShake", &AVisualEffectsManager::execApplyScreenShake },
		{ "ApplySectorVisualTheme", &AVisualEffectsManager::execApplySectorVisualTheme },
		{ "ApplyVisualEffectSettings", &AVisualEffectsManager::execApplyVisualEffectSettings },
		{ "BlendVisualEffectSettings", &AVisualEffectsManager::execBlendVisualEffectSettings },
		{ "CreateBulletTrail", &AVisualEffectsManager::execCreateBulletTrail },
		{ "DisableBulletTimeEffects", &AVisualEffectsManager::execDisableBulletTimeEffects },
		{ "EnableBulletTimeEffects", &AVisualEffectsManager::execEnableBulletTimeEffects },
		{ "EnableLODSystem", &AVisualEffectsManager::execEnableLODSystem },
		{ "GetCurrentVisualState", &AVisualEffectsManager::execGetCurrentVisualState },
		{ "GetVisualEffectsManager", &AVisualEffectsManager::execGetVisualEffectsManager },
		{ "SetEffectQuality", &AVisualEffectsManager::execSetEffectQuality },
		{ "SetGlobalScalarParameter", &AVisualEffectsManager::execSetGlobalScalarParameter },
		{ "SetGlobalVectorParameter", &AVisualEffectsManager::execSetGlobalVectorParameter },
		{ "SetSectorAmbientColor", &AVisualEffectsManager::execSetSectorAmbientColor },
		{ "SetSectorFogSettings", &AVisualEffectsManager::execSetSectorFogSettings },
		{ "SetVisualState", &AVisualEffectsManager::execSetVisualState },
		{ "SetVisualStateImmediate", &AVisualEffectsManager::execSetVisualStateImmediate },
		{ "TriggerImpactEffect", &AVisualEffectsManager::execTriggerImpactEffect },
		{ "TriggerMuzzleFlashEffect", &AVisualEffectsManager::execTriggerMuzzleFlashEffect },
		{ "UpdateBulletTimeIntensity", &AVisualEffectsManager::execUpdateBulletTimeIntensity },
		{ "UpdateEffectLOD", &AVisualEffectsManager::execUpdateEffectLOD },
		{ "UpdateTimeParameters", &AVisualEffectsManager::execUpdateTimeParameters },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(AVisualEffectsManager);
UClass* Z_Construct_UClass_AVisualEffectsManager_NoRegister()
{
	return AVisualEffectsManager::StaticClass();
}
struct Z_Construct_UClass_AVisualEffectsManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Visual Effects Manager for Rough Reality\n * Manages post-processing effects, shaders, and visual state transitions\n */" },
#endif
		{ "IncludePath", "VFX/VisualEffectsManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visual Effects Manager for Rough Reality\nManages post-processing effects, shaders, and visual state transitions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PostProcessComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Components */" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentVisualState_MetaData[] = {
		{ "Category", "Visual State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Visual State */" },
#endif
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visual State" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsTransitioning_MetaData[] = {
		{ "Category", "Visual State" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalMaterialParameters_MetaData[] = {
		{ "Category", "Materials" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Material Parameter Collection */" },
#endif
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material Parameter Collection" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisualStatePresets_MetaData[] = {
		{ "Category", "Effect Presets" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Visual Effect Presets */" },
#endif
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visual Effect Presets" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BulletTimeSettings_MetaData[] = {
		{ "Category", "Effect Presets" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TransitionCurve_MetaData[] = {
		{ "Category", "Transitions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Transition Curves */" },
#endif
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Transition Curves" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultTransitionDuration_MetaData[] = {
		{ "Category", "Transitions" },
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnVisualStateChanged_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Events */" },
#endif
		{ "ModuleRelativePath", "VFX/VisualEffectsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PostProcessComponent;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentVisualState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentVisualState;
	static void NewProp_bIsTransitioning_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsTransitioning;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_GlobalMaterialParameters;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VisualStatePresets_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_VisualStatePresets_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_VisualStatePresets_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_VisualStatePresets;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BulletTimeSettings;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TransitionCurve;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DefaultTransitionDuration;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnVisualStateChanged;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AVisualEffectsManager_ApplyDamageEffect, "ApplyDamageEffect" }, // 2692084102
		{ &Z_Construct_UFunction_AVisualEffectsManager_ApplyExplosionEffect, "ApplyExplosionEffect" }, // 1133433647
		{ &Z_Construct_UFunction_AVisualEffectsManager_ApplyHealEffect, "ApplyHealEffect" }, // 3373747554
		{ &Z_Construct_UFunction_AVisualEffectsManager_ApplyScreenShake, "ApplyScreenShake" }, // 2014819190
		{ &Z_Construct_UFunction_AVisualEffectsManager_ApplySectorVisualTheme, "ApplySectorVisualTheme" }, // 2296164497
		{ &Z_Construct_UFunction_AVisualEffectsManager_ApplyVisualEffectSettings, "ApplyVisualEffectSettings" }, // 3645266348
		{ &Z_Construct_UFunction_AVisualEffectsManager_BlendVisualEffectSettings, "BlendVisualEffectSettings" }, // 2308815336
		{ &Z_Construct_UFunction_AVisualEffectsManager_CreateBulletTrail, "CreateBulletTrail" }, // 1982856191
		{ &Z_Construct_UFunction_AVisualEffectsManager_DisableBulletTimeEffects, "DisableBulletTimeEffects" }, // 3788755448
		{ &Z_Construct_UFunction_AVisualEffectsManager_EnableBulletTimeEffects, "EnableBulletTimeEffects" }, // 3840111932
		{ &Z_Construct_UFunction_AVisualEffectsManager_EnableLODSystem, "EnableLODSystem" }, // 1236664134
		{ &Z_Construct_UFunction_AVisualEffectsManager_GetCurrentVisualState, "GetCurrentVisualState" }, // 3495768319
		{ &Z_Construct_UFunction_AVisualEffectsManager_GetVisualEffectsManager, "GetVisualEffectsManager" }, // 3867905370
		{ &Z_Construct_UFunction_AVisualEffectsManager_SetEffectQuality, "SetEffectQuality" }, // 1395087481
		{ &Z_Construct_UFunction_AVisualEffectsManager_SetGlobalScalarParameter, "SetGlobalScalarParameter" }, // 2994694089
		{ &Z_Construct_UFunction_AVisualEffectsManager_SetGlobalVectorParameter, "SetGlobalVectorParameter" }, // 1690380204
		{ &Z_Construct_UFunction_AVisualEffectsManager_SetSectorAmbientColor, "SetSectorAmbientColor" }, // 2714062268
		{ &Z_Construct_UFunction_AVisualEffectsManager_SetSectorFogSettings, "SetSectorFogSettings" }, // 1791463430
		{ &Z_Construct_UFunction_AVisualEffectsManager_SetVisualState, "SetVisualState" }, // 3517391523
		{ &Z_Construct_UFunction_AVisualEffectsManager_SetVisualStateImmediate, "SetVisualStateImmediate" }, // 2561383513
		{ &Z_Construct_UFunction_AVisualEffectsManager_TriggerImpactEffect, "TriggerImpactEffect" }, // 1425586901
		{ &Z_Construct_UFunction_AVisualEffectsManager_TriggerMuzzleFlashEffect, "TriggerMuzzleFlashEffect" }, // 2829072569
		{ &Z_Construct_UFunction_AVisualEffectsManager_UpdateBulletTimeIntensity, "UpdateBulletTimeIntensity" }, // 1657973845
		{ &Z_Construct_UFunction_AVisualEffectsManager_UpdateEffectLOD, "UpdateEffectLOD" }, // 1058897651
		{ &Z_Construct_UFunction_AVisualEffectsManager_UpdateTimeParameters, "UpdateTimeParameters" }, // 1641005702
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AVisualEffectsManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_PostProcessComponent = { "PostProcessComponent", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AVisualEffectsManager, PostProcessComponent), Z_Construct_UClass_UPostProcessComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PostProcessComponent_MetaData), NewProp_PostProcessComponent_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_CurrentVisualState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_CurrentVisualState = { "CurrentVisualState", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AVisualEffectsManager, CurrentVisualState), Z_Construct_UEnum_RoughReality_EGameVisualState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentVisualState_MetaData), NewProp_CurrentVisualState_MetaData) }; // 3351377574
void Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_bIsTransitioning_SetBit(void* Obj)
{
	((AVisualEffectsManager*)Obj)->bIsTransitioning = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_bIsTransitioning = { "bIsTransitioning", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AVisualEffectsManager), &Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_bIsTransitioning_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsTransitioning_MetaData), NewProp_bIsTransitioning_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_GlobalMaterialParameters = { "GlobalMaterialParameters", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AVisualEffectsManager, GlobalMaterialParameters), Z_Construct_UClass_UMaterialParameterCollection_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalMaterialParameters_MetaData), NewProp_GlobalMaterialParameters_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_VisualStatePresets_ValueProp = { "VisualStatePresets", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FVisualEffectSettings, METADATA_PARAMS(0, nullptr) }; // 2717571534
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_VisualStatePresets_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_VisualStatePresets_Key_KeyProp = { "VisualStatePresets_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_RoughReality_EGameVisualState, METADATA_PARAMS(0, nullptr) }; // 3351377574
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_VisualStatePresets = { "VisualStatePresets", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AVisualEffectsManager, VisualStatePresets), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisualStatePresets_MetaData), NewProp_VisualStatePresets_MetaData) }; // 3351377574 2717571534
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_BulletTimeSettings = { "BulletTimeSettings", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AVisualEffectsManager, BulletTimeSettings), Z_Construct_UScriptStruct_FBulletTimeEffectSettings, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BulletTimeSettings_MetaData), NewProp_BulletTimeSettings_MetaData) }; // 370155421
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_TransitionCurve = { "TransitionCurve", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AVisualEffectsManager, TransitionCurve), Z_Construct_UClass_UCurveFloat_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TransitionCurve_MetaData), NewProp_TransitionCurve_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_DefaultTransitionDuration = { "DefaultTransitionDuration", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AVisualEffectsManager, DefaultTransitionDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultTransitionDuration_MetaData), NewProp_DefaultTransitionDuration_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_OnVisualStateChanged = { "OnVisualStateChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AVisualEffectsManager, OnVisualStateChanged), Z_Construct_UDelegateFunction_RoughReality_OnVisualStateChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnVisualStateChanged_MetaData), NewProp_OnVisualStateChanged_MetaData) }; // 3566825714
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AVisualEffectsManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_PostProcessComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_CurrentVisualState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_CurrentVisualState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_bIsTransitioning,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_GlobalMaterialParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_VisualStatePresets_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_VisualStatePresets_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_VisualStatePresets_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_VisualStatePresets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_BulletTimeSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_TransitionCurve,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_DefaultTransitionDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AVisualEffectsManager_Statics::NewProp_OnVisualStateChanged,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AVisualEffectsManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AVisualEffectsManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AVisualEffectsManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AVisualEffectsManager_Statics::ClassParams = {
	&AVisualEffectsManager::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AVisualEffectsManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AVisualEffectsManager_Statics::PropPointers),
	0,
	0x009000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AVisualEffectsManager_Statics::Class_MetaDataParams), Z_Construct_UClass_AVisualEffectsManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AVisualEffectsManager()
{
	if (!Z_Registration_Info_UClass_AVisualEffectsManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AVisualEffectsManager.OuterSingleton, Z_Construct_UClass_AVisualEffectsManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AVisualEffectsManager.OuterSingleton;
}
template<> ROUGHREALITY_API UClass* StaticClass<AVisualEffectsManager>()
{
	return AVisualEffectsManager::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AVisualEffectsManager);
AVisualEffectsManager::~AVisualEffectsManager() {}
// End Class AVisualEffectsManager

// Begin Registration
struct Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_VFX_VisualEffectsManager_h_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EGameVisualState_StaticEnum, TEXT("EGameVisualState"), &Z_Registration_Info_UEnum_EGameVisualState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3351377574U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FVisualEffectSettings::StaticStruct, Z_Construct_UScriptStruct_FVisualEffectSettings_Statics::NewStructOps, TEXT("VisualEffectSettings"), &Z_Registration_Info_UScriptStruct_VisualEffectSettings, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FVisualEffectSettings), 2717571534U) },
		{ FBulletTimeEffectSettings::StaticStruct, Z_Construct_UScriptStruct_FBulletTimeEffectSettings_Statics::NewStructOps, TEXT("BulletTimeEffectSettings"), &Z_Registration_Info_UScriptStruct_BulletTimeEffectSettings, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FBulletTimeEffectSettings), 370155421U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AVisualEffectsManager, AVisualEffectsManager::StaticClass, TEXT("AVisualEffectsManager"), &Z_Registration_Info_UClass_AVisualEffectsManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AVisualEffectsManager), 2244136291U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_VFX_VisualEffectsManager_h_1635189548(TEXT("/Script/RoughReality"),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_VFX_VisualEffectsManager_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_VFX_VisualEffectsManager_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_VFX_VisualEffectsManager_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_VFX_VisualEffectsManager_h_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_VFX_VisualEffectsManager_h_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_VFX_VisualEffectsManager_h_Statics::EnumInfo));
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
