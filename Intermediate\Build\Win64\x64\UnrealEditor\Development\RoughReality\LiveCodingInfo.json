{"RemapUnityFiles": {"Module.RoughReality.1.cpp.obj": ["AsyncLoadingManager.gen.cpp.obj", "CharacterStatsDataAsset.gen.cpp.obj", "GameAnalyticsManager.gen.cpp.obj", "GameEventSystem.gen.cpp.obj"], "Module.RoughReality.2.cpp.obj": ["GameplayLoopManager.gen.cpp.obj", "LevelTile.gen.cpp.obj", "ObjectPoolManager.gen.cpp.obj", "ProceduralLevelBuilder.gen.cpp.obj"], "Module.RoughReality.3.cpp.obj": ["ProgressionManager.gen.cpp.obj", "RookieCharacter.gen.cpp.obj", "RoughReality.init.gen.cpp.obj", "RoughRealityCharacter.gen.cpp.obj", "RoughRealityDataAsset.gen.cpp.obj", "RoughRealityGameMode.gen.cpp.obj", "RoughRealityGameModeBase.gen.cpp.obj"], "Module.RoughReality.4.cpp.obj": ["RoughSaveGame.gen.cpp.obj", "TileDefinitionDataAsset.gen.cpp.obj", "VisualEffectsManager.gen.cpp.obj", "WeaponBase.gen.cpp.obj"], "Module.RoughReality.5.cpp.obj": ["WeaponDataAsset.gen.cpp.obj", "PerModuleInline.gen.cpp.obj", "GameAnalyticsManager.cpp.obj", "RookieCharacter.cpp.obj", "AsyncLoadingManager.cpp.obj", "GameEventSystem.cpp.obj", "GameplayLoopManager.cpp.obj", "ObjectPoolManager.cpp.obj", "RoughRealityDataAsset.cpp.obj", "RoughRealityGameModeBase.cpp.obj", "CharacterStatsDataAsset.cpp.obj", "TileDefinitionDataAsset.cpp.obj", "WeaponDataAsset.cpp.obj", "LevelTile.cpp.obj", "ProceduralLevelBuilder.cpp.obj", "ProgressionManager.cpp.obj", "RoughReality.cpp.obj", "RoughRealityCharacter.cpp.obj", "RoughRealityGameMode.cpp.obj", "RoughSaveGame.cpp.obj", "VisualEffectsManager.cpp.obj", "WeaponBase.cpp.obj"]}}