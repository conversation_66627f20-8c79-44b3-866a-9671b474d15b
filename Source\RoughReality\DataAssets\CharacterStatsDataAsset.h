// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "../Core/RoughRealityDataAsset.h"
#include "CharacterStatsDataAsset.generated.h"

/**
 * Data Asset for character statistics and configuration
 * Used to configure Rookie's base stats and abilities
 */
UCLASS(BlueprintType, Blueprintable)
class ROUGHREALITY_API UCharacterStatsDataAsset : public URoughRealityDataAsset
{
	GENERATED_BODY()

public:
	UCharacterStatsDataAsset();

	/** Health Configuration */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Health", meta = (ClampMin = "1.0"))
	float MaxHealth = 100.0f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Health", meta = (ClampMin = "0.0"))
	float HealthRegenRate = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Health", meta = (ClampMin = "0.0"))
	float HealthRegenDelay = 5.0f;

	/** Movement Configuration */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Movement", meta = (ClampMin = "100.0"))
	float MaxWalkSpeed = 500.0f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Movement", meta = (ClampMin = "100.0"))
	float MaxRunSpeed = 800.0f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Movement", meta = (ClampMin = "300.0"))
	float JumpVelocity = 700.0f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Movement", meta = (ClampMin = "0.0", ClampMax = "1.0"))
	float AirControl = 0.35f;

	/** Bullet Time Configuration */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Bullet Time", meta = (ClampMin = "10.0"))
	float MaxBulletTimeEnergy = 100.0f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Bullet Time", meta = (ClampMin = "1.0"))
	float BulletTimeDrainRate = 25.0f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Bullet Time", meta = (ClampMin = "1.0"))
	float BulletTimeRechargeRate = 15.0f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Bullet Time", meta = (ClampMin = "0.1", ClampMax = "0.9"))
	float BulletTimeScale = 0.3f;

	/** Time Rewind Configuration */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Time Rewind", meta = (ClampMin = "1"))
	int32 MaxRewindCharges = 3;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Time Rewind", meta = (ClampMin = "0.1"))
	float SnapshotInterval = 0.2f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Time Rewind", meta = (ClampMin = "1.0"))
	float RewindDuration = 3.0f;

	/** Dash Configuration */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Dash", meta = (ClampMin = "100.0"))
	float DashDistance = 1000.0f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Dash", meta = (ClampMin = "0.1"))
	float DashDuration = 0.3f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Dash", meta = (ClampMin = "0.5"))
	float DashCooldown = 2.0f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Dash")
	bool bDashThroughEnemies = false;

	/** Combat Configuration */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Combat", meta = (ClampMin = "0.0"))
	float DamageMultiplier = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Combat", meta = (ClampMin = "0.0"))
	float DefenseMultiplier = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Combat", meta = (ClampMin = "0.0"))
	float CriticalHitChance = 0.1f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Combat", meta = (ClampMin = "1.0"))
	float CriticalHitMultiplier = 2.0f;

	/** Interaction Configuration */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Interaction", meta = (ClampMin = "50.0"))
	float InteractionRange = 200.0f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Interaction", meta = (ClampMin = "0.1"))
	float InteractionTime = 1.0f;

	/** Audio Configuration */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Audio")
	TSoftObjectPtr<class USoundBase> FootstepSound;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Audio")
	TSoftObjectPtr<class USoundBase> JumpSound;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Audio")
	TSoftObjectPtr<class USoundBase> LandSound;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Audio")
	TSoftObjectPtr<class USoundBase> DashSound;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Audio")
	TSoftObjectPtr<class USoundBase> BulletTimeStartSound;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Audio")
	TSoftObjectPtr<class USoundBase> BulletTimeStopSound;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Audio")
	TSoftObjectPtr<class USoundBase> RewindSound;

	/** Visual Effects Configuration */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "VFX")
	TSoftObjectPtr<class UParticleSystem> DashEffect;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "VFX")
	TSoftObjectPtr<class UParticleSystem> BulletTimeEffect;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "VFX")
	TSoftObjectPtr<class UParticleSystem> RewindEffect;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "VFX")
	TSoftObjectPtr<class UParticleSystem> DeathEffect;

public:
	/** Validation */
	virtual bool ValidateAsset() const override;

	/** Get calculated stats based on upgrades */
	UFUNCTION(BlueprintCallable, Category = "Stats")
	float GetEffectiveMaxHealth(int32 UpgradeLevel = 0) const;

	UFUNCTION(BlueprintCallable, Category = "Stats")
	float GetEffectiveDashDistance(int32 UpgradeLevel = 0) const;

	UFUNCTION(BlueprintCallable, Category = "Stats")
	float GetEffectiveBulletTimeEnergy(int32 UpgradeLevel = 0) const;

	UFUNCTION(BlueprintCallable, Category = "Stats")
	int32 GetEffectiveRewindCharges(int32 UpgradeLevel = 0) const;

protected:
	/** Upgrade scaling factors */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Upgrades", meta = (ClampMin = "0.0"))
	float HealthUpgradeMultiplier = 0.2f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Upgrades", meta = (ClampMin = "0.0"))
	float DashUpgradeMultiplier = 0.15f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Upgrades", meta = (ClampMin = "0.0"))
	float BulletTimeUpgradeMultiplier = 0.25f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Upgrades", meta = (ClampMin = "0"))
	int32 RewindChargesPerUpgrade = 1;
};
