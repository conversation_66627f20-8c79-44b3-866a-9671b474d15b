{"Version": "1.2", "Data": {"Source": "g:\\gamedev\\roughreality\\source\\roughreality\\levelgeneration\\leveltile.cpp", "ProvidedModule": "", "PCH": "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\x64\\roughrealityeditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.cpp20.h.pch", "Includes": ["g:\\gamedev\\roughreality\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\roughreality\\definitions.roughreality.h", "g:\\gamedev\\roughreality\\source\\roughreality\\levelgeneration\\leveltile.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "g:\\gamedev\\roughreality\\source\\roughreality\\levelgeneration\\procedurallevelbuilder.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\procedurallevelbuilder.generated.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\leveltile.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\components\\audiocomponent.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\public\\audio\\soundparametercontrollerinterface.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\soundparametercontrollerinterface.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\audioextensions\\public\\iaudioparametertransmitter.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\audiomixer\\public\\quartz\\audiomixerquantizedcommands.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\audiomixer\\public\\quartz\\audiomixerclock.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\audiomixer\\public\\quartz\\quartzmetronome.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\sound\\quartzsubscription.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\sound\\quartzsubscriptiontoken.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\sound\\quartzinterfaces.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\audiocomponent.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystatics.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetsystemlibrary.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\kismetsystemlibrary.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\sound\\dialoguetypes.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\dialoguetypes.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystaticstypes.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystaticstypes.generated.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystatics.generated.h", "g:\\gamedev\\roughreality\\source\\roughreality\\dataassets\\tiledefinitiondataasset.h", "g:\\gamedev\\roughreality\\source\\roughreality\\core\\roughrealitydataasset.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\roughrealitydataasset.generated.h", "g:\\gamedev\\roughreality\\intermediate\\build\\win64\\unrealeditor\\inc\\roughreality\\uht\\tiledefinitiondataasset.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}