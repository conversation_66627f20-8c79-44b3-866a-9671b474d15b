// Copyright Epic Games, Inc. All Rights Reserved.

#include "GameAnalyticsManager.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "EngineUtils.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/DateTime.h"

// Static member definition
AGameAnalyticsManager* AGameAnalyticsManager::Instance = nullptr;

AGameAnalyticsManager::AGameAnalyticsManager()
{
	PrimaryActorTick.bCanEverTick = true;
	PrimaryActorTick.TickInterval = 1.0f; // Tick once per second

	bEnableAnalytics = true;
	bEnableHeatmaps = true;
	bEnablePerformanceTracking = true;
	EventBatchSize = 100.0f;
	FlushInterval = 30.0f;
	AnalyticsEndpoint = TEXT("https://analytics.roughreality.com/api/events");

	SessionStartTime = 0.0f;
	LastFlushTime = 0.0f;
	FrameRateAccumulator = 0.0f;
	FrameCount = 0;
}

void AGameAnalyticsManager::BeginPlay()
{
	Super::BeginPlay();
	
	Instance = this;
	InitializeSession();
	
	UE_LOG(LogTemp, Log, TEXT("Game Analytics Manager initialized"));
}

void AGameAnalyticsManager::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	if (!bEnableAnalytics) return;

	// Update performance tracking
	if (bEnablePerformanceTracking)
	{
		UpdateFrameRateTracking(DeltaTime);
	}

	// Process pending events
	ProcessPendingEvents();

	// Check if we need to flush data
	float CurrentTime = GetWorld()->GetTimeSeconds();
	if (CurrentTime - LastFlushTime > FlushInterval)
	{
		FlushAnalyticsData();
		LastFlushTime = CurrentTime;
	}
}

void AGameAnalyticsManager::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	EndCurrentSession();
	Instance = nullptr;
	Super::EndPlay(EndPlayReason);
}

void AGameAnalyticsManager::LogPlayerAction(const FString& ActionType, const FString& Category, const FVector& Location, const TMap<FString, FString>& Parameters)
{
	if (!bEnableAnalytics) return;

	FPlayerActionEvent Event;
	Event.EventType = ActionType;
	Event.EventCategory = Category;
	Event.Location = Location;
	Event.Timestamp = GetWorld()->GetTimeSeconds();
	Event.Parameters = Parameters;

	PendingEvents.Add(Event);
	OnAnalyticsEvent.Broadcast(Event);
}

void AGameAnalyticsManager::LogWeaponFired(const FString& WeaponName, const FVector& Location, bool bHit, float Damage)
{
	TMap<FString, FString> Parameters;
	Parameters.Add(TEXT("WeaponName"), WeaponName);
	Parameters.Add(TEXT("Hit"), bHit ? TEXT("true") : TEXT("false"));
	Parameters.Add(TEXT("Damage"), FString::SanitizeFloat(Damage));

	LogPlayerAction(TEXT("WeaponFired"), TEXT("Combat"), Location, Parameters);

	// Update weapon analytics
	FWeaponAnalytics* WeaponData = WeaponAnalytics.Find(WeaponName);
	if (!WeaponData)
	{
		FWeaponAnalytics NewWeaponData;
		NewWeaponData.WeaponName = WeaponName;
		WeaponAnalytics.Add(WeaponName, NewWeaponData);
		WeaponData = &WeaponAnalytics[WeaponName];
	}

	WeaponData->ShotsFired++;
	if (bHit)
	{
		WeaponData->ShotsHit++;
		WeaponData->TotalDamageDealt += Damage;
	}

	// Record heatmap data
	if (bEnableHeatmaps)
	{
		RecordHeatmapEvent(TEXT("WeaponFire"), Location);
	}
}

void AGameAnalyticsManager::LogWeaponReload(const FString& WeaponName, const FVector& Location)
{
	TMap<FString, FString> Parameters;
	Parameters.Add(TEXT("WeaponName"), WeaponName);

	LogPlayerAction(TEXT("WeaponReload"), TEXT("Combat"), Location, Parameters);

	// Update weapon analytics
	FWeaponAnalytics* WeaponData = WeaponAnalytics.Find(WeaponName);
	if (WeaponData)
	{
		WeaponData->ReloadCount++;
	}
}

void AGameAnalyticsManager::LogEnemyKill(const FString& EnemyType, const FString& WeaponUsed, const FVector& Location)
{
	TMap<FString, FString> Parameters;
	Parameters.Add(TEXT("EnemyType"), EnemyType);
	Parameters.Add(TEXT("WeaponUsed"), WeaponUsed);

	LogPlayerAction(TEXT("EnemyKill"), TEXT("Combat"), Location, Parameters);

	// Update weapon analytics
	FWeaponAnalytics* WeaponData = WeaponAnalytics.Find(WeaponUsed);
	if (WeaponData)
	{
		WeaponData->Kills++;
	}

	// Record heatmap data
	if (bEnableHeatmaps)
	{
		RecordHeatmapEvent(TEXT("EnemyKill"), Location, 2);
	}
}

void AGameAnalyticsManager::LogPlayerDeath(const FString& CauseOfDeath, const FVector& Location)
{
	TMap<FString, FString> Parameters;
	Parameters.Add(TEXT("CauseOfDeath"), CauseOfDeath);

	LogPlayerAction(TEXT("PlayerDeath"), TEXT("Player"), Location, Parameters);

	// Record heatmap data
	if (bEnableHeatmaps)
	{
		RecordHeatmapEvent(TEXT("PlayerDeath"), Location, 3);
	}
}

void AGameAnalyticsManager::LogLevelComplete(int32 SectorIndex, int32 LevelIndex, float CompletionTime)
{
	TMap<FString, FString> Parameters;
	Parameters.Add(TEXT("SectorIndex"), FString::FromInt(SectorIndex));
	Parameters.Add(TEXT("LevelIndex"), FString::FromInt(LevelIndex));
	Parameters.Add(TEXT("CompletionTime"), FString::SanitizeFloat(CompletionTime));

	LogPlayerAction(TEXT("LevelComplete"), TEXT("Progression"), FVector::ZeroVector, Parameters);
}

void AGameAnalyticsManager::LogBulletTimeUsage(const FVector& Location, float Duration)
{
	TMap<FString, FString> Parameters;
	Parameters.Add(TEXT("Duration"), FString::SanitizeFloat(Duration));

	LogPlayerAction(TEXT("BulletTimeUsed"), TEXT("Ability"), Location, Parameters);

	// Record heatmap data
	if (bEnableHeatmaps)
	{
		RecordHeatmapEvent(TEXT("BulletTime"), Location);
	}
}

void AGameAnalyticsManager::LogTimeRewind(const FVector& Location)
{
	TMap<FString, FString> EmptyParams;
	LogPlayerAction(TEXT("TimeRewind"), TEXT("Ability"), Location, EmptyParams);

	// Record heatmap data
	if (bEnableHeatmaps)
	{
		RecordHeatmapEvent(TEXT("TimeRewind"), Location);
	}
}

void AGameAnalyticsManager::RecordHeatmapEvent(const FString& EventType, const FVector& Location, int32 Intensity)
{
	if (!bEnableHeatmaps) return;

	FHeatmapData HeatmapEntry;
	HeatmapEntry.Location = Location;
	HeatmapEntry.EventType = EventType;
	HeatmapEntry.Intensity = Intensity;
	HeatmapEntry.Timestamp = GetWorld()->GetTimeSeconds();

	HeatmapData.Add(HeatmapEntry);
}

TArray<FHeatmapData> AGameAnalyticsManager::GetHeatmapData(const FString& EventType) const
{
	TArray<FHeatmapData> FilteredData;

	for (const FHeatmapData& Data : HeatmapData)
	{
		if (Data.EventType == EventType)
		{
			FilteredData.Add(Data);
		}
	}

	return FilteredData;
}

void AGameAnalyticsManager::ClearHeatmapData()
{
	HeatmapData.Empty();
	UE_LOG(LogTemp, Log, TEXT("Heatmap data cleared"));
}

void AGameAnalyticsManager::UpdateWeaponUsageTime(const FString& WeaponName, float DeltaTime)
{
	FWeaponAnalytics* WeaponData = WeaponAnalytics.Find(WeaponName);
	if (!WeaponData)
	{
		FWeaponAnalytics NewWeaponData;
		NewWeaponData.WeaponName = WeaponName;
		WeaponAnalytics.Add(WeaponName, NewWeaponData);
		WeaponData = &WeaponAnalytics[WeaponName];
	}

	WeaponData->TimeUsed += DeltaTime;
}

FWeaponAnalytics AGameAnalyticsManager::GetWeaponAnalytics(const FString& WeaponName) const
{
	const FWeaponAnalytics* WeaponData = WeaponAnalytics.Find(WeaponName);
	return WeaponData ? *WeaponData : FWeaponAnalytics();
}

TArray<FWeaponAnalytics> AGameAnalyticsManager::GetAllWeaponAnalytics() const
{
	TArray<FWeaponAnalytics> AllWeaponData;
	
	for (const auto& WeaponPair : WeaponAnalytics)
	{
		AllWeaponData.Add(WeaponPair.Value);
	}

	return AllWeaponData;
}

void AGameAnalyticsManager::UpdatePerformanceMetrics()
{
	if (!bEnablePerformanceTracking) return;

	// Update frame rate metrics
	if (FrameCount > 0)
	{
		CurrentPerformanceMetrics.AverageFrameRate = FrameRateAccumulator / FrameCount;
		CurrentPerformanceMetrics.AverageFrameTime = 1.0f / CurrentPerformanceMetrics.AverageFrameRate;
	}

	// Get memory usage
	FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
	CurrentPerformanceMetrics.MemoryUsageMB = MemStats.UsedPhysical / (1024 * 1024);

	// Reset frame rate tracking
	FrameRateAccumulator = 0.0f;
	FrameCount = 0;
}

FPerformanceMetrics AGameAnalyticsManager::GetCurrentPerformanceMetrics() const
{
	return CurrentPerformanceMetrics;
}

void AGameAnalyticsManager::FlushAnalyticsData()
{
	if (PendingEvents.Num() == 0) return;

	// In a real implementation, this would send data to a server
	UE_LOG(LogTemp, Log, TEXT("Flushing %d analytics events"), PendingEvents.Num());

	// For now, just clear the pending events
	PendingEvents.Empty();
}

void AGameAnalyticsManager::SaveAnalyticsToFile(const FString& Filename)
{
	FString AnalyticsDirectory = GetAnalyticsDirectory();
	FString FilePath = FPaths::Combine(AnalyticsDirectory, Filename);

	FString JsonData = TEXT("{\n");
	JsonData += FString::Printf(TEXT("  \"SessionID\": \"%s\",\n"), *SessionID);
	JsonData += FString::Printf(TEXT("  \"SessionDuration\": %.2f,\n"), GetSessionDuration());
	JsonData += FString::Printf(TEXT("  \"EventCount\": %d,\n"), PendingEvents.Num());
	JsonData += TEXT("  \"Events\": [\n");

	for (int32 i = 0; i < PendingEvents.Num(); i++)
	{
		const FPlayerActionEvent& Event = PendingEvents[i];
		JsonData += TEXT("    {\n");
		JsonData += FString::Printf(TEXT("      \"Type\": \"%s\",\n"), *Event.EventType);
		JsonData += FString::Printf(TEXT("      \"Category\": \"%s\",\n"), *Event.EventCategory);
		JsonData += FString::Printf(TEXT("      \"Timestamp\": %.2f\n"), Event.Timestamp);
		JsonData += TEXT("    }");
		if (i < PendingEvents.Num() - 1)
		{
			JsonData += TEXT(",");
		}
		JsonData += TEXT("\n");
	}

	JsonData += TEXT("  ]\n");
	JsonData += TEXT("}");

	FFileHelper::SaveStringToFile(JsonData, *FilePath);
	UE_LOG(LogTemp, Log, TEXT("Analytics data saved to: %s"), *FilePath);
}

void AGameAnalyticsManager::LoadAnalyticsFromFile(const FString& Filename)
{
	// TODO: Implement loading analytics data from file
	UE_LOG(LogTemp, Log, TEXT("Loading analytics from file: %s"), *Filename);
}

void AGameAnalyticsManager::ExportHeatmapToCSV(const FString& Filename)
{
	FString AnalyticsDirectory = GetAnalyticsDirectory();
	FString FilePath = FPaths::Combine(AnalyticsDirectory, Filename);

	FString CSVData = TEXT("EventType,X,Y,Z,Intensity,Timestamp\n");

	for (const FHeatmapData& Data : HeatmapData)
	{
		CSVData += FString::Printf(TEXT("%s,%.2f,%.2f,%.2f,%d,%.2f\n"),
			*Data.EventType,
			Data.Location.X,
			Data.Location.Y,
			Data.Location.Z,
			Data.Intensity,
			Data.Timestamp
		);
	}

	FFileHelper::SaveStringToFile(CSVData, *FilePath);
	UE_LOG(LogTemp, Log, TEXT("Heatmap data exported to: %s"), *FilePath);
}

void AGameAnalyticsManager::StartNewSession()
{
	InitializeSession();
}

void AGameAnalyticsManager::EndCurrentSession()
{
	if (!SessionID.IsEmpty())
	{
		UE_LOG(LogTemp, Log, TEXT("Ending analytics session: %s (Duration: %.2f seconds)"), 
			*SessionID, GetSessionDuration());
		
		FlushAnalyticsData();
		SessionID.Empty();
	}
}

float AGameAnalyticsManager::GetSessionDuration() const
{
	if (SessionStartTime > 0.0f)
	{
		return GetWorld()->GetTimeSeconds() - SessionStartTime;
	}
	return 0.0f;
}

AGameAnalyticsManager* AGameAnalyticsManager::GetAnalyticsManager(const UObject* WorldContext)
{
	if (Instance && IsValid(Instance))
	{
		return Instance;
	}

	// Try to find existing instance
	UWorld* World = GEngine->GetWorldFromContextObject(WorldContext, EGetWorldErrorMode::LogAndReturnNull);
	if (World)
	{
		for (TActorIterator<AGameAnalyticsManager> ActorItr(World); ActorItr; ++ActorItr)
		{
			Instance = *ActorItr;
			return Instance;
		}

		// Create new instance if none found
		Instance = World->SpawnActor<AGameAnalyticsManager>();
	}

	return Instance;
}

void AGameAnalyticsManager::InitializeSession()
{
	SessionID = GenerateSessionID();
	SessionStartTime = GetWorld()->GetTimeSeconds();
	LastFlushTime = SessionStartTime;

	// Clear previous data
	PendingEvents.Empty();
	HeatmapData.Empty();
	WeaponAnalytics.Empty();

	UE_LOG(LogTemp, Log, TEXT("Started new analytics session: %s"), *SessionID);
}

void AGameAnalyticsManager::ProcessPendingEvents()
{
	// In a real implementation, this would batch and send events to a server
	// For now, we just limit the number of pending events
	if (PendingEvents.Num() > EventBatchSize)
	{
		// Remove oldest events
		int32 EventsToRemove = PendingEvents.Num() - EventBatchSize;
		PendingEvents.RemoveAt(0, EventsToRemove);
	}
}

void AGameAnalyticsManager::SendAnalyticsData(const TArray<FPlayerActionEvent>& Events)
{
	// TODO: Implement actual network sending
	UE_LOG(LogTemp, Log, TEXT("Sending %d analytics events to %s"), Events.Num(), *AnalyticsEndpoint);
}

FString AGameAnalyticsManager::GenerateSessionID() const
{
	FDateTime Now = FDateTime::Now();
	return FString::Printf(TEXT("RR_%s_%d"), 
		*Now.ToString(TEXT("%Y%m%d_%H%M%S")), 
		FMath::RandRange(1000, 9999));
}

void AGameAnalyticsManager::UpdateFrameRateTracking(float DeltaTime)
{
	if (DeltaTime > 0.0f)
	{
		float CurrentFPS = 1.0f / DeltaTime;
		FrameRateAccumulator += CurrentFPS;
		FrameCount++;

		// Update min/max frame rates
		if (FrameCount == 1)
		{
			CurrentPerformanceMetrics.MinFrameRate = CurrentFPS;
			CurrentPerformanceMetrics.MaxFrameRate = CurrentFPS;
		}
		else
		{
			CurrentPerformanceMetrics.MinFrameRate = FMath::Min(CurrentPerformanceMetrics.MinFrameRate, CurrentFPS);
			CurrentPerformanceMetrics.MaxFrameRate = FMath::Max(CurrentPerformanceMetrics.MaxFrameRate, CurrentFPS);
		}
	}
}

FString AGameAnalyticsManager::GetAnalyticsDirectory() const
{
	FString BaseDir = FPaths::ProjectSavedDir();
	FString AnalyticsDir = FPaths::Combine(BaseDir, TEXT("Analytics"));
	
	// Ensure directory exists
	IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
	if (!PlatformFile.DirectoryExists(*AnalyticsDir))
	{
		PlatformFile.CreateDirectoryTree(*AnalyticsDir);
	}
	
	return AnalyticsDir;
}

bool AGameAnalyticsManager::EnsureAnalyticsDirectoryExists() const
{
	FString AnalyticsDir = GetAnalyticsDirectory();
	IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
	return PlatformFile.DirectoryExists(*AnalyticsDir) || PlatformFile.CreateDirectoryTree(*AnalyticsDir);
}
