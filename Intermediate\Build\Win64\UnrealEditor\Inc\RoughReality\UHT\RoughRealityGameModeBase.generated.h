// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Core/RoughRealityGameModeBase.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class ARookieCharacter;
struct FGameplayTag;
#ifdef ROUGHREALITY_RoughRealityGameModeBase_generated_h
#error "RoughRealityGameModeBase.generated.h already included, missing '#pragma once' in RoughRealityGameModeBase.h"
#endif
#define ROUGHREALITY_RoughRealityGameModeBase_generated_h

#define FID_RoughReality_Source_RoughReality_Core_RoughRealityGameModeBase_h_14_DELEGATE \
ROUGHREALITY_API void FOnGameStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnGameStateChanged, FGameplayTag NewState);


#define FID_RoughReality_Source_RoughReality_Core_RoughRealityGameModeBase_h_15_DELEGATE \
ROUGHREALITY_API void FOnPlayerDied_DelegateWrapper(const FMulticastScriptDelegate& OnPlayerDied, ARookieCharacter* DeadPlayer);


#define FID_RoughReality_Source_RoughReality_Core_RoughRealityGameModeBase_h_16_DELEGATE \
ROUGHREALITY_API void FOnRunCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnRunCompleted, bool bSuccess);


#define FID_RoughReality_Source_RoughReality_Core_RoughRealityGameModeBase_h_25_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execResetSaveData); \
	DECLARE_FUNCTION(execLoadGameData); \
	DECLARE_FUNCTION(execSaveGameData); \
	DECLARE_FUNCTION(execGetTotalTeeth); \
	DECLARE_FUNCTION(execSpendTeeth); \
	DECLARE_FUNCTION(execAddTeeth); \
	DECLARE_FUNCTION(execRespawnPlayer); \
	DECLARE_FUNCTION(execHandlePlayerDeath); \
	DECLARE_FUNCTION(execAdvanceToNextSector); \
	DECLARE_FUNCTION(execAdvanceToNextLevel); \
	DECLARE_FUNCTION(execEndCurrentRun); \
	DECLARE_FUNCTION(execStartNewRun); \
	DECLARE_FUNCTION(execIsInGameState); \
	DECLARE_FUNCTION(execSetGameState);


#define FID_RoughReality_Source_RoughReality_Core_RoughRealityGameModeBase_h_25_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesARoughRealityGameModeBase(); \
	friend struct Z_Construct_UClass_ARoughRealityGameModeBase_Statics; \
public: \
	DECLARE_CLASS(ARoughRealityGameModeBase, AGameModeBase, COMPILED_IN_FLAGS(0 | CLASS_Transient | CLASS_Config), CASTCLASS_None, TEXT("/Script/RoughReality"), NO_API) \
	DECLARE_SERIALIZER(ARoughRealityGameModeBase)


#define FID_RoughReality_Source_RoughReality_Core_RoughRealityGameModeBase_h_25_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	ARoughRealityGameModeBase(ARoughRealityGameModeBase&&); \
	ARoughRealityGameModeBase(const ARoughRealityGameModeBase&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ARoughRealityGameModeBase); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ARoughRealityGameModeBase); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ARoughRealityGameModeBase) \
	NO_API virtual ~ARoughRealityGameModeBase();


#define FID_RoughReality_Source_RoughReality_Core_RoughRealityGameModeBase_h_22_PROLOG
#define FID_RoughReality_Source_RoughReality_Core_RoughRealityGameModeBase_h_25_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_RoughReality_Source_RoughReality_Core_RoughRealityGameModeBase_h_25_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_Core_RoughRealityGameModeBase_h_25_INCLASS_NO_PURE_DECLS \
	FID_RoughReality_Source_RoughReality_Core_RoughRealityGameModeBase_h_25_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ROUGHREALITY_API UClass* StaticClass<class ARoughRealityGameModeBase>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_RoughReality_Source_RoughReality_Core_RoughRealityGameModeBase_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
