{"version": "2.1.0", "$schema": "https://schemastore.azurewebsites.net/schemas/json/sarif-2.1.0-rtm.5.json", "runs": [{"results": [{"ruleId": "C4263", "message": {"text": "'void AR<PERSON>ieCharacter::TakeDamage(float)': member function does not override any base class virtual member function"}, "analysisTarget": {"uri": "file:///G:/Gamedev/RoughReality/Source/RoughReality/Weapons/WeaponBase.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///G:/Gamedev/RoughReality/Source/RoughReality/Characters/RookieCharacter.h"}, "region": {"startLine": 210, "startColumn": 7}}}]}, {"ruleId": "C4264", "message": {"text": "'float APawn::TakeDamage(float,const FDamageEvent &,AController *,AActor *)': no override available for virtual member function from base 'APawn'; function is hidden"}, "analysisTarget": {"uri": "file:///G:/Gamedev/RoughReality/Source/RoughReality/Weapons/WeaponBase.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///G:/Gamedev/RoughReality/Source/RoughReality/Characters/RookieCharacter.h"}, "region": {"startLine": 312, "startColumn": 1}}}], "relatedLocations": [{"id": 0, "physicalLocation": {"artifactLocation": {"uri": "file:///F:/Unreal/UE_5.5/Engine/Source/Runtime/Engine/Classes/GameFramework/Pawn.h"}, "region": {"startLine": 285, "startColumn": 27}}, "message": {"text": "see declaration of 'APawn::TakeDamage'"}}, {"id": 1, "physicalLocation": {"artifactLocation": {"uri": "file:///F:/Unreal/UE_5.5/Engine/Intermediate/Build/Win64/UnrealEditor/Inc/Engine/UHT/GameplayStatics.generated.h"}, "region": {"startLine": 17, "startColumn": 7}}, "message": {"text": "see declaration of 'APawn'"}}]}], "tool": {"driver": {"name": "MSVC", "shortDescription": {"text": "Microsoft Visual C++ Compiler Warnings/Errors"}, "informationUri": "https://docs.microsoft.com/cpp/error-messages/compiler-errors-1/c-cpp-build-errors"}}}]}