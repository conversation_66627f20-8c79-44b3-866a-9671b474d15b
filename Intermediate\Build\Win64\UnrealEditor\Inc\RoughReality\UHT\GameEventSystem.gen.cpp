// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "RoughReality/Core/GameEventSystem.h"
#include "Runtime/GameplayTags/Classes/GameplayTagContainer.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeGameEventSystem() {}

// Begin Cross Module References
COREUOBJECT_API UClass* Z_Construct_UClass_UObject_NoRegister();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTag();
ROUGHREALITY_API UClass* Z_Construct_UClass_AGameEventSystem();
ROUGHREALITY_API UClass* Z_Construct_UClass_AGameEventSystem_NoRegister();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_GameEventDelegate__DelegateSignature();
ROUGHREALITY_API UFunction* Z_Construct_UDelegateFunction_RoughReality_GameEventMulticastDelegate__DelegateSignature();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FEventListener();
ROUGHREALITY_API UScriptStruct* Z_Construct_UScriptStruct_FGameEventData();
UPackage* Z_Construct_UPackage__Script_RoughReality();
// End Cross Module References

// Begin ScriptStruct FGameEventData
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_GameEventData;
class UScriptStruct* FGameEventData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_GameEventData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_GameEventData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FGameEventData, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("GameEventData"));
	}
	return Z_Registration_Info_UScriptStruct_GameEventData.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FGameEventData>()
{
	return FGameEventData::StaticStruct();
}
struct Z_Construct_UScriptStruct_FGameEventData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventTag_MetaData[] = {
		{ "Category", "Event" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Instigator_MetaData[] = {
		{ "Category", "Event" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Target_MetaData[] = {
		{ "Category", "Event" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "Category", "Event" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Magnitude_MetaData[] = {
		{ "Category", "Event" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "Category", "Event" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Timestamp_MetaData[] = {
		{ "Category", "Event" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventTag;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Instigator;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Magnitude;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Parameters;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Timestamp;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FGameEventData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FGameEventData_Statics::NewProp_EventTag = { "EventTag", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGameEventData, EventTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventTag_MetaData), NewProp_EventTag_MetaData) }; // 1298103297
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FGameEventData_Statics::NewProp_Instigator = { "Instigator", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGameEventData, Instigator), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Instigator_MetaData), NewProp_Instigator_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FGameEventData_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGameEventData, Target), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Target_MetaData), NewProp_Target_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FGameEventData_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGameEventData, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FGameEventData_Statics::NewProp_Magnitude = { "Magnitude", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGameEventData, Magnitude), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Magnitude_MetaData), NewProp_Magnitude_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FGameEventData_Statics::NewProp_Parameters_ValueProp = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FGameEventData_Statics::NewProp_Parameters_Key_KeyProp = { "Parameters_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FGameEventData_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGameEventData, Parameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FGameEventData_Statics::NewProp_Timestamp = { "Timestamp", nullptr, (EPropertyFlags)0x0010000000000004, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FGameEventData, Timestamp), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Timestamp_MetaData), NewProp_Timestamp_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FGameEventData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGameEventData_Statics::NewProp_EventTag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGameEventData_Statics::NewProp_Instigator,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGameEventData_Statics::NewProp_Target,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGameEventData_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGameEventData_Statics::NewProp_Magnitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGameEventData_Statics::NewProp_Parameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGameEventData_Statics::NewProp_Parameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGameEventData_Statics::NewProp_Parameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FGameEventData_Statics::NewProp_Timestamp,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FGameEventData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FGameEventData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	&NewStructOps,
	"GameEventData",
	Z_Construct_UScriptStruct_FGameEventData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FGameEventData_Statics::PropPointers),
	sizeof(FGameEventData),
	alignof(FGameEventData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FGameEventData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FGameEventData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FGameEventData()
{
	if (!Z_Registration_Info_UScriptStruct_GameEventData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_GameEventData.InnerSingleton, Z_Construct_UScriptStruct_FGameEventData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_GameEventData.InnerSingleton;
}
// End ScriptStruct FGameEventData

// Begin Delegate FGameEventDelegate
struct Z_Construct_UDelegateFunction_RoughReality_GameEventDelegate__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventGameEventDelegate_Parms
	{
		FGameEventData EventData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_RoughReality_GameEventDelegate__DelegateSignature_Statics::NewProp_EventData = { "EventData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventGameEventDelegate_Parms, EventData), Z_Construct_UScriptStruct_FGameEventData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventData_MetaData), NewProp_EventData_MetaData) }; // 3793269421
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_GameEventDelegate__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_GameEventDelegate__DelegateSignature_Statics::NewProp_EventData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_GameEventDelegate__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_GameEventDelegate__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "GameEventDelegate__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_GameEventDelegate__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_GameEventDelegate__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_GameEventDelegate__DelegateSignature_Statics::_Script_RoughReality_eventGameEventDelegate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00120000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_GameEventDelegate__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_GameEventDelegate__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_GameEventDelegate__DelegateSignature_Statics::_Script_RoughReality_eventGameEventDelegate_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_GameEventDelegate__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_GameEventDelegate__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FGameEventDelegate_DelegateWrapper(const FScriptDelegate& GameEventDelegate, FGameEventData const& EventData)
{
	struct _Script_RoughReality_eventGameEventDelegate_Parms
	{
		FGameEventData EventData;
	};
	_Script_RoughReality_eventGameEventDelegate_Parms Parms;
	Parms.EventData=EventData;
	GameEventDelegate.ProcessDelegate<UObject>(&Parms);
}
// End Delegate FGameEventDelegate

// Begin Delegate FGameEventMulticastDelegate
struct Z_Construct_UDelegateFunction_RoughReality_GameEventMulticastDelegate__DelegateSignature_Statics
{
	struct _Script_RoughReality_eventGameEventMulticastDelegate_Parms
	{
		FGameEventData EventData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_RoughReality_GameEventMulticastDelegate__DelegateSignature_Statics::NewProp_EventData = { "EventData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_RoughReality_eventGameEventMulticastDelegate_Parms, EventData), Z_Construct_UScriptStruct_FGameEventData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventData_MetaData), NewProp_EventData_MetaData) }; // 3793269421
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_RoughReality_GameEventMulticastDelegate__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_RoughReality_GameEventMulticastDelegate__DelegateSignature_Statics::NewProp_EventData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_GameEventMulticastDelegate__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_RoughReality_GameEventMulticastDelegate__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_RoughReality, nullptr, "GameEventMulticastDelegate__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_RoughReality_GameEventMulticastDelegate__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_GameEventMulticastDelegate__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_RoughReality_GameEventMulticastDelegate__DelegateSignature_Statics::_Script_RoughReality_eventGameEventMulticastDelegate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_RoughReality_GameEventMulticastDelegate__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_RoughReality_GameEventMulticastDelegate__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_RoughReality_GameEventMulticastDelegate__DelegateSignature_Statics::_Script_RoughReality_eventGameEventMulticastDelegate_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_RoughReality_GameEventMulticastDelegate__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_RoughReality_GameEventMulticastDelegate__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FGameEventMulticastDelegate_DelegateWrapper(const FMulticastScriptDelegate& GameEventMulticastDelegate, FGameEventData const& EventData)
{
	struct _Script_RoughReality_eventGameEventMulticastDelegate_Parms
	{
		FGameEventData EventData;
	};
	_Script_RoughReality_eventGameEventMulticastDelegate_Parms Parms;
	Parms.EventData=EventData;
	GameEventMulticastDelegate.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FGameEventMulticastDelegate

// Begin ScriptStruct FEventListener
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_EventListener;
class UScriptStruct* FEventListener::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_EventListener.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_EventListener.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FEventListener, (UObject*)Z_Construct_UPackage__Script_RoughReality(), TEXT("EventListener"));
	}
	return Z_Registration_Info_UScriptStruct_EventListener.OuterSingleton;
}
template<> ROUGHREALITY_API UScriptStruct* StaticStruct<FEventListener>()
{
	return FEventListener::StaticStruct();
}
struct Z_Construct_UScriptStruct_FEventListener_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ListenerObject_MetaData[] = {
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventTag_MetaData[] = {
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Delegate_MetaData[] = {
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Priority_MetaData[] = {
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ListenerObject;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventTag;
	static const UECodeGen_Private::FDelegatePropertyParams NewProp_Delegate;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FEventListener>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FEventListener_Statics::NewProp_ListenerObject = { "ListenerObject", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEventListener, ListenerObject), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ListenerObject_MetaData), NewProp_ListenerObject_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FEventListener_Statics::NewProp_EventTag = { "EventTag", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEventListener, EventTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventTag_MetaData), NewProp_EventTag_MetaData) }; // 1298103297
const UECodeGen_Private::FDelegatePropertyParams Z_Construct_UScriptStruct_FEventListener_Statics::NewProp_Delegate = { "Delegate", nullptr, (EPropertyFlags)0x0010000000080000, UECodeGen_Private::EPropertyGenFlags::Delegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEventListener, Delegate), Z_Construct_UDelegateFunction_RoughReality_GameEventDelegate__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Delegate_MetaData), NewProp_Delegate_MetaData) }; // 2426103147
void Z_Construct_UScriptStruct_FEventListener_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FEventListener*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FEventListener_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FEventListener), &Z_Construct_UScriptStruct_FEventListener_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FEventListener_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FEventListener, Priority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Priority_MetaData), NewProp_Priority_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FEventListener_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEventListener_Statics::NewProp_ListenerObject,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEventListener_Statics::NewProp_EventTag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEventListener_Statics::NewProp_Delegate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEventListener_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FEventListener_Statics::NewProp_Priority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FEventListener_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FEventListener_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
	nullptr,
	&NewStructOps,
	"EventListener",
	Z_Construct_UScriptStruct_FEventListener_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FEventListener_Statics::PropPointers),
	sizeof(FEventListener),
	alignof(FEventListener),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000005),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FEventListener_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FEventListener_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FEventListener()
{
	if (!Z_Registration_Info_UScriptStruct_EventListener.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_EventListener.InnerSingleton, Z_Construct_UScriptStruct_FEventListener_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_EventListener.InnerSingleton;
}
// End ScriptStruct FEventListener

// Begin Class AGameEventSystem Function BroadcastBulletTimeActivated
struct Z_Construct_UFunction_AGameEventSystem_BroadcastBulletTimeActivated_Statics
{
	struct GameEventSystem_eventBroadcastBulletTimeActivated_Parms
	{
		const UObject* WorldContext;
		AActor* Player;
		float Duration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Common Events" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldContext_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldContext;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastBulletTimeActivated_Statics::NewProp_WorldContext = { "WorldContext", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastBulletTimeActivated_Parms, WorldContext), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldContext_MetaData), NewProp_WorldContext_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastBulletTimeActivated_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastBulletTimeActivated_Parms, Player), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastBulletTimeActivated_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastBulletTimeActivated_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameEventSystem_BroadcastBulletTimeActivated_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastBulletTimeActivated_Statics::NewProp_WorldContext,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastBulletTimeActivated_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastBulletTimeActivated_Statics::NewProp_Duration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastBulletTimeActivated_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_BroadcastBulletTimeActivated_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "BroadcastBulletTimeActivated", nullptr, nullptr, Z_Construct_UFunction_AGameEventSystem_BroadcastBulletTimeActivated_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastBulletTimeActivated_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameEventSystem_BroadcastBulletTimeActivated_Statics::GameEventSystem_eventBroadcastBulletTimeActivated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastBulletTimeActivated_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_BroadcastBulletTimeActivated_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameEventSystem_BroadcastBulletTimeActivated_Statics::GameEventSystem_eventBroadcastBulletTimeActivated_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameEventSystem_BroadcastBulletTimeActivated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_BroadcastBulletTimeActivated_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execBroadcastBulletTimeActivated)
{
	P_GET_OBJECT(UObject,Z_Param_WorldContext);
	P_GET_OBJECT(AActor,Z_Param_Player);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	AGameEventSystem::BroadcastBulletTimeActivated(Z_Param_WorldContext,Z_Param_Player,Z_Param_Duration);
	P_NATIVE_END;
}
// End Class AGameEventSystem Function BroadcastBulletTimeActivated

// Begin Class AGameEventSystem Function BroadcastEnemyKilled
struct Z_Construct_UFunction_AGameEventSystem_BroadcastEnemyKilled_Statics
{
	struct GameEventSystem_eventBroadcastEnemyKilled_Parms
	{
		const UObject* WorldContext;
		AActor* Enemy;
		AActor* Killer;
		FString WeaponUsed;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Common Events" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldContext_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponUsed_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldContext;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Enemy;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Killer;
	static const UECodeGen_Private::FStrPropertyParams NewProp_WeaponUsed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastEnemyKilled_Statics::NewProp_WorldContext = { "WorldContext", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastEnemyKilled_Parms, WorldContext), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldContext_MetaData), NewProp_WorldContext_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastEnemyKilled_Statics::NewProp_Enemy = { "Enemy", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastEnemyKilled_Parms, Enemy), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastEnemyKilled_Statics::NewProp_Killer = { "Killer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastEnemyKilled_Parms, Killer), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastEnemyKilled_Statics::NewProp_WeaponUsed = { "WeaponUsed", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastEnemyKilled_Parms, WeaponUsed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponUsed_MetaData), NewProp_WeaponUsed_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameEventSystem_BroadcastEnemyKilled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastEnemyKilled_Statics::NewProp_WorldContext,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastEnemyKilled_Statics::NewProp_Enemy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastEnemyKilled_Statics::NewProp_Killer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastEnemyKilled_Statics::NewProp_WeaponUsed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastEnemyKilled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_BroadcastEnemyKilled_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "BroadcastEnemyKilled", nullptr, nullptr, Z_Construct_UFunction_AGameEventSystem_BroadcastEnemyKilled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastEnemyKilled_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameEventSystem_BroadcastEnemyKilled_Statics::GameEventSystem_eventBroadcastEnemyKilled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastEnemyKilled_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_BroadcastEnemyKilled_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameEventSystem_BroadcastEnemyKilled_Statics::GameEventSystem_eventBroadcastEnemyKilled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameEventSystem_BroadcastEnemyKilled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_BroadcastEnemyKilled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execBroadcastEnemyKilled)
{
	P_GET_OBJECT(UObject,Z_Param_WorldContext);
	P_GET_OBJECT(AActor,Z_Param_Enemy);
	P_GET_OBJECT(AActor,Z_Param_Killer);
	P_GET_PROPERTY(FStrProperty,Z_Param_WeaponUsed);
	P_FINISH;
	P_NATIVE_BEGIN;
	AGameEventSystem::BroadcastEnemyKilled(Z_Param_WorldContext,Z_Param_Enemy,Z_Param_Killer,Z_Param_WeaponUsed);
	P_NATIVE_END;
}
// End Class AGameEventSystem Function BroadcastEnemyKilled

// Begin Class AGameEventSystem Function BroadcastEvent
struct Z_Construct_UFunction_AGameEventSystem_BroadcastEvent_Statics
{
	struct GameEventSystem_eventBroadcastEvent_Parms
	{
		FGameEventData EventData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Game Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Event Broadcasting */" },
#endif
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Event Broadcasting" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastEvent_Statics::NewProp_EventData = { "EventData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastEvent_Parms, EventData), Z_Construct_UScriptStruct_FGameEventData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventData_MetaData), NewProp_EventData_MetaData) }; // 3793269421
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameEventSystem_BroadcastEvent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastEvent_Statics::NewProp_EventData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastEvent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_BroadcastEvent_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "BroadcastEvent", nullptr, nullptr, Z_Construct_UFunction_AGameEventSystem_BroadcastEvent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastEvent_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameEventSystem_BroadcastEvent_Statics::GameEventSystem_eventBroadcastEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastEvent_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_BroadcastEvent_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameEventSystem_BroadcastEvent_Statics::GameEventSystem_eventBroadcastEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameEventSystem_BroadcastEvent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_BroadcastEvent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execBroadcastEvent)
{
	P_GET_STRUCT_REF(FGameEventData,Z_Param_Out_EventData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->BroadcastEvent(Z_Param_Out_EventData);
	P_NATIVE_END;
}
// End Class AGameEventSystem Function BroadcastEvent

// Begin Class AGameEventSystem Function BroadcastEventByTag
struct Z_Construct_UFunction_AGameEventSystem_BroadcastEventByTag_Statics
{
	struct GameEventSystem_eventBroadcastEventByTag_Parms
	{
		FGameplayTag EventTag;
		AActor* EventInstigator;
		AActor* Target;
		FVector Location;
		float Magnitude;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Game Events" },
		{ "CPP_Default_EventInstigator", "None" },
		{ "CPP_Default_Location", "" },
		{ "CPP_Default_Magnitude", "0.000000" },
		{ "CPP_Default_Target", "None" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventTag_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventTag;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EventInstigator;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Magnitude;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastEventByTag_Statics::NewProp_EventTag = { "EventTag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastEventByTag_Parms, EventTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventTag_MetaData), NewProp_EventTag_MetaData) }; // 1298103297
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastEventByTag_Statics::NewProp_EventInstigator = { "EventInstigator", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastEventByTag_Parms, EventInstigator), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastEventByTag_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastEventByTag_Parms, Target), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastEventByTag_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastEventByTag_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastEventByTag_Statics::NewProp_Magnitude = { "Magnitude", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastEventByTag_Parms, Magnitude), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameEventSystem_BroadcastEventByTag_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastEventByTag_Statics::NewProp_EventTag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastEventByTag_Statics::NewProp_EventInstigator,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastEventByTag_Statics::NewProp_Target,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastEventByTag_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastEventByTag_Statics::NewProp_Magnitude,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastEventByTag_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_BroadcastEventByTag_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "BroadcastEventByTag", nullptr, nullptr, Z_Construct_UFunction_AGameEventSystem_BroadcastEventByTag_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastEventByTag_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameEventSystem_BroadcastEventByTag_Statics::GameEventSystem_eventBroadcastEventByTag_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastEventByTag_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_BroadcastEventByTag_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameEventSystem_BroadcastEventByTag_Statics::GameEventSystem_eventBroadcastEventByTag_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameEventSystem_BroadcastEventByTag()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_BroadcastEventByTag_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execBroadcastEventByTag)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_EventTag);
	P_GET_OBJECT(AActor,Z_Param_EventInstigator);
	P_GET_OBJECT(AActor,Z_Param_Target);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Magnitude);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->BroadcastEventByTag(Z_Param_Out_EventTag,Z_Param_EventInstigator,Z_Param_Target,Z_Param_Out_Location,Z_Param_Magnitude);
	P_NATIVE_END;
}
// End Class AGameEventSystem Function BroadcastEventByTag

// Begin Class AGameEventSystem Function BroadcastEventDelayed
struct Z_Construct_UFunction_AGameEventSystem_BroadcastEventDelayed_Statics
{
	struct GameEventSystem_eventBroadcastEventDelayed_Parms
	{
		FGameEventData EventData;
		float Delay;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Game Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delayed Events */" },
#endif
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delayed Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventData;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Delay;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastEventDelayed_Statics::NewProp_EventData = { "EventData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastEventDelayed_Parms, EventData), Z_Construct_UScriptStruct_FGameEventData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventData_MetaData), NewProp_EventData_MetaData) }; // 3793269421
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastEventDelayed_Statics::NewProp_Delay = { "Delay", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastEventDelayed_Parms, Delay), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameEventSystem_BroadcastEventDelayed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastEventDelayed_Statics::NewProp_EventData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastEventDelayed_Statics::NewProp_Delay,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastEventDelayed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_BroadcastEventDelayed_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "BroadcastEventDelayed", nullptr, nullptr, Z_Construct_UFunction_AGameEventSystem_BroadcastEventDelayed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastEventDelayed_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameEventSystem_BroadcastEventDelayed_Statics::GameEventSystem_eventBroadcastEventDelayed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastEventDelayed_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_BroadcastEventDelayed_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameEventSystem_BroadcastEventDelayed_Statics::GameEventSystem_eventBroadcastEventDelayed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameEventSystem_BroadcastEventDelayed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_BroadcastEventDelayed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execBroadcastEventDelayed)
{
	P_GET_STRUCT_REF(FGameEventData,Z_Param_Out_EventData);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Delay);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->BroadcastEventDelayed(Z_Param_Out_EventData,Z_Param_Delay);
	P_NATIVE_END;
}
// End Class AGameEventSystem Function BroadcastEventDelayed

// Begin Class AGameEventSystem Function BroadcastEventWithParameters
struct Z_Construct_UFunction_AGameEventSystem_BroadcastEventWithParameters_Statics
{
	struct GameEventSystem_eventBroadcastEventWithParameters_Parms
	{
		FGameplayTag EventTag;
		TMap<FString,FString> Parameters;
		AActor* EventInstigator;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Game Events" },
		{ "CPP_Default_EventInstigator", "None" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventTag_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventTag;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Parameters;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EventInstigator;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastEventWithParameters_Statics::NewProp_EventTag = { "EventTag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastEventWithParameters_Parms, EventTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventTag_MetaData), NewProp_EventTag_MetaData) }; // 1298103297
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastEventWithParameters_Statics::NewProp_Parameters_ValueProp = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastEventWithParameters_Statics::NewProp_Parameters_Key_KeyProp = { "Parameters_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastEventWithParameters_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastEventWithParameters_Parms, Parameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastEventWithParameters_Statics::NewProp_EventInstigator = { "EventInstigator", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastEventWithParameters_Parms, EventInstigator), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameEventSystem_BroadcastEventWithParameters_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastEventWithParameters_Statics::NewProp_EventTag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastEventWithParameters_Statics::NewProp_Parameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastEventWithParameters_Statics::NewProp_Parameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastEventWithParameters_Statics::NewProp_Parameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastEventWithParameters_Statics::NewProp_EventInstigator,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastEventWithParameters_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_BroadcastEventWithParameters_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "BroadcastEventWithParameters", nullptr, nullptr, Z_Construct_UFunction_AGameEventSystem_BroadcastEventWithParameters_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastEventWithParameters_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameEventSystem_BroadcastEventWithParameters_Statics::GameEventSystem_eventBroadcastEventWithParameters_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastEventWithParameters_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_BroadcastEventWithParameters_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameEventSystem_BroadcastEventWithParameters_Statics::GameEventSystem_eventBroadcastEventWithParameters_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameEventSystem_BroadcastEventWithParameters()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_BroadcastEventWithParameters_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execBroadcastEventWithParameters)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_EventTag);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_Parameters);
	P_GET_OBJECT(AActor,Z_Param_EventInstigator);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->BroadcastEventWithParameters(Z_Param_Out_EventTag,Z_Param_Out_Parameters,Z_Param_EventInstigator);
	P_NATIVE_END;
}
// End Class AGameEventSystem Function BroadcastEventWithParameters

// Begin Class AGameEventSystem Function BroadcastLevelCompleted
struct Z_Construct_UFunction_AGameEventSystem_BroadcastLevelCompleted_Statics
{
	struct GameEventSystem_eventBroadcastLevelCompleted_Parms
	{
		const UObject* WorldContext;
		int32 SectorIndex;
		int32 LevelIndex;
		float CompletionTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Common Events" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldContext_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldContext;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SectorIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LevelIndex;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CompletionTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastLevelCompleted_Statics::NewProp_WorldContext = { "WorldContext", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastLevelCompleted_Parms, WorldContext), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldContext_MetaData), NewProp_WorldContext_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastLevelCompleted_Statics::NewProp_SectorIndex = { "SectorIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastLevelCompleted_Parms, SectorIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastLevelCompleted_Statics::NewProp_LevelIndex = { "LevelIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastLevelCompleted_Parms, LevelIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastLevelCompleted_Statics::NewProp_CompletionTime = { "CompletionTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastLevelCompleted_Parms, CompletionTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameEventSystem_BroadcastLevelCompleted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastLevelCompleted_Statics::NewProp_WorldContext,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastLevelCompleted_Statics::NewProp_SectorIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastLevelCompleted_Statics::NewProp_LevelIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastLevelCompleted_Statics::NewProp_CompletionTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastLevelCompleted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_BroadcastLevelCompleted_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "BroadcastLevelCompleted", nullptr, nullptr, Z_Construct_UFunction_AGameEventSystem_BroadcastLevelCompleted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastLevelCompleted_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameEventSystem_BroadcastLevelCompleted_Statics::GameEventSystem_eventBroadcastLevelCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastLevelCompleted_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_BroadcastLevelCompleted_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameEventSystem_BroadcastLevelCompleted_Statics::GameEventSystem_eventBroadcastLevelCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameEventSystem_BroadcastLevelCompleted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_BroadcastLevelCompleted_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execBroadcastLevelCompleted)
{
	P_GET_OBJECT(UObject,Z_Param_WorldContext);
	P_GET_PROPERTY(FIntProperty,Z_Param_SectorIndex);
	P_GET_PROPERTY(FIntProperty,Z_Param_LevelIndex);
	P_GET_PROPERTY(FFloatProperty,Z_Param_CompletionTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	AGameEventSystem::BroadcastLevelCompleted(Z_Param_WorldContext,Z_Param_SectorIndex,Z_Param_LevelIndex,Z_Param_CompletionTime);
	P_NATIVE_END;
}
// End Class AGameEventSystem Function BroadcastLevelCompleted

// Begin Class AGameEventSystem Function BroadcastPlayerDamaged
struct Z_Construct_UFunction_AGameEventSystem_BroadcastPlayerDamaged_Statics
{
	struct GameEventSystem_eventBroadcastPlayerDamaged_Parms
	{
		const UObject* WorldContext;
		AActor* Player;
		float Damage;
		AActor* DamageSource;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Common Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Common Game Events - Static helpers */" },
#endif
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Common Game Events - Static helpers" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldContext_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldContext;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Damage;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DamageSource;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastPlayerDamaged_Statics::NewProp_WorldContext = { "WorldContext", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastPlayerDamaged_Parms, WorldContext), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldContext_MetaData), NewProp_WorldContext_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastPlayerDamaged_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastPlayerDamaged_Parms, Player), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastPlayerDamaged_Statics::NewProp_Damage = { "Damage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastPlayerDamaged_Parms, Damage), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastPlayerDamaged_Statics::NewProp_DamageSource = { "DamageSource", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastPlayerDamaged_Parms, DamageSource), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameEventSystem_BroadcastPlayerDamaged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastPlayerDamaged_Statics::NewProp_WorldContext,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastPlayerDamaged_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastPlayerDamaged_Statics::NewProp_Damage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastPlayerDamaged_Statics::NewProp_DamageSource,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastPlayerDamaged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_BroadcastPlayerDamaged_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "BroadcastPlayerDamaged", nullptr, nullptr, Z_Construct_UFunction_AGameEventSystem_BroadcastPlayerDamaged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastPlayerDamaged_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameEventSystem_BroadcastPlayerDamaged_Statics::GameEventSystem_eventBroadcastPlayerDamaged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastPlayerDamaged_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_BroadcastPlayerDamaged_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameEventSystem_BroadcastPlayerDamaged_Statics::GameEventSystem_eventBroadcastPlayerDamaged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameEventSystem_BroadcastPlayerDamaged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_BroadcastPlayerDamaged_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execBroadcastPlayerDamaged)
{
	P_GET_OBJECT(UObject,Z_Param_WorldContext);
	P_GET_OBJECT(AActor,Z_Param_Player);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Damage);
	P_GET_OBJECT(AActor,Z_Param_DamageSource);
	P_FINISH;
	P_NATIVE_BEGIN;
	AGameEventSystem::BroadcastPlayerDamaged(Z_Param_WorldContext,Z_Param_Player,Z_Param_Damage,Z_Param_DamageSource);
	P_NATIVE_END;
}
// End Class AGameEventSystem Function BroadcastPlayerDamaged

// Begin Class AGameEventSystem Function BroadcastTimeRewind
struct Z_Construct_UFunction_AGameEventSystem_BroadcastTimeRewind_Statics
{
	struct GameEventSystem_eventBroadcastTimeRewind_Parms
	{
		const UObject* WorldContext;
		AActor* Player;
		FVector RewindLocation;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Common Events" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldContext_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RewindLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldContext;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RewindLocation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastTimeRewind_Statics::NewProp_WorldContext = { "WorldContext", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastTimeRewind_Parms, WorldContext), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldContext_MetaData), NewProp_WorldContext_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastTimeRewind_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastTimeRewind_Parms, Player), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastTimeRewind_Statics::NewProp_RewindLocation = { "RewindLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastTimeRewind_Parms, RewindLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RewindLocation_MetaData), NewProp_RewindLocation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameEventSystem_BroadcastTimeRewind_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastTimeRewind_Statics::NewProp_WorldContext,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastTimeRewind_Statics::NewProp_Player,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastTimeRewind_Statics::NewProp_RewindLocation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastTimeRewind_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_BroadcastTimeRewind_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "BroadcastTimeRewind", nullptr, nullptr, Z_Construct_UFunction_AGameEventSystem_BroadcastTimeRewind_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastTimeRewind_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameEventSystem_BroadcastTimeRewind_Statics::GameEventSystem_eventBroadcastTimeRewind_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastTimeRewind_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_BroadcastTimeRewind_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameEventSystem_BroadcastTimeRewind_Statics::GameEventSystem_eventBroadcastTimeRewind_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameEventSystem_BroadcastTimeRewind()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_BroadcastTimeRewind_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execBroadcastTimeRewind)
{
	P_GET_OBJECT(UObject,Z_Param_WorldContext);
	P_GET_OBJECT(AActor,Z_Param_Player);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_RewindLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	AGameEventSystem::BroadcastTimeRewind(Z_Param_WorldContext,Z_Param_Player,Z_Param_Out_RewindLocation);
	P_NATIVE_END;
}
// End Class AGameEventSystem Function BroadcastTimeRewind

// Begin Class AGameEventSystem Function BroadcastWeaponFired
struct Z_Construct_UFunction_AGameEventSystem_BroadcastWeaponFired_Statics
{
	struct GameEventSystem_eventBroadcastWeaponFired_Parms
	{
		const UObject* WorldContext;
		AActor* Weapon;
		AActor* Shooter;
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Common Events" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldContext_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldContext;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Weapon;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Shooter;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastWeaponFired_Statics::NewProp_WorldContext = { "WorldContext", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastWeaponFired_Parms, WorldContext), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldContext_MetaData), NewProp_WorldContext_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastWeaponFired_Statics::NewProp_Weapon = { "Weapon", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastWeaponFired_Parms, Weapon), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastWeaponFired_Statics::NewProp_Shooter = { "Shooter", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastWeaponFired_Parms, Shooter), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameEventSystem_BroadcastWeaponFired_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventBroadcastWeaponFired_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameEventSystem_BroadcastWeaponFired_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastWeaponFired_Statics::NewProp_WorldContext,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastWeaponFired_Statics::NewProp_Weapon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastWeaponFired_Statics::NewProp_Shooter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_BroadcastWeaponFired_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastWeaponFired_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_BroadcastWeaponFired_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "BroadcastWeaponFired", nullptr, nullptr, Z_Construct_UFunction_AGameEventSystem_BroadcastWeaponFired_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastWeaponFired_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameEventSystem_BroadcastWeaponFired_Statics::GameEventSystem_eventBroadcastWeaponFired_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C22401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_BroadcastWeaponFired_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_BroadcastWeaponFired_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameEventSystem_BroadcastWeaponFired_Statics::GameEventSystem_eventBroadcastWeaponFired_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameEventSystem_BroadcastWeaponFired()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_BroadcastWeaponFired_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execBroadcastWeaponFired)
{
	P_GET_OBJECT(UObject,Z_Param_WorldContext);
	P_GET_OBJECT(AActor,Z_Param_Weapon);
	P_GET_OBJECT(AActor,Z_Param_Shooter);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	AGameEventSystem::BroadcastWeaponFired(Z_Param_WorldContext,Z_Param_Weapon,Z_Param_Shooter,Z_Param_Out_Location);
	P_NATIVE_END;
}
// End Class AGameEventSystem Function BroadcastWeaponFired

// Begin Class AGameEventSystem Function CancelDelayedEvent
struct Z_Construct_UFunction_AGameEventSystem_CancelDelayedEvent_Statics
{
	struct GameEventSystem_eventCancelDelayedEvent_Parms
	{
		FGameplayTag EventTag;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Game Events" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventTag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventTag;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameEventSystem_CancelDelayedEvent_Statics::NewProp_EventTag = { "EventTag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventCancelDelayedEvent_Parms, EventTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventTag_MetaData), NewProp_EventTag_MetaData) }; // 1298103297
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameEventSystem_CancelDelayedEvent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_CancelDelayedEvent_Statics::NewProp_EventTag,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_CancelDelayedEvent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_CancelDelayedEvent_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "CancelDelayedEvent", nullptr, nullptr, Z_Construct_UFunction_AGameEventSystem_CancelDelayedEvent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_CancelDelayedEvent_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameEventSystem_CancelDelayedEvent_Statics::GameEventSystem_eventCancelDelayedEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_CancelDelayedEvent_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_CancelDelayedEvent_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameEventSystem_CancelDelayedEvent_Statics::GameEventSystem_eventCancelDelayedEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameEventSystem_CancelDelayedEvent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_CancelDelayedEvent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execCancelDelayedEvent)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_EventTag);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CancelDelayedEvent(Z_Param_Out_EventTag);
	P_NATIVE_END;
}
// End Class AGameEventSystem Function CancelDelayedEvent

// Begin Class AGameEventSystem Function ClearEventHistory
struct Z_Construct_UFunction_AGameEventSystem_ClearEventHistory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Game Events" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_ClearEventHistory_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "ClearEventHistory", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_ClearEventHistory_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_ClearEventHistory_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AGameEventSystem_ClearEventHistory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_ClearEventHistory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execClearEventHistory)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearEventHistory();
	P_NATIVE_END;
}
// End Class AGameEventSystem Function ClearEventHistory

// Begin Class AGameEventSystem Function EnableEventLogging
struct Z_Construct_UFunction_AGameEventSystem_EnableEventLogging_Statics
{
	struct GameEventSystem_eventEnableEventLogging_Parms
	{
		bool bEnable;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Game Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Event Debugging */" },
#endif
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Event Debugging" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bEnable_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnable;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AGameEventSystem_EnableEventLogging_Statics::NewProp_bEnable_SetBit(void* Obj)
{
	((GameEventSystem_eventEnableEventLogging_Parms*)Obj)->bEnable = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AGameEventSystem_EnableEventLogging_Statics::NewProp_bEnable = { "bEnable", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(GameEventSystem_eventEnableEventLogging_Parms), &Z_Construct_UFunction_AGameEventSystem_EnableEventLogging_Statics::NewProp_bEnable_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameEventSystem_EnableEventLogging_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_EnableEventLogging_Statics::NewProp_bEnable,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_EnableEventLogging_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_EnableEventLogging_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "EnableEventLogging", nullptr, nullptr, Z_Construct_UFunction_AGameEventSystem_EnableEventLogging_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_EnableEventLogging_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameEventSystem_EnableEventLogging_Statics::GameEventSystem_eventEnableEventLogging_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_EnableEventLogging_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_EnableEventLogging_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameEventSystem_EnableEventLogging_Statics::GameEventSystem_eventEnableEventLogging_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameEventSystem_EnableEventLogging()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_EnableEventLogging_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execEnableEventLogging)
{
	P_GET_UBOOL(Z_Param_bEnable);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->EnableEventLogging(Z_Param_bEnable);
	P_NATIVE_END;
}
// End Class AGameEventSystem Function EnableEventLogging

// Begin Class AGameEventSystem Function GetActiveEventTags
struct Z_Construct_UFunction_AGameEventSystem_GetActiveEventTags_Statics
{
	struct GameEventSystem_eventGetActiveEventTags_Parms
	{
		TArray<FGameplayTag> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Game Events" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameEventSystem_GetActiveEventTags_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(0, nullptr) }; // 1298103297
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGameEventSystem_GetActiveEventTags_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventGetActiveEventTags_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 1298103297
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameEventSystem_GetActiveEventTags_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_GetActiveEventTags_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_GetActiveEventTags_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_GetActiveEventTags_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_GetActiveEventTags_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "GetActiveEventTags", nullptr, nullptr, Z_Construct_UFunction_AGameEventSystem_GetActiveEventTags_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_GetActiveEventTags_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameEventSystem_GetActiveEventTags_Statics::GameEventSystem_eventGetActiveEventTags_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_GetActiveEventTags_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_GetActiveEventTags_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameEventSystem_GetActiveEventTags_Statics::GameEventSystem_eventGetActiveEventTags_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameEventSystem_GetActiveEventTags()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_GetActiveEventTags_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execGetActiveEventTags)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FGameplayTag>*)Z_Param__Result=P_THIS->GetActiveEventTags();
	P_NATIVE_END;
}
// End Class AGameEventSystem Function GetActiveEventTags

// Begin Class AGameEventSystem Function GetEventCount
struct Z_Construct_UFunction_AGameEventSystem_GetEventCount_Statics
{
	struct GameEventSystem_eventGetEventCount_Parms
	{
		FGameplayTag EventTag;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Game Events" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventTag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventTag;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameEventSystem_GetEventCount_Statics::NewProp_EventTag = { "EventTag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventGetEventCount_Parms, EventTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventTag_MetaData), NewProp_EventTag_MetaData) }; // 1298103297
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AGameEventSystem_GetEventCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventGetEventCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameEventSystem_GetEventCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_GetEventCount_Statics::NewProp_EventTag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_GetEventCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_GetEventCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_GetEventCount_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "GetEventCount", nullptr, nullptr, Z_Construct_UFunction_AGameEventSystem_GetEventCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_GetEventCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameEventSystem_GetEventCount_Statics::GameEventSystem_eventGetEventCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_GetEventCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_GetEventCount_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameEventSystem_GetEventCount_Statics::GameEventSystem_eventGetEventCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameEventSystem_GetEventCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_GetEventCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execGetEventCount)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_EventTag);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetEventCount(Z_Param_Out_EventTag);
	P_NATIVE_END;
}
// End Class AGameEventSystem Function GetEventCount

// Begin Class AGameEventSystem Function GetEventHistory
struct Z_Construct_UFunction_AGameEventSystem_GetEventHistory_Statics
{
	struct GameEventSystem_eventGetEventHistory_Parms
	{
		FGameplayTag EventTag;
		int32 MaxEvents;
		TArray<FGameEventData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Game Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Event History */" },
#endif
		{ "CPP_Default_MaxEvents", "10" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Event History" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventTag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventTag;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxEvents;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameEventSystem_GetEventHistory_Statics::NewProp_EventTag = { "EventTag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventGetEventHistory_Parms, EventTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventTag_MetaData), NewProp_EventTag_MetaData) }; // 1298103297
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AGameEventSystem_GetEventHistory_Statics::NewProp_MaxEvents = { "MaxEvents", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventGetEventHistory_Parms, MaxEvents), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameEventSystem_GetEventHistory_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGameEventData, METADATA_PARAMS(0, nullptr) }; // 3793269421
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_AGameEventSystem_GetEventHistory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventGetEventHistory_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3793269421
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameEventSystem_GetEventHistory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_GetEventHistory_Statics::NewProp_EventTag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_GetEventHistory_Statics::NewProp_MaxEvents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_GetEventHistory_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_GetEventHistory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_GetEventHistory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_GetEventHistory_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "GetEventHistory", nullptr, nullptr, Z_Construct_UFunction_AGameEventSystem_GetEventHistory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_GetEventHistory_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameEventSystem_GetEventHistory_Statics::GameEventSystem_eventGetEventHistory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_GetEventHistory_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_GetEventHistory_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameEventSystem_GetEventHistory_Statics::GameEventSystem_eventGetEventHistory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameEventSystem_GetEventHistory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_GetEventHistory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execGetEventHistory)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_EventTag);
	P_GET_PROPERTY(FIntProperty,Z_Param_MaxEvents);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FGameEventData>*)Z_Param__Result=P_THIS->GetEventHistory(Z_Param_Out_EventTag,Z_Param_MaxEvents);
	P_NATIVE_END;
}
// End Class AGameEventSystem Function GetEventHistory

// Begin Class AGameEventSystem Function GetGameEventSystem
struct Z_Construct_UFunction_AGameEventSystem_GetGameEventSystem_Statics
{
	struct GameEventSystem_eventGetGameEventSystem_Parms
	{
		const UObject* WorldContext;
		AGameEventSystem* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Game Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Static Access */" },
#endif
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Static Access" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldContext_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WorldContext;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AGameEventSystem_GetGameEventSystem_Statics::NewProp_WorldContext = { "WorldContext", nullptr, (EPropertyFlags)0x0010000000000082, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventGetGameEventSystem_Parms, WorldContext), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldContext_MetaData), NewProp_WorldContext_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AGameEventSystem_GetGameEventSystem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventGetGameEventSystem_Parms, ReturnValue), Z_Construct_UClass_AGameEventSystem_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameEventSystem_GetGameEventSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_GetGameEventSystem_Statics::NewProp_WorldContext,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_GetGameEventSystem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_GetGameEventSystem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_GetGameEventSystem_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "GetGameEventSystem", nullptr, nullptr, Z_Construct_UFunction_AGameEventSystem_GetGameEventSystem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_GetGameEventSystem_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameEventSystem_GetGameEventSystem_Statics::GameEventSystem_eventGetGameEventSystem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04022401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_GetGameEventSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_GetGameEventSystem_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameEventSystem_GetGameEventSystem_Statics::GameEventSystem_eventGetGameEventSystem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameEventSystem_GetGameEventSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_GetGameEventSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execGetGameEventSystem)
{
	P_GET_OBJECT(UObject,Z_Param_WorldContext);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(AGameEventSystem**)Z_Param__Result=AGameEventSystem::GetGameEventSystem(Z_Param_WorldContext);
	P_NATIVE_END;
}
// End Class AGameEventSystem Function GetGameEventSystem

// Begin Class AGameEventSystem Function IsEventFiltered
struct Z_Construct_UFunction_AGameEventSystem_IsEventFiltered_Statics
{
	struct GameEventSystem_eventIsEventFiltered_Parms
	{
		FGameplayTag EventTag;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Game Events" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventTag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventTag;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameEventSystem_IsEventFiltered_Statics::NewProp_EventTag = { "EventTag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventIsEventFiltered_Parms, EventTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventTag_MetaData), NewProp_EventTag_MetaData) }; // 1298103297
void Z_Construct_UFunction_AGameEventSystem_IsEventFiltered_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((GameEventSystem_eventIsEventFiltered_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AGameEventSystem_IsEventFiltered_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(GameEventSystem_eventIsEventFiltered_Parms), &Z_Construct_UFunction_AGameEventSystem_IsEventFiltered_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameEventSystem_IsEventFiltered_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_IsEventFiltered_Statics::NewProp_EventTag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_IsEventFiltered_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_IsEventFiltered_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_IsEventFiltered_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "IsEventFiltered", nullptr, nullptr, Z_Construct_UFunction_AGameEventSystem_IsEventFiltered_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_IsEventFiltered_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameEventSystem_IsEventFiltered_Statics::GameEventSystem_eventIsEventFiltered_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_IsEventFiltered_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_IsEventFiltered_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameEventSystem_IsEventFiltered_Statics::GameEventSystem_eventIsEventFiltered_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameEventSystem_IsEventFiltered()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_IsEventFiltered_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execIsEventFiltered)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_EventTag);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsEventFiltered(Z_Param_Out_EventTag);
	P_NATIVE_END;
}
// End Class AGameEventSystem Function IsEventFiltered

// Begin Class AGameEventSystem Function IsValidEventTag
struct Z_Construct_UFunction_AGameEventSystem_IsValidEventTag_Statics
{
	struct GameEventSystem_eventIsValidEventTag_Parms
	{
		FGameplayTag EventTag;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Game Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Event Validation */" },
#endif
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Event Validation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventTag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventTag;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameEventSystem_IsValidEventTag_Statics::NewProp_EventTag = { "EventTag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventIsValidEventTag_Parms, EventTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventTag_MetaData), NewProp_EventTag_MetaData) }; // 1298103297
void Z_Construct_UFunction_AGameEventSystem_IsValidEventTag_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((GameEventSystem_eventIsValidEventTag_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AGameEventSystem_IsValidEventTag_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(GameEventSystem_eventIsValidEventTag_Parms), &Z_Construct_UFunction_AGameEventSystem_IsValidEventTag_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameEventSystem_IsValidEventTag_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_IsValidEventTag_Statics::NewProp_EventTag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_IsValidEventTag_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_IsValidEventTag_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_IsValidEventTag_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "IsValidEventTag", nullptr, nullptr, Z_Construct_UFunction_AGameEventSystem_IsValidEventTag_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_IsValidEventTag_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameEventSystem_IsValidEventTag_Statics::GameEventSystem_eventIsValidEventTag_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_IsValidEventTag_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_IsValidEventTag_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameEventSystem_IsValidEventTag_Statics::GameEventSystem_eventIsValidEventTag_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameEventSystem_IsValidEventTag()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_IsValidEventTag_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execIsValidEventTag)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_EventTag);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsValidEventTag(Z_Param_Out_EventTag);
	P_NATIVE_END;
}
// End Class AGameEventSystem Function IsValidEventTag

// Begin Class AGameEventSystem Function LogEventStatistics
struct Z_Construct_UFunction_AGameEventSystem_LogEventStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Game Events" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_LogEventStatistics_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "LogEventStatistics", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_LogEventStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_LogEventStatistics_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AGameEventSystem_LogEventStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_LogEventStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execLogEventStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LogEventStatistics();
	P_NATIVE_END;
}
// End Class AGameEventSystem Function LogEventStatistics

// Begin Class AGameEventSystem Function OnDelayedEventTimer
struct Z_Construct_UFunction_AGameEventSystem_OnDelayedEventTimer_Statics
{
	struct GameEventSystem_eventOnDelayedEventTimer_Parms
	{
		FGameEventData EventData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Delayed event callback */" },
#endif
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Delayed event callback" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameEventSystem_OnDelayedEventTimer_Statics::NewProp_EventData = { "EventData", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventOnDelayedEventTimer_Parms, EventData), Z_Construct_UScriptStruct_FGameEventData, METADATA_PARAMS(0, nullptr) }; // 3793269421
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameEventSystem_OnDelayedEventTimer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_OnDelayedEventTimer_Statics::NewProp_EventData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_OnDelayedEventTimer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_OnDelayedEventTimer_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "OnDelayedEventTimer", nullptr, nullptr, Z_Construct_UFunction_AGameEventSystem_OnDelayedEventTimer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_OnDelayedEventTimer_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameEventSystem_OnDelayedEventTimer_Statics::GameEventSystem_eventOnDelayedEventTimer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00040401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_OnDelayedEventTimer_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_OnDelayedEventTimer_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameEventSystem_OnDelayedEventTimer_Statics::GameEventSystem_eventOnDelayedEventTimer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameEventSystem_OnDelayedEventTimer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_OnDelayedEventTimer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execOnDelayedEventTimer)
{
	P_GET_STRUCT(FGameEventData,Z_Param_EventData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnDelayedEventTimer(Z_Param_EventData);
	P_NATIVE_END;
}
// End Class AGameEventSystem Function OnDelayedEventTimer

// Begin Class AGameEventSystem Function RegisterEventListener
struct Z_Construct_UFunction_AGameEventSystem_RegisterEventListener_Statics
{
	struct GameEventSystem_eventRegisterEventListener_Parms
	{
		UObject* Listener;
		FGameplayTag EventTag;
		FScriptDelegate Delegate;
		int32 Priority;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Game Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Event Listening */" },
#endif
		{ "CPP_Default_Priority", "0" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Event Listening" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventTag_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Delegate_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Listener;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventTag;
	static const UECodeGen_Private::FDelegatePropertyParams NewProp_Delegate;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AGameEventSystem_RegisterEventListener_Statics::NewProp_Listener = { "Listener", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventRegisterEventListener_Parms, Listener), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameEventSystem_RegisterEventListener_Statics::NewProp_EventTag = { "EventTag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventRegisterEventListener_Parms, EventTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventTag_MetaData), NewProp_EventTag_MetaData) }; // 1298103297
const UECodeGen_Private::FDelegatePropertyParams Z_Construct_UFunction_AGameEventSystem_RegisterEventListener_Statics::NewProp_Delegate = { "Delegate", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Delegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventRegisterEventListener_Parms, Delegate), Z_Construct_UDelegateFunction_RoughReality_GameEventDelegate__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Delegate_MetaData), NewProp_Delegate_MetaData) }; // 2426103147
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AGameEventSystem_RegisterEventListener_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventRegisterEventListener_Parms, Priority), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameEventSystem_RegisterEventListener_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_RegisterEventListener_Statics::NewProp_Listener,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_RegisterEventListener_Statics::NewProp_EventTag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_RegisterEventListener_Statics::NewProp_Delegate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_RegisterEventListener_Statics::NewProp_Priority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_RegisterEventListener_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_RegisterEventListener_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "RegisterEventListener", nullptr, nullptr, Z_Construct_UFunction_AGameEventSystem_RegisterEventListener_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_RegisterEventListener_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameEventSystem_RegisterEventListener_Statics::GameEventSystem_eventRegisterEventListener_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_RegisterEventListener_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_RegisterEventListener_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameEventSystem_RegisterEventListener_Statics::GameEventSystem_eventRegisterEventListener_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameEventSystem_RegisterEventListener()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_RegisterEventListener_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execRegisterEventListener)
{
	P_GET_OBJECT(UObject,Z_Param_Listener);
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_EventTag);
	P_GET_PROPERTY_REF(FDelegateProperty,Z_Param_Out_Delegate);
	P_GET_PROPERTY(FIntProperty,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterEventListener(Z_Param_Listener,Z_Param_Out_EventTag,FGameEventDelegate(Z_Param_Out_Delegate),Z_Param_Priority);
	P_NATIVE_END;
}
// End Class AGameEventSystem Function RegisterEventListener

// Begin Class AGameEventSystem Function RegisterValidEventTag
struct Z_Construct_UFunction_AGameEventSystem_RegisterValidEventTag_Statics
{
	struct GameEventSystem_eventRegisterValidEventTag_Parms
	{
		FGameplayTag EventTag;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Game Events" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventTag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventTag;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameEventSystem_RegisterValidEventTag_Statics::NewProp_EventTag = { "EventTag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventRegisterValidEventTag_Parms, EventTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventTag_MetaData), NewProp_EventTag_MetaData) }; // 1298103297
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameEventSystem_RegisterValidEventTag_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_RegisterValidEventTag_Statics::NewProp_EventTag,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_RegisterValidEventTag_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_RegisterValidEventTag_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "RegisterValidEventTag", nullptr, nullptr, Z_Construct_UFunction_AGameEventSystem_RegisterValidEventTag_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_RegisterValidEventTag_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameEventSystem_RegisterValidEventTag_Statics::GameEventSystem_eventRegisterValidEventTag_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_RegisterValidEventTag_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_RegisterValidEventTag_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameEventSystem_RegisterValidEventTag_Statics::GameEventSystem_eventRegisterValidEventTag_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameEventSystem_RegisterValidEventTag()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_RegisterValidEventTag_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execRegisterValidEventTag)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_EventTag);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterValidEventTag(Z_Param_Out_EventTag);
	P_NATIVE_END;
}
// End Class AGameEventSystem Function RegisterValidEventTag

// Begin Class AGameEventSystem Function SetEventFilter
struct Z_Construct_UFunction_AGameEventSystem_SetEventFilter_Statics
{
	struct GameEventSystem_eventSetEventFilter_Parms
	{
		FGameplayTag EventTag;
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Game Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Event Filtering */" },
#endif
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Event Filtering" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventTag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventTag;
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameEventSystem_SetEventFilter_Statics::NewProp_EventTag = { "EventTag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventSetEventFilter_Parms, EventTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventTag_MetaData), NewProp_EventTag_MetaData) }; // 1298103297
void Z_Construct_UFunction_AGameEventSystem_SetEventFilter_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((GameEventSystem_eventSetEventFilter_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AGameEventSystem_SetEventFilter_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(GameEventSystem_eventSetEventFilter_Parms), &Z_Construct_UFunction_AGameEventSystem_SetEventFilter_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameEventSystem_SetEventFilter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_SetEventFilter_Statics::NewProp_EventTag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_SetEventFilter_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_SetEventFilter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_SetEventFilter_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "SetEventFilter", nullptr, nullptr, Z_Construct_UFunction_AGameEventSystem_SetEventFilter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_SetEventFilter_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameEventSystem_SetEventFilter_Statics::GameEventSystem_eventSetEventFilter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_SetEventFilter_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_SetEventFilter_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameEventSystem_SetEventFilter_Statics::GameEventSystem_eventSetEventFilter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameEventSystem_SetEventFilter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_SetEventFilter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execSetEventFilter)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_EventTag);
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetEventFilter(Z_Param_Out_EventTag,Z_Param_bEnabled);
	P_NATIVE_END;
}
// End Class AGameEventSystem Function SetEventFilter

// Begin Class AGameEventSystem Function UnregisterAllListeners
struct Z_Construct_UFunction_AGameEventSystem_UnregisterAllListeners_Statics
{
	struct GameEventSystem_eventUnregisterAllListeners_Parms
	{
		UObject* Listener;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Game Events" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Listener;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AGameEventSystem_UnregisterAllListeners_Statics::NewProp_Listener = { "Listener", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventUnregisterAllListeners_Parms, Listener), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameEventSystem_UnregisterAllListeners_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_UnregisterAllListeners_Statics::NewProp_Listener,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_UnregisterAllListeners_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_UnregisterAllListeners_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "UnregisterAllListeners", nullptr, nullptr, Z_Construct_UFunction_AGameEventSystem_UnregisterAllListeners_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_UnregisterAllListeners_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameEventSystem_UnregisterAllListeners_Statics::GameEventSystem_eventUnregisterAllListeners_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_UnregisterAllListeners_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_UnregisterAllListeners_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameEventSystem_UnregisterAllListeners_Statics::GameEventSystem_eventUnregisterAllListeners_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameEventSystem_UnregisterAllListeners()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_UnregisterAllListeners_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execUnregisterAllListeners)
{
	P_GET_OBJECT(UObject,Z_Param_Listener);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnregisterAllListeners(Z_Param_Listener);
	P_NATIVE_END;
}
// End Class AGameEventSystem Function UnregisterAllListeners

// Begin Class AGameEventSystem Function UnregisterEventListener
struct Z_Construct_UFunction_AGameEventSystem_UnregisterEventListener_Statics
{
	struct GameEventSystem_eventUnregisterEventListener_Parms
	{
		UObject* Listener;
		FGameplayTag EventTag;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Game Events" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventTag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Listener;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventTag;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AGameEventSystem_UnregisterEventListener_Statics::NewProp_Listener = { "Listener", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventUnregisterEventListener_Parms, Listener), Z_Construct_UClass_UObject_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AGameEventSystem_UnregisterEventListener_Statics::NewProp_EventTag = { "EventTag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(GameEventSystem_eventUnregisterEventListener_Parms, EventTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventTag_MetaData), NewProp_EventTag_MetaData) }; // 1298103297
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AGameEventSystem_UnregisterEventListener_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_UnregisterEventListener_Statics::NewProp_Listener,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AGameEventSystem_UnregisterEventListener_Statics::NewProp_EventTag,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_UnregisterEventListener_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AGameEventSystem_UnregisterEventListener_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AGameEventSystem, nullptr, "UnregisterEventListener", nullptr, nullptr, Z_Construct_UFunction_AGameEventSystem_UnregisterEventListener_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_UnregisterEventListener_Statics::PropPointers), sizeof(Z_Construct_UFunction_AGameEventSystem_UnregisterEventListener_Statics::GameEventSystem_eventUnregisterEventListener_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AGameEventSystem_UnregisterEventListener_Statics::Function_MetaDataParams), Z_Construct_UFunction_AGameEventSystem_UnregisterEventListener_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AGameEventSystem_UnregisterEventListener_Statics::GameEventSystem_eventUnregisterEventListener_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AGameEventSystem_UnregisterEventListener()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AGameEventSystem_UnregisterEventListener_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AGameEventSystem::execUnregisterEventListener)
{
	P_GET_OBJECT(UObject,Z_Param_Listener);
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_EventTag);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnregisterEventListener(Z_Param_Listener,Z_Param_Out_EventTag);
	P_NATIVE_END;
}
// End Class AGameEventSystem Function UnregisterEventListener

// Begin Class AGameEventSystem
void AGameEventSystem::StaticRegisterNativesAGameEventSystem()
{
	UClass* Class = AGameEventSystem::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "BroadcastBulletTimeActivated", &AGameEventSystem::execBroadcastBulletTimeActivated },
		{ "BroadcastEnemyKilled", &AGameEventSystem::execBroadcastEnemyKilled },
		{ "BroadcastEvent", &AGameEventSystem::execBroadcastEvent },
		{ "BroadcastEventByTag", &AGameEventSystem::execBroadcastEventByTag },
		{ "BroadcastEventDelayed", &AGameEventSystem::execBroadcastEventDelayed },
		{ "BroadcastEventWithParameters", &AGameEventSystem::execBroadcastEventWithParameters },
		{ "BroadcastLevelCompleted", &AGameEventSystem::execBroadcastLevelCompleted },
		{ "BroadcastPlayerDamaged", &AGameEventSystem::execBroadcastPlayerDamaged },
		{ "BroadcastTimeRewind", &AGameEventSystem::execBroadcastTimeRewind },
		{ "BroadcastWeaponFired", &AGameEventSystem::execBroadcastWeaponFired },
		{ "CancelDelayedEvent", &AGameEventSystem::execCancelDelayedEvent },
		{ "ClearEventHistory", &AGameEventSystem::execClearEventHistory },
		{ "EnableEventLogging", &AGameEventSystem::execEnableEventLogging },
		{ "GetActiveEventTags", &AGameEventSystem::execGetActiveEventTags },
		{ "GetEventCount", &AGameEventSystem::execGetEventCount },
		{ "GetEventHistory", &AGameEventSystem::execGetEventHistory },
		{ "GetGameEventSystem", &AGameEventSystem::execGetGameEventSystem },
		{ "IsEventFiltered", &AGameEventSystem::execIsEventFiltered },
		{ "IsValidEventTag", &AGameEventSystem::execIsValidEventTag },
		{ "LogEventStatistics", &AGameEventSystem::execLogEventStatistics },
		{ "OnDelayedEventTimer", &AGameEventSystem::execOnDelayedEventTimer },
		{ "RegisterEventListener", &AGameEventSystem::execRegisterEventListener },
		{ "RegisterValidEventTag", &AGameEventSystem::execRegisterValidEventTag },
		{ "SetEventFilter", &AGameEventSystem::execSetEventFilter },
		{ "UnregisterAllListeners", &AGameEventSystem::execUnregisterAllListeners },
		{ "UnregisterEventListener", &AGameEventSystem::execUnregisterEventListener },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(AGameEventSystem);
UClass* Z_Construct_UClass_AGameEventSystem_NoRegister()
{
	return AGameEventSystem::StaticClass();
}
struct Z_Construct_UClass_AGameEventSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Centralized Game Event System for Rough Reality\n * Provides decoupled communication between game systems\n */" },
#endif
		{ "IncludePath", "Core/GameEventSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Centralized Game Event System for Rough Reality\nProvides decoupled communication between game systems" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilteredEvents_MetaData[] = {
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidEventTags_MetaData[] = {
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventQueue_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Event Queue for delayed processing */" },
#endif
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Event Queue for delayed processing" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableEventLogging_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Configuration */" },
#endif
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxEventHistorySize_MetaData[] = {
		{ "Category", "Configuration" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoCleanupListeners_MetaData[] = {
		{ "Category", "Configuration" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalEventsBroadcast_MetaData[] = {
		{ "Category", "Statistics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/** Statistics */" },
#endif
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Statistics" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventCounts_MetaData[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/GameEventSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_FilteredEvents_ElementProp;
	static const UECodeGen_Private::FSetPropertyParams NewProp_FilteredEvents;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ValidEventTags_ElementProp;
	static const UECodeGen_Private::FSetPropertyParams NewProp_ValidEventTags;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventQueue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_EventQueue;
	static void NewProp_bEnableEventLogging_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableEventLogging;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxEventHistorySize;
	static void NewProp_bAutoCleanupListeners_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoCleanupListeners;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalEventsBroadcast;
	static const UECodeGen_Private::FIntPropertyParams NewProp_EventCounts_ValueProp;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventCounts_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_EventCounts;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AGameEventSystem_BroadcastBulletTimeActivated, "BroadcastBulletTimeActivated" }, // 544262885
		{ &Z_Construct_UFunction_AGameEventSystem_BroadcastEnemyKilled, "BroadcastEnemyKilled" }, // 2997964139
		{ &Z_Construct_UFunction_AGameEventSystem_BroadcastEvent, "BroadcastEvent" }, // 1268692922
		{ &Z_Construct_UFunction_AGameEventSystem_BroadcastEventByTag, "BroadcastEventByTag" }, // 2341059881
		{ &Z_Construct_UFunction_AGameEventSystem_BroadcastEventDelayed, "BroadcastEventDelayed" }, // 824885914
		{ &Z_Construct_UFunction_AGameEventSystem_BroadcastEventWithParameters, "BroadcastEventWithParameters" }, // 3013769630
		{ &Z_Construct_UFunction_AGameEventSystem_BroadcastLevelCompleted, "BroadcastLevelCompleted" }, // 830234582
		{ &Z_Construct_UFunction_AGameEventSystem_BroadcastPlayerDamaged, "BroadcastPlayerDamaged" }, // 1409950050
		{ &Z_Construct_UFunction_AGameEventSystem_BroadcastTimeRewind, "BroadcastTimeRewind" }, // 1342270105
		{ &Z_Construct_UFunction_AGameEventSystem_BroadcastWeaponFired, "BroadcastWeaponFired" }, // 1744385960
		{ &Z_Construct_UFunction_AGameEventSystem_CancelDelayedEvent, "CancelDelayedEvent" }, // 1814417859
		{ &Z_Construct_UFunction_AGameEventSystem_ClearEventHistory, "ClearEventHistory" }, // 855933631
		{ &Z_Construct_UFunction_AGameEventSystem_EnableEventLogging, "EnableEventLogging" }, // 598429595
		{ &Z_Construct_UFunction_AGameEventSystem_GetActiveEventTags, "GetActiveEventTags" }, // 775076864
		{ &Z_Construct_UFunction_AGameEventSystem_GetEventCount, "GetEventCount" }, // 869667505
		{ &Z_Construct_UFunction_AGameEventSystem_GetEventHistory, "GetEventHistory" }, // 627411236
		{ &Z_Construct_UFunction_AGameEventSystem_GetGameEventSystem, "GetGameEventSystem" }, // 2890896233
		{ &Z_Construct_UFunction_AGameEventSystem_IsEventFiltered, "IsEventFiltered" }, // 605012555
		{ &Z_Construct_UFunction_AGameEventSystem_IsValidEventTag, "IsValidEventTag" }, // 3530663365
		{ &Z_Construct_UFunction_AGameEventSystem_LogEventStatistics, "LogEventStatistics" }, // 4271976921
		{ &Z_Construct_UFunction_AGameEventSystem_OnDelayedEventTimer, "OnDelayedEventTimer" }, // 621915671
		{ &Z_Construct_UFunction_AGameEventSystem_RegisterEventListener, "RegisterEventListener" }, // 945879272
		{ &Z_Construct_UFunction_AGameEventSystem_RegisterValidEventTag, "RegisterValidEventTag" }, // 2256679355
		{ &Z_Construct_UFunction_AGameEventSystem_SetEventFilter, "SetEventFilter" }, // 160723967
		{ &Z_Construct_UFunction_AGameEventSystem_UnregisterAllListeners, "UnregisterAllListeners" }, // 1990285667
		{ &Z_Construct_UFunction_AGameEventSystem_UnregisterEventListener, "UnregisterEventListener" }, // 1465138863
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AGameEventSystem>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AGameEventSystem_Statics::NewProp_FilteredEvents_ElementProp = { "FilteredEvents", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(0, nullptr) }; // 1298103297
static_assert(TModels_V<CGetTypeHashable, FGameplayTag>, "The structure 'FGameplayTag' is used in a TSet but does not have a GetValueTypeHash defined");
const UECodeGen_Private::FSetPropertyParams Z_Construct_UClass_AGameEventSystem_Statics::NewProp_FilteredEvents = { "FilteredEvents", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Set, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameEventSystem, FilteredEvents), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilteredEvents_MetaData), NewProp_FilteredEvents_MetaData) }; // 1298103297
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AGameEventSystem_Statics::NewProp_ValidEventTags_ElementProp = { "ValidEventTags", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(0, nullptr) }; // 1298103297
static_assert(TModels_V<CGetTypeHashable, FGameplayTag>, "The structure 'FGameplayTag' is used in a TSet but does not have a GetValueTypeHash defined");
const UECodeGen_Private::FSetPropertyParams Z_Construct_UClass_AGameEventSystem_Statics::NewProp_ValidEventTags = { "ValidEventTags", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Set, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameEventSystem, ValidEventTags), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidEventTags_MetaData), NewProp_ValidEventTags_MetaData) }; // 1298103297
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AGameEventSystem_Statics::NewProp_EventQueue_Inner = { "EventQueue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGameEventData, METADATA_PARAMS(0, nullptr) }; // 3793269421
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AGameEventSystem_Statics::NewProp_EventQueue = { "EventQueue", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameEventSystem, EventQueue), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventQueue_MetaData), NewProp_EventQueue_MetaData) }; // 3793269421
void Z_Construct_UClass_AGameEventSystem_Statics::NewProp_bEnableEventLogging_SetBit(void* Obj)
{
	((AGameEventSystem*)Obj)->bEnableEventLogging = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AGameEventSystem_Statics::NewProp_bEnableEventLogging = { "bEnableEventLogging", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AGameEventSystem), &Z_Construct_UClass_AGameEventSystem_Statics::NewProp_bEnableEventLogging_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableEventLogging_MetaData), NewProp_bEnableEventLogging_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AGameEventSystem_Statics::NewProp_MaxEventHistorySize = { "MaxEventHistorySize", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameEventSystem, MaxEventHistorySize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxEventHistorySize_MetaData), NewProp_MaxEventHistorySize_MetaData) };
void Z_Construct_UClass_AGameEventSystem_Statics::NewProp_bAutoCleanupListeners_SetBit(void* Obj)
{
	((AGameEventSystem*)Obj)->bAutoCleanupListeners = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AGameEventSystem_Statics::NewProp_bAutoCleanupListeners = { "bAutoCleanupListeners", nullptr, (EPropertyFlags)0x0020080000000015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AGameEventSystem), &Z_Construct_UClass_AGameEventSystem_Statics::NewProp_bAutoCleanupListeners_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoCleanupListeners_MetaData), NewProp_bAutoCleanupListeners_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AGameEventSystem_Statics::NewProp_TotalEventsBroadcast = { "TotalEventsBroadcast", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameEventSystem, TotalEventsBroadcast), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalEventsBroadcast_MetaData), NewProp_TotalEventsBroadcast_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AGameEventSystem_Statics::NewProp_EventCounts_ValueProp = { "EventCounts", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AGameEventSystem_Statics::NewProp_EventCounts_Key_KeyProp = { "EventCounts_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(0, nullptr) }; // 1298103297
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_AGameEventSystem_Statics::NewProp_EventCounts = { "EventCounts", nullptr, (EPropertyFlags)0x0020080000000014, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AGameEventSystem, EventCounts), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventCounts_MetaData), NewProp_EventCounts_MetaData) }; // 1298103297
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AGameEventSystem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameEventSystem_Statics::NewProp_FilteredEvents_ElementProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameEventSystem_Statics::NewProp_FilteredEvents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameEventSystem_Statics::NewProp_ValidEventTags_ElementProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameEventSystem_Statics::NewProp_ValidEventTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameEventSystem_Statics::NewProp_EventQueue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameEventSystem_Statics::NewProp_EventQueue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameEventSystem_Statics::NewProp_bEnableEventLogging,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameEventSystem_Statics::NewProp_MaxEventHistorySize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameEventSystem_Statics::NewProp_bAutoCleanupListeners,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameEventSystem_Statics::NewProp_TotalEventsBroadcast,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameEventSystem_Statics::NewProp_EventCounts_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameEventSystem_Statics::NewProp_EventCounts_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AGameEventSystem_Statics::NewProp_EventCounts,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AGameEventSystem_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AGameEventSystem_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_RoughReality,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AGameEventSystem_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AGameEventSystem_Statics::ClassParams = {
	&AGameEventSystem::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AGameEventSystem_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AGameEventSystem_Statics::PropPointers),
	0,
	0x009000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AGameEventSystem_Statics::Class_MetaDataParams), Z_Construct_UClass_AGameEventSystem_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AGameEventSystem()
{
	if (!Z_Registration_Info_UClass_AGameEventSystem.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AGameEventSystem.OuterSingleton, Z_Construct_UClass_AGameEventSystem_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AGameEventSystem.OuterSingleton;
}
template<> ROUGHREALITY_API UClass* StaticClass<AGameEventSystem>()
{
	return AGameEventSystem::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AGameEventSystem);
AGameEventSystem::~AGameEventSystem() {}
// End Class AGameEventSystem

// Begin Registration
struct Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_GameEventSystem_h_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FGameEventData::StaticStruct, Z_Construct_UScriptStruct_FGameEventData_Statics::NewStructOps, TEXT("GameEventData"), &Z_Registration_Info_UScriptStruct_GameEventData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FGameEventData), 3793269421U) },
		{ FEventListener::StaticStruct, Z_Construct_UScriptStruct_FEventListener_Statics::NewStructOps, TEXT("EventListener"), &Z_Registration_Info_UScriptStruct_EventListener, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FEventListener), 4240786613U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AGameEventSystem, AGameEventSystem::StaticClass, TEXT("AGameEventSystem"), &Z_Registration_Info_UClass_AGameEventSystem, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AGameEventSystem), 1458381307U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_GameEventSystem_h_2408336547(TEXT("/Script/RoughReality"),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_GameEventSystem_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_GameEventSystem_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_GameEventSystem_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_RoughReality_Source_RoughReality_Core_GameEventSystem_h_Statics::ScriptStructInfo),
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
